<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'FluentCRMDBMigrator' => $baseDir . '/database/FluentCRMDBMigrator.php',
    'FluentCrmMigrations\\CampaignEmails' => $baseDir . '/database/migrations/CampaignEmails.php',
    'FluentCrmMigrations\\CampaignUrlMetrics' => $baseDir . '/database/migrations/CampaignUrlMetrics.php',
    'FluentCrmMigrations\\Campaigns' => $baseDir . '/database/migrations/Campaigns.php',
    'FluentCrmMigrations\\CompaniesMigrator' => $baseDir . '/database/migrations/CompaniesMigrator.php',
    'FluentCrmMigrations\\FunnelMetrics' => $baseDir . '/database/migrations/FunnelMetrics.php',
    'FluentCrmMigrations\\FunnelSequences' => $baseDir . '/database/migrations/FunnelSequences.php',
    'FluentCrmMigrations\\FunnelSubscribers' => $baseDir . '/database/migrations/FunnelSubscribers.php',
    'FluentCrmMigrations\\Funnels' => $baseDir . '/database/migrations/Funnels.php',
    'FluentCrmMigrations\\Lists' => $baseDir . '/database/migrations/Lists.php',
    'FluentCrmMigrations\\Meta' => $baseDir . '/database/migrations/Meta.php',
    'FluentCrmMigrations\\SubscriberEventTracking' => $baseDir . '/database/migrations/SubscriberEventTracking.php',
    'FluentCrmMigrations\\SubscriberMeta' => $baseDir . '/database/migrations/SubscriberMeta.php',
    'FluentCrmMigrations\\SubscriberNotes' => $baseDir . '/database/migrations/SubscriberNotes.php',
    'FluentCrmMigrations\\SubscriberPivot' => $baseDir . '/database/migrations/SubscriberPivot.php',
    'FluentCrmMigrations\\Subscribers' => $baseDir . '/database/migrations/Subscribers.php',
    'FluentCrmMigrations\\Tags' => $baseDir . '/database/migrations/Tags.php',
    'FluentCrmMigrations\\TermRelations' => $baseDir . '/database/migrations/TermRelations.php',
    'FluentCrmMigrations\\Terms' => $baseDir . '/database/migrations/Terms.php',
    'FluentCrmMigrations\\UrlStores' => $baseDir . '/database/migrations/UrlStores.php',
);
