{"packages": [{"name": "wpfluent/framework", "version": "1.0.4", "version_normalized": "*******", "source": {"type": "git", "url": "**************:wpfluent/framework.git", "reference": "388ddf78f1e40cf54edb64c593e645a3bfa5359b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wpfluent/framework/zipball/388ddf78f1e40cf54edb64c593e645a3bfa5359b", "reference": "388ddf78f1e40cf54edb64c593e645a3bfa5359b", "shasum": ""}, "require": {"php": ">=5.5.9"}, "time": "2021-07-04T07:35:22+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "source", "autoload": {"psr-4": {"WPFluent\\": "src/WPFluent"}}, "license": ["MIT"], "authors": [{"name": "Sheikh <PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A lightweight rest api based framework to build WordPress plugins.", "install-path": "../wpfluent/framework"}], "dev": true, "dev-package-names": []}