<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit3ec9aaa182e9c7febe2801c6e81775d2
{
    public static $files = array (
        '9680a2abca0f3f510cf2fd1b6d61afe6' => __DIR__ . '/../..' . '/boot/globals.php',
    );

    public static $prefixLengthsPsr4 = array (
        'W' => 
        array (
            'WPFluent\\' => 9,
        ),
        'F' => 
        array (
            'FluentCrm\\Includes\\' => 19,
            'FluentCrm\\Framework\\' => 20,
            'FluentCrm\\App\\' => 14,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'WPFluent\\' => 
        array (
            0 => __DIR__ . '/..' . '/wpfluent/framework/src/WPFluent',
        ),
        'FluentCrm\\Includes\\' => 
        array (
            0 => __DIR__ . '/../..' . '/includes',
        ),
        'FluentCrm\\Framework\\' => 
        array (
            0 => __DIR__ . '/..' . '/wpfluent/framework/src/WPFluent',
        ),
        'FluentCrm\\App\\' => 
        array (
            0 => __DIR__ . '/../..' . '/app',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
        'FluentCRMDBMigrator' => __DIR__ . '/../..' . '/database/FluentCRMDBMigrator.php',
        'FluentCrmMigrations\\CampaignEmails' => __DIR__ . '/../..' . '/database/migrations/CampaignEmails.php',
        'FluentCrmMigrations\\CampaignUrlMetrics' => __DIR__ . '/../..' . '/database/migrations/CampaignUrlMetrics.php',
        'FluentCrmMigrations\\Campaigns' => __DIR__ . '/../..' . '/database/migrations/Campaigns.php',
        'FluentCrmMigrations\\CompaniesMigrator' => __DIR__ . '/../..' . '/database/migrations/CompaniesMigrator.php',
        'FluentCrmMigrations\\FunnelMetrics' => __DIR__ . '/../..' . '/database/migrations/FunnelMetrics.php',
        'FluentCrmMigrations\\FunnelSequences' => __DIR__ . '/../..' . '/database/migrations/FunnelSequences.php',
        'FluentCrmMigrations\\FunnelSubscribers' => __DIR__ . '/../..' . '/database/migrations/FunnelSubscribers.php',
        'FluentCrmMigrations\\Funnels' => __DIR__ . '/../..' . '/database/migrations/Funnels.php',
        'FluentCrmMigrations\\Lists' => __DIR__ . '/../..' . '/database/migrations/Lists.php',
        'FluentCrmMigrations\\Meta' => __DIR__ . '/../..' . '/database/migrations/Meta.php',
        'FluentCrmMigrations\\SubscriberEventTracking' => __DIR__ . '/../..' . '/database/migrations/SubscriberEventTracking.php',
        'FluentCrmMigrations\\SubscriberMeta' => __DIR__ . '/../..' . '/database/migrations/SubscriberMeta.php',
        'FluentCrmMigrations\\SubscriberNotes' => __DIR__ . '/../..' . '/database/migrations/SubscriberNotes.php',
        'FluentCrmMigrations\\SubscriberPivot' => __DIR__ . '/../..' . '/database/migrations/SubscriberPivot.php',
        'FluentCrmMigrations\\Subscribers' => __DIR__ . '/../..' . '/database/migrations/Subscribers.php',
        'FluentCrmMigrations\\Tags' => __DIR__ . '/../..' . '/database/migrations/Tags.php',
        'FluentCrmMigrations\\TermRelations' => __DIR__ . '/../..' . '/database/migrations/TermRelations.php',
        'FluentCrmMigrations\\Terms' => __DIR__ . '/../..' . '/database/migrations/Terms.php',
        'FluentCrmMigrations\\UrlStores' => __DIR__ . '/../..' . '/database/migrations/UrlStores.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit3ec9aaa182e9c7febe2801c6e81775d2::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit3ec9aaa182e9c7febe2801c6e81775d2::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit3ec9aaa182e9c7febe2801c6e81775d2::$classMap;

        }, null, ClassLoader::class);
    }
}
