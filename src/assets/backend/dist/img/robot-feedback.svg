<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 57.18 64.03"><g id="Layer_2" data-name="Layer 2"><g id="Shadow"><path d="M33.26,63.11c0,.48-3.28.89-7.34.89s-7.35-.39-7.35-.89,3.29-.89,7.35-.89S33.26,62.63,33.26,63.11Z" transform="translate(-0.9 0.03)" style="fill:#181b21;fill-opacity:0.15000000596046448;fill-rule:evenodd"/></g><g id="Hand_-_Behind" data-name="Hand - Behind"><path d="M33.85,43.77c3.87-2,10.19-2.68,14.92-2.27l-1.26-6.37c-5.12.19-11.3,3.29-15.3,6.29Z" transform="translate(-0.9 0.03)" style="fill:#385661;fill-rule:evenodd"/><path d="M45.69,38.28l-.35-1.76a12.6,12.6,0,0,0-6.09,1.78A33.8,33.8,0,0,1,45.69,38.28Z" transform="translate(-0.9 0.03)" style="fill:#598291;fill-rule:evenodd"/><path d="M46.9,33.61a4.83,4.83,0,1,1-4.39,5.27,4.82,4.82,0,0,1,4.39-5.27Z" transform="translate(-0.9 0.03)" style="fill:#598291;fill-rule:evenodd"/><path d="M47,34.41a4,4,0,1,1-3.59,4.4A4,4,0,0,1,47,34.41Z" transform="translate(-0.9 0.03)" style="fill:#d3e8ef;fill-rule:evenodd"/><path d="M51.05,36.93c-.5,1.73-2.47,3.15-4.92,3.38a7,7,0,0,1-2.46-.17,4.66,4.66,0,0,1-.35-1.33v-.06a4,4,0,0,1,7.73-1.82Z" transform="translate(-0.9 0.03)" style="fill:#fff;fill-rule:evenodd"/><path d="M57.81,38a3,3,0,0,1-.11,2.72,3.07,3.07,0,0,1-1.59,1.56,2,2,0,0,1,0,.35,3.16,3.16,0,0,1-1.59,2c-1,.56-2.44.81-3.31-.08a19.85,19.85,0,0,0-2.2-2A20,20,0,0,0,46.52,41a3.93,3.93,0,0,1-2-3,5.32,5.32,0,0,1,1.78-4.11,21.14,21.14,0,0,0,3.32-4.05c.87-1.46,3.46-.32,4.12.87A2.63,2.63,0,0,1,53.68,33,4.05,4.05,0,0,1,53,34,13.56,13.56,0,0,1,57.81,38Z" transform="translate(-0.9 0.03)" style="fill:#598291;fill-rule:evenodd"/><path d="M46.88,40.42a16.9,16.9,0,0,1,4.86,3.7c1.08,1.12,4.35-.53,3.55-2.41a4.85,4.85,0,0,0-2.76-2.87c-.37.46-1,1.23-1.55,1.23A15.6,15.6,0,0,1,53.52,37a.71.71,0,0,1-.11.89c-.22.23-.32.31-.54.55A5.53,5.53,0,0,1,56,41.66,2.5,2.5,0,0,0,57.2,38.5a13.77,13.77,0,0,0-6.47-4.64,1.93,1.93,0,0,1,1.3,0c.73-.44,1.57-1.82,1-2.82-.41-.76-2.32-1.71-2.83-.85a20.45,20.45,0,0,1-3.44,4.2C44.32,36.87,45.1,39.45,46.88,40.42Z" transform="translate(-0.9 0.03)" style="fill:#d3e8ef;fill-rule:evenodd"/><path d="M45.31,37a2.85,2.85,0,0,0,1.55,2.43,26,26,0,0,1,5.43,4c.6.58,2.76,0,3-1.72a4.85,4.85,0,0,0-2.76-2.87c-.37.46-1,1.23-1.55,1.23A15.6,15.6,0,0,1,53.52,37a.71.71,0,0,1-.11.89c-.22.23-.32.31-.54.55a5.32,5.32,0,0,1,2.73,2.39,1.68,1.68,0,0,0,.55-.1A1.74,1.74,0,0,0,57.2,38.5a13.77,13.77,0,0,0-6.47-4.64,1.93,1.93,0,0,1,1.3,0c.73-.44,1.57-1.82,1-2.82-.41-.76-2.32-1.71-2.83-.85a20.45,20.45,0,0,1-3.44,4.2A4.87,4.87,0,0,0,45.31,37Z" transform="translate(-0.9 0.03)" style="fill:#fff;fill-rule:evenodd"/></g><g id="Body"><path d="M26.26,53.41a1.73,1.73,0,0,1-1-1,23.8,23.8,0,0,1-.61-3q-.09-.64-.15-1.26a1.53,1.53,0,0,1,.61-1.55,13.18,13.18,0,0,1,3.44-1.27c.3-.07.61-.16.92-.25h.29l.95.18a12.66,12.66,0,0,1,3.52,1A1.52,1.52,0,0,1,35,47.75,10.37,10.37,0,0,0,34.91,49a21.6,21.6,0,0,1-.39,3,1.57,1.57,0,0,1-.91,1A10.63,10.63,0,0,1,26.26,53.41Z" transform="translate(-0.9 0.03)" style="fill:#598291;fill-rule:evenodd"/><path d="M26.41,52.68a1,1,0,0,1-.54-.54,20.81,20.81,0,0,1-.73-4.07.88.88,0,0,1,.32-.89,16.31,16.31,0,0,1,4.14-1.4,16.35,16.35,0,0,1,4.25,1.06.84.84,0,0,1,.39.86,20.55,20.55,0,0,1-.42,4.14,1,1,0,0,1-.5.58A9.69,9.69,0,0,1,26.41,52.68Z" transform="translate(-0.9 0.03)" style="fill:#d3e8ef;fill-rule:evenodd"/><path d="M32.72,52.67a9.55,9.55,0,0,1-2.8.51l-.31-7.42a29.1,29.1,0,0,1,3.48.75C33,48.56,32.91,50.65,32.72,52.67Z" transform="translate(-0.9 0.03)" style="fill:#fff;fill-rule:evenodd"/><path d="M24.2,51.46a2.5,2.5,0,0,1-1.46-1.54,33.7,33.7,0,0,1-.94-4.44c-.09-.64-.16-1.27-.22-1.89a2.29,2.29,0,0,1,.94-2.33,19.81,19.81,0,0,1,5.17-1.84c.47-.12.94-.23,1.4-.36L29.3,39h.22a7,7,0,0,1,1.48.23,18.93,18.93,0,0,1,5.31,1.49A2.23,2.23,0,0,1,37.42,43c0,.63-.07,1.27-.07,1.9a31,31,0,0,1-.58,4.5A2.54,2.54,0,0,1,35.44,51,15.89,15.89,0,0,1,24.2,51.46Z" transform="translate(-0.9 0.03)" style="fill:#598291;fill-rule:evenodd"/><path d="M24.56,50.41a1.42,1.42,0,0,1-.8-.83,33,33,0,0,1-1.13-6.15,1.26,1.26,0,0,1,.48-1.35c1.2-.8,3.87-1.51,6.25-2.11,2.4.41,5.14.9,6.4,1.62a1.24,1.24,0,0,1,.58,1.3,31.69,31.69,0,0,1-.62,6.22A1.47,1.47,0,0,1,35,50,14.82,14.82,0,0,1,24.56,50.41Z" transform="translate(-0.9 0.03)" style="fill:#598291;fill-rule:evenodd"/><path d="M29,51.13a1.74,1.74,0,0,1-1.57-1l-.75-9.45c.88-.25,1.81-.49,2.7-.71,2.4.41,5.14.9,6.4,1.62a1.24,1.24,0,0,1,.58,1.3,31.69,31.69,0,0,1-.62,6.22A1.47,1.47,0,0,1,35,50,14.57,14.57,0,0,1,29,51.13Z" transform="translate(-0.9 0.03)" style="fill:#7eaaba;fill-rule:evenodd"/><path d="M23.05,46.56c-.19-1.05-.29-2-.4-3.11a1.23,1.23,0,0,1,.48-1.34c1.19-.8,3.87-1.52,6.25-2.12,2.39.41,5.14.91,6.39,1.62a1.24,1.24,0,0,1,.59,1.3c0,1.14-.06,2.08-.16,3.14C32,47.66,27.52,47.34,23.05,46.56Z" transform="translate(-0.9 0.03)" style="fill:#d3e8ef;fill-rule:evenodd"/><path d="M27.54,47.13a1.45,1.45,0,0,1-.48-1.28l-.27-5.17c.84-.25,1.74-.48,2.59-.69,2.39.41,5.14.91,6.39,1.62a1.24,1.24,0,0,1,.59,1.3c0,1.14-.06,2.08-.16,3.14A19.77,19.77,0,0,1,27.54,47.13Z" transform="translate(-0.9 0.03)" style="fill:#fff;fill-rule:evenodd"/><path d="M32.33,43.82h0a.78.78,0,0,1,.75-.81h1.1a.76.76,0,0,1,.78.74L35.05,46a2.24,2.24,0,0,1-.72,1.86,3.22,3.22,0,0,1-2,.7A3.35,3.35,0,0,1,30.24,48a2.2,2.2,0,0,1-.86-1.78L29.29,44a.77.77,0,0,1,.74-.81h1.09a.78.78,0,0,1,.8.74Z" transform="translate(-0.9 0.03)" style="fill:#fff;fill-rule:evenodd"/><path d="M34.43,43.85l.09,2.23A1.67,1.67,0,0,1,34,47.5a2.69,2.69,0,0,1-1.71.55,2.61,2.61,0,0,1-1.74-.42,1.72,1.72,0,0,1-.6-1.37L29.83,44a.3.3,0,0,1,.25-.25h1.08a.23.23,0,0,1,.23.23l.09,2.07a1,1,0,0,0,.2.59.77.77,0,0,0,.57.14.79.79,0,0,0,.59-.2A.86.86,0,0,0,33,46l-.08-2.15a.27.27,0,0,1,.25-.26h1.06A.26.26,0,0,1,34.43,43.85Z" transform="translate(-0.9 0.03)" style="fill:#0b87e8;fill-rule:evenodd"/><path d="M33.16,43.74a.14.14,0,0,0-.12.13L33.13,46a.9.9,0,0,1-.17.69,1,1,0,0,1-.66.25v1a2.51,2.51,0,0,0,1.63-.52,1.61,1.61,0,0,0,.5-1.33l-.09-2.24h0a.11.11,0,0,0-.13-.11Z" transform="translate(-0.9 0.03)" style="fill:#57b6ff;fill-rule:evenodd"/></g><g id="Float"><path d="M29.52,55.41c.7.31,1.07.69,1,1.07s-.58.58-1.33.58a9.89,9.89,0,0,1-2.3-.26,11.45,11.45,0,0,1-2.22-.7c-.71-.32-1.07-.7-1-1.08s.57-.58,1.33-.58a9.83,9.83,0,0,1,2.3.26A10.26,10.26,0,0,1,29.52,55.41Zm-5.2-.26a2.8,2.8,0,0,0,.62.32,9.34,9.34,0,0,0,2.06.66,10.44,10.44,0,0,0,2.16.25,2.15,2.15,0,0,0,.67-.05,3.46,3.46,0,0,0-.6-.32,9.93,9.93,0,0,0-2.07-.64A9.72,9.72,0,0,0,25,55.12,4,4,0,0,0,24.32,55.15Z" transform="translate(-0.9 0.03)" style="fill:#008bf6"/><path d="M28.12,57.6c.43.21.66.46.61.73s-.39.44-.86.44a5.08,5.08,0,0,1-1.4-.13,5.61,5.61,0,0,1-1.33-.44c-.44-.22-.65-.48-.61-.74s.38-.44.85-.44a6,6,0,0,1,1.4.13A5.16,5.16,0,0,1,28.12,57.6ZM25,57.49c0,.18.84.54,1.52.7s1.65.16,1.68,0-.8-.51-1.53-.69S25.08,57.31,25,57.49Z" transform="translate(-0.9 0.03)" style="fill:#008bf6"/></g><g id="Bow_Tie" data-name="Bow Tie"><path d="M26.92,41.86l-.08,0a.64.64,0,0,0-.32.85,5.77,5.77,0,0,0,2.19,2.71c.35.18.62,0,.93-.24a8.36,8.36,0,0,0,2.18-3.1l-.76-.93A11,11,0,0,0,26.92,41.86Z" transform="translate(-0.9 0.03)" style="fill:#3c942c;fill-rule:evenodd"/><path d="M30,42.75a8.08,8.08,0,0,1-.35.8,3.78,3.78,0,0,0,1.11-1.07l.37-.13a10.4,10.4,0,0,1-1.87,2.41.88.88,0,0,1-.25.18.11.11,0,0,1-.08,0,4.83,4.83,0,0,1-1.34-1.47C28.4,43.25,29.2,43,30,42.75Z" transform="translate(-0.9 0.03)" style="fill:#82e371;fill-rule:evenodd"/><path d="M34.63,39.66h0a.63.63,0,0,1,.75.51,6.11,6.11,0,0,1,.23,2.29c-.06.39-.26.39-.58.39a6.08,6.08,0,0,1-2.91-1l-.17-1A8,8,0,0,1,34.63,39.66Z" transform="translate(-0.9 0.03)" style="fill:#3c942c;fill-rule:evenodd"/><path d="M33.39,41.26a5.82,5.82,0,0,0,.66.44,2.7,2.7,0,0,1-1.19-.18l-.25.12A7.86,7.86,0,0,0,35,42.4a.45.45,0,0,0,.23,0v-.06A5.79,5.79,0,0,0,35,40.4C34.47,40.71,33.94,41,33.39,41.26Z" transform="translate(-0.9 0.03)" style="fill:#82e371;fill-rule:evenodd"/><path d="M30.28,41.12a9.15,9.15,0,0,1,2.09-.75,1,1,0,0,1,.83.16,4.44,4.44,0,0,1,.28,1.65,2.49,2.49,0,0,1-2.72,1A4.7,4.7,0,0,1,30,41.85C29.87,41.6,29.86,41.41,30.28,41.12Z" transform="translate(-0.9 0.03)" style="fill:#3c942c;fill-rule:evenodd"/><path d="M30.66,42.29a4.34,4.34,0,0,0,.35.49,2.21,2.21,0,0,0,2.05-.73,5.51,5.51,0,0,0-.06-.57A5,5,0,0,1,30.66,42.29Z" transform="translate(-0.9 0.03)" style="fill:#82e371;fill-rule:evenodd"/></g><g id="Hand_-_Front" data-name="Hand - Front"><path d="M22.16,45.34c1,4.95,6.67,8.93,10.89,11.1l2.47-6.2A13.34,13.34,0,0,1,24.69,43.7C24.06,42.71,21.59,42.5,22.16,45.34Z" transform="translate(-0.9 0.03)" style="fill:#385661;fill-rule:evenodd"/><path d="M26.72,47.41a2.72,2.72,0,0,0-1.49-.51A18,18,0,0,0,31,52l.88-1.41A15.69,15.69,0,0,1,26.72,47.41Z" transform="translate(-0.9 0.03)" style="fill:#598291;fill-rule:evenodd"/><path d="M31.76,49.59a3.88,3.88,0,0,1,5.4,1,4.19,4.19,0,0,1-.55,5.76l-.11.08a3.89,3.89,0,0,1-5.41-1,4.17,4.17,0,0,1,.54-5.75Z" transform="translate(-0.9 0.03)" style="fill:#598291;fill-rule:evenodd"/><path d="M32.16,50.15a3.21,3.21,0,0,1,4.46.85,3.49,3.49,0,0,1-.5,4.76l-.11.08A3.23,3.23,0,0,1,31.52,55,3.48,3.48,0,0,1,32,50.24Z" transform="translate(-0.9 0.03)" style="fill:#d3e8ef;fill-rule:evenodd"/><path d="M36,50.3c-2.7-.22-3.47,2.22-3.47,3.93A3.14,3.14,0,0,1,31,53.72,3.36,3.36,0,0,1,32,50.24,3.16,3.16,0,0,1,36,50.3Z" transform="translate(-0.9 0.03)" style="fill:#fff;fill-rule:evenodd"/><path d="M43.67,56.33C44,58.18,41.6,60.14,40,58.78c-.54-.5-.68-.6-1-.93-1.49-.62-3.38-1-4.63-1.93h0a2.16,2.16,0,0,1-.69-3v-.66c-.55-.76-.88-1.71-.18-2.38a20.38,20.38,0,0,0,2.86-2.94,1.69,1.69,0,0,1,2.12-.25,2.64,2.64,0,0,1,1.43,2,2.72,2.72,0,0,1-1.45,2.57,1.11,1.11,0,0,0-.3.17,7.73,7.73,0,0,0,1.71,1.3h.24c1.2-.09,3.06,0,3.63,1.33a2.15,2.15,0,0,1-.15,1.82l-.09.18A1.82,1.82,0,0,1,43.67,56.33Z" transform="translate(-0.9 0.03)" style="fill:#598291;fill-rule:evenodd"/><path d="M33.82,54.61c1,1.65,3.19,1.85,4.76,2.71a6.92,6.92,0,0,1-1.67-2.13c1.67,1.39,3.16,3,3.55,3.29a1.88,1.88,0,0,0,2.64-2c-.46-2.15-3.79-2.85-5.46-4.79a2.78,2.78,0,0,1-1.27.45,11.6,11.6,0,0,1,1.9-1.37,2.16,2.16,0,0,0,1.14-2c-.15-1.35-2-2.1-2.5-1.44a25.33,25.33,0,0,1-2.95,3C32.75,51.47,33,53.21,33.82,54.61Zm6.57-1.42c1.39.73,2.31,1.25,2.82,2.31C43.94,54.16,42.71,53,40.39,53.19Z" transform="translate(-0.9 0.03)" style="fill:#d3e8ef;fill-rule:evenodd"/><path d="M36.52,52a10.15,10.15,0,0,1,1.75-1.25,2.16,2.16,0,0,0,1.14-2c-.15-1.35-2-2.1-2.5-1.44.6,0,1.16,1.25,1.11,1.81-.06.83-2,2.06-2.66,2.74A1.29,1.29,0,0,0,36.52,52Zm-.89.93c1.54.8,3.9,3.51,4.82,4.29s2.63,0,2.63-.76c-.46-2.14-3.8-2.84-5.46-4.78a3.88,3.88,0,0,1-1.1.43A1.45,1.45,0,0,1,35.63,52.93Zm4.76.26c1.39.73,2.31,1.25,2.82,2.31C43.94,54.16,42.71,53,40.39,53.19Z" transform="translate(-0.9 0.03)" style="fill:#fff;fill-rule:evenodd"/></g><g id="Head"><path d="M43.85,17.65a14,14,0,0,1,2.9,8.06A1.83,1.83,0,0,1,46,27.16L41.6,30.54,37.06,17.68l5.24-.54A1.5,1.5,0,0,1,43.85,17.65Z" transform="translate(-0.9 0.03)" style="fill:#598291;fill-rule:evenodd"/><path d="M45.34,26.24a.64.64,0,0,0,.32-.54A12.78,12.78,0,0,0,43,18.37a.52.52,0,0,0-.1-.11c-.16-.08-3.81.38-4.28.38l3.55,10.08Z" transform="translate(-0.9 0.03)" style="fill:#d3e8ef;fill-rule:evenodd"/><path d="M45.32,26.24a.64.64,0,0,0,.33-.54,9.48,9.48,0,0,0-.28-2.31c.1,1.12.11,2.18-.13,2.35L41.89,28l.25.7Zm-.49-4.5A12.73,12.73,0,0,0,43,18.38s.06,0-.1-.1-3.82.33-4.28.38l2.06,5.82Z" transform="translate(-0.9 0.03)" style="fill:#fff;fill-rule:evenodd"/><path d="M44.43,20.69l-1,1.3a.22.22,0,0,0,0,.16,29.5,29.5,0,0,1,1,4.15l.09.56L44,27.3c0-.29-.07-.58-.13-.87a28.31,28.31,0,0,0-1-4.06.8.8,0,0,1,.11-.75l1.14-1.52C44.2,20.31,44.3,20.5,44.43,20.69Z" transform="translate(-0.9 0.03)" style="fill:#d3e8ef;fill-rule:evenodd"/><path d="M44.52,21l-.84,1.06a.2.2,0,0,0,0,.15,26.85,26.85,0,0,1,1,4.14,1.77,1.77,0,0,1,0,.32l-.55.44-.09-.61a24.56,24.56,0,0,0-1-4.06.79.79,0,0,1,.14-.74l1-1.33A1,1,0,0,1,44.52,21Z" transform="translate(-0.9 0.03)" style="fill:#7eaaba;fill-rule:evenodd"/><path d="M19.16,5.4c-3.32,2.63-3.66,6.15-2.47,10l-1.3.4C14,11.4,14.56,7.32,18.32,4.32Z" transform="translate(-0.9 0.03)" style="fill:#598291;fill-rule:evenodd"/><path d="M24.37,3.71h0A4.19,4.19,0,1,1,19.75,0,4.19,4.19,0,0,1,24.37,3.71Z" transform="translate(-0.9 0.03)" style="fill:#3c942c;fill-rule:evenodd"/><path d="M23.52,3.82h0a3.34,3.34,0,1,1-3.68-3A3.34,3.34,0,0,1,23.52,3.83Z" transform="translate(-0.9 0.03)" style="fill:#3c942c;fill-rule:evenodd"/><path d="M17.2,4a3.3,3.3,0,0,0,6.08,1.37A3.3,3.3,0,0,1,20.52,7.5a3.35,3.35,0,0,1-3.63-2.95,3.2,3.2,0,0,1,.5-2.13A3.05,3.05,0,0,0,17.2,4Zm5.68-.81c.12.95-.75,1.62-1.88,1.49a2.53,2.53,0,0,1-2.28-2c-.1-1,.76-1.62,1.9-1.49A2.56,2.56,0,0,1,22.88,3.19Z" transform="translate(-0.9 0.03)" style="fill:#82e371;fill-rule:evenodd"/><path d="M14.12,19.32a12,12,0,0,1-2.3-4.87c-.21-1.37,3.23-3.14,4.24-3.62s4.53-2.05,5.46-1.05A11.38,11.38,0,0,1,23.9,14.6c.26,1.46-3.22,3.24-4.22,3.75S15.07,20.42,14.12,19.32Z" transform="translate(-0.9 0.03)" style="fill:#006cbf;fill-rule:evenodd"/><path d="M14.86,18.59a1.75,1.75,0,0,0,.6.05,15.49,15.49,0,0,0,6-2.5c.28-.19,1.46-1.1,1.46-1.45a10.51,10.51,0,0,0-2.1-4.29,5.05,5.05,0,0,0-2,.31,20.07,20.07,0,0,0-2.34,1c-.69.35-3.5,1.84-3.69,2.61A10.37,10.37,0,0,0,14.86,18.59Z" transform="translate(-0.9 0.03)" style="fill:#006cbf;fill-rule:evenodd"/><path d="M19.3,17.4a12.72,12.72,0,0,0,3.33-2.19c.08-.09.33-.38.33-.52a10.51,10.51,0,0,0-2.1-4.29,5.05,5.05,0,0,0-2,.31,20.07,20.07,0,0,0-2.34,1Z" transform="translate(-0.9 0.03)" style="fill:#57b6ff;fill-rule:evenodd"/><path d="M21.73,45a28.51,28.51,0,0,1-7.06,1c-1.72,0-3.94-.3-5.05-1.86-3.36-4.57-6.78-13-7.36-18.68C2,23.3,4.06,21.37,5.52,20.19a37.08,37.08,0,0,1,5.65-3.61,67.49,67.49,0,0,1,7-3.28,70.83,70.83,0,0,1,7.35-2.53A36.86,36.86,0,0,1,32,9.47c1.86-.16,4.67-.18,6,1.53,3.5,4.5,6.91,12.94,7.66,18.58.27,1.93-1.12,3.67-2.36,4.88a29.53,29.53,0,0,1-5.8,4.13,61.65,61.65,0,0,1-7.69,3.68A63.72,63.72,0,0,1,21.73,45Z" transform="translate(-0.9 0.03)" style="fill:#598291;fill-rule:evenodd"/><path d="M14.65,44.66c-1.9,0-3.33-.41-4-1.31-3.1-4.23-6.52-12.46-7.1-18-.12-1.2,1-2.63,2.8-4.12a51.58,51.58,0,0,1,12.31-6.7,50.91,50.91,0,0,1,13.53-3.72c2.36-.23,4.13,0,4.86,1,3.42,4.39,6.67,12.66,7.37,17.89.13,1.11-.63,2.41-2,3.74C39.72,36,34.66,38.85,29.33,41S18.41,44.68,14.65,44.66Z" transform="translate(-0.9 0.03)" style="fill:#bdd6de;fill-rule:evenodd"/><path d="M7.06,37A41.27,41.27,0,0,1,3.52,25.36c-.09-1.2,1-2.63,2.83-4.1a51.23,51.23,0,0,1,12.31-6.7,51.22,51.22,0,0,1,13.52-3.72c2.37-.23,4.13.05,4.86,1a41,41,0,0,1,5.55,10.83C40.21,25.64,25,36.2,7.06,37Zm7.57,7.64c-1.89,0-3.33-.41-4-1.32-.11-.16-.25-.33-.36-.51a2.83,2.83,0,0,0,.77.45,6.61,6.61,0,0,0,3.33.65c3.75,0,9.34-1.55,14.66-3.7s10.4-4.92,13.12-7.49A6.81,6.81,0,0,0,44.09,30a3,3,0,0,0,.25-.88,4.34,4.34,0,0,1,.08.63c.13,1.11-.63,2.41-2,3.74C39.72,36,34.66,38.85,29.33,41S18.41,44.68,14.63,44.64Z" transform="translate(-0.9 0.03)" style="fill:#fff;fill-rule:evenodd"/><path d="M29.14,22.35a18.21,18.21,0,0,0-.7-4.14.44.44,0,0,1,.27-.54A35.78,35.78,0,0,1,36.35,16c.57-.09,1-.13,1.36.49a47.6,47.6,0,0,1,4.85,13.48c.09.61-1,1.87-1.36,2.24a23.29,23.29,0,0,1-4.91,3.63,49.31,49.31,0,0,1-6.92,3.4A50,50,0,0,1,22,41.63a23.83,23.83,0,0,1-6.06.85c-.54,0-2.17-.15-2.54-.66a48,48,0,0,1-6-13c-.2-.7.15-1,.62-1.3a69.19,69.19,0,0,1,9.23-5.21.46.46,0,0,1,.57.2,10.09,10.09,0,0,0,2.23,2.88C21.26,26.56,29.16,23.81,29.14,22.35Z" transform="translate(-0.9 0.03)" style="fill:#bdd6de;fill-rule:evenodd"/><path d="M29.65,23.2a47.49,47.49,0,0,0-1.26-5,.46.46,0,0,1,.26-.54,40.94,40.94,0,0,1,8.24-2c.57-.09,1-.15,1.36.49A47.23,47.23,0,0,1,43.1,29.62c.08.62-1,1.88-1.36,2.23a23.68,23.68,0,0,1-4.91,3.64,52.2,52.2,0,0,1-6.92,3.4,53.75,53.75,0,0,1-7.33,2.42,23.37,23.37,0,0,1-6.06.83c-.54,0-2.18-.14-2.54-.65a48,48,0,0,1-6-13c-.21-.69.14-1,.61-1.3a63.62,63.62,0,0,1,9-5.06.42.42,0,0,1,.57.21,16.64,16.64,0,0,0,3,4.16c.64.54,2.95,0,4.86-.66C27.65,25.29,29.71,24,29.65,23.2Z" transform="translate(-0.9 0.03)" style="fill:#1e2432;fill-rule:evenodd"/><path d="M29.69,23.25c0-.33-.67-3-1.25-5a.46.46,0,0,1,.26-.54,40.94,40.94,0,0,1,8.24-2l.38-.06a32.42,32.42,0,0,1,3.3,7.21c0,.31.08.63.08.92,0,.45-1.14,1.5-1.5,1.81-5,4.1-18.47,9.62-24.88,10.16-1.6.1-2.05.18-2.92-1a62.62,62.62,0,0,1-2.87-6.56c-.13-.39-.14-.87.06-1a63.62,63.62,0,0,1,9-5.06.42.42,0,0,1,.57.21,16.64,16.64,0,0,0,3,4.16c.64.54,2.95,0,4.86-.66C27.65,25.29,29.71,24,29.69,23.25ZM13.36,40.46c.36.51,2.06.64,2.57.64,6.89.13,20.79-5.55,25.59-10.45C41.91,30.3,43,29,42.91,28.4v-.1c.11.45.19.89.25,1.31.09.61-1,1.87-1.36,2.23-4.73,4.87-18.42,10.46-25.21,10.3-.55,0-2.18-.15-2.55-.66-.24-.35-.49-.71-.74-1.11A.24.24,0,0,0,13.36,40.46Z" transform="translate(-0.9 0.03)" style="fill:#2d3956;fill-rule:evenodd"/><path d="M32.55,24.06a18,18,0,0,0,1.6,4c1,1.87,4.62.41,4.16-1.68a13.21,13.21,0,0,0-1.6-4C35.6,20.57,32,22,32.55,24.06ZM20.4,29A19.77,19.77,0,0,1,22,33,1.81,1.81,0,0,1,21,35a7.56,7.56,0,0,0-2.88-.17,1.77,1.77,0,0,1-.5-.59,13.37,13.37,0,0,1-1.39-3.62C15.78,28.56,19.37,27.08,20.4,29Z" transform="translate(-0.9 0.03)" style="fill:#82e371;fill-rule:evenodd"/><path d="M24,33.7a13.88,13.88,0,0,0,5.55-.54C29.16,39.55,24.22,37.52,24,33.7Z" transform="translate(-0.9 0.03)" style="fill:#82e371;fill-rule:evenodd"/><path d="M27.06,11.69v.38A7.32,7.32,0,0,1,26.57,15,19.37,19.37,0,0,1,21,18a21.09,21.09,0,0,1-6.15,1.57,8,8,0,0,1-2.35-1.86c-.09-.07-.16-.16-.25-.24l.61-.33.06.06a9.28,9.28,0,0,0,1.93,1.61,20.85,20.85,0,0,0,5.87-1.49,20.73,20.73,0,0,0,5.34-2.83A8.39,8.39,0,0,0,26.38,12v-.09A6,6,0,0,0,27.06,11.69Z" transform="translate(-0.9 0.03)" style="fill:#bdd6de;fill-rule:evenodd"/><path d="M27.41,11.61v.72a7.9,7.9,0,0,1-.51,3,21.59,21.59,0,0,1-5.76,3.08A21.1,21.1,0,0,1,14.8,20a7.79,7.79,0,0,1-2.43-1.87c-.14-.16-.3-.32-.46-.49l.62-.33c.09.09.19.19.28.3a9.51,9.51,0,0,0,2,1.63,20.8,20.8,0,0,0,6.06-1.55,21.2,21.2,0,0,0,5.51-2.9,9.41,9.41,0,0,0,.34-2.53v-.44A5.49,5.49,0,0,0,27.41,11.61Z" transform="translate(-0.9 0.03)" style="fill:#7eaaba;fill-rule:evenodd"/><path d="M6.3,43.29a2.21,2.21,0,0,1-2.16-.86A16.15,16.15,0,0,1,.93,34.94a1.55,1.55,0,0,1,.59-1.55l2.83-1.88C6.33,30.18,9,36.08,8,36.94L9.25,39c.47.06,1.51,3-.11,3.7Z" transform="translate(-0.9 0.03)" style="fill:#598291;fill-rule:evenodd"/><path d="M6.3,42.36a1.36,1.36,0,0,1-1.39-.57,15,15,0,0,1-3-6.89.74.74,0,0,1,.26-.76L5,32.28c2.11-1.62,6.32,9.26,4.11,9.51Z" transform="translate(-0.9 0.03)" style="fill:#bdd6de;fill-rule:evenodd"/><path d="M3.12,38.9a14.38,14.38,0,0,1-1.23-4,.76.76,0,0,1,.27-.76L5,32.28c1.11-.86,2.89,1.86,3.89,4.6Z" transform="translate(-0.9 0.03)" style="fill:#fff;fill-rule:evenodd"/><path d="M6.8,36.88a.94.94,0,0,1,.41.32c.51,1,1.55,3.33,2.07,4.56h0l-.54.1c-.55-1.34-1.47-3.43-1.94-4.34a.37.37,0,0,0-.12-.13c-.38-.16-1.91-.35-2.38-.48A6,6,0,0,0,4,36.23C4.52,36.4,6.28,36.68,6.8,36.88Z" transform="translate(-0.9 0.03)" style="fill:#bdd6de;fill-rule:evenodd"/><path d="M6.71,37a.89.89,0,0,1,.41.33c.47,1,1.46,3.22,2,4.46l-.58.12c-.56-1.3-1.45-3.34-1.9-4.22a.49.49,0,0,0-.12-.13c-.32-.16-1.68-.31-2.16-.44A6,6,0,0,0,4,36.42C4.63,36.58,6.12,36.82,6.71,37Z" transform="translate(-0.9 0.03)" style="fill:#7eaaba;fill-rule:evenodd"/><path d="M5.55,37.82c.84,2.1.94,4,.24,4.1S3.84,40.47,3,38.37s-1-4-.25-4.1S4.71,35.72,5.55,37.82Z" transform="translate(-0.9 0.03)" style="fill:#fff;fill-rule:evenodd"/><path d="M5.28,38c.79,2,.93,3.64.24,3.82s-1.87-1.35-2.66-3.35S2,34.75,2.62,34.62,4.49,36,5.28,38Z" transform="translate(-0.9 0.03)" style="fill:#7eaaba;fill-rule:evenodd"/><path d="M4.71,38.3c.54,1.33.67,2.4.24,2.55S3.78,40,3.24,38.62,2.6,36.15,3,36.06,4.17,37,4.71,38.3Z" transform="translate(-0.9 0.03)" style="fill:#bdd6de;fill-rule:evenodd"/><path d="M4.8,38.3c.32.82.38,1.57.09,1.61s-.79-.58-1.11-1.41S3.41,37,3.69,36.9,4.48,37.48,4.8,38.3Z" transform="translate(-0.9 0.03)" style="fill:#7eaaba;fill-rule:evenodd"/></g></g></svg>