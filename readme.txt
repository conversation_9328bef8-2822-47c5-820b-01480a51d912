=== FluentCRM - Email Newsletter, Automation, Email Marketing, Email Campaigns, Optins, Leads, and CRM Solution ===
Contributors: techjewel,adrea<PERSON>rian,heera,wpmanageninja
Tags: email marketing, newsletter, crm, email newsletter, subscribers
Requires at least: 5.0
Tested up to: 6.8
Requires PHP: 7.3
Stable tag: 2.9.60
License: GPLv2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html

The easiest and fastest Email Marketing, Newsletter, Marketing Automation Plugin & CRM Solution for WordPress

== Description ==
= The easiest and fastest Email Marketing, Newsletter, Marketing Automation Plugin & CRM Solution for WordPress =

👉 Official Website Link: [Official Website](https://fluentcrm.com/)
👉 Join Our FB Community: [FluentCRM Facebook Group](https://web.facebook.com/groups/fluentcrm)
👉 Official 5 Minutes Guide: [Getting started in 5 minutes](https://fluentcrm.com/fluentcrm-101/)

[youtube https://www.youtube.com/watch?v=eN5UfpZ5mbA]


= Your Self Hosted CRM & Email Marketing Solution in WordPress  =

[FluentCRM](https://fluentcrm.com) is the best and complete feature-rich Email Marketing & CRM solution. It is also the simplest and fastest CRM and Marketing Plugin on WordPress. Manage your customer relationships, build your email lists, send email campaigns, build funnels, make more profit, and increase your conversion rates. (Yes, It's Free!)

= The most complete Email Newsletter and Campaigns Plugin for WordPress =
[youtube https://www.youtube.com/watch?v=QSVpos-WJFg]

Want to run email campaigns or email newsletters for your leads or customers? FluentCRM is the perfect solution for your email newsletter and email campaigns. Just import a CSV or WordPress user and you're ready to go. Segment your contacts and send emails whenever you want.

= FEATURES - Every online business needs =

<h3>🎉 All-in-One Email Marketing Solution</h3>
With FluentCRM, send email campaigns to targeted audiences, send sequential emails, onboard your new customers even automate your full email marketing with a powerful automation visual automation builder.
After sending each email campaign, you can analyze the open rate, click rate, and revenue earned from that specific campaign. You can also check the data matrix like who opened the emails and who clicked and based on that, you can do more actions and segment your customers further.

* Send or Schedule Email Campaigns (Unlimited contacts and campaigns)
* In-detailed campaign reports
* Run Automation for customer onboarding or on different WordPress events.
* Send automated emails and track performance

<h3>🎉 Customer relationship management (CRM)</h3>

* Store all your customer information in a central place.
* Automatically import your contacts from CSV/WP Users/WooCommerce customers and view them.
* Know and analyze every bit of information about your contact. FluentCRM will show all the relevant data from your e-commerce, membership, LMS, etc which gives for better visibility.
* Track every marketing email by Contact level
* Store Notes, Meeting logs, and Calls in the contact profile
* Segment your contacts by <b>Lists</b>, <b>Tags</b> and filter for your appropriate target.

<h3>🎉 Contact Segmentation and Reporting</h3>

* Create Tags/Lists as many as you want and assign your contacts to your appropriate segment
* Filter or search your customers by List or Tags
* Send Marketing emails by including tags or lists and even excluding some contacts that are not intended to get a specific campaign email.
* Collect and Manage <b>unlimited contacts and leads</b>

<h3>🎉 Email Marketing Campaigns</h3>

* Save and Store all your pre-written email copies in a central place and use that whenever you want.
* Build a beautiful email body using an excellent email builder powered by Block Editor.
* Add Columns/images or dynamic content and make the email as simple or just fancy, Just based on your customer's taste.
* Use Dynamic Smart code like contact name or the country in your email or subject to personalize the experience.
* Send or Schedule emails at a future date and time, and you are ready to go.
* See in detail reporting about the open rate, click rate
* See which links are clicked or which contacts opened your links
* Send unlimited emails.

<h3>🎉 Email Newsletter Solution</h3>
FluentCRM will help you to collect leads, automate emails, display subscription forms, send email newsletters all inside WordPress. FluentCRM is a <b>100% self-hosted WordPress Plugin</b> to send Newsletter, marketing campaign, newsletter broadcasts. After sending Email Newsletter, you can track how many emails were opened and clicked also which links were clicked and who clicked your links. You will get total analytics from your email newsletter campaign. Try FluentCRM for sending your Email Newsletter and increase your conversion rate.

[youtube https://www.youtube.com/watch?v=xdCjupXRp5w]

<h3>🎉 Marketing Funnel Builder</h3>

* Design high-converting customer journeys with FluentCRM's Powerful funnel builder.
* Integrated with your favorite WordPress plugins.
* Start funnel sequences from different user actions like sign-up, form submission, product purchase, LMS course enrollment.
* Build a simple funnel, like collecting contact to sell your paid products
* Automate your full marketing steps and give your customers a personal touch with our timely actions and responses.
* Measure funnel metrics in every step and find where you can do better.

<h3>🎉 Integrated Optin Forms</h3>

* Create and use opt-in forms right from FluentCRM, and for more advanced forms, use FluentForms (Totally Free) to collect your leads.
* Use forms with your Funnel builder as a trigger and automate your post form submission.
* Segment your users based on different form submissions or submitted data.

<h3>🎉 BUILT-IN ANALYTICS & DASHBOARDS</h3>

* View your full business insight right from the FluentCRM dashboard by using graphs, charts, data widgets
* Track every marketing email and get in-detail metrics
* View all the data points of your customers from Purchase History, and Support tickets to the offline phone call using activity logs.
* Integrate and use with other plugins like WooCommerce, EDD, LifterLMS, LearnDash, AffiliateWP, Paid Membership Pro, FluentForms, WPFusion, and many more coming.

[youtube https://www.youtube.com/watch?v=F9tRe2PA37s]

<h3>✉️Build & Send Email Newsletter in WordPress</h3>
FluentCRM makes it easier than ever to connect with your audience through compelling email newsletters right from your WordPress site. This robust plugin offers seamless integration, allowing you to design, send, and track email newsletters without leaving your dashboard. Utilize our customizable templates to create visually appealing email newsletters that capture your subscribers' attention. FluentCRM's powerful segmentation tools let you target specific groups within your audience, ensuring your content reaches the right people. With detailed analytics at your fingertips, you can continuously optimize your email newsletter strategies and enhance subscriber engagement. Transform your email marketing with FluentCRM and keep your community informed and engaged with impactful email newsletters.
[youtube https://www.youtube.com/watch?v=V1ddf9k0yTk]


<h3>Email Marketing for WooCommerce with FluentCRM</h3>
FluentCRM simplifies email marketing automation for WooCommerce stores. Directly from your WordPress dashboard, automate emails based on customer actions like purchases or refunds. Segment your audience by shopping behaviors and preferences to send personalized email newsletters and promotions. With FluentCRM, you gain insights from detailed analytics to optimize your campaigns, enhancing customer engagement and increasing revenue. Streamline your WooCommerce email marketing effortlessly with FluentCRM.

**🎉 Other Features (But not least)**

* Turn your WordPress post commenters into Subscribers
* Double Optin feature for getting quality leads
* Automatically make your WordPress users your subscribers
* Manage and customize your email footer and subscription Preference.
* Find out why your contacts are unsubscribing

= 🛡️ Fully GDPR Ready =
FluentCRM is a self-hosted WordPress plugin. You own your data, and no external SAAS connection is required to run your CRM system with FluentCRM.

= 🚀 Modern. Powerful. Super Fast 🚀 =

* Build with VueJS and REST API as a Single-page Application
* Super fast and lean interface so anyone can use it without any learning curve
* Super awesome Dashboard with charts, graphs, and essential data points to show your full business overview.
* Integrated Email Marketing analytics to see the conversion rates.
* Mail Deliverability: Send 100 or 50,000 emails. You are covered. With a powerful email-sending mechanism, FluentCRM will send your emails efficiently.

>__👦 Chris Lema, VP, Products at Liquid Web / Nexcess__
>...Those are all external solutions that don't run within your WordPress (or WooCommerce) site. Until now, the only native WordPress marketing automation solution I knew of was Groundhogg. But that was before I discovered FluentCRM.

>__👨 Davinder Singh Kainth, Founder, The WP Weekly__
>Finally, an email marketing solution that looks native to the WordPress user interface and is super easy to use. Ideal for beginners to quickly get started with email lead generation to advanced users wanting to set up funnels with advanced tagging.

= FluentCRM Review by Experts =

[youtube https://www.youtube.com/watch?v=Vl3Tybe73e8]

[youtube https://www.youtube.com/watch?v=wFzX0Suv0rw]

[youtube https://www.youtube.com/watch?v=ECRYRsZjMM0]

[youtube https://www.youtube.com/watch?v=imYQhXNE4h0]


= SEAMLESS INTEGRATIONS (Pro) =
FluentCRM features and integrations that will help your business –

<h4>Native WORDPRESS INTEGRATION:</h4>
FluentCRM has all the native WordPress integration that will help you to collect leads, create contact segmentations and help to grow your email list.

* <a href="https://fluentcrm.com/docs/import-contacts-to-fluentcrm/"><b>WordPress User:</b></a> Import and Sync WordPress users automatically and segment by list and tag.
* <b>Comment Form Integration:</b> Let your users choose if they want to subscribe to your blog from the comment form and automatically add a subscriber and segment in the list or tag.
* <b>User Sign up Form:</b> Automatically add your new signup as CRM lead and segment your contacts.

<h4>WooCommerce Integration:</h4>
FluentCRM is the perfect companion for your WooCommerce store. You can segment your contacts by different WooCommerce Data Properties including Purchased Products, Purchased Categories, Lifetime value and more.
You can send emails to your customers by segmenting them by specific product purchase.
With <b>WooCommerce Advanced Reports</b>, View sales by product and time range, and compare with other periods to assess store performance.

<h4>Easy Digital Downloads Integration:</h4>
FluentCRM provides the same level of integrations like WooCommerce.

<h4>LMS INTEGRATION:</h4>
LifterLMS, LearnDash, TutorLMS, and LearnPress Integrations enable running funnels and marketing automation for various Learning Management System (LMS) actions such as course enrollment, course completion, and lesson completion.

<h4>MEMBERSHIP INTEGRATION:</h4>

<ul><li>Paid Membership Pro</li><li>MemberPress</li><li>Wishlist Members</li><li>Restrict Content Pro</li><li>WPFusion</li></ul>

<h4>Form INTEGRATION:</h4>
<ul><li>Fluent Forms</li><li>Elementor Pro Form</li><li>Divi Bloom</li><li>ThriveArchitect</li><li>Beaver Builder Form</li></ul>

<h4>Other INTEGRATIONS:</h4>
<ul><li>AffiliateWP Automation</li><li>Webhook</li></ul>

<h4>Automation INTEGRATIONS:</h4>
<ul><li>Zapier</li><li>Pabbly Connect</li><li>Integrately/Make</li></ul>

<h4>Page Builder INTEGRATIONS:</h4>
<ul><li>ThriveThemes</li><li>Divi Themes</li><li>Elementor</li><li>Gutenberg</li><li>Oxygen Builder</li><li>Beaver Builder</li></ul>

👉 [CLICK HERE TO LEARN MORE](https://fluentcrm.com/?utm_campaign=wordpress-org-visitor&utm_medium=learn_more_about_crm&utm_source=WordPress.org)👈

== Other Plugins By The Same Team ==
<ul><li><a href="https://wordpress.org/plugins/fluentform/" target="_blank">Fluent Forms WordPress Form Builder Plugin</a></li>
	<li><a href="https://wordpress.org/plugins/ninja-tables/" target="_blank">Ninja Tables - WP DataTables Plugin for WordPress</a></li>
	<li><a href="https://wordpress.org/plugins/fluent-smtp/" target="_blank">FluentSMTP - WordPress Mail SMTP, SES, SendGrid, MailGun Plugin</a></li></ul>

== Installation ==
This section describes how to install the plugin and get it working.

0. Just search for FluentCRM in WordPress Plugins and click install and activate.

OR

1. Upload the plugin files to the `/wp-content/plugins/fluent-crm` directory, or install the plugin through the WordPress plugins screen directly.
2. Activate the plugin through the \'Plugins\' screen in WordPress
3. Use the `FluentCRM` -> `Settings` screen to configure the plugin

== Frequently Asked Questions ==
= Are there any limitations about how many emails I can send? =

No, there are no limitations. You can send as many emails as you want and also no limitations on contact numbers.

= How are emails sent? =

You can use any SMTP service like MailGun, SendGrid, Amazon SES. We recommend using Amazon SES because it’s a reliable and cost-effective solution.

= Is it 100% self-hosted? =

Yes, FluentCRM is 100% self-hosted. FluentCRM will not connect with any of our SAAS servers. You own the data, and your data should be hosted on your hosting server.

= Is it a GDPR-Compliant? =

Yes, your data never goes to a different server like MailChimp, ActiveCampaign, or 100s of other CRM. All the data will be saved and managed into WordPress.

= Where do I report security bugs found in FluentCRM? =
You can report any security bugs found in the source code of FluentCRM plugins through the <a href="https://patchstack.com/database/vdp/fluent-crm">Patchstack Vulnerability Disclosure Program</a>. The Patchstack team will assist you with verification, CVE assignment and take care of notifying us.

= Will it is a performance issue for WordPress? =

Absolutely not! From the very first, We were careful about this. It stores all the Campaign and Contact data in custom database tables, so it will not affect your WordPress database. We built the application with VueJS, and it’s only run when you go to the admin dashboard of Fluent CRM. Also, The Admin UI is super fast as It’s a SPA and communicates over ajax.

= How can I migrate From GroundHogg CRM =

Migrating from GroundHogg CRM is super simple. Go to GroundHogg CRM and export all your contacts by it's Tag and then import in FluentCRM and Map the corresponding Data.

= How can I migrate From JetPack CRM =

Migrating from JetPack CRM is super easy and Simple. Go to JetPack CRM contacts and export all your JetPack CRM contacts by it's Tag and then import in FluentCRM and Map the corresponding Data.

= How can I migrate From ActiveCampaign or other SAAS Solutions? =
With FluentCRM's automated migration tool, you can migrate from ActiveCampaign, MailerLite, MailChimp, Drip, and ConvertKit easily in minutes.
Just go to contact imports then select your current provider (ActiveCampaign, MailerLite, MailChimp, Drip, ConvertKit) once you connect then you can map the data easily and import all the contacts, tags/lists, custom fields, and associate data.

= Do you have a developer documentation? =
FluentCRM has fully featured developer documentation. Now you can extend it's functionalities with your own custom code. You can take a look of our developer documentation
👉 [View Rest Developer Documentation](https://developers.fluentcrm.com/)👈

= Do you have a REST API Documentation? =
FluentCRM has a full REST API support. If you want to integrate with Other Systems or build an APP then You can take a look of our REST API Documentation
👉 [View Rest API Documentation](https://rest-api.fluentcrm.com/)👈

= I want to report a bug; where to report? =
The entire source code is <a href="https://github.com/fluentcrm/fluent-crm/">available on github</a>. Please feel free to fork and send a pull request or report a bug.

= I found a security bug; where to report? =
You can report any security bugs found in the source code of FluentCRM plugins through the <a href="https://fluentcrm.com/security-and-vulnerability-disclosure-program/">Our Vulnerability Disclosure Program</a>.

== Screenshots ==
1. FluentCRM Dashboard
2. Email Builder
3. All Contacts
4. Contact Overview
5. Campaign Reports
6. Optin Forms
7. Marketing Funnel Builder
8. General Settings
9. Contact Segments
10. Pro Integrations

== Changelog ==

= 2.9.60 (Date: May 14, 2025) =
New: Introduced built-in templates feature
New: List-wise double opt-in email settings now available
New: Voxel New Order Placed Trigger
New: Option to send custom emails as transactional email
New: Custom menu tab functionality added on the company profile page
New: Dynamic segments based on active WooCommerce subscriptions
New: Added Contact Unsubscribe hook for enhanced customization
New: Bulk add/update contacts REST API endpoint
Fixed: Sorting Issue in Purchase History tab in Contact Profile
Fixed: Custom Field multi-line text Issue
Fixed: CSV export issue with the contacts filter
Fixed: Links tracking issue in Link Stats in Campaign details.
Other Improvements & Bug Fixes

= 2.9.50 (Date: April 17, 2025) =
New: Shortcode support for multiple email campaign archives
New: Shortcodes are now searchable
New: Voxel Integration ( Product purchase history in contact )
New: IPv6 compatibility Added
Improvement: WordPress version 6.8 compatible
Improvement: Toggle for column visibility in automation funnels table
Improvement: Unsaved changes warning in block editor
Improvement: More translations string added
Improvement: Search functionality for System Logs
Fixed: Global footer displaying incorrectly in email previews
Fixed: Custom field values couldn't be cleared once set
Fixed: Custom email footer settings import issue
Fixed: Encoding issue in Custom Field Text
Fixed: Label search functionality errors
Fixed: Fatal error during funnel import process
Fixed: AB Cart Tag and list not removing after order completion

= 2.9.48 (Date: March 20, 2025) =
New: Re-apply Option for Completed Sequence
New: Tags and Lists are now searchable in Dynamic Selection
New: Selectable Custom Fields now Editable & Sortable
New: Smartcodes for WooCommerce Subscription Triggers
Improvement: Added Copy email and phone from contact lists
Improvement: Tags and lists display in ascending order
Improvement: LearnPress course finished hook Updated
Improvement: LatestPostBlock now displays all custom post types
improvement: Added currency to Shipping and Tax Total
Improvement: Introduced Filter to manage new bounced email
Improvement: Tags and subscriber lists now sorted in ascending order
Improvement: Added operator type selection for taxonomy filters in LatestPost Block
Fixed: Padding, Margin, and Line-Height issues inside Column block
Fixed: Dynamic coupon amount issue with existing template
Fixed: Excerpt length of LatestPostBlock issue
Fixed: UpdateContactProperty Action float subtraction issue
Other Improvements & Bug Fixes

= 2.9.45 (Date: February 24, 2025) =
New: Subscription Cancelled Trigger (Fluent Forms)
New: Subscription Payment Received Trigger (Fluent Forms)
New: FluentForm Subscriptions Widget in Contact Profile
New: Update Custom Fields Using Bulk Actions
New: Filter option for failed emails
New: Show non-recurring memberships in MemberPress Widget
New: Woo Subscription Cancelled trigger
New: Option to sort custom fields
New: Wishlist Membership Widget in Contact Profile
Improvement: The slug retains one character even after the title is cleared
Improvement: Display which user sent the campaign
Improvement: All tables with adjustable column widths
Improvement: Redesigned the Addons section with improved UI/UX
Improvement: Added tooltip for Skipped AB cart status
Improvement: Added a button to copy the bounce handler URL
Fixed: Custom numeric field filter issue
Fixed: WooCommerce Coupon Discount amount not working
Other Improvements & Bug Fixes

= 2.9.40 (Date: January 22, 2025) =
New: Quick Search in Automation Actions, Benchmarks, Goals
New: MemberPress Subscriptions Widget
New: Export/Import Email Campaign
New: Export/Import Email Campaign Contacts
New: Brevo (ex Sendinblue) Bounce Handler
New: Support for Polish Characters in slug (Tags/Lists)
Improvement: Contact filtering options: Never Clicked/Opened
Improvement: Quick preview added in email templates
Improvement: Post Image Type for Latest Post block
Improvement: Current date in Update Contact Property action in Automation
Improvement: WooCommerce Product Image Styling (order_items_table)
Improvement: Back Button for Campaign Archives
Improvement: Restart section added in ‘Remove From List’ Trigger
Improvement: Added 'Check All' option contact exporter
Improvement: More Filters in email campaign archive
Improvement: Added ‘Select All’ tag/list option while importing contacts
Improvement: UI Improvements (Automation Label Color)
Improvement: Smoother One-click Unsubscribe
Improvement: Coupon systems support for multi-vendor/extensions along with woocommerce
Improvement: Added Gravatar & Fallback Compliance for Contact Avatar
Fixed: Spammed/Complained Status Issue in Bounce Handler
Fixed: Theme colors not displaying in Emails
Fixed: Pagination for recurring campaign emails
Fixed: Sync WooCommerce order (trashed order issue)


= 2.9.31 (Date: December 27, 2024) =
New: Added Email Preview in Campaigns
Improvement: AB Cart item table responsive
Improvement: User delete option sync between compliance settings and general settings
Improvement: Replaced google fonts with Bunny
Fixed: AB Cart Recovered Revenue issue
Fixed: Campaigns revenue report issue
Fixed: Latest Post Block random sort issue
Fixed: Table alignment issue in blocks
Fixed: Number values in the 'Text' custom field misinterpreted as date issue.
Fixed: Variable button size not working in block editor
Fixed: MailChimp Migration import limitations
Fixed: Theme & default color issue in editor
Other Improvements & Bug Fixes


= 2.9.30 (Date: December 09, 2024) =
New: Labels in Automations & Campaign
New: WordPress date format support in Custom Field
New: Test Outgoing Webhook functionality
Improvement: UI enhancements for Lists & Tags popover
Improvement: Corrected date handling
Improvement: Auto-Mapping CSV Fields with Custom Fields
Improvement: Better SQL Queries
Improvement: Users now searchable in manager settings
Fixed: Default link color issue while editing the email template
Fixed: Default values not working for Smartcode (manage_subscription_html, unsubscribe_html)
Fixed: Ordering in Dashboard Chart
Fixed: Campaign Revenue Report issues
Fixed: URL decode issue in A/B Testing for Campaigns
Fixed: Some Deprecation Warnings
Other Improvements & Bug Fixes

= 2.9.25 (Date: October 16, 2024) =
- New: Export/Import Recurring Campaign
- New: Smart Code support in Custom Email Address field
- New: Customer Profile button for EDD
- New: Added internal description to the funnels page
- Improvement: Product Image  & Currency Added in Ab Cart Details/Email
- Improvement: Multiline Custom Field
- Improvement: UX in Latest Post Block
- Improvement: Added tax row in Abandoned Cart
- Improvement: Abandoned Cart Details Mobile Responsiveness
- Improvement: Sorting Option in Purchase History in Contact for Woo/EDD
- Improvement: Changing product now possible from block sidebar
- Fixed: URL encoding Issue
- Fixed: Automation Wait Delay Issue
- Fixed: Dashboard Chat Dates Order
- Fixed: Email Editor LetterCase
- Fixed: Company Custom Field CSV Import Issue
- Fixed: Company Custom Field Issue while creating
- Fixed: Smart Code wp.url Issue
- Fixed: Ab Cart Smart Code Issue
- Fixed: MemberPress Contact Import Issue
- Other Improvements & Bug Fixes

= 2.9.24 (Date: August 20, 2024) =
- Hotfix: Compatibility issues fixed for Email Editor with some woo addons

= 2.9.23 (Date: August 19, 2024) =
- Added Custom Field or Date of Birth on Wait Time in Automation
- Added WooCommerce Variation Product on Advanced Filter and Automation Conditions
- Added keyboard ⌘ (or ctr) + s to save emails and automations
- Added Links Report for Individual Contacts
- Fixed: Dynamic Segment Contact Count Issue
- Fixed: WooCommerce Revenue Report Issue fixed on Contact Profile
- Added contact.company.* smartcodes
- Added SMTP2GO Bounce Handler
- Improvement on Email Builder

= 2.9.22 (Date: August 14, 2024) =
- Hotfix:  Lodash & _ conflict issue for Email Editor

= 2.9.21 (Date: August 13, 2024) =
- Fixed email builder conflict issues
- Fixed Duplicate Cron Jobs issues

= 2.9.2 (Date: August 12, 2024) =
- New: Built-in Automation Templates
- New: FluentSMTP logs to the Emails Section of Profile
- New: Email Filter to the Emails Section of Profile
- Fixed: Email Editor Issue
- Fixed: ActiveCampaign Import Contacts Issue
- Fixed: Event Tracking Fetch Issue
- Fixed: Sending Double opt-in Email
- Fixed: Webhook Issue
- Fixed: Automation Twice Run Issue
- Improvement: UI of the Custom Fields
- Other Improvements & Bug Fixes

= 2.9.0 (Date: May 29, 2024) =
- New: Custom Fields on Company Module
- New: Bricks Builder Conditional Elements Integration
- New: All Automations Activity Feed
- New: Create Tags on the fly from the contact profile
- CSV Import Improvement
- Contact Bulk Actions Update - Now you can select all the contacts from the filter
- Improvement: Improved FluentCRM PHP API for Contacts
- Improvement: Improved FluentCRM REST API for Contacts
- Fixed: Thrive Suites Integration Issue Fixed
- Fixed: PHP 8.x Compatibility Issue Fixed for CSV import

= 2.8.45 (Date: Mar 01, 2024) =
- New: WooCommerce Subscription Expiration Trigger
- New: WP User Role Based Segmentation for Advanced Filters
- New: BuddyBoss / BuddyPress Tags for Invites and Group Membership
- Performance: Improved Email Sending Database Queries
- Fix: WooCommerce Address Field Syncing Issue Fixed
- Fix: LearnDash Course SmartCode Issue Fixed
- Elementor Form Integration Improvement
- Security Improvement: Company Logo Auto Fetching File-Type Check Added

= 2.8.44 (Date: Feb 06, 2024) =
- Improved Action Scheduler for Email Sending
- Added Campaign Email Shareable Link
- New Smart Codes - WP User
- Improved Contact Profile API
- Bug Fixes and Improvements
- Improved Data Clean-Up Tool

= 2.8.43 (Date: Jan 30, 2024) =
- Improved WooCommerce Orders History and Sync
- Email Campaign Analytics Improvement
- Fixed Template Import issue
- Auto Login Option with Smart Links (Pro)
- Add All Post Type for Recurring Campaigns Conditions (Pro)

= 2.8.42 (Date: Jan 28, 2024) =
* Support For WooCommerce HPOS Integration
* Fixed Customer's Order History Issues
* Improve Litespeed Cache Compatibility

= 2.8.41 (Date: Jan 27, 2024) =
* Add Compatibility support for Old Format Manage Subscription & Unsubscribe URLs
* Fixed Email Templating Previews
* Reset Managed Contact Hash on Contact Password Update

= 2.8.40 (Date: Jan 26, 2024) =
* Multi Threader Email - Send Emails faster
* Custom Contacts Fields Grouping
* Event Tracking for contacts
* Latest Post Block improvement
* One-click List-Unsubscription Header
* System Logs for debugging
* Scheduled Jobs improvement
* Added Postal Server support for Email Bounce Handling
* Webview for Email Campaigns improvements and privacy improvements
* Other Improvements & Bug Fixes

= 2.8.34 (Date: Nov 14, 2023) =
* Added One Click Unsubscribe option in compliance features
* Image upload compatibility with WordPress 6.4 fixed
* Contact import with company name supported now
* Active Campaign import improvement (Thanks to WPFusion)

= 2.8.33 (Date: Nov 03, 2023) =
* Fixed Spacing & FontSize Issue
* Improvement & Bug Fixes on Both Global & Custom Footer
* Fixed Syntax Issue in Recurring Email History
* Fixed Email Open Performance Issue
* Other Improvements & Bug Fixes


= 2.8.30 (Date: Sep 05, 2023) =
* Improvement on Company module
* New trigger: Company added to contact Trigger
* New Trigger: Company Removed Trigger
* Company Specific Automation Actions
* Custom email preference management page
* New Trigger: Contact Created
* Navigation and UI improvements
* Duplicate segment or export contacts
* Other improvements including translatable strings, permissions in CRM managers
* Other Bug Fixes

= 2.8.20 (Date: Jul 18, 2023) =
* Campaign Email Scheduling and sending speed increased
* UI Improvement
* Confirmation prompt for email campaigns
* Double Opt-in Email pre-header
* Ability to delete contact profile picture
* Company attach/detach
* Integration Improvements
* Bug fixes & improvements

= 2.8.0 (Date: Apr 14, 2023) =
* Added Company module
* More detailed contact overview
* Massive UI enhancements
* FluentCRM Navigation Experience
* Ability to check email preview for specific contacts
* New WooCommerce Subscription Triggers (Pro)
* Improvements and bug fixes

= 2.7.40 (Date: Mar 01, 2023) =
* Email Conditional Sections issues fixed
* List & Tag selection UI improved
* Campaign Email Activity Improvements
* Fixed Redirecting issues for non-unicode characters
* Fixed import issue for Restrict Content Pro

= 2.7.0 Date: Jan 23, 2023 =
* Improvements in Contact Filtering
* New developer documentation
* Refactored plugin and performance improvements
* Enhancements and bug fixes
* New: Use SmartCode on the Activity notes from the automation
* Fixed: Theme color is not showing in the Gutenberg editor
* Fixed: Image alignment issue in the block editor
* Fixed: Email template saving issue
* Fixed: The unsubscribe link is not working in the double opt-in confirmation page issue
* Fixed: The multiline custom field is not working in the email body issue

= 2.6.0 Date: Oct 20, 2022 =
* Faster email editor
* Improved Email Sequences
* Improvement on Automation Goals
* Select and modify email template blocks in bulk
* Experimental features for Faster Contact Navigations, Date Formats
* UI & UX Improvements
* Bug fixes and minor improvements
* Experimental Feature: Email Archives in the frontend (Pro)
* Email campaign, sequence, automation activity conditions on Contact Advanced Filters (pro)
* New WooCommerce/EDD/LearnDash/LifterLMS conditions (Pro)
* New Trigger: Birthday Automation (Pro)
* New Action: Remove WordPress User Role
* New Trigger: Leave from a Course(LearnDash)

= 2.5.95 (Date: Aug 19, 2022) =
* Advanced wait action in Automation
* Added restart automation to all(almost) triggers
* Sequence filtering for automation
* View revenue for specific emails
* Create Fluent Support tickets from Automation
* Split test automation scenarios (pro)
* Revenue metrics in email sequences
* More conditions in Advanced Filtering (pro)
* Enable/Disable auto sync for integrated tools
* Email preference management short-code
* Detailed CRM reporting (pro)
* Pre-populate Fluent Forms data from FluentCRM
* Improved Tag import and auto-syncing for MailChimp Driver
* Bug fixes & improvements

= 2.5.93 (Date: July 07, 2022 ) =
* Improved scheduled campaigns
* Huge Performance Improvement
* Fixed Country Name Filters
* Improved Contact Imports
* WP User Sync Issue fixed
* Contact Exclude from campaign fixed
* WP Ultimo conflict issue resolved

= 2.5.9 (Date: May 27, 2022 ) =
* UI Improvements
* Integration Improvements
* More Bulk Actions Added
* Added Subscriber Segment selections view for campaign
* Added more filters for contacts

= 2.5.8 (Date: March 07, 2022 ) =
* Fixed campaign sending issue for some server
* Double Optin issue has been fixed
* Integration Improvements
* UI Improvements

= 2.5.6 (Date: February 28, 2022 ) =
* Added Auto Migration from ActiveCampaign, MailerLite, MailChimp, Drip, ConvertKit
* Fixed CSV Import Issue for duplicate emails
* Email Builder Issues fixed for latest version of WP
* Improved Contact Filtering
* Integration Improvements
* Improved UI

= 2.5.5 (Date: February 07, 2022 ) =
* Bulk Actions Improvements for Contacts
* Integrations Improvements
* Automation triggers issues are fixed
* UI & UX improvements

= 2.5.2 (Date: February 01, 2022 ) =
* Compatability with WordPress 5.9
* Improved Email Builder
* CSV import duplicate data issue fixed
* Automation Improvement
* Tagging Improvement
* UI&UX improvement in several screens across the app

= 2.5.1 (Date: January 28, 2022) =
* Improved Contact import
* Contact Search and Segment Improvement
* Email Campaign improvement and better reporting
* Automation Triggers and Actions improvements
* More Integrations
* Framework update.
* Tag & List add/remove confirmation

== Upgrade Notice ==
The latest version is compatible with the previous version, So nothing to worry about.
