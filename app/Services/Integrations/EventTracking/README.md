# Custom Event Tracking Trigger with Smart Codes

This integration adds a new custom event tracking trigger to FluentCRM that includes smart code capabilities, similar to how WooCommerce integration works.

## Features

- **Custom Event Tracking Trigger**: A new trigger that fires when events are tracked for contacts
- **Smart Code Support**: Event data is available as smart codes in emails and automation actions
- **Seamless Integration**: Works with the existing FluentCRM event tracking system

## Files Created

1. **CustomEventTrackingTrigger.php** - The main trigger class
2. **EventSmartCodeParse.php** - Smart code parser for event data
3. **EventTrackingInit.php** - Integration initialization and registration
4. **README.md** - This documentation file

## How It Works

### 1. Trigger Registration
The trigger is automatically registered when the integration is loaded. It listens for the `fluent_crm/event_tracked` action and fires the custom trigger.

### 2. Smart Code Availability
When the trigger fires, the following smart codes become available:

#### Current Event Smart Codes (in automation context)
- `{{custom_event.event_key}}` - The event key
- `{{custom_event.title}}` - The event title  
- `{{custom_event.value}}` - The event value
- `{{custom_event.counter}}` - How many times this event occurred
- `{{custom_event.provider}}` - The event provider
- `{{custom_event.date}}` - When the event was recorded
- `{{custom_event.datetime}}` - Date and time when the event was recorded
- `{{custom_event.created_by}}` - User who created the event
- `{{custom_event.id}}` - Event ID

#### Last Event Smart Codes (always available)
- `{{event_last.event_key}}` - Last event key
- `{{event_last.title}}` - Last event title
- `{{event_last.value}}` - Last event value
- `{{event_last.counter}}` - Last event counter
- `{{event_last.provider}}` - Last event provider
- `{{event_last.date}}` - Last event date
- `{{event_last.datetime}}` - Last event date and time
- `{{event_last.created_by}}` - Last event created by user
- `{{event_last.id}}` - Last event ID

## Usage Example

### Creating an Event
```php
// Track an event for a contact
FluentCrmApi('event_tracker')->track([
    'subscriber_id' => 123, // or use 'email' => '<EMAIL>'
    'event_key' => 'product_viewed',
    'title' => 'Product Viewed',
    'value' => 'Premium Widget',
    'provider' => 'custom'
]);
```

### Setting Up the Automation
1. Go to FluentCRM > Automations
2. Create a new automation
3. Choose "Custom Event Tracked (with Smart Codes)" as the trigger
4. Configure the event key you want to track
5. Add email actions and use the smart codes in your email content

### Example Email Content
```html
<h2>Thanks for viewing {{custom_event.value}}!</h2>
<p>Hi {{contact.first_name}},</p>
<p>We noticed you viewed <strong>{{custom_event.value}}</strong> on {{custom_event.date}}.</p>
<p>This is the {{custom_event.counter}} time you've shown interest in this item.</p>
```

## Technical Details

### Trigger Name
- `fluent_crm/custom_event_tracked`

### Smart Code Groups
- `custom_event` - Current event data (available in automation context)
- `event_last` - Last event data (always available)

### Dependencies
- FluentCRM Core (with event tracking enabled)
- FluentCampaign Pro

### Compatibility
- Works with existing event tracking system
- Compatible with all FluentCRM automation features
- Follows FluentCRM coding standards and patterns

## Installation

The integration is automatically loaded when FluentCampaign Pro is active. No additional installation steps required.

## Requirements

- FluentCRM with event tracking experimental feature enabled
- FluentCampaign Pro plugin active

## Support

This is a custom integration that extends FluentCRM's event tracking capabilities. For support with the base event tracking functionality, refer to FluentCRM documentation.
