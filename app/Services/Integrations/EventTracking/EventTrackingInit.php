<?php

namespace FluentCampaign\App\Services\Integrations\EventTracking;

use FluentCampaign\App\Services\Funnel\Triggers\CustomEventTrackingTrigger;
use FluentCrm\App\Services\Helper;

class EventTrackingInit
{
    public function init()
    {
        // Only initialize if event tracking is enabled
        if (!Helper::isExperimentalEnabled('event_tracking')) {
            return;
        }

        // Initialize the custom event tracking trigger
        new CustomEventTrackingTrigger();

        // Initialize the smart code parser
        (new EventSmartCodeParse())->init();

        // Hook into the existing event tracking system to fire our custom trigger
        \add_action('fluent_crm/event_tracked', array($this, 'handleEventTracked'), 10, 2);

        // Add our trigger to the list of custom event triggers for smart code recognition
        \add_filter('fluent_crm/custom_event_trigger_names', array($this, 'addCustomEventTriggerNames'));

        // Add admin notice to inform users about the new trigger
        \add_action('admin_notices', array($this, 'showCustomEventTriggerNotice'));

        // Handle AJAX request to dismiss notice
        \add_action('wp_ajax_fluentcrm_dismiss_custom_event_notice', array($this, 'handleDismissNotice'));
    }

    /**
     * Handle the event tracked action and fire our custom trigger
     * 
     * @param \FluentCrm\App\Models\EventTracker $event
     * @param \FluentCrm\App\Models\Subscriber $subscriber
     */
    public function handleEventTracked($event, $subscriber)
    {
        // Fire our custom trigger with the same arguments
        \do_action('fluentcrm_funnel_start_fluent_crm/custom_event_tracked', $event, $subscriber);
    }

    /**
     * Add our custom event trigger names to the list for smart code recognition
     * 
     * @param array $triggerNames
     * @return array
     */
    public function addCustomEventTriggerNames($triggerNames)
    {
        $triggerNames[] = 'fluent_crm/custom_event_tracked';
        return $triggerNames;
    }

    /**
     * Show admin notice about the new custom event tracking trigger
     */
    public function showCustomEventTriggerNotice()
    {
        // Only show on FluentCRM pages
        if (!isset($_GET['page']) || \strpos($_GET['page'], 'fluentcrm') === false) {
            return;
        }

        // Check if user has dismissed this notice
        if (\get_user_meta(\get_current_user_id(), 'fluentcrm_custom_event_trigger_notice_dismissed', true)) {
            return;
        }

        ?>
        <div class="notice notice-info is-dismissible" id="fluentcrm-custom-event-trigger-notice">
            <h3><?php \_e('🎉 New Custom Event Tracking Trigger Available!', 'fluentcampaign-pro'); ?></h3>
            <p>
                <?php \_e('A new "Custom Event Tracked (with Smart Codes)" trigger has been added to your FluentCRM automations. This trigger includes smart code capabilities that allow you to use event data in your emails and actions.', 'fluentcampaign-pro'); ?>
            </p>
            <p>
                <strong><?php \_e('Available Smart Codes:', 'fluentcampaign-pro'); ?></strong>
                <code>{{custom_event.event_key}}</code>,
                <code>{{custom_event.title}}</code>,
                <code>{{custom_event.value}}</code>,
                <code>{{custom_event.counter}}</code>,
                <code>{{custom_event.provider}}</code>,
                <code>{{custom_event.date}}</code>
            </p>
            <p>
                <a href="<?php echo \admin_url('admin.php?page=fluentcrm-admin#/funnels'); ?>" class="button button-primary">
                    <?php \_e('Create New Automation', 'fluentcampaign-pro'); ?>
                </a>
                <button type="button" class="button" onclick="fluentcrmDismissCustomEventNotice()">
                    <?php \_e('Dismiss Notice', 'fluentcampaign-pro'); ?>
                </button>
            </p>
        </div>
        <script>
        function fluentcrmDismissCustomEventNotice() {
            jQuery.post(ajaxurl, {
                action: 'fluentcrm_dismiss_custom_event_notice',
                nonce: '<?php echo \wp_create_nonce('fluentcrm_dismiss_notice'); ?>'
            });
            jQuery('#fluentcrm-custom-event-trigger-notice').fadeOut();
        }
        </script>
        <?php
    }

    /**
     * Handle AJAX request to dismiss the notice
     */
    public function handleDismissNotice()
    {
        if (!\wp_verify_nonce($_POST['nonce'], 'fluentcrm_dismiss_notice')) {
            \wp_die('Security check failed');
        }

        \update_user_meta(\get_current_user_id(), 'fluentcrm_custom_event_trigger_notice_dismissed', true);
        \wp_die();
    }
}
