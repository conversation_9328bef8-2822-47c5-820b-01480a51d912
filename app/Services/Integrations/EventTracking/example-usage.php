<?php
/**
 * Example Usage of Custom Event Tracking Trigger with Smart Codes
 * 
 * This file demonstrates how to use the custom event tracking integration
 * in various scenarios.
 */

// Ensure this is only run in WordPress context
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Example 1: Track a simple event
 */
function track_product_view_event($product_id, $user_email) {
    // Track when a user views a product
    $result = FluentCrmApi('event_tracker')->track([
        'email' => $user_email,
        'event_key' => 'product_viewed',
        'title' => 'Product Viewed',
        'value' => 'Product ID: ' . $product_id,
        'provider' => 'custom_store'
    ]);
    
    if (is_wp_error($result)) {
        error_log('Failed to track product view: ' . $result->get_error_message());
    }
}

/**
 * Example 2: Track a purchase event with detailed information
 */
function track_purchase_event($order_data, $customer_email) {
    $result = FluentCrmApi('event_tracker')->track([
        'email' => $customer_email,
        'event_key' => 'purchase_completed',
        'title' => 'Purchase Completed',
        'value' => json_encode([
            'order_id' => $order_data['id'],
            'total' => $order_data['total'],
            'currency' => $order_data['currency'],
            'items' => $order_data['items']
        ]),
        'provider' => 'custom_ecommerce'
    ]);
    
    return $result;
}

/**
 * Example 3: Track user engagement events
 */
function track_engagement_event($user_id, $action, $details = '') {
    $user = get_user_by('ID', $user_id);
    if (!$user) {
        return false;
    }
    
    $result = FluentCrmApi('event_tracker')->track([
        'email' => $user->user_email,
        'event_key' => 'user_engagement',
        'title' => 'User Engagement: ' . ucfirst($action),
        'value' => $details,
        'provider' => 'engagement_tracker'
    ]);
    
    return $result;
}

/**
 * Example 4: Hook into WordPress actions to automatically track events
 */
function setup_automatic_event_tracking() {
    // Track when users log in
    add_action('wp_login', function($user_login, $user) {
        track_engagement_event($user->ID, 'login', 'User logged in');
    }, 10, 2);
    
    // Track when posts are viewed (if you have a post view tracking system)
    add_action('wp_head', function() {
        if (is_single() && is_user_logged_in()) {
            global $post;
            track_engagement_event(
                get_current_user_id(), 
                'post_view', 
                'Viewed: ' . $post->post_title
            );
        }
    });
    
    // Track form submissions (example with Contact Form 7)
    add_action('wpcf7_mail_sent', function($contact_form) {
        $submission = \WPCF7_Submission::get_instance();
        $posted_data = $submission->get_posted_data();
        
        if (isset($posted_data['your-email'])) {
            FluentCrmApi('event_tracker')->track([
                'email' => $posted_data['your-email'],
                'event_key' => 'form_submitted',
                'title' => 'Contact Form Submitted',
                'value' => 'Form: ' . $contact_form->title(),
                'provider' => 'contact_form_7'
            ]);
        }
    });
}

/**
 * Example 5: Bulk event tracking for data migration
 */
function migrate_existing_events_to_fluentcrm($events_data) {
    $success_count = 0;
    $error_count = 0;
    
    foreach ($events_data as $event) {
        $result = FluentCrmApi('event_tracker')->track([
            'email' => $event['email'],
            'event_key' => $event['event_key'],
            'title' => $event['title'],
            'value' => $event['value'] ?? '',
            'provider' => $event['provider'] ?? 'migration'
        ]);
        
        if (is_wp_error($result)) {
            $error_count++;
            error_log('Migration error: ' . $result->get_error_message());
        } else {
            $success_count++;
        }
    }
    
    return [
        'success' => $success_count,
        'errors' => $error_count
    ];
}

/**
 * Example 6: Custom event tracking with validation
 */
function track_validated_event($email, $event_key, $title, $value = '', $provider = 'custom') {
    // Validate email
    if (!is_email($email)) {
        return new WP_Error('invalid_email', 'Invalid email address provided');
    }
    
    // Validate event key
    if (empty($event_key) || !is_string($event_key)) {
        return new WP_Error('invalid_event_key', 'Event key must be a non-empty string');
    }
    
    // Validate title
    if (empty($title) || !is_string($title)) {
        return new WP_Error('invalid_title', 'Title must be a non-empty string');
    }
    
    // Track the event
    return FluentCrmApi('event_tracker')->track([
        'email' => $email,
        'event_key' => sanitize_key($event_key),
        'title' => sanitize_text_field($title),
        'value' => sanitize_textarea_field($value),
        'provider' => sanitize_text_field($provider)
    ]);
}

/**
 * Example 7: REST API endpoint for external event tracking
 */
function register_custom_event_api_endpoint() {
    register_rest_route('fluentcrm/v1', '/track-event', [
        'methods' => 'POST',
        'callback' => 'handle_external_event_tracking',
        'permission_callback' => function() {
            return current_user_can('manage_options'); // Adjust permissions as needed
        },
        'args' => [
            'email' => [
                'required' => true,
                'validate_callback' => function($param) {
                    return is_email($param);
                }
            ],
            'event_key' => [
                'required' => true,
                'sanitize_callback' => 'sanitize_key'
            ],
            'title' => [
                'required' => true,
                'sanitize_callback' => 'sanitize_text_field'
            ],
            'value' => [
                'sanitize_callback' => 'sanitize_textarea_field'
            ],
            'provider' => [
                'default' => 'api',
                'sanitize_callback' => 'sanitize_text_field'
            ]
        ]
    ]);
}

function handle_external_event_tracking($request) {
    $result = FluentCrmApi('event_tracker')->track([
        'email' => $request['email'],
        'event_key' => $request['event_key'],
        'title' => $request['title'],
        'value' => $request['value'] ?? '',
        'provider' => $request['provider']
    ]);
    
    if (is_wp_error($result)) {
        return new WP_Error('tracking_failed', $result->get_error_message(), ['status' => 400]);
    }
    
    return [
        'success' => true,
        'event_id' => $result->id,
        'message' => 'Event tracked successfully'
    ];
}

// Initialize automatic tracking (uncomment to enable)
// add_action('init', 'setup_automatic_event_tracking');

// Register API endpoint (uncomment to enable)
// add_action('rest_api_init', 'register_custom_event_api_endpoint');
