<?php

namespace FluentCampaign\App\Services\Integrations\EventTracking;

use FluentCrm\App\Models\EventTracker;
use FluentCrm\App\Models\FunnelSubscriber;
use FluentCrm\Framework\Support\Arr;

class EventSmartCodeParse
{
    public function init()
    {
        add_filter('fluent_crm/smartcode_group_callback_custom_event', array($this, 'parseCurrentEvent'), 10, 4);
        add_filter('fluent_crm/smartcode_group_callback_event_last', array($this, 'parseLastEvent'), 10, 4);
        
        add_filter('fluent_crm/extended_smart_codes', array($this, 'pushGeneralCodes'));
        add_filter('fluent_crm_funnel_context_smart_codes', array($this, 'pushContextCodes'), 10, 2);
    }

    public function parseCurrentEvent($code, $valueKey, $defaultValue, $subscriber)
    {
        if (empty($subscriber->funnel_subscriber_id)) {
            return $this->parseLastEvent($code, $valueKey, $defaultValue, $subscriber);
        }

        $funnelSub = FunnelSubscriber::where('id', $subscriber->funnel_subscriber_id)->first();

        if (!$funnelSub || !$funnelSub->source_ref_id || !$this->isCustomEventTrigger($funnelSub->source_trigger_name)) {
            return $this->parseLastEvent($code, $valueKey, $defaultValue, $subscriber);
        }

        try {
            $event = EventTracker::find($funnelSub->source_ref_id);
        } catch (\Exception $exception) {
            return $defaultValue;
        }

        if (!$event || !$event->id) {
            return $defaultValue;
        }

        return $this->parseEventProps($event, $valueKey, $defaultValue);
    }

    public function parseLastEvent($code, $valueKey, $defaultValue, $subscriber)
    {
        $lastEvent = EventTracker::where('subscriber_id', $subscriber->id)
            ->orderBy('updated_at', 'DESC')
            ->first();

        if (!$lastEvent) {
            return $defaultValue;
        }

        return $this->parseEventProps($lastEvent, $valueKey, $defaultValue);
    }

    public function pushGeneralCodes($codes)
    {
        $codes['event_last'] = [
            'key'        => 'event_last',
            'title'      => 'Last Event Tracked',
            'shortcodes' => $this->getSmartCodes()
        ];

        return $codes;
    }

    public function pushContextCodes($codes, $context)
    {
        if (!$this->isCustomEventTrigger($context)) {
            return $codes;
        }

        $codes[] = [
            'key'        => 'custom_event',
            'title'      => 'Current Event - Custom Tracking',
            'shortcodes' => $this->getSmartCodes('current')
        ];

        return $codes;
    }

    /**
     * @param $event EventTracker
     * @param $valueKey string
     * @param $defaultValue string
     * @return string
     */
    protected function parseEventProps($event, $valueKey, $defaultValue = '')
    {
        if (!$event || !$event->id) {
            return $defaultValue;
        }

        switch ($valueKey) {
            case 'event_key':
                return $event->event_key;
            case 'title':
                return $event->title;
            case 'value':
                return $event->value ?: $defaultValue;
            case 'counter':
                return (string) $event->counter;
            case 'provider':
                return $event->provider ?: 'custom';
            case 'date':
                return date_i18n(get_option('date_format'), strtotime($event->created_at));
            case 'datetime':
                return date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($event->created_at));
            case 'created_by':
                if ($event->created_by) {
                    $user = get_user_by('ID', $event->created_by);
                    return $user ? $user->display_name : $defaultValue;
                }
                return $defaultValue;
            case 'id':
                return (string) $event->id;
        }

        return $defaultValue;
    }

    private function getSmartCodes($context = '')
    {
        $generalCodes = [
            '{{event_last.event_key}}'   => __('Event Key', 'fluentcampaign-pro'),
            '{{event_last.title}}'       => __('Event Title', 'fluentcampaign-pro'),
            '{{event_last.value}}'       => __('Event Value', 'fluentcampaign-pro'),
            '{{event_last.counter}}'     => __('Event Counter', 'fluentcampaign-pro'),
            '{{event_last.provider}}'    => __('Event Provider', 'fluentcampaign-pro'),
            '{{event_last.date}}'        => __('Event Date', 'fluentcampaign-pro'),
            '{{event_last.datetime}}'    => __('Event Date & Time', 'fluentcampaign-pro'),
            '{{event_last.created_by}}'  => __('Created By User', 'fluentcampaign-pro'),
            '{{event_last.id}}'          => __('Event ID', 'fluentcampaign-pro'),
        ];

        if ($context == 'current') {
            return [
                '{{custom_event.event_key}}'   => __('Event Key', 'fluentcampaign-pro'),
                '{{custom_event.title}}'       => __('Event Title', 'fluentcampaign-pro'),
                '{{custom_event.value}}'       => __('Event Value', 'fluentcampaign-pro'),
                '{{custom_event.counter}}'     => __('Event Counter', 'fluentcampaign-pro'),
                '{{custom_event.provider}}'    => __('Event Provider', 'fluentcampaign-pro'),
                '{{custom_event.date}}'        => __('Event Date', 'fluentcampaign-pro'),
                '{{custom_event.datetime}}'    => __('Event Date & Time', 'fluentcampaign-pro'),
                '{{custom_event.created_by}}'  => __('Created By User', 'fluentcampaign-pro'),
                '{{custom_event.id}}'          => __('Event ID', 'fluentcampaign-pro'),
            ];
        }

        return $generalCodes;
    }

    private function isCustomEventTrigger($triggerName)
    {
        $supportedTriggers = apply_filters('fluent_crm/custom_event_trigger_names', [
            'fluent_crm/custom_event_tracked'
        ]);

        return in_array($triggerName, $supportedTriggers);
    }
}
