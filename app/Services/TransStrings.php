<?php

namespace FluentCrm\App\Services;

//This is an auto-generated file. Please do not edit manually

class TransStrings
{

    public static function getStrings()
    {
        return [
            '(y)'                                                                                    => __('(y)', 'fluent-crm'),
            '+ Add'                                                                                  => __('+ Add', 'fluent-crm'),
            '+ Any SMTP Provider'                                                                    => __('+ Any SMTP Provider', 'fluent-crm'),
            '+ New Option'                                                                           => __('+ New Option', 'fluent-crm'),
            '+ Photo'                                                                                => __('+ Photo', 'fluent-crm'),
            'A'                                                                                      => __('A', 'fluent-crm'),
            'A double opt-in email will be sent if the contact is new'                               => __('A double opt-in email will be sent if the contact is new', 'fluent-crm'),
            'A/B Testing Result'                                                                     => __('A/B Testing Result', 'fluent-crm'),
            'API Password:'                                                                          => __('API Password:', 'fluent-crm'),
            'API Username:'                                                                          => __('API Username:', 'fluent-crm'),
            'APIs'                                                                                   => __('APIs', 'fluent-crm'),
            'A_B_Testing_Alert'                                                                      => __('This subject will not be used to send emails. Please set A/B testing subjects', 'fluent-crm'),
            'AbEmailSubjectPromo.desc'                                                               => __('You can split test your Email Subject Line and Test which Subject Lines got more open and click rate. This is a pro feature', 'fluent-crm'),
            'AbEmailSubjectPromo.title'                                                              => __('Send Emails with Multiple Subject Line (A/B) test', 'fluent-crm'),
            'About this company'                                                                     => __('About this company', 'fluent-crm'),
            'Action'                                                                                 => __('Action', 'fluent-crm'),
            'Action Blocks'                                                                          => __('Action Blocks', 'fluent-crm'),
            'Action Type'                                                                            => __('Action Type', 'fluent-crm'),
            'Actions'                                                                                => __('Actions', 'fluent-crm'),
            'Active'                                                                                 => __('Active', 'fluent-crm'),
            'Add'                                                                                    => __('Add', 'fluent-crm'),
            'Add Action'                                                                             => __('Add Action', 'fluent-crm'),
            'Add Action / Goal'                                                                      => __('Add Action / Goal', 'fluent-crm'),
            'Add Action/Benchmark/Conditions'                                                        => __('Add Action/Benchmark/Conditions', 'fluent-crm'),
            'Add Action/Goals/Conditions'                                                            => __('Add Action/Goals/Conditions', 'fluent-crm'),
            'Add Address Info'                                                                       => __('Add Address Info', 'fluent-crm'),
            'Add Another Conditional Group'                                                          => __('Add Another Conditional Group', 'fluent-crm'),
            'Add Automation'                                                                         => __('Add Automation', 'fluent-crm'),
            'Add Campaign Title'                                                                     => __('Add Campaign Title', 'fluent-crm'),
            'Add Company'                                                                            => __('Add Company', 'fluent-crm'),
            'Add Condition'                                                                          => __('Add Condition', 'fluent-crm'),
            'Add Contact'                                                                            => __('Add Contact', 'fluent-crm'),
            'Add Custom Data'                                                                        => __('Add Custom Data', 'fluent-crm'),
            'Add Existing'                                                                           => __('Add Existing', 'fluent-crm'),
            'Add Field'                                                                              => __('Add Field', 'fluent-crm'),
            'Add More'                                                                               => __('Add More', 'fluent-crm'),
            'Add New'                                                                                => __('Add New', 'fluent-crm'),
            'Add New Condition'                                                                      => __('Add New Condition', 'fluent-crm'),
            'Add New Contact'                                                                        => __('Add New Contact', 'fluent-crm'),
            'Add New Custom Field'                                                                   => __('Add New Custom Field', 'fluent-crm'),
            'Add New Key'                                                                            => __('Add New Key', 'fluent-crm'),
            'Add New Manager'                                                                        => __('Add New Manager', 'fluent-crm'),
            'Add New REST API Key'                                                                   => __('Add New REST API Key', 'fluent-crm'),
            'Add New Smart Link'                                                                     => __('Add New Smart Link', 'fluent-crm'),
            'Add OR Condition'                                                                       => __('Add OR Condition', 'fluent-crm'),
            'Add Options'                                                                            => __('Add Options', 'fluent-crm'),
            'Add Sequence'                                                                           => __('Add Sequence', 'fluent-crm'),
            'Add SmartCodes'                                                                         => __('Add SmartCodes', 'fluent-crm'),
            'Add Social Media URLs'                                                                  => __('Add Social Media URLs', 'fluent-crm'),
            'Add Subscribers'                                                                        => __('Add Subscribers', 'fluent-crm'),
            'Add Tags'                                                                               => __('Add Tags', 'fluent-crm'),
            'Add Tags to Subscribers'                                                                => __('Add Tags to Subscribers', 'fluent-crm'),
            'Add To Automation'                                                                      => __('Add To Automation', 'fluent-crm'),
            'Add To Automation Funnel'                                                               => __('Add To Automation Funnel', 'fluent-crm'),
            'Add To Company'                                                                         => __('Add To Company', 'fluent-crm'),
            'Add To Email Sequence'                                                                  => __('Add To Email Sequence', 'fluent-crm'),
            'Add To Lists'                                                                           => __('Add To Lists', 'fluent-crm'),
            'Add To Sequence'                                                                        => __('Add To Sequence', 'fluent-crm'),
            'Add To Sequence: '                                                                      => __('Add To Sequence: ', 'fluent-crm'),
            'Add To Tags'                                                                            => __('Add To Tags', 'fluent-crm'),
            'Add Value'                                                                              => __('Add Value', 'fluent-crm'),
            'Add a Sequence Email'                                                                   => __('Add a Sequence Email', 'fluent-crm'),
            'Add existing company'                                                                   => __('Add existing company', 'fluent-crm'),
            'Add existing contacts'                                                                  => __('Add existing contacts', 'fluent-crm'),
            'Add new Manager'                                                                        => __('Add new Manager', 'fluent-crm'),
            'Add new filter to narrow down your contacts based on different properties'              => __('Add new filter to narrow down your contacts based on different properties', 'fluent-crm'),
            'Add or Remove'                                                                          => __('Add or Remove', 'fluent-crm'),
            'Add to Automation'                                                                      => __('Add to Sequence', 'fluent-crm'),
            'Add to List'                                                                            => __('Add to List', 'fluent-crm'),
            'Add to Sequence'                                                                        => __('Add to Sequence', 'fluent-crm'),
            'Add to Tags'                                                                            => __('Add to Tags', 'fluent-crm'),
            'Add to this Sequence'                                                                   => __('Add to this Sequence', 'fluent-crm'),
            'Add_Contacts_To_Automation_Confirm_Message'                                             => __('Are you sure you want to add contacts to selected Automation Funnel?', 'fluent-crm'),
            'Add_Contacts_To_Company_Confirm_Message'                                                => __('Are you sure you want to add contacts to selected company?', 'fluent-crm'),
            'Add_Contacts_To_Email_Sequence_Confirm_Message'                                         => __('Are you sure you want to add contacts to selected email sequence?', 'fluent-crm'),
            'Add_The_siapitd'                                                                        => __('The subscriber is already present in the database.', 'fluent-crm'),
            'Added'                                                                                  => __('Added', 'fluent-crm'),
            'Added @ '                                                                               => __('Added @ ', 'fluent-crm'),
            'Additional Info'                                                                        => __('Additional Info', 'fluent-crm'),
            'Addons.Recommended.Plugins.desc'                                                        => __('Extend FluentCRM functionalities and supercharge your email marketing and automation', 'fluent-crm'),
            'Addons.fluentcrm_pro.title'                                                             => __('Supercharge your Email Marketing and Automation with FluentCRM Pro', 'fluent-crm'),
            'Address'                                                                                => __('Address', 'fluent-crm'),
            'Address Fields'                                                                         => __('Address Fields', 'fluent-crm'),
            'Address Information'                                                                    => __('Address Information', 'fluent-crm'),
            'Address Line 1'                                                                         => __('Address Line 1', 'fluent-crm'),
            'Address Line 2'                                                                         => __('Address Line 2', 'fluent-crm'),
            'Admin Email Address'                                                                    => __('Admin Email Address', 'fluent-crm'),
            'Admin Email Addresses (Internal Use only)'                                              => __('Admin Email Addresses (Internal Use only)', 'fluent-crm'),
            'Admin_Email_Help'                                                                       => __('This email will be used to send important notification to administrator', 'fluent-crm'),
            'Admin_Email_Inline_Help'                                                                => __('Only for internal use. This email will not be shown to your customers. Use comma separated value to add multiple emails.', 'fluent-crm'),
            'Advanced Features Config'                                                               => __('Advanced Features Config', 'fluent-crm'),
            'Advanced Features Settings'                                                             => __('Advanced Features Settings', 'fluent-crm'),
            'Advanced Filter'                                                                        => __('Advanced Filter', 'fluent-crm'),
            'Advanced Filter is a pro feature'                                                       => __('Advanced Filter is a pro feature', 'fluent-crm'),
            'After 1 Hour'                                                                           => __('After 1 Hour', 'fluent-crm'),
            'After 1 Week'                                                                           => __('After 1 Week', 'fluent-crm'),
            'After 2 Days'                                                                           => __('After 2 Days', 'fluent-crm'),
            'All'                                                                                    => __('All', 'fluent-crm'),
            'All Campaigns'                                                                          => __('All Campaigns', 'fluent-crm'),
            'All Done'                                                                               => __('All Done', 'fluent-crm'),
            'All Email Activities'                                                                   => __('All Email Activities', 'fluent-crm'),
            'All Emails'                                                                             => __('All Emails', 'fluent-crm'),
            'All Lists'                                                                              => __('All Lists', 'fluent-crm'),
            'All Products'                                                                           => __('All Products', 'fluent-crm'),
            'All Sequences'                                                                          => __('All Sequences', 'fluent-crm'),
            'All available contacts'                                                                 => __('All available contacts', 'fluent-crm'),
            'All available subscribers'                                                              => __('All available subscribers', 'fluent-crm'),
            'All contacts from'                                                                      => __('All contacts from', 'fluent-crm'),
            'All the selected logs that are'                                                         => __('All the selected logs that are', 'fluent-crm'),
            'AllSegments.desc'                                                                       => __('Create dynamic Segments of contacts by using dynamic contact properties and filter your target audience', 'fluent-crm'),
            'All_Create_YFES'                                                                        => __('Create Your First Email Sequence', 'fluent-crm'),
            'All_Email_Hist'                                                                         => __('All your email history from this recurring campaign wil be shown here.', 'fluent-crm'),
            'All_Looks_lydnsasey'                                                                    => __('Looks like you did not set any sequence emails yet', 'fluent-crm'),
            'Almost Done!'                                                                           => __('Almost Done!', 'fluent-crm'),
            'Already_Contact_in_Automation'                                                          => __('Contacts already in the Automation will be skipped & only subscribed contacts will be attached', 'fluent-crm'),
            'Already_Contact_in_Company'                                                             => __('Contacts already in the company will be skipped', 'fluent-crm'),
            'Already_Contact_in_Email_Sequence'                                                      => __('Contacts already in the email sequence will be skipped', 'fluent-crm'),
            'Anonymize ip Address for associate contact data'                                        => __('Anonymize ip Address for associate contact data', 'fluent-crm'),
            'Api key delete confirmation'                                                            => __('Are you sure you want to delete the api key?', 'fluent-crm'),
            'Api key delete warning'                                                                 => __('Note: Once deleted, the API key cannot be recovered.', 'fluent-crm'),
            'Apply'                                                                                  => __('Apply', 'fluent-crm'),
            'Apply Condition'                                                                        => __('Apply Condition', 'fluent-crm'),
            'Apply Lists'                                                                            => __('Apply Lists', 'fluent-crm'),
            'Apply Lists when clicked (optional)'                                                    => __('Apply Lists when clicked (optional)', 'fluent-crm'),
            'Apply Tags'                                                                             => __('Apply Tags', 'fluent-crm'),
            'Apply Tags when clicked'                                                                => __('Apply Tags when clicked', 'fluent-crm'),
            'April'                                                                                  => __('April', 'fluent-crm'),
            'Archived'                                                                               => __('Archived', 'fluent-crm'),
            'Are you sure to delete this?'                                                           => __('Are you sure to delete this?', 'fluent-crm'),
            'Are you sure to delete?'                                                                => __('Are you sure to delete?', 'fluent-crm'),
            'Are you sure to send double optin?'                                                     => __('Are you sure to send double optin?', 'fluent-crm'),
            'Are you sure you want to delete this Sequence?'                                         => __('Are you sure you want to delete this Sequence?', 'fluent-crm'),
            'Are you sure you want to remove this?'                                                  => __('Are you sure you want to remove this?', 'fluent-crm'),
            'Assign Company'                                                                         => __('Assign Company', 'fluent-crm'),
            'Assign selected companies'                                                              => __('Assign selected companies', 'fluent-crm'),
            'Assigned List in FluentCRM (optional)'                                                  => __('Assigned List in FluentCRM (optional)', 'fluent-crm'),
            'Associate FluentCRM Manager (Non-Admin Only)'                                           => __('Associate FluentCRM Manager (Non-Admin Only)', 'fluent-crm'),
            'Attach Selected Contacts'                                                               => __('Attach Selected Contacts', 'fluent-crm'),
            'Attached Tags and Lists'                                                                => __('Attached Tags and Lists', 'fluent-crm'),
            'Attached Trigger could not be found'                                                    => __('Attached Trigger could not be found', 'fluent-crm'),
            'August'                                                                                 => __('August', 'fluent-crm'),
            'Auto Create'                                                                            => __('Auto Create', 'fluent-crm'),
            'Automation Funnel'                                                                      => __('Automation Funnel', 'fluent-crm'),
            'Automation Funnels'                                                                     => __('Automation Funnels', 'fluent-crm'),
            'Automation Name'                                                                        => __('Automation Name', 'fluent-crm'),
            'Automation Trigger Title'                                                               => __('Automation Trigger Title', 'fluent-crm'),
            'Automation_Delete_Alert'                                                                => __('Are you sure you want to delete this automation?', 'fluent-crm'),
            'Automation_Sync_Note_1'                                                                 => __('If you add any new step to your automation after some contacts completed the automation, you can resume the newly added steps to the existing completed contacts.', 'fluent-crm'),
            'Automation_Sync_Note_2'                                                                 => __('Sync new steps to completed contacts', 'fluent-crm'),
            'Automation_Sync_Note_3'                                                                 => __('The new steps will start processing within 5 minutes once you confirm.', 'fluent-crm'),
            'Automation_Sync_Note_4'                                                                 => __('Looks like all the steps are synced with your Automation contacts. No action is required.', 'fluent-crm'),
            'Automation_Sync_Note_5'                                                                 => __('You need to have FluentCRM Pro to use this feature', 'fluent-crm'),
            'Automation_Sync_Note_6'                                                                 => __('New steps are synced for your completed contacts', 'fluent-crm'),
            'Automations'                                                                            => __('Automations', 'fluent-crm'),
            'Average Order Value (AOV)'                                                              => __('Average Order Value (AOV)', 'fluent-crm'),
            'Average Order/Customer (AOC)'                                                           => __('Average Order/Customer (AOC)', 'fluent-crm'),
            'B'                                                                                      => __('B', 'fluent-crm'),
            'Back'                                                                                   => __('Back', 'fluent-crm'),
            'Back To Campaigns'                                                                      => __('Back To Campaigns', 'fluent-crm'),
            'Back to Sequence'                                                                       => __('Back to Sequence', 'fluent-crm'),
            'Background Color'                                                                       => __('Background Color', 'fluent-crm'),
            'Basic Info'                                                                             => __('Basic Info', 'fluent-crm'),
            'Basic Information'                                                                      => __('Basic Information', 'fluent-crm'),
            'Basic_Info_Of_Rec_Camp'                                                                 => __('Provide the basic information of your recurring email campaign.', 'fluent-crm'),
            'BenchMark/Trigger Block'                                                                => __('BenchMark/Trigger Block', 'fluent-crm'),
            'Benchmarks'                                                                             => __('Benchmarks', 'fluent-crm'),
            'BlockEditor.You_can_Use_Fallback_value'                                                 => __('You can use fallback value using | separator. Example:', 'fluent-crm'),
            'Blog Posts'                                                                             => __('Blog Posts', 'fluent-crm'),
            'Body Background Color'                                                                  => __('Body Background Color', 'fluent-crm'),
            'Border Radius'                                                                          => __('Border Radius', 'fluent-crm'),
            'Bounce Handler'                                                                         => __('Bounce Handler', 'fluent-crm'),
            'Bounce Handling Settings'                                                               => __('Bounce Handling Settings', 'fluent-crm'),
            'Broadcast'                                                                              => __('Broadcast', 'fluent-crm'),
            'Broadcasts'                                                                             => __('Broadcasts', 'fluent-crm'),
            'Build Email By Drag and Drop Visual Editor'                                             => __('Build Email By Drag and Drop Visual Editor', 'fluent-crm'),
            'Business Full Address'                                                                  => __('Business Full Address', 'fluent-crm'),
            'Business Name'                                                                          => __('Business Name', 'fluent-crm'),
            'Business Settings'                                                                      => __('Business Settings', 'fluent-crm'),
            'Button'                                                                                 => __('Button', 'fluent-crm'),
            'Button Preview'                                                                         => __('Button Preview', 'fluent-crm'),
            'Button Text'                                                                            => __('Button Text', 'fluent-crm'),
            'Button URL'                                                                             => __('Button URL', 'fluent-crm'),
            'By'                                                                                     => __('By', 'fluent-crm'),
            'By Advanced Filter'                                                                     => __('By Advanced Filter', 'fluent-crm'),
            'By Date'                                                                                => __('By Date', 'fluent-crm'),
            'By Dynamic Segment'                                                                     => __('By Dynamic Segment', 'fluent-crm'),
            'By List & Tag'                                                                          => __('By List & Tag', 'fluent-crm'),
            'CONDITION TYPE'                                                                         => __('CONDITION TYPE', 'fluent-crm'),
            'CRM'                                                                                    => __('CRM', 'fluent-crm'),
            'CRM Managers'                                                                           => __('CRM Managers', 'fluent-crm'),
            'CRM Managers - Roles and Permissions'                                                   => __('CRM Managers - Roles and Permissions', 'fluent-crm'),
            'CRON Job Status'                                                                        => __('CRON Job Status', 'fluent-crm'),
            'CSV'                                                                                    => __('CSV', 'fluent-crm'),
            'CSV File'                                                                               => __('CSV File', 'fluent-crm'),
            'CSV Headers'                                                                            => __('CSV Headers', 'fluent-crm'),
            'Cam_Add_Remove_ToyCb'                                                                   => __('Add/Remove Tag of your Contacts based on email interaction', 'fluent-crm'),
            'Cam_Broadcast_Schedu'                                                                   => __('Broadcast/Schedule This Email Campaign Now', 'fluent-crm'),
            'Cam_Contacts_psf'                                                                       => __('Contacts processed so far...', 'fluent-crm'),
            'Cam_Continue_S'                                                                         => __('Continue [Subject & Settings]', 'fluent-crm'),
            'Cam_Continue_TNS_'                                                                      => __('Continue To Next Step [Recipients]', 'fluent-crm'),
            'Cam_Create_YFEC'                                                                        => __('Create Your First Email Campaign', 'fluent-crm'),
            'Cam_Current_ST_oySS'                                                                    => __('Current Server Time (Based on your Site Settings)', 'fluent-crm'),
            'Cam_Email_bsu'                                                                          => __('Email body successfully updated', 'fluent-crm'),
            'Cam_If_yteiaYcbten'                                                                     => __('If you think everything is alright. You can broadcast/Schedule the emails now.', 'fluent-crm'),
            'Cam_Looks_lydnbaecy'                                                                    => __('Looks like you did not broadcast any email campaign yet', 'fluent-crm'),
            'Cam_Please_pSLfAT'                                                                      => __('Please provide Subject Lines for A/B Test.', 'fluent-crm'),
            'Cam_Please_peB'                                                                         => __('Please provide email Body.', 'fluent-crm'),
            'Cam_Please_peS'                                                                         => __('Please provide email Subject.', 'fluent-crm'),
            'Cam_Please_peb'                                                                         => __('Please provide email body.', 'fluent-crm'),
            'Cam_Remove_TFS'                                                                         => __('Remove Tags From Subscribers', 'fluent-crm'),
            'Cam_Select_Swcsl'                                                                       => __('Select Subscribers who click selected links', 'fluent-crm'),
            'Cam_Select_Swdnoe'                                                                      => __('Select Subscribers who did not open email', 'fluent-crm'),
            'Cam_Select_Swote'                                                                       => __('Select Subscribers who open the emails', 'fluent-crm'),
            'Cam_Select_Utwc_MfaS'                                                                   => __('Select URLS that where clicked (Will Match for any Selected URLs)', 'fluent-crm'),
            'Cam_Send_Now_Confirm_Header'                                                            => __('Are you sure to proceed?', 'fluent-crm'),
            'Cam_Send_Now_Message'                                                                   => __('Note: The associated contacts in this campaign will receive emails shortly.', 'fluent-crm'),
            'Cam_Send_osce'                                                                          => __('Send or schedule campaign emails', 'fluent-crm'),
            'Cam_Sorry_nlftcc'                                                                       => __('Sorry no links found that contacts clicked', 'fluent-crm'),
            'Cam_Type_cetstolbtsc'                                                                   => __('Type custom email to send test or leave blank to send current user email', 'fluent-crm'),
            'Cam_contacts_hbp'                                                                       => __('contacts has been processed', 'fluent-crm'),
            'Camp_Notice_About_Time'                                                                 => __('Based on your time selection, the emails will be scheduled within that range randomly', 'fluent-crm'),
            'Campaign'                                                                               => __('Campaign', 'fluent-crm'),
            'Campaign Actions'                                                                       => __('Campaign Actions', 'fluent-crm'),
            'Campaign Archive Settings'                                                              => __('Campaign Archive Settings', 'fluent-crm'),
            'Campaign Archives'                                                                      => __('Campaign Archives', 'fluent-crm'),
            'Campaign Archives (Pro)'                                                                => __('Campaign Archives (Pro)', 'fluent-crm'),
            'Campaign Archives on Frontend'                                                          => __('Campaign Archives on Frontend', 'fluent-crm'),
            'Campaign Content'                                                                       => __('Campaign Content', 'fluent-crm'),
            'Campaign Details'                                                                       => __('Campaign Details', 'fluent-crm'),
            'Campaign Link Clicks'                                                                   => __('Campaign Link Clicks', 'fluent-crm'),
            'Campaign Medium (required)'                                                             => __('Campaign Medium (required)', 'fluent-crm'),
            'Campaign Name (required)'                                                               => __('Campaign Name (required)', 'fluent-crm'),
            'Campaign Performance'                                                                   => __('Campaign Performance', 'fluent-crm'),
            'Campaign Recipients'                                                                    => __('Campaign Recipients', 'fluent-crm'),
            'Campaign Search Keyword'                                                                => __('Campaign Search Keyword', 'fluent-crm'),
            'Campaign Settings'                                                                      => __('Campaign Settings', 'fluent-crm'),
            'Campaign Source (required)'                                                             => __('Campaign Source (required)', 'fluent-crm'),
            'Campaign Status:'                                                                       => __('Campaign Status:', 'fluent-crm'),
            'Campaign Term'                                                                          => __('Campaign Term', 'fluent-crm'),
            'Campaign Title'                                                                         => __('Campaign Title', 'fluent-crm'),
            'Campaign deleted.'                                                                      => __('Campaign deleted.', 'fluent-crm'),
            'Campaign status is not in draft status. Please reload the page'                         => __('Campaign status is not in draft status. Please reload the page', 'fluent-crm'),
            'Campaign_Feature_Note'                                                                  => __('Campaign Archive showcase is a pro features. Please upgrade to pro.', 'fluent-crm'),
            'Campaign_Shortcode_Help'                                                                => __('Use the following shortcode in a page/post to show the list of past campaigns', 'fluent-crm'),
            'Campaigns'                                                                              => __('Campaigns', 'fluent-crm'),
            'Can a subscriber manage list subscriptions?'                                            => __('Can a subscriber manage list subscriptions?', 'fluent-crm'),
            'Cancel'                                                                                 => __('Cancel', 'fluent-crm'),
            'Cancel Editing'                                                                         => __('Cancel Editing', 'fluent-crm'),
            'Cancel Schedule'                                                                        => __('Cancel Schedule', 'fluent-crm'),
            'Cancel This email'                                                                      => __('Cancel This email', 'fluent-crm'),
            'Cancel this schedule'                                                                   => __('Cancel this schedule', 'fluent-crm'),
            'Cancelled'                                                                              => __('Cancelled', 'fluent-crm'),
            'Category'                                                                               => __('Category', 'fluent-crm'),
            'Change'                                                                                 => __('Change', 'fluent-crm'),
            'Change (%)'                                                                             => __('Change (%)', 'fluent-crm'),
            'Change Automation Trigger'                                                              => __('Change Automation Trigger', 'fluent-crm'),
            'Change Category'                                                                        => __('Change Category', 'fluent-crm'),
            'Change Company Category'                                                                => __('Change Company Category', 'fluent-crm'),
            'Change Company Type'                                                                    => __('Change Company Type', 'fluent-crm'),
            'Change Contact Status'                                                                  => __('Change Contact Status', 'fluent-crm'),
            'Change Contact Type'                                                                    => __('Change Contact Type', 'fluent-crm'),
            'Change Funnel Status'                                                                   => __('Change Funnel Status', 'fluent-crm'),
            'Change Status'                                                                          => __('Change Status', 'fluent-crm'),
            'Change Type'                                                                            => __('Change Type', 'fluent-crm'),
            'Change User Role to '                                                                   => __('Change User Role to ', 'fluent-crm'),
            'Change preview contact'                                                                 => __('Change preview contact', 'fluent-crm'),
            'Chart Report'                                                                           => __('Chart Report', 'fluent-crm'),
            'Check All'                                                                              => __('Check All', 'fluent-crm'),
            'Check the documentation'                                                                => __('Check the documentation', 'fluent-crm'),
            'Choose New'                                                                             => __('Choose New', 'fluent-crm'),
            'Choose Values'                                                                          => __('Choose Values', 'fluent-crm'),
            'Choose a List'                                                                          => __('Choose a List', 'fluent-crm'),
            'Choose an option:'                                                                      => __('Choose an option:', 'fluent-crm'),
            'City'                                                                                   => __('City', 'fluent-crm'),
            'Clear Filters'                                                                          => __('Clear Filters', 'fluent-crm'),
            'Click'                                                                                  => __('Click', 'fluent-crm'),
            'Click Here to Renew your License'                                                       => __('Click Here to Renew your License', 'fluent-crm'),
            'Click Rate'                                                                             => __('Click Rate', 'fluent-crm'),
            'Click here'                                                                             => __('Click here', 'fluent-crm'),
            'Clicked'                                                                                => __('Clicked', 'fluent-crm'),
            'Clicks'                                                                                 => __('Clicks', 'fluent-crm'),
            'Clone'                                                                                  => __('Clone', 'fluent-crm'),
            'Close'                                                                                  => __('Close', 'fluent-crm'),
            'Close Log'                                                                              => __('Close Log', 'fluent-crm'),
            'Color'                                                                                  => __('Color', 'fluent-crm'),
            'Columns'                                                                                => __('Columns', 'fluent-crm'),
            'Comma Separated (,)'                                                                    => __('Comma Separated (,)', 'fluent-crm'),
            'Commerce Fields'                                                                        => __('Commerce Fields', 'fluent-crm'),
            'Companies'                                                                              => __('Companies', 'fluent-crm'),
            'Companies imported successfully'                                                        => __('Companies imported successfully', 'fluent-crm'),
            'Company'                                                                                => __('Company', 'fluent-crm'),
            'Company / Business'                                                                     => __('Company / Business', 'fluent-crm'),
            'Company Email'                                                                          => __('Company Email', 'fluent-crm'),
            'Company Fields'                                                                         => __('Company Fields', 'fluent-crm'),
            'Company ID:'                                                                            => __('Company ID:', 'fluent-crm'),
            'Company Industry'                                                                       => __('Company Industry', 'fluent-crm'),
            'Company Logo'                                                                           => __('Company Logo', 'fluent-crm'),
            'Company Module'                                                                         => __('Company Module', 'fluent-crm'),
            'Company Module Settings'                                                                => __('Company Module Settings', 'fluent-crm'),
            'Company Name'                                                                           => __('Company Name', 'fluent-crm'),
            'Company Name (required)'                                                                => __('Company Name (required)', 'fluent-crm'),
            'Company Owner'                                                                          => __('Company Owner', 'fluent-crm'),
            'Company Phone Number'                                                                   => __('Company Phone Number', 'fluent-crm'),
            'Company Type'                                                                           => __('Company Type', 'fluent-crm'),
            'Company association removed'                                                            => __('Company association removed', 'fluent-crm'),
            'Company marked as primary'                                                              => __('Company marked as primary', 'fluent-crm'),
            'Company_Logo_Auto_Download_Help'                                                        => __('Try to get company logo automatically from the given website url', 'fluent-crm'),
            'Company_Logo_Auto_Download_Note'                                                        => __('If you enable this, FluentCRM will try to fetch the company logo automatically from the given website url.', 'fluent-crm'),
            'Company_Module_Help'                                                                    => __('With company module, you can manage Companies/Businesses and assign contacts to companies.', 'fluent-crm'),
            'Company_Module_Help_1'                                                                  => __('If you enable this, then company module will be enabled and then you can add companies and assign contacts to companies.', 'fluent-crm'),
            'Compare Date'                                                                           => __('Compare Date', 'fluent-crm'),
            'Complete'                                                                               => __('Complete', 'fluent-crm'),
            'Complete Import'                                                                        => __('Complete Import', 'fluent-crm'),
            'Complete Installation'                                                                  => __('Complete Installation', 'fluent-crm'),
            'Completed'                                                                              => __('Completed', 'fluent-crm'),
            'Compliance'                                                                             => __('Compliance', 'fluent-crm'),
            'Compliance Settings'                                                                    => __('Compliance Settings', 'fluent-crm'),
            'Compose'                                                                                => __('Compose', 'fluent-crm'),
            'Compose Your Email Body'                                                                => __('Compose Your Email Body', 'fluent-crm'),
            'Condition Blocks'                                                                       => __('Condition Blocks', 'fluent-crm'),
            'Condition Value'                                                                        => __('Condition Value', 'fluent-crm'),
            'Condition block is not available'                                                       => __('Condition block is not available', 'fluent-crm'),
            'Condition:'                                                                             => __('Condition:', 'fluent-crm'),
            'Conditional Action'                                                                     => __('Conditional Action', 'fluent-crm'),
            'Conditionals'                                                                           => __('Conditionals', 'fluent-crm'),
            'Conditions'                                                                             => __('Conditions', 'fluent-crm'),
            'Configuration'                                                                          => __('Configuration', 'fluent-crm'),
            'Configure FluentSMTP'                                                                   => __('Configure FluentSMTP', 'fluent-crm'),
            'Confirm'                                                                                => __('Confirm', 'fluent-crm'),
            'Confirm Import'                                                                         => __('Confirm Import', 'fluent-crm'),
            'Congratulations'                                                                        => __('Congratulations', 'fluent-crm'),
            'Connect'                                                                                => __('Connect', 'fluent-crm'),
            'Connect with'                                                                           => __('Connect with', 'fluent-crm'),
            'Connect with your CRM'                                                                  => __('Connect with your CRM', 'fluent-crm'),
            'Contact'                                                                                => __('Contact', 'fluent-crm'),
            'Contact Export Limit'                                                                   => __('Contact Export Limit', 'fluent-crm'),
            'Contact Export Offset'                                                                  => __('Contact Export Offset', 'fluent-crm'),
            'Contact Field'                                                                          => __('Contact Field', 'fluent-crm'),
            'Contact ID:'                                                                            => __('Contact ID:', 'fluent-crm'),
            'Contact Segment Lists'                                                                  => __('Contact Segment Lists', 'fluent-crm'),
            'Contact Selections'                                                                     => __('Contact Selections', 'fluent-crm'),
            'Contact Source'                                                                         => __('Contact Source', 'fluent-crm'),
            'Contact Tags'                                                                           => __('Contact Tags', 'fluent-crm'),
            'Contact Type'                                                                           => __('Contact Type', 'fluent-crm'),
            'Contact can see all lists and manage subscriptions'                                     => __('Contact can see all lists and manage subscriptions', 'fluent-crm'),
            'Contact is not associated with any companies'                                           => __('Contact is not associated with any companies', 'fluent-crm'),
            'Contact only see and manage the following list subscriptions'                           => __('Contact only see and manage the following list subscriptions', 'fluent-crm'),
            'Contact_Delete_Alert'                                                                   => __('Are you sure you want to delete this contact?', 'fluent-crm'),
            'Contact_not_in_Company'                                                                 => __('Contacts doesn\'t exist in the company will be skipped', 'fluent-crm'),
            'Contacts'                                                                               => __('Contacts', 'fluent-crm'),
            'Contacts has been attached with this campaign'                                          => __('Contacts has been attached with this campaign', 'fluent-crm'),
            'Contacts that are invalid'                                                              => __('Contacts that are invalid', 'fluent-crm'),
            'Contains'                                                                               => __('Contains', 'fluent-crm'),
            'Content Background Color'                                                               => __('Content Background Color', 'fluent-crm'),
            'Content Font Family'                                                                    => __('Content Font Family', 'fluent-crm'),
            'Content Max Width (PX)'                                                                 => __('Content Max Width (PX)', 'fluent-crm'),
            'Continue'                                                                               => __('Continue', 'fluent-crm'),
            'Continue [Map Data]'                                                                    => __('Continue [Map Data]', 'fluent-crm'),
            'Continue [Review and Import]'                                                           => __('Continue [Review and Import]', 'fluent-crm'),
            'Continue to next step [conditions]'                                                     => __('Continue to next step [conditions]', 'fluent-crm'),
            'Copied to your clipboard'                                                               => __('Copied to your clipboard', 'fluent-crm'),
            'Country'                                                                                => __('Country', 'fluent-crm'),
            'Counts'                                                                                 => __('Counts', 'fluent-crm'),
            'Cre_Please_pNotS'                                                                       => __('Please provide Name of this Segment', 'fluent-crm'),
            'Create'                                                                                 => __('Create', 'fluent-crm'),
            'Create & Add Another'                                                                   => __('Create & Add Another', 'fluent-crm'),
            'Create Campaign'                                                                        => __('Create Campaign', 'fluent-crm'),
            'Create Company'                                                                         => __('Create Company', 'fluent-crm'),
            'Create Contact'                                                                         => __('Create Contact', 'fluent-crm'),
            'Create Custom Segment'                                                                  => __('Create Custom Segment', 'fluent-crm'),
            'Create Email Template'                                                                  => __('Create Email Template', 'fluent-crm'),
            'Create Form'                                                                            => __('Create Form', 'fluent-crm'),
            'Create New'                                                                             => __('Create New', 'fluent-crm'),
            'Create New Campaign'                                                                    => __('Create New Campaign', 'fluent-crm'),
            'Create New Incoming Webhook'                                                            => __('Create New Incoming Webhook', 'fluent-crm'),
            'Create New Recurring Campaign'                                                          => __('Create New Recurring Campaign', 'fluent-crm'),
            'Create New Sequence'                                                                    => __('Create New Sequence', 'fluent-crm'),
            'Create New Template'                                                                    => __('Create New Template', 'fluent-crm'),
            'Create Recurring Campaign'                                                              => __('Create Recurring Campaign', 'fluent-crm'),
            'Create Segment'                                                                         => __('Create Segment', 'fluent-crm'),
            'Create Smart Link'                                                                      => __('Create Smart Link', 'fluent-crm'),
            'Create Template'                                                                        => __('Create Template', 'fluent-crm'),
            'Create Webhook'                                                                         => __('Create Webhook', 'fluent-crm'),
            'Create Your First Form'                                                                 => __('Create Your First Form', 'fluent-crm'),
            'Create a Form'                                                                          => __('Create a Form', 'fluent-crm'),
            'Create a New Automation'                                                                => __('Create a New Automation', 'fluent-crm'),
            'Create a New Form'                                                                      => __('Create a New Form', 'fluent-crm'),
            'Create a new Smart link'                                                                => __('Create a new Smart link', 'fluent-crm'),
            'Create a note'                                                                          => __('Create a note', 'fluent-crm'),
            'Create a recurring email broadcast'                                                     => __('Create a recurring email broadcast', 'fluent-crm'),
            'Create company'                                                                         => __('Create company', 'fluent-crm'),
            'Create company & Assign'                                                                => __('Create company & Assign', 'fluent-crm'),
            'Create contact & Assign'                                                                => __('Create contact & Assign', 'fluent-crm'),
            'Create new email sequence'                                                              => __('Create new email sequence', 'fluent-crm'),
            'Create or Update contacts from incoming Webhooks'                                       => __('Create or Update contacts from incoming Webhooks', 'fluent-crm'),
            'Create owner as contact if not exist'                                                   => __('Create owner as contact if not exist', 'fluent-crm'),
            'CreateForm.desc'                                                                        => __('Paste the following shortcode to any page or post to start growing your audience', 'fluent-crm'),
            'CreateLink.Action.list_help'                                                            => __('These Lists will be applied to the contact whenever this link is clicked', 'fluent-crm'),
            'CreateLink.Action.tag_help'                                                             => __('These tags will be applied to the contact whenever this link is clicked', 'fluent-crm'),
            'CreateLink.Detach_Action.list_help'                                                     => __('These Tags will be removed to a contact whenever this link is clicked', 'fluent-crm'),
            'CreateLink.Detach_Action.tag_help'                                                      => __('These tags will be removed from the contact whenever this link is clicked', 'fluent-crm'),
            'Created'                                                                                => __('Created', 'fluent-crm'),
            'Created At'                                                                             => __('Created At', 'fluent-crm'),
            'Created at'                                                                             => __('Created at', 'fluent-crm'),
            'Cumulative'                                                                             => __('Cumulative', 'fluent-crm'),
            'Current'                                                                                => __('Current', 'fluent-crm'),
            'Current Date & Time (server):'                                                          => __('Current Date & Time (server):', 'fluent-crm'),
            'Current Status'                                                                         => __('Current Status', 'fluent-crm'),
            'Custom'                                                                                 => __('Custom', 'fluent-crm'),
            'Custom Contact Field'                                                                   => __('Custom Contact Field', 'fluent-crm'),
            'Custom Contact Fields'                                                                  => __('Custom Contact Fields', 'fluent-crm'),
            'Custom Contact Properties'                                                              => __('Custom Contact Properties', 'fluent-crm'),
            'Custom Email Footer Text'                                                               => __('Custom Email Footer Text', 'fluent-crm'),
            'Custom Field Label'                                                                     => __('Custom Field Label', 'fluent-crm'),
            'Custom Field Slug'                                                                      => __('Custom Field Slug', 'fluent-crm'),
            'Custom Fields'                                                                          => __('Custom Fields', 'fluent-crm'),
            'Custom Fields Config'                                                                   => __('Custom Fields Config', 'fluent-crm'),
            'Custom Profile Data'                                                                    => __('Custom Profile Data', 'fluent-crm'),
            'CustomFieldForm.slug.desc'                                                              => __('You can not change the slug once save a custom field', 'fluent-crm'),
            'Customer Since'                                                                         => __('Customer Since', 'fluent-crm'),
            'Customer Summary'                                                                       => __('Customer Summary', 'fluent-crm'),
            'Customize_Email_Footer_Sec'                                                             => __('You can customize your footer in this section. If no custom footer being added here, global footer will be added to this email.', 'fluent-crm'),
            'Daily'                                                                                  => __('Daily', 'fluent-crm'),
            'Danger Zone'                                                                            => __('Danger Zone', 'fluent-crm'),
            'Das_Activate_FFI'                                                                       => __('Activate Fluent Forms Integration', 'fluent-crm'),
            'Das_View_ESSS'                                                                          => __('View Email Sending Service Settings', 'fluent-crm'),
            'Dashboard'                                                                              => __('Dashboard', 'fluent-crm'),
            'Data Cleanup'                                                                           => __('Data Cleanup', 'fluent-crm'),
            'Data Sync is required for'                                                              => __('Data Sync is required for', 'fluent-crm'),
            'Date'                                                                                   => __('Date', 'fluent-crm'),
            'Date & Time Format'                                                                     => __('Date & Time Format', 'fluent-crm'),
            'Date Added'                                                                             => __('Date Added', 'fluent-crm'),
            'Date Time difference (EG: 2 hours ago)'                                                 => __('Date Time difference (EG: 2 hours ago)', 'fluent-crm'),
            'Date of Birth'                                                                          => __('Date of Birth', 'fluent-crm'),
            'Date_And_Time_Format_Label'                                                             => __('Your selected date and time format will be shown on different views.', 'fluent-crm'),
            'Dates'                                                                                  => __('Dates', 'fluent-crm'),
            'Day'                                                                                    => __('Day', 'fluent-crm'),
            'Days'                                                                                   => __('Days', 'fluent-crm'),
            'Days Ago'                                                                               => __('Days Ago', 'fluent-crm'),
            'December'                                                                               => __('December', 'fluent-crm'),
            'Dedicated API and SMTP connections'                                                     => __('Dedicated API and SMTP connections', 'fluent-crm'),
            'Default Companies'                                                                      => __('Default Companies', 'fluent-crm'),
            'Default Content Color'                                                                  => __('Default Content Color', 'fluent-crm'),
            'Default From Settings'                                                                  => __('Default From Settings', 'fluent-crm'),
            'Default Headings Color'                                                                 => __('Default Headings Color', 'fluent-crm'),
            'Default Link Color'                                                                     => __('Default Link Color', 'fluent-crm'),
            'Default List'                                                                           => __('Default List', 'fluent-crm'),
            'Default List to Contact (Optional)'                                                     => __('Default List to Contact (Optional)', 'fluent-crm'),
            'Default Reply to Name (Optional)'                                                       => __('Default Reply to Name (Optional)', 'fluent-crm'),
            'Default Tag ID (optional)'                                                              => __('Default Tag ID (optional)', 'fluent-crm'),
            'Default Tag for Contact (Optional)'                                                     => __('Default Tag for Contact (Optional)', 'fluent-crm'),
            'Default Tags'                                                                           => __('Default Tags', 'fluent-crm'),
            'Default contact status (for new contacts)'                                              => __('Default contact status (for new contacts)', 'fluent-crm'),
            'Default footer has been disabled. Please include'                                       => __('Default footer has been disabled. Please include', 'fluent-crm'),
            'Delay'                                                                                  => __('Delay', 'fluent-crm'),
            'Delete'                                                                                 => __('Delete', 'fluent-crm'),
            'Delete API Key'                                                                         => __('Delete API Key', 'fluent-crm'),
            'Delete Automation'                                                                      => __('Delete Automation', 'fluent-crm'),
            'Delete Campaign'                                                                        => __('Delete Campaign', 'fluent-crm'),
            'Delete Companies'                                                                       => __('Delete Companies', 'fluent-crm'),
            'Delete Contact'                                                                         => __('Delete Contact', 'fluent-crm'),
            'Delete Contacts'                                                                        => __('Delete Contacts', 'fluent-crm'),
            'Delete Funnels'                                                                         => __('Delete Funnels', 'fluent-crm'),
            'Delete Selected'                                                                        => __('Delete Selected', 'fluent-crm'),
            'Delete Selected Contacts'                                                               => __('Delete Selected Contacts', 'fluent-crm'),
            'Delete Sequence'                                                                        => __('Delete Sequence', 'fluent-crm'),
            'Delete Template'                                                                        => __('Delete Template', 'fluent-crm'),
            'Delete Templates'                                                                       => __('Delete Templates', 'fluent-crm'),
            'Delete connected contact when a user get deleted'                                       => __('Delete connected contact when a user get deleted', 'fluent-crm'),
            'Delete older data more than?'                                                           => __('Delete older data more than?', 'fluent-crm'),
            'Delete this group'                                                                      => __('Delete this group', 'fluent-crm'),
            'Delete_Block_Alert'                                                                     => __('Are you sure you want to delete this block?', 'fluent-crm'),
            'Delete_Template_Alert'                                                                  => __('Are you sure you want to delete this template?', 'fluent-crm'),
            'Delivery Date'                                                                          => __('Delivery Date', 'fluent-crm'),
            'Delivery Date & time'                                                                   => __('Delivery Date & time', 'fluent-crm'),
            'Delivery time'                                                                          => __('Delivery time', 'fluent-crm'),
            'Description'                                                                            => __('Description', 'fluent-crm'),
            'Design Your Button'                                                                     => __('Design Your Button', 'fluent-crm'),
            'Detach Tags and Lists'                                                                  => __('Detach Tags and Lists', 'fluent-crm'),
            'Disable Default Email Footer'                                                           => __('Disable Default Email Footer', 'fluent-crm'),
            'Disabled'                                                                               => __('Disabled', 'fluent-crm'),
            'Divi Bloom Integration'                                                                 => __('Divi Bloom Integration', 'fluent-crm'),
            'Divi Themes'                                                                            => __('Divi Themes', 'fluent-crm'),
            'Do Not Trigger Automations (Tag & List related Events)'                                 => __('Do Not Trigger Automations (Tag & List related Events)', 'fluent-crm'),
            'Do another Action'                                                                      => __('Do another Action', 'fluent-crm'),
            'Do you want to update the companies data'                                               => __('Do you want to update the companies data', 'fluent-crm'),
            'Docs.description'                                                                       => __('and we will be happy to answer your questions and assist you with any problems.', 'fluent-crm'),
            'Documentation'                                                                          => __('Documentation', 'fluent-crm'),
            'Does not include (in any)'                                                              => __('Does not include (in any)', 'fluent-crm'),
            'Double Opt-In'                                                                          => __('Double Opt-In', 'fluent-crm'),
            'Double Opt-in Email Settings Details'                                                   => __('Double Opt-in Email Settings Details', 'fluent-crm'),
            'Double Opt-in Settings'                                                                 => __('Double Opt-in Settings', 'fluent-crm'),
            'Download sample file'                                                                   => __('Download sample file', 'fluent-crm'),
            'Draft'                                                                                  => __('Draft', 'fluent-crm'),
            'Draft_Email_Info'                                                                       => __('Draft emails was automatically created from your email configuration. You can now review, edit and
                    schedule and send the campaign.', 'fluent-crm'),
            'Drafts'                                                                                 => __('Drafts', 'fluent-crm'),
            'Drop'                                                                                   => __('Drop', 'fluent-crm'),
            'Drop JSON file here or'                                                                 => __('Drop JSON file here or', 'fluent-crm'),
            'Drop file here or'                                                                      => __('Drop file here or', 'fluent-crm'),
            'Duplicate'                                                                              => __('Duplicate', 'fluent-crm'),
            'Dynamic Segments'                                                                       => __('Dynamic Segments', 'fluent-crm'),
            'DynamicSegmentCampaignPromo.title'                                                      => __('Use your dynamic segments to broadcast Email Campaigns', 'fluent-crm'),
            'ECOMMERCE INTEGRATION:'                                                                 => __('ECOMMERCE INTEGRATION:', 'fluent-crm'),
            'EDD Integration:'                                                                       => __('EDD Integration:', 'fluent-crm'),
            'EG: Tag'                                                                                => __('EG: Tag', 'fluent-crm'),
            'EG: User Type'                                                                          => __('EG: User Type', 'fluent-crm'),
            'Edi_If_ysatrtFstettt'                                                                   => __('If you select a time range then FluentCRM schedule the email to that time range', 'fluent-crm'),
            'Edit'                                                                                   => __('Edit', 'fluent-crm'),
            'Edit Campaign'                                                                          => __('Edit Campaign', 'fluent-crm'),
            'Edit Configuration'                                                                     => __('Edit Configuration', 'fluent-crm'),
            'Edit Connected Automation'                                                              => __('Edit Connected Automation', 'fluent-crm'),
            'Edit Connection'                                                                        => __('Edit Connection', 'fluent-crm'),
            'Edit Email Body'                                                                        => __('Edit Email Body', 'fluent-crm'),
            'Edit Emails'                                                                            => __('Edit Emails', 'fluent-crm'),
            'Edit Form'                                                                              => __('Edit Form', 'fluent-crm'),
            'Edit Funnel'                                                                            => __('Edit Funnel', 'fluent-crm'),
            'Edit Integration Settings'                                                              => __('Edit Integration Settings', 'fluent-crm'),
            'Edit Manager'                                                                           => __('Edit Manager', 'fluent-crm'),
            'Edit Note'                                                                              => __('Edit Note', 'fluent-crm'),
            'Edit Primary Automation Trigger'                                                        => __('Edit Primary Automation Trigger', 'fluent-crm'),
            'Edit Recipients'                                                                        => __('Edit Recipients', 'fluent-crm'),
            'Edit Sequence and Settings'                                                             => __('Edit Sequence and Settings', 'fluent-crm'),
            'Edit Smart link'                                                                        => __('Edit Smart link', 'fluent-crm'),
            'Edit Subject'                                                                           => __('Edit Subject', 'fluent-crm'),
            'Edit Template'                                                                          => __('Edit Template', 'fluent-crm'),
            'Edit The Form'                                                                          => __('Edit The Form', 'fluent-crm'),
            'Editor is loading. Please wait'                                                         => __('Editor is loading. Please wait', 'fluent-crm'),
            'Elementor Page Builder'                                                                 => __('Elementor Page Builder', 'fluent-crm'),
            'Elementor Pro Form Integration'                                                         => __('Elementor Pro Form Integration', 'fluent-crm'),
            'Ema_Add_UPFU'                                                                           => __('Add UTM Parameters For URLs', 'fluent-crm'),
            'Ema_Enable_Atfes'                                                                       => __('Enable A/B testing for email subjects', 'fluent-crm'),
            'Ema_Your_ppwbctpair'                                                                    => __('Your provided priority will be converted to percent and it\'s relative', 'fluent-crm'),
            'Email'                                                                                  => __('Email', 'fluent-crm'),
            'Email Address'                                                                          => __('Email Address', 'fluent-crm'),
            'Email Address for bi-monthly newsletter'                                                => __('Email Address for bi-monthly newsletter', 'fluent-crm'),
            'Email Body'                                                                             => __('Email Body', 'fluent-crm'),
            'Email Campaign'                                                                         => __('Email Campaign', 'fluent-crm'),
            'Email Campaigns'                                                                        => __('Email Campaigns', 'fluent-crm'),
            'Email Click Logs'                                                                       => __('Email Click Logs', 'fluent-crm'),
            'Email Click tracking is disabled via PHP Hook'                                          => __('Email Click tracking is disabled via PHP Hook', 'fluent-crm'),
            'Email Clicks'                                                                           => __('Email Clicks', 'fluent-crm'),
            'Email Configuration'                                                                    => __('Email Configuration', 'fluent-crm'),
            'Email Footer Settings'                                                                  => __('Email Footer Settings', 'fluent-crm'),
            'Email Footer Text'                                                                      => __('Email Footer Text', 'fluent-crm'),
            'Email Footer Type'                                                                      => __('Email Footer Type', 'fluent-crm'),
            'Email From Name'                                                                        => __('Email From Name', 'fluent-crm'),
            'Email History'                                                                          => __('Email History', 'fluent-crm'),
            'Email History Logs'                                                                     => __('Email History Logs', 'fluent-crm'),
            'Email Link Click Stats'                                                                 => __('Email Link Click Stats', 'fluent-crm'),
            'Email Logging for better visibility'                                                    => __('Email Logging for better visibility', 'fluent-crm'),
            'Email Open Logs'                                                                        => __('Email Open Logs', 'fluent-crm'),
            'Email Open Stats'                                                                       => __('Email Open Stats', 'fluent-crm'),
            'Email Open tracking is disabled via PHP Hook'                                           => __('Email Open tracking is disabled via PHP Hook', 'fluent-crm'),
            'Email Pre-Header'                                                                       => __('Email Pre-Header', 'fluent-crm'),
            'Email Preference Settings'                                                              => __('Email Preference Settings', 'fluent-crm'),
            'Email Preview'                                                                          => __('Email Preview', 'fluent-crm'),
            'Email Preview (for better view, please send test email)'                                => __('Email Preview (for better view, please send test email)', 'fluent-crm'),
            'Email Routing based on the sender email address'                                        => __('Email Routing based on the sender email address', 'fluent-crm'),
            'Email Sending Stats'                                                                    => __('Email Sending Stats', 'fluent-crm'),
            'Email Sending will be started soon'                                                     => __('Email Sending will be started soon', 'fluent-crm'),
            'Email Sent'                                                                             => __('Email Sent', 'fluent-crm'),
            'Email Sent %'                                                                           => __('Email Sent %', 'fluent-crm'),
            'Email Sequences'                                                                        => __('Email Sequences', 'fluent-crm'),
            'Email Service Provider Settings'                                                        => __('Email Service Provider Settings', 'fluent-crm'),
            'Email Settings'                                                                         => __('Email Settings', 'fluent-crm'),
            'Email Styling Settings'                                                                 => __('Email Styling Settings', 'fluent-crm'),
            'Email Styling Settings & Footer Settings'                                               => __('Email Styling Settings & Footer Settings', 'fluent-crm'),
            'Email Subject'                                                                          => __('Email Subject', 'fluent-crm'),
            'Email Subject & Details'                                                                => __('Email Subject & Details', 'fluent-crm'),
            'Email Templates'                                                                        => __('Email Templates', 'fluent-crm'),
            'Email field is required'                                                                => __('Email field is required', 'fluent-crm'),
            'Email opened'                                                                           => __('Email opened', 'fluent-crm'),
            'EmailSequencePromo.title'                                                               => __('Send Sequence/Drip emails to your subscribers with Email Sequence Module', 'fluent-crm'),
            'EmailSettings.Preference.desc'                                                          => __('Please specify if you want to let your subscribers manage the associate lists or not', 'fluent-crm'),
            'EmailSettings.replyToEmail.help'                                                        => __('Provide Valid Email Address that will be used in reply to (Optional)', 'fluent-crm'),
            'EmailSettings.unsub_redirect_help'                                                      => __('The provided url will be used to redirect a user when unsubscribe. Leave blank if you do not want a redirect', 'fluent-crm'),
            'EmailSettings.unsub_redirect_label'                                                     => __('Redirect URL after unsubscribe (leave blank for inline message)', 'fluent-crm'),
            'Email_Camp_Enable_Help'                                                                 => __('If you want to showcase your email campaigns in the frontend page with a shortcode then you may enable this features', 'fluent-crm'),
            'Email_Campaign_Insert_Error_Alert'                                                      => __('Sorry you can not insert the selected template type in this email campaign', 'fluent-crm'),
            'Email_Schedule_Info'                                                                    => __('Emails are being scheduled and sending in the background at the same time', 'fluent-crm'),
            'Email_Sequence_Import_Note'                                                             => __('You can import your exported email sequence JSON file here. Please upload your JSON file', 'fluent-crm'),
            'Emails'                                                                                 => __('Emails', 'fluent-crm'),
            'Emails Analytics'                                                                       => __('Emails Analytics', 'fluent-crm'),
            'Emails Stats'                                                                           => __('Emails Stats', 'fluent-crm'),
            'Emails are currently on processing'                                                     => __('Emails are currently on processing', 'fluent-crm'),
            'Emails will be sent now.'                                                               => __('Emails will be sent now.', 'fluent-crm'),
            'Emails_Will_Sent_Auto'                                                                  => __('Emails will be sent automatically regardless of any conditions. If you want to send conditionally', 'fluent-crm'),
            'Empty'                                                                                  => __('Empty', 'fluent-crm'),
            'Enable Campaign Archive Frontend Feature'                                               => __('Enable Campaign Archive Frontend Feature', 'fluent-crm'),
            'Enable Company Module for Contacts'                                                     => __('Enable Company Module for Contacts', 'fluent-crm'),
            'Enable Double-Optin Email Confirmation'                                                 => __('Enable Double-Optin Email Confirmation', 'fluent-crm'),
            'Enable Preference Form Shortcode'                                                       => __('Enable Preference Form Shortcode', 'fluent-crm'),
            'Enable Quick Contact Navigation'                                                        => __('Enable Quick Contact Navigation', 'fluent-crm'),
            'Enable Specific Days Only'                                                              => __('Enable Specific Days Only', 'fluent-crm'),
            'Enabled'                                                                                => __('Enabled', 'fluent-crm'),
            'End Range'                                                                              => __('End Range', 'fluent-crm'),
            'End date'                                                                               => __('End date', 'fluent-crm'),
            'Ends With'                                                                              => __('Ends With', 'fluent-crm'),
            'Equal'                                                                                  => __('Equal', 'fluent-crm'),
            'Error'                                                                                  => __('Error', 'fluent-crm'),
            'Error Details'                                                                          => __('Error Details', 'fluent-crm'),
            'Errors'                                                                                 => __('Errors', 'fluent-crm'),
            'Errors Found'                                                                           => __('Errors Found', 'fluent-crm'),
            'Estimated Contacts:'                                                                    => __('Estimated Contacts:', 'fluent-crm'),
            'Event Name'                                                                             => __('Event Name', 'fluent-crm'),
            'Event_Tracking_Module_Help'                                                             => __('Event Tracking is a flexible feature that helps you collect data on a wide variety of contact behavior. You can create an event for any activity programmatically or from different automations. Then you can use those events to filter contacts or use on automation conditional logics.', 'fluent-crm'),
            'Every Friday'                                                                           => __('Every Friday', 'fluent-crm'),
            'Every Monday'                                                                           => __('Every Monday', 'fluent-crm'),
            'Every Saturday'                                                                         => __('Every Saturday', 'fluent-crm'),
            'Every Sunday'                                                                           => __('Every Sunday', 'fluent-crm'),
            'Every Thursday'                                                                         => __('Every Thursday', 'fluent-crm'),
            'Every Tuesday'                                                                          => __('Every Tuesday', 'fluent-crm'),
            'Every Wednesday'                                                                        => __('Every Wednesday', 'fluent-crm'),
            'Everything is ready.'                                                                   => __('Everything is ready.', 'fluent-crm'),
            'Example:'                                                                               => __('Example:', 'fluent-crm'),
            'Excluded Contacts'                                                                      => __('Excluded Contacts', 'fluent-crm'),
            'Exp_Leave_tbfnloo'                                                                      => __('Leave these blank for no limit or offset', 'fluent-crm'),
            'Exp_Please_sctywte'                                                                     => __('Please select columns that you want to export', 'fluent-crm'),
            'Experimental Features'                                                                  => __('Experimental Features', 'fluent-crm'),
            'Experimental Features Settings'                                                         => __('Experimental Features Settings', 'fluent-crm'),
            'Experimental Settings'                                                                  => __('Experimental Settings', 'fluent-crm'),
            'Export'                                                                                 => __('Export', 'fluent-crm'),
            'Export Contacts'                                                                        => __('Export Contacts', 'fluent-crm'),
            'Export Subscriber'                                                                      => __('Export Subscriber', 'fluent-crm'),
            'Export Template'                                                                        => __('Export Template', 'fluent-crm'),
            'Exporting... Please wait'                                                               => __('Exporting... Please wait', 'fluent-crm'),
            'Facebook Company Page URL'                                                              => __('Facebook Company Page URL', 'fluent-crm'),
            'Facebook Page Url'                                                                      => __('Facebook Page Url', 'fluent-crm'),
            'Features of Fluent SMTP Plugin'                                                         => __('Features of Fluent SMTP Plugin', 'fluent-crm'),
            'Features_Enable_Or_Disable'                                                             => __('This section contains all the advanced features that you can enable or disable.', 'fluent-crm'),
            'February'                                                                               => __('February', 'fluent-crm'),
            'Feel free to add a note regarding this URL'                                             => __('Feel free to add a note regarding this URL', 'fluent-crm'),
            'Fetching License Information Please wait'                                               => __('Fetching License Information Please wait', 'fluent-crm'),
            'Field'                                                                                  => __('Field', 'fluent-crm'),
            'Field Type'                                                                             => __('Field Type', 'fluent-crm'),
            'Field Value Options'                                                                    => __('Field Value Options', 'fluent-crm'),
            'Filter'                                                                                 => __('Filter', 'fluent-crm'),
            'Filter By Property and find appropriate Contacts'                                       => __('Filter By Property and find appropriate Contacts', 'fluent-crm'),
            'Filter Subscribers'                                                                     => __('Filter Subscribers', 'fluent-crm'),
            'Filter by status'                                                                       => __('Filter by status', 'fluent-crm'),
            'Filtered by'                                                                            => __('Filtered by', 'fluent-crm'),
            'Filters.instruction'                                                                    => __('Add new filter to narrow down your contacts based on different properties', 'fluent-crm'),
            'First Name'                                                                             => __('First Name', 'fluent-crm'),
            'Fluent Forms Integration'                                                               => __('Fluent Forms Integration', 'fluent-crm'),
            'FluentCRM Field'                                                                        => __('FluentCRM Field', 'fluent-crm'),
            'FluentCRM_License.desc'                                                                 => __('Please Provide a license key of FluentCRM - Email Marketing Addon', 'fluent-crm'),
            'FluentCrm_Welcome_desc'                                                                 => __('It’s completely optional and shouldn’t take longer than two minutes.', 'fluent-crm'),
            'FluentSMTP'                                                                             => __('FluentSMTP', 'fluent-crm'),
            'FluentSMTP plugin has successfully installed'                                           => __('FluentSMTP plugin has successfully installed', 'fluent-crm'),
            'Fluent_CRM_Exp'                                                                         => __('Adds the new FluentCRM navigation experience to the dashboard', 'fluent-crm'),
            'Fluent_CRM_Nav_Hide'                                                                    => __('If you enable this, then FluentCRM will hide the default WP Admin Sidebar and Replace with FluentCRM\'s own navigation for better focus.', 'fluent-crm'),
            'Fluent_CRM_Pro_Alert'                                                                   => __('Get more related contact info with FluentCRM Pro. You can get more info about the contact like their purchase history, membership, course info and more.', 'fluent-crm'),
            'Fluentcrm_Pref_Help'                                                                    => __('Please use the shortcode [fluentcrm_pref] to show the form for your subscribers', 'fluent-crm'),
            'Font Family'                                                                            => __('Font Family', 'fluent-crm'),
            'Font Size'                                                                              => __('Font Size', 'fluent-crm'),
            'Font Style'                                                                             => __('Font Style', 'fluent-crm'),
            'Footer Text Color'                                                                      => __('Footer Text Color', 'fluent-crm'),
            'For Step by Step instruction please'                                                    => __('For Step by Step instruction please', 'fluent-crm'),
            'For_Fluent_FtacwyC'                                                                     => __('Fluent Forms that are connected with your CRM', 'fluent-crm'),
            'For_Grow_YAbOF'                                                                         => __('Grow Your Audience by Opt-in Forms', 'fluent-crm'),
            'For_Internal_S_'                                                                        => __('Internal Subtitle (Optional)', 'fluent-crm'),
            'For_Looks_Lydncafy'                                                                     => __('Looks Like you did not create any forms yet!', 'fluent-crm'),
            'Form INTEGRATION:'                                                                      => __('Form INTEGRATION:', 'fluent-crm'),
            'Form Submissions from'                                                                  => __('Form Submissions from', 'fluent-crm'),
            'Form Title'                                                                             => __('Form Title', 'fluent-crm'),
            'Forms'                                                                                  => __('Forms', 'fluent-crm'),
            'Forms.desc'                                                                             => __('Fluent Forms that are connected with your CRM', 'fluent-crm'),
            'Forms.if_need_desc'                                                                     => __('If you need to create and connect more advanced forms please use Fluent Forms.', 'fluent-crm'),
            'Friday'                                                                                 => __('Friday', 'fluent-crm'),
            'Friendly Name for identification'                                                       => __('Friendly Name for identification', 'fluent-crm'),
            'From Email'                                                                             => __('From Email', 'fluent-crm'),
            'From Email Address'                                                                     => __('From Email Address', 'fluent-crm'),
            'From Name'                                                                              => __('From Name', 'fluent-crm'),
            'Fun_Total_Rftf'                                                                         => __('Total Revenue from this funnel', 'fluent-crm'),
            'Fun_Waiting_fdoc'                                                                       => __('Waiting for double opt-in confirmation', 'fluent-crm'),
            'Funnel Builder will be available soon'                                                  => __('Funnel Builder will be available soon', 'fluent-crm'),
            'Funnel Items'                                                                           => __('Funnel Items', 'fluent-crm'),
            'Funnel Name'                                                                            => __('Funnel Name', 'fluent-crm'),
            'Funnel Report'                                                                          => __('Funnel Report', 'fluent-crm'),
            'Funnel_Conditional_Action_desc'                                                         => __('Add Conditional Action, apply rules and split your automation funnel', 'fluent-crm'),
            'Funnel_Form_Close_Header'                                                               => __('Funnel data changed', 'fluent-crm'),
            'Funnel_Form_Close_Message'                                                              => __('Do you want to skip the data change?', 'fluent-crm'),
            'Funnel_Step_Alert'                                                                      => __('Add newly added steps to previously completed contacts.', 'fluent-crm'),
            'General Settings'                                                                       => __('General Settings', 'fluent-crm'),
            'Get FluentCRM Email Pro'                                                                => __('Get FluentCRM Email Pro', 'fluent-crm'),
            'Get FluentCRM Pro'                                                                      => __('Get FluentCRM Pro', 'fluent-crm'),
            'Get FluentCRM Pro Now'                                                                  => __('Get FluentCRM Pro Now', 'fluent-crm'),
            'Get Pre-Defined Contact Lists'                                                          => __('Get Pre-Defined Contact Lists', 'fluent-crm'),
            'Get more related contact info with Pro'                                                 => __('Get more related contact info with Pro', 'fluent-crm'),
            'Get_Advance_Report_Desc'                                                                => __('Advanced Report is available on Premium Version of FluentCRM along wth lots of integrations, advanced automation, sequence emails and more', 'fluent-crm'),
            'Get_Advance_Report_Title'                                                               => __('Get Advanced Report of your contacts, emails, clicks and revenue', 'fluent-crm'),
            'Get_Improved_Help'                                                                      => __('Get improved features and faster fixes by sharing non-sensitive data via usage tracking that shows us
                how FluentCRM is used. No personal data is tracked or stored.', 'fluent-crm'),
            'Getting Started'                                                                        => __('Getting Started', 'fluent-crm'),
            'Getting Started With Audience'                                                          => __('Getting Started With Audience', 'fluent-crm'),
            'Global Email Settings'                                                                  => __('Global Email Settings', 'fluent-crm'),
            'Go Back'                                                                                => __('Go Back', 'fluent-crm'),
            'Go to CRM Dashboard'                                                                    => __('Go to CRM Dashboard', 'fluent-crm'),
            'Go to company record'                                                                   => __('Go to company record', 'fluent-crm'),
            'Goals'                                                                                  => __('Goals', 'fluent-crm'),
            'Goals / Benchmark'                                                                      => __('Goals / Benchmark', 'fluent-crm'),
            'Goto FluentSMTP Settings'                                                               => __('Goto FluentSMTP Settings', 'fluent-crm'),
            'Great!'                                                                                 => __('Great!', 'fluent-crm'),
            'Greater Than'                                                                           => __('Greater Than', 'fluent-crm'),
            'Grow Your Audience'                                                                     => __('Grow Your Audience', 'fluent-crm'),
            'Gut_Suggesting_vB6t8'                                                                   => __('Suggesting value: Between 600 to 800', 'fluent-crm'),
            'Gutenberg / WordPress Editor Conditional Blocks'                                        => __('Gutenberg / WordPress Editor Conditional Blocks', 'fluent-crm'),
            'Have a new license Key?'                                                                => __('Have a new license Key?', 'fluent-crm'),
            'Headings Font Family'                                                                   => __('Headings Font Family', 'fluent-crm'),
            'Help us to make FluentCRM better'                                                       => __('Help us to make FluentCRM better', 'fluent-crm'),
            'Home.Default_tag_id.instruction'                                                        => __('Will be applied to the contacts who does not have any mapped tags in', 'fluent-crm'),
            'Hours'                                                                                  => __('Hours', 'fluent-crm'),
            'How can we help you?'                                                                   => __('How can we help you?', 'fluent-crm'),
            'How often you want to send this email?'                                                 => __('How often you want to send this email?', 'fluent-crm'),
            'ID'                                                                                     => __('ID', 'fluent-crm'),
            'IP Address'                                                                             => __('IP Address', 'fluent-crm'),
            'Identifiers'                                                                            => __('Identifiers', 'fluent-crm'),
            'Identify the paid keywords'                                                             => __('Identify the paid keywords', 'fluent-crm'),
            'If you use'                                                                             => __('If you use', 'fluent-crm'),
            'If you want to disable auto-syncing'                                                    => __('If you want to disable auto-syncing', 'fluent-crm'),
            'Immediately'                                                                            => __('Immediately', 'fluent-crm'),
            'Import'                                                                                 => __('Import', 'fluent-crm'),
            'Import Automation Funnel'                                                               => __('Import Automation Funnel', 'fluent-crm'),
            'Import CSV'                                                                             => __('Import CSV', 'fluent-crm'),
            'Import Companies'                                                                       => __('Import Companies', 'fluent-crm'),
            'Import Contacts'                                                                        => __('Import Contacts', 'fluent-crm'),
            'Import From Other Providers'                                                            => __('Import From Other Providers', 'fluent-crm'),
            'Import Funnel From JSON File'                                                           => __('Import Funnel From JSON File', 'fluent-crm'),
            'Import Subscribers'                                                                     => __('Import Subscribers', 'fluent-crm'),
            'Import Template'                                                                        => __('Import Template', 'fluent-crm'),
            'Import Users Now'                                                                       => __('Import Users Now', 'fluent-crm'),
            'Import has been completed, You may close this modal now'                                => __('Import has been completed, You may close this modal now', 'fluent-crm'),
            'Import only active subscribers from'                                                    => __('Import only active subscribers from', 'fluent-crm'),
            'Import_Dynamic_Segment'                                                                 => __('Import your exported dynamic segment JSON file here. Please upload your JSON file.', 'fluent-crm'),
            'Import_Email_Template'                                                                  => __('Import your exported email template JSON file here. Please upload your JSON file.', 'fluent-crm'),
            'Importing now...'                                                                       => __('Importing now...', 'fluent-crm'),
            'Importing_Error_message'                                                                => __('Something is wrong when importing. Please check the error message below', 'fluent-crm'),
            'In'                                                                                     => __('In', 'fluent-crm'),
            'In Details Reporting'                                                                   => __('In Details Reporting', 'fluent-crm'),
            'Include Contact Info in Personal Data export by WP'                                     => __('Include Contact Info in Personal Data export by WP', 'fluent-crm'),
            'Included Contacts'                                                                      => __('Included Contacts', 'fluent-crm'),
            'Includes none of (match all)'                                                           => __('Includes none of (match all)', 'fluent-crm'),
            'Incoming Webhook Settings'                                                              => __('Incoming Webhook Settings', 'fluent-crm'),
            'Incoming Webhooks'                                                                      => __('Incoming Webhooks', 'fluent-crm'),
            'Incomplete'                                                                             => __('Incomplete', 'fluent-crm'),
            'Individual Reporting'                                                                   => __('Individual Reporting', 'fluent-crm'),
            'Industry'                                                                               => __('Industry', 'fluent-crm'),
            'Info'                                                                                   => __('Info', 'fluent-crm'),
            'Insert'                                                                                 => __('Insert', 'fluent-crm'),
            'Insert a valid url that this link will direct to'                                       => __('Insert a valid url that this link will direct to', 'fluent-crm'),
            'Installed'                                                                              => __('Installed', 'fluent-crm'),
            'Integration Settings'                                                                   => __('Integration Settings', 'fluent-crm'),
            'IntegrationSettings.data_sync_completed'                                                => __('Data syncing has been completed. Please reload this page now', 'fluent-crm'),
            'IntegrationSettings.importing_desc'                                                     => __('Please do not close this window or navigate to other page', 'fluent-crm'),
            'IntegrationSettings.send_double_optin'                                                  => __('A double opt-in email will be sent automatically', 'fluent-crm'),
            'IntegrationSettings.sync_desc'                                                          => __('Syncing may take few seconds to minutes based on your customers size in', 'fluent-crm'),
            'IntegrationSettings.woo_desc'                                                           => __('Collect and segment email contacts from wooCommerce checkout and send emails. Run funnels, segment your customers into FluentCRM', 'fluent-crm'),
            'Integrations'                                                                           => __('Integrations', 'fluent-crm'),
            'Internal Campaign Title'                                                                => __('Internal Campaign Title', 'fluent-crm'),
            'Internal Description'                                                                   => __('Internal Description', 'fluent-crm'),
            'Internal Label'                                                                         => __('Internal Label', 'fluent-crm'),
            'Internal Subtitle'                                                                      => __('Internal Subtitle', 'fluent-crm'),
            'Internal Title'                                                                         => __('Internal Title', 'fluent-crm'),
            'Invalid Emails:'                                                                        => __('Invalid Emails:', 'fluent-crm'),
            'Invalid schedule date'                                                                  => __('Invalid schedule date', 'fluent-crm'),
            'Invalid schedule date range'                                                            => __('Invalid schedule date range', 'fluent-crm'),
            'Invalid_Link_Detected'                                                                  => __('Invalid links detected. Check the following button / links', 'fluent-crm'),
            'January'                                                                                => __('January', 'fluent-crm'),
            'July'                                                                                   => __('July', 'fluent-crm'),
            'June'                                                                                   => __('June', 'fluent-crm'),
            'Key'                                                                                    => __('Key', 'fluent-crm'),
            'LMS INTEGRATION:'                                                                       => __('LMS INTEGRATION:', 'fluent-crm'),
            'Label'                                                                                  => __('Label', 'fluent-crm'),
            'Last 3 months'                                                                          => __('Last 3 months', 'fluent-crm'),
            'Last Activity'                                                                          => __('Last Activity', 'fluent-crm'),
            'Last Change Date'                                                                       => __('Last Change Date', 'fluent-crm'),
            'Last Changed'                                                                           => __('Last Changed', 'fluent-crm'),
            'Last Executed At'                                                                       => __('Last Executed At', 'fluent-crm'),
            'Last Name'                                                                              => __('Last Name', 'fluent-crm'),
            'Last Order Date'                                                                        => __('Last Order Date', 'fluent-crm'),
            'Last Purchase Date'                                                                     => __('Last Purchase Date', 'fluent-crm'),
            'Last month'                                                                             => __('Last month', 'fluent-crm'),
            'Last order'                                                                             => __('Last order', 'fluent-crm'),
            'Last quarter'                                                                           => __('Last quarter', 'fluent-crm'),
            'Last week'                                                                              => __('Last week', 'fluent-crm'),
            'Latest Action'                                                                          => __('Latest Action', 'fluent-crm'),
            'Launch Visual Editor'                                                                   => __('Launch Visual Editor', 'fluent-crm'),
            'Learn More'                                                                             => __('Learn More', 'fluent-crm'),
            'Learn more about FluentSMTP Plugin.'                                                    => __('Learn more about FluentSMTP Plugin.', 'fluent-crm'),
            'LearnDash Integration:'                                                                 => __('LearnDash Integration:', 'fluent-crm'),
            'Less Than'                                                                              => __('Less Than', 'fluent-crm'),
            'Let\'s Go'                                                                              => __('Let\'s Go', 'fluent-crm'),
            'License Management'                                                                     => __('License Management', 'fluent-crm'),
            'Lifetime Value'                                                                         => __('Lifetime Value', 'fluent-crm'),
            'LifterLMS:'                                                                             => __('LifterLMS:', 'fluent-crm'),
            'Lin_SmartLinks_aytta'                                                                   => __('SmartLinks allow you to tag and segment your subscribers when they click a link in an email, on your site or anywhere!', 'fluent-crm'),
            'Lin_This_iapfPdtFPta'                                                                   => __('This is a premium feature. Please download the FluentCRM Pro to activate this feature.', 'fluent-crm'),
            'Line'                                                                                   => __('Line', 'fluent-crm'),
            'Line Height'                                                                            => __('Line Height', 'fluent-crm'),
            'Link Metrics'                                                                           => __('Link Metrics', 'fluent-crm'),
            'Link Title'                                                                             => __('Link Title', 'fluent-crm'),
            'Link activity'                                                                          => __('Link activity', 'fluent-crm'),
            'LinkedIn Company Page URL'                                                              => __('LinkedIn Company Page URL', 'fluent-crm'),
            'Linkedin Url'                                                                           => __('Linkedin Url', 'fluent-crm'),
            'Lis_List_acoycYcalaa'                                                                   => __('List are categories of your contacts. You can add lists and assign contacts to your list for better segmentation', 'fluent-crm'),
            'List'                                                                                   => __('List', 'fluent-crm'),
            'List_Campaigns_Help'                                                                    => __('Leave blank if you do not want to filter campaigns', 'fluent-crm'),
            'List_Campaigns_Label'                                                                   => __('List the campaigns if the title match the provided keyword', 'fluent-crm'),
            'Lists'                                                                                  => __('Lists', 'fluent-crm'),
            'Loading'                                                                                => __('Loading', 'fluent-crm'),
            'Loading Campaign Data...'                                                               => __('Loading Campaign Data...', 'fluent-crm'),
            'Loading Editor...'                                                                      => __('Loading Editor...', 'fluent-crm'),
            'Loading Preview. Please wait...'                                                        => __('Loading Preview. Please wait...', 'fluent-crm'),
            'Loading Settings...'                                                                    => __('Loading Settings...', 'fluent-crm'),
            'Loading...'                                                                             => __('Loading...', 'fluent-crm'),
            'Loading.....'                                                                           => __('Loading.....', 'fluent-crm'),
            'Logo'                                                                                   => __('Logo', 'fluent-crm'),
            'Looks like there had a problem to sync the data'                                        => __('Looks like there had a problem to sync the data', 'fluent-crm'),
            'Looks like your license has been expired'                                               => __('Looks like your license has been expired', 'fluent-crm'),
            'MEMBERSHIP INTEGRATION:'                                                                => __('MEMBERSHIP INTEGRATION:', 'fluent-crm'),
            'Main Contact Properties'                                                                => __('Main Contact Properties', 'fluent-crm'),
            'Manage APIs'                                                                            => __('Manage APIs', 'fluent-crm'),
            'Managers'                                                                               => __('Managers', 'fluent-crm'),
            'Managers.user_email.info'                                                               => __('Please Provide Email address of your existing system user', 'fluent-crm'),
            'Map Contact Fields'                                                                     => __('Map Contact Fields', 'fluent-crm'),
            'Map Data'                                                                               => __('Map Data', 'fluent-crm'),
            'Map List'                                                                               => __('Map List', 'fluent-crm'),
            'Map Tags'                                                                               => __('Map Tags', 'fluent-crm'),
            'Map the data'                                                                           => __('Map the data', 'fluent-crm'),
            'Map your Data'                                                                          => __('Map your Data', 'fluent-crm'),
            'Map_Completed_Ycctmn'                                                                   => __('Completed. You can close this modal now', 'fluent-crm'),
            'Map_Contacts_tad'                                                                       => __('Contacts that are duplicate', 'fluent-crm'),
            'Map_Importing_CfyCPD'                                                                   => __('Importing Contacts from your CSV. Please Do not close this modal', 'fluent-crm'),
            'Map_Status_ftns'                                                                        => __('Status for the new subscribers', 'fluent-crm'),
            'Map_Subscribers_is'                                                                     => __('Subscribers imported successfully.', 'fluent-crm'),
            'Map_Total_S_I'                                                                          => __('Total Skipped (Including Invalid):', 'fluent-crm'),
            'Mapper.title'                                                                           => __('Map CSV Fields with Contact Property', 'fluent-crm'),
            'Mapper.title_desc'                                                                      => __('Please map the csv headers with the respective subscriber fields.', 'fluent-crm'),
            'March'                                                                                  => __('March', 'fluent-crm'),
            'Mark as Primary'                                                                        => __('Mark as Primary', 'fluent-crm'),
            'Marketing medium: (e.g. cpc, banner, email)'                                            => __('Marketing medium: (e.g. cpc, banner, email)', 'fluent-crm'),
            'Match Type'                                                                             => __('Match Type', 'fluent-crm'),
            'Matching '                                                                              => __('Matching ', 'fluent-crm'),
            'Max Campaigns to list (max 50)'                                                         => __('Max Campaigns to list (max 50)', 'fluent-crm'),
            'Maximum Email Limit Per Second'                                                         => __('Maximum Email Limit Per Second', 'fluent-crm'),
            'May'                                                                                    => __('May', 'fluent-crm'),
            'MemberPress.'                                                                           => __('MemberPress.', 'fluent-crm'),
            'Migrating'                                                                              => __('Migrating', 'fluent-crm'),
            'Minutes'                                                                                => __('Minutes', 'fluent-crm'),
            'Monday'                                                                                 => __('Monday', 'fluent-crm'),
            'Month'                                                                                  => __('Month', 'fluent-crm'),
            'Monthly'                                                                                => __('Monthly', 'fluent-crm'),
            'Months'                                                                                 => __('Months', 'fluent-crm'),
            'MyAwesomeBusiness'                                                                      => __('MyAwesomeBusiness', 'fluent-crm'),
            'MyAwesomeBusiness Inc.'                                                                 => __('MyAwesomeBusiness Inc.', 'fluent-crm'),
            'Name'                                                                                   => __('Name', 'fluent-crm'),
            'Name Prefix'                                                                            => __('Name Prefix', 'fluent-crm'),
            'Name of this key'                                                                       => __('Name of this key', 'fluent-crm'),
            'Name that will be used to send emails'                                                  => __('Name that will be used to send emails', 'fluent-crm'),
            'Name this Custom Segment'                                                               => __('Name this Custom Segment', 'fluent-crm'),
            'Navigation'                                                                             => __('Navigation', 'fluent-crm'),
            'Never-mind, I changed my mind'                                                          => __('Never-mind, I changed my mind', 'fluent-crm'),
            'New Dynamic Segment'                                                                    => __('New Dynamic Segment', 'fluent-crm'),
            'New Subscriber Status'                                                                  => __('New Subscriber Status', 'fluent-crm'),
            'Next'                                                                                   => __('Next', 'fluent-crm'),
            'Next Email'                                                                             => __('Next Email', 'fluent-crm'),
            'Next Run'                                                                               => __('Next Run', 'fluent-crm'),
            'Next Step'                                                                              => __('Next Step', 'fluent-crm'),
            'Next [Map Columns]'                                                                     => __('Next [Map Columns]', 'fluent-crm'),
            'Next [Review Data]'                                                                     => __('Next [Review Data]', 'fluent-crm'),
            'Next step'                                                                              => __('Next step', 'fluent-crm'),
            'No'                                                                                     => __('No', 'fluent-crm'),
            'No Companies Found'                                                                     => __('No Companies Found', 'fluent-crm'),
            'No Comparison'                                                                          => __('No Comparison', 'fluent-crm'),
            'No Contacts Found'                                                                      => __('No Contacts Found', 'fluent-crm'),
            'No Data Available'                                                                      => __('No Data Available', 'fluent-crm'),
            'No Data Found'                                                                          => __('No Data Found', 'fluent-crm'),
            'No Form Found'                                                                          => __('No Form Found', 'fluent-crm'),
            'No Subscriber Found'                                                                    => __('No Subscriber Found', 'fluent-crm'),
            'No Thanks'                                                                              => __('No Thanks', 'fluent-crm'),
            'No changes found'                                                                       => __('No changes found', 'fluent-crm'),
            'No companies found based on your search'                                                => __('No companies found based on your search', 'fluent-crm'),
            'No contacts found based on your search'                                                 => __('No contacts found based on your search', 'fluent-crm'),
            'No items found'                                                                         => __('No items found', 'fluent-crm'),
            'No link activity recorded yet'                                                          => __('No link activity recorded yet', 'fluent-crm'),
            'No lists found'                                                                         => __('No lists found', 'fluent-crm'),
            'No mapping found.'                                                                      => __('No mapping found.', 'fluent-crm'),
            'No subscriber found to send test. Please add at least one contact as subscribed status' => __('No subscriber found to send test. Please add at least one contact as subscribed status', 'fluent-crm'),
            'No tags found'                                                                          => __('No tags found', 'fluent-crm'),
            'No, Contact can not manage list subscriptions'                                          => __('No, Contact can not manage list subscriptions', 'fluent-crm'),
            'No_Additional_Info_Alert'                                                               => __('No additional information available for this contact. Related data like purchase history, membership, course info will be shown.', 'fluent-crm'),
            'Not Contains'                                                                           => __('Not Contains', 'fluent-crm'),
            'Not Empty'                                                                              => __('Not Empty', 'fluent-crm'),
            'Not Equal'                                                                              => __('Not Equal', 'fluent-crm'),
            'Not Right Now'                                                                          => __('Not Right Now', 'fluent-crm'),
            'Not Scheduled'                                                                          => __('Not Scheduled', 'fluent-crm'),
            'Not_Import_Dynamic_Segments_Alert'                                                      => __('Do not import segment from untrusted sources.', 'fluent-crm'),
            'Not_Import_Templates_Alert'                                                             => __('Do not import templates from untrusted sources.', 'fluent-crm'),
            'Note'                                                                                   => __('Note', 'fluent-crm'),
            'Note (Optional)'                                                                        => __('Note (Optional)', 'fluent-crm'),
            'Notes'                                                                                  => __('Notes', 'fluent-crm'),
            'Notes & Activities'                                                                     => __('Notes & Activities', 'fluent-crm'),
            'Notes export feature is only available on pro version'                                  => __('Notes export feature is only available on pro version', 'fluent-crm'),
            'November'                                                                               => __('November', 'fluent-crm'),
            'Number of Employees'                                                                    => __('Number of Employees', 'fluent-crm'),
            'OR'                                                                                     => __('OR', 'fluent-crm'),
            'October'                                                                                => __('October', 'fluent-crm'),
            'Oops!'                                                                                  => __('Oops!', 'fluent-crm'),
            'Open & Clicks'                                                                          => __('Open & Clicks', 'fluent-crm'),
            'Open Count'                                                                             => __('Open Count', 'fluent-crm'),
            'Open Rate'                                                                              => __('Open Rate', 'fluent-crm'),
            'Open_Rate_Info'                                                                         => __('Open rate is estimated based on pixel loads. it may not show the correct analytics as many email clients load or block tracking regardless of the actual email open', 'fluent-crm'),
            'Opened'                                                                                 => __('Opened', 'fluent-crm'),
            'Opens'                                                                                  => __('Opens', 'fluent-crm'),
            'Operator'                                                                               => __('Operator', 'fluent-crm'),
            'Optimized API connection with Mail Service Providers'                                   => __('Optimized API connection with Mail Service Providers', 'fluent-crm'),
            'Order Count'                                                                            => __('Order Count', 'fluent-crm'),
            'Overall Conversion Rate'                                                                => __('Overall Conversion Rate', 'fluent-crm'),
            'Oxygen Builder Integration'                                                             => __('Oxygen Builder Integration', 'fluent-crm'),
            'Page'                                                                                   => __('Page', 'fluent-crm'),
            'Page Builder INTEGRATIONS:'                                                             => __('Page Builder INTEGRATIONS:', 'fluent-crm'),
            'Paid Membership Pro integration.'                                                       => __('Paid Membership Pro integration.', 'fluent-crm'),
            'Path: '                                                                                 => __('Path: ', 'fluent-crm'),
            'Pause Sending'                                                                          => __('Pause Sending', 'fluent-crm'),
            'Pending'                                                                                => __('Pending', 'fluent-crm'),
            'Permissions'                                                                            => __('Permissions', 'fluent-crm'),
            'Phone'                                                                                  => __('Phone', 'fluent-crm'),
            'Phone Number'                                                                           => __('Phone Number', 'fluent-crm'),
            'Phone/Mobile'                                                                           => __('Phone/Mobile', 'fluent-crm'),
            'Pick a date'                                                                            => __('Pick a date', 'fluent-crm'),
            'Pick a date and time'                                                                   => __('Pick a date and time', 'fluent-crm'),
            'Please'                                                                                 => __('Please', 'fluent-crm'),
            'Please Field Option Values'                                                             => __('Please Field Option Values', 'fluent-crm'),
            'Please Provide Text only (HTML will not support)'                                       => __('Please Provide Text only (HTML will not support)', 'fluent-crm'),
            'Please Provide a Form Title'                                                            => __('Please Provide a Form Title', 'fluent-crm'),
            'Please Provide label'                                                                   => __('Please Provide label', 'fluent-crm'),
            'Please Provide your business information'                                               => __('Please Provide your business information', 'fluent-crm'),
            'Please Select Clicked URLS'                                                             => __('Please Select Clicked URLS', 'fluent-crm'),
            'Please Select Tags first'                                                               => __('Please Select Tags first', 'fluent-crm'),
            'Please Upload a CSV first'                                                              => __('Please Upload a CSV first', 'fluent-crm'),
            'Please add at least one Tag'                                                            => __('Please add at least one Tag', 'fluent-crm'),
            'Please add at least one list'                                                           => __('Please add at least one list', 'fluent-crm'),
            'Please configure'                                                                       => __('Please configure', 'fluent-crm'),
            'Please enter a keyword'                                                                 => __('Please enter a keyword', 'fluent-crm'),
            'Please provide a day'                                                                   => __('Please provide a day', 'fluent-crm'),
            'Please provide a license key'                                                           => __('Please provide a license key', 'fluent-crm'),
            'Please provide a template name'                                                         => __('Please provide a template name', 'fluent-crm'),
            'Please provide a time'                                                                  => __('Please provide a time', 'fluent-crm'),
            'Please provide an unique title'                                                         => __('Please provide an unique title', 'fluent-crm'),
            'Please provide condition value'                                                         => __('Please provide condition value', 'fluent-crm'),
            'Please purchase Pro'                                                                    => __('Please purchase Pro', 'fluent-crm'),
            'Please review before you delete your old logs'                                          => __('Please review before you delete your old logs', 'fluent-crm'),
            'Please save the details as you can not retrieve again'                                  => __('Please save the details as you can not retrieve again', 'fluent-crm'),
            'Please select a date and time'                                                          => __('Please select a date and time', 'fluent-crm'),
            'Please select allowed days to send emails'                                              => __('Please select allowed days to send emails', 'fluent-crm'),
            'Please select at least one tag'                                                         => __('Please select at least one tag', 'fluent-crm'),
            'Please select companies first'                                                          => __('Please select companies first', 'fluent-crm'),
            'Please select subscribers first'                                                        => __('Please select subscribers first', 'fluent-crm'),
            'Please select the filters'                                                              => __('Please select the filters', 'fluent-crm'),
            'Please select the segment'                                                              => __('Please select the segment', 'fluent-crm'),
            'Please set date and time'                                                               => __('Please set date and time', 'fluent-crm'),
            'Please update FluentCRM Pro first'                                                      => __('Please update FluentCRM Pro first', 'fluent-crm'),
            'Please upgrade.'                                                                        => __('Please upgrade.', 'fluent-crm'),
            'Please view the'                                                                        => __('Please view the', 'fluent-crm'),
            'Plugin_Not_Installed_Alert'                                                             => __('Sorry! The plugin could not be installed. Please install manually', 'fluent-crm'),
            'Postal Code'                                                                            => __('Postal Code', 'fluent-crm'),
            'Prefix'                                                                                 => __('Prefix', 'fluent-crm'),
            'Prev step'                                                                              => __('Prev step', 'fluent-crm'),
            'Preview'                                                                                => __('Preview', 'fluent-crm'),
            'Preview Email'                                                                          => __('Preview Email', 'fluent-crm'),
            'Preview Form'                                                                           => __('Preview Form', 'fluent-crm'),
            'Preview Log Summary'                                                                    => __('Preview Log Summary', 'fluent-crm'),
            'Preview Text:'                                                                          => __('Preview Text:', 'fluent-crm'),
            'Preview The Form'                                                                       => __('Preview The Form', 'fluent-crm'),
            'Previous'                                                                               => __('Previous', 'fluent-crm'),
            'Previous Emails'                                                                        => __('Previous Emails', 'fluent-crm'),
            'Previous Month'                                                                         => __('Previous Month', 'fluent-crm'),
            'Previous Period'                                                                        => __('Previous Period', 'fluent-crm'),
            'Previous Quarter'                                                                       => __('Previous Quarter', 'fluent-crm'),
            'Previous Year'                                                                          => __('Previous Year', 'fluent-crm'),
            'Previous_Email_Hist'                                                                    => __('All Previous email history for this recurring campaign.', 'fluent-crm'),
            'Primary'                                                                                => __('Primary', 'fluent-crm'),
            'Primary Company'                                                                        => __('Primary Company', 'fluent-crm'),
            'Primary Fields'                                                                         => __('Primary Fields', 'fluent-crm'),
            'Primary Fields that can be editable'                                                    => __('Primary Fields that can be editable', 'fluent-crm'),
            'Priority (%)'                                                                           => __('Priority (%)', 'fluent-crm'),
            'Pro'                                                                                    => __('Pro', 'fluent-crm'),
            'Pro_Activities_otcwb'                                                                   => __('Activities of this contact will be shown here', 'fluent-crm'),
            'Pro_Change_SS'                                                                          => __('Change Subscription Status', 'fluent-crm'),
            'Pro_Contact_F_A'                                                                        => __('Contact Files & Attachments', 'fluent-crm'),
            'Pro_Contact_N_A'                                                                        => __('Contact Notes & Activities', 'fluent-crm'),
            'Pro_Emails_fdcaa'                                                                       => __('Emails from different campaigns and automations', 'fluent-crm'),
            'Pro_Form_SfFFwbshCFF'                                                                   => __('Form Submission from Fluent Forms will be shown here. Currently, Fluent Forms is not installed', 'fluent-crm'),
            'Pro_No_NfPatfn'                                                                         => __('No Note found. Please add the first note', 'fluent-crm'),
            'Pro_Please_saof'                                                                        => __('Please select an option first', 'fluent-crm'),
            'Pro_Purchase_hfEwbsh'                                                                   => __('Purchase history from EDD/WooCommerce will be shown here. Currently no E-Commerce Plugin found in your installation', 'fluent-crm'),
            'Processing'                                                                             => __('Processing', 'fluent-crm'),
            'Processing now...'                                                                      => __('Processing now...', 'fluent-crm'),
            'Processing now.Please wait a bit...'                                                    => __('Processing now.Please wait a bit...', 'fluent-crm'),
            'Product, promo code, or slogan (e.g. spring_sale)'                                      => __('Product, promo code, or slogan (e.g. spring_sale)', 'fluent-crm'),
            'Profile'                                                                                => __('Profile', 'fluent-crm'),
            'ProfileAutomations.Contact_Added_manually_to_Automation'                                => __('Contact has been added manually to this automation funnel', 'fluent-crm'),
            'Provide Name'                                                                           => __('Provide Name', 'fluent-crm'),
            'Provide Valid Email Address that will be used to send emails'                           => __('Provide Valid Email Address that will be used to send emails', 'fluent-crm'),
            'Provide campaign details'                                                               => __('Provide campaign details', 'fluent-crm'),
            'Public User Clicks'                                                                     => __('Public User Clicks', 'fluent-crm'),
            'Publish'                                                                                => __('Publish', 'fluent-crm'),
            'Purchase Count'                                                                         => __('Purchase Count', 'fluent-crm'),
            'Purchase History from'                                                                  => __('Purchase History from', 'fluent-crm'),
            'Purchase one here'                                                                      => __('Purchase one here', 'fluent-crm'),
            'PurchaseHistoryBlock.empty_desc'                                                        => __('will be shown here, Currently no purchase history found for this contact', 'fluent-crm'),
            'Purged'                                                                                 => __('Purged', 'fluent-crm'),
            'Quick Contact Navigation'                                                               => __('Quick Contact Navigation', 'fluent-crm'),
            'Quick Links'                                                                            => __('Quick Links', 'fluent-crm'),
            'Quick Overview'                                                                         => __('Quick Overview', 'fluent-crm'),
            'Quick Stats'                                                                            => __('Quick Stats', 'fluent-crm'),
            'Quick_Nav_Enable_Help'                                                                  => __('Enable this feature if you want Quick navigation bar for navigating next or previous contacts easily from single contact screen', 'fluent-crm'),
            'REST API'                                                                               => __('REST API', 'fluent-crm'),
            'REST API Access Management'                                                             => __('REST API Access Management', 'fluent-crm'),
            'REST API key has been created to selected user'                                         => __('REST API key has been created to selected user', 'fluent-crm'),
            'Raw_Please_PHoyE'                                                                       => __('Please Provide HTML of your Email', 'fluent-crm'),
            'Re-Sync'                                                                                => __('Re-Sync', 'fluent-crm'),
            'Re-apply New Steps'                                                                     => __('Re-apply New Steps', 'fluent-crm'),
            'Read CLI Documentation'                                                                 => __('Read CLI Documentation', 'fluent-crm'),
            'Read the documentation'                                                                 => __('Read the documentation', 'fluent-crm'),
            'Real-Time Email Delivery'                                                               => __('Real-Time Email Delivery', 'fluent-crm'),
            'Reason:'                                                                                => __('Reason:', 'fluent-crm'),
            'Rec_All_coSLs'                                                                          => __('All contacts on Selected List segment', 'fluent-crm'),
            'Rec_Camp_Delete_Alert'                                                                  => __('Are you sure you want to delete this Recurring Campaign?', 'fluent-crm'),
            'Rec_Camp_Empty_Alert'                                                                   => __('Looks like you do not have any recurring Email Campaigns yet.', 'fluent-crm'),
            'Rec_Continue_TNS_aS'                                                                    => __('Continue To Next Step [Review and Send]', 'fluent-crm'),
            'Rec_Please_dnctw'                                                                       => __('Please do not close this window.', 'fluent-crm'),
            'Rec_Please_stwdsywts'                                                                   => __('Please select to which dynamic segment you want to send emails for this campaign', 'fluent-crm'),
            'Rec_Processing_EhMit'                                                                   => __('Processing Error happened. Maybe it\'s timeout error. Resume or StartOver', 'fluent-crm'),
            'Rec_Select_LaTtywtef'                                                                   => __('Select Lists and Tags that you want to exclude from this campaign. Excluded contacts will be subtracted from your included selection', 'fluent-crm'),
            'Rec_Select_LaTtywtse'                                                                   => __('Select Lists and Tags that you want to send emails for this campaign. You can create multiple row to send to all of them', 'fluent-crm'),
            'Rec_Select_Loasfatst'                                                                   => __('Select List or all subscribers first and then select tag', 'fluent-crm'),
            'Rec_This_wfaac'                                                                         => __('This will filter all available contacts', 'fluent-crm'),
            'Rec_contacts_fboys'                                                                     => __('contacts found based on your selection', 'fluent-crm'),
            'Recipient Sections'                                                                     => __('Recipient Sections', 'fluent-crm'),
            'Recipient settings has been updated'                                                    => __('Recipient settings has been updated', 'fluent-crm'),
            'Recipients'                                                                             => __('Recipients', 'fluent-crm'),
            'Recipients.instruction'                                                                 => __('Invalid selection of lists and tags in contacts included.', 'fluent-crm'),
            'Recommended Plugins and Addons'                                                         => __('Recommended Plugins and Addons', 'fluent-crm'),
            'Recurring Campaigns'                                                                    => __('Recurring Campaigns', 'fluent-crm'),
            'Recurring Email Campaigns'                                                              => __('Recurring Email Campaigns', 'fluent-crm'),
            'Refresh Email Preview'                                                                  => __('Refresh Email Preview', 'fluent-crm'),
            'Relationship Type'                                                                      => __('Relationship Type', 'fluent-crm'),
            'Reload Page'                                                                            => __('Reload Page', 'fluent-crm'),
            'Remove'                                                                                 => __('Remove', 'fluent-crm'),
            'Remove Association'                                                                     => __('Remove Association', 'fluent-crm'),
            'Remove From Company'                                                                    => __('Remove From Company', 'fluent-crm'),
            'Remove From Lists'                                                                      => __('Remove From Lists', 'fluent-crm'),
            'Remove From Sequence'                                                                   => __('Remove From Sequence', 'fluent-crm'),
            'Remove From Tags'                                                                       => __('Remove From Tags', 'fluent-crm'),
            'Remove Lists when clicked (optional)'                                                   => __('Remove Lists when clicked (optional)', 'fluent-crm'),
            'Remove Tags'                                                                            => __('Remove Tags', 'fluent-crm'),
            'Remove Tags when clicked (optional)'                                                    => __('Remove Tags when clicked (optional)', 'fluent-crm'),
            'Remove User Role'                                                                       => __('Remove User Role', 'fluent-crm'),
            'Remove User Role: '                                                                     => __('Remove User Role: ', 'fluent-crm'),
            'Remove_Contacts_From_Company_Confirm_Message'                                           => __('Are you sure you want to remove contacts to selected company?', 'fluent-crm'),
            'Replace Options'                                                                        => __('Replace Options', 'fluent-crm'),
            'Replace Value'                                                                          => __('Replace Value', 'fluent-crm'),
            'Reply To Email'                                                                         => __('Reply To Email', 'fluent-crm'),
            'Reply To Name'                                                                          => __('Reply To Name', 'fluent-crm'),
            'Reply to Email (Optional)'                                                              => __('Reply to Email (Optional)', 'fluent-crm'),
            'Reply to Name'                                                                          => __('Reply to Name', 'fluent-crm'),
            'Reply to Name (Optional)'                                                               => __('Reply to Name (Optional)', 'fluent-crm'),
            'Report Type:'                                                                           => __('Report Type:', 'fluent-crm'),
            'Reports'                                                                                => __('Reports', 'fluent-crm'),
            'Require FluentCRM Pro'                                                                  => __('Require FluentCRM Pro', 'fluent-crm'),
            'Resend'                                                                                 => __('Resend', 'fluent-crm'),
            'Resend Any Emails'                                                                      => __('Resend Any Emails', 'fluent-crm'),
            'Reset FluentCRM Database tables'                                                        => __('Reset FluentCRM Database tables', 'fluent-crm'),
            'Rest API Status'                                                                        => __('Rest API Status', 'fluent-crm'),
            'RestApi.Please_Create_A_Manager'                                                        => __('Looks like you did not have any additional Manager yet. Please create a manager (non administrator role) first for FluentCRM', 'fluent-crm'),
            'RestApi.Use_Basic_Auth_Info'                                                            => __('Use Basic Authorization for REST API requests. Please check the official documentation for more details', 'fluent-crm'),
            'Restrict Content Pro Integration'                                                       => __('Restrict Content Pro Integration', 'fluent-crm'),
            'Resume'                                                                                 => __('Resume', 'fluent-crm'),
            'Resume Sending'                                                                         => __('Resume Sending', 'fluent-crm'),
            'Retry Sending'                                                                          => __('Retry Sending', 'fluent-crm'),
            'Revenue'                                                                                => __('Revenue', 'fluent-crm'),
            'Revenue From Sequence Emails'                                                           => __('Revenue From Sequence Emails', 'fluent-crm'),
            'Revenue Report'                                                                         => __('Revenue Report', 'fluent-crm'),
            'Revenue from this email campaign'                                                       => __('Revenue from this email campaign', 'fluent-crm'),
            'Review'                                                                                 => __('Review', 'fluent-crm'),
            'Review & Import'                                                                        => __('Review & Import', 'fluent-crm'),
            'Review & Send'                                                                          => __('Review & Send', 'fluent-crm'),
            'Run Action Task to do particular task on the contact'                                   => __('Run Action Task to do particular task on the contact', 'fluent-crm'),
            'Run Manually'                                                                           => __('Run Manually', 'fluent-crm'),
            'SMTP/Email Sending Service Settings'                                                    => __('SMTP/Email Sending Service Settings', 'fluent-crm'),
            'Sales'                                                                                  => __('Sales', 'fluent-crm'),
            'Saturday'                                                                               => __('Saturday', 'fluent-crm'),
            'Save'                                                                                   => __('Save', 'fluent-crm'),
            'Save & close'                                                                           => __('Save & close', 'fluent-crm'),
            'Save Note'                                                                              => __('Save Note', 'fluent-crm'),
            'Save Settings'                                                                          => __('Save Settings', 'fluent-crm'),
            'Save Template'                                                                          => __('Save Template', 'fluent-crm'),
            'Save as dynamic Segment'                                                                => __('Save as dynamic Segment', 'fluent-crm'),
            'Save as template'                                                                       => __('Save as template', 'fluent-crm'),
            'Saved'                                                                                  => __('Saved', 'fluent-crm'),
            'Sch_Emails_wbsast'                                                                      => __('Emails will be sent at scheduled time.', 'fluent-crm'),
            'Schedule'                                                                               => __('Schedule', 'fluent-crm'),
            'Schedule Campaign'                                                                      => __('Schedule Campaign', 'fluent-crm'),
            'Schedule Time'                                                                          => __('Schedule Time', 'fluent-crm'),
            'Schedule the emails'                                                                    => __('Schedule the emails', 'fluent-crm'),
            'Schedule this campaign'                                                                 => __('Schedule this campaign', 'fluent-crm'),
            'Schedule_Cam_Confirm_Header'                                                            => __('Are you sure to proceed?', 'fluent-crm'),
            'Schedule_Date_Time_Info'                                                                => __('Schedule emails within a specified date-time range', 'fluent-crm'),
            'Scheduled'                                                                              => __('Scheduled', 'fluent-crm'),
            'Scheduled (pending)'                                                                    => __('Scheduled (pending)', 'fluent-crm'),
            'Scheduled At'                                                                           => __('Scheduled At', 'fluent-crm'),
            'Scheduled at:'                                                                          => __('Scheduled at:', 'fluent-crm'),
            'Scheduled on'                                                                           => __('Scheduled on', 'fluent-crm'),
            'Scheduling Settings'                                                                    => __('Scheduling Settings', 'fluent-crm'),
            'Search'                                                                                 => __('Search', 'fluent-crm'),
            'Select All'                                                                             => __('Select All', 'fluent-crm'),
            'Search Companies'                                                                       => __('Search Companies', 'fluent-crm'),
            'Search Results for:'                                                                    => __('Search Results for:', 'fluent-crm'),
            'Search Type and Enter...'                                                               => __('Search Type and Enter...', 'fluent-crm'),
            'Search by title...'                                                                     => __('Search by title...', 'fluent-crm'),
            'Search contact'                                                                         => __('Search contact', 'fluent-crm'),
            'Search...'                                                                              => __('Search...', 'fluent-crm'),
            'Segment Name'                                                                           => __('Segment Name', 'fluent-crm'),
            'Select'                                                                                 => __('Select', 'fluent-crm'),
            'Select A List'                                                                          => __('Select A List', 'fluent-crm'),
            'Select Action'                                                                          => __('Select Action', 'fluent-crm'),
            'Select Activity Type'                                                                   => __('Select Activity Type', 'fluent-crm'),
            'Select Automation'                                                                      => __('Select Automation', 'fluent-crm'),
            'Select Automation Funnel'                                                               => __('Select Automation Funnel', 'fluent-crm'),
            'Select Bulk Action'                                                                     => __('Select Bulk Action', 'fluent-crm'),
            'Select Companies'                                                                       => __('Select Companies', 'fluent-crm'),
            'Select Condition'                                                                       => __('Select Condition', 'fluent-crm'),
            'Select Contact'                                                                         => __('Select Contact', 'fluent-crm'),
            'Select Current CRM'                                                                     => __('Select Current CRM', 'fluent-crm'),
            'Select Day of the month'                                                                => __('Select Day of the month', 'fluent-crm'),
            'Select Day of the week'                                                                 => __('Select Day of the week', 'fluent-crm'),
            'Select Dynamic Segment'                                                                 => __('Select Dynamic Segment', 'fluent-crm'),
            'Select Email Recipients'                                                                => __('Select Email Recipients', 'fluent-crm'),
            'Select Field Type'                                                                      => __('Select Field Type', 'fluent-crm'),
            'Select Image for Your Email Body'                                                       => __('Select Image for Your Email Body', 'fluent-crm'),
            'Select List'                                                                            => __('Select List', 'fluent-crm'),
            'Select List First'                                                                      => __('Select List First', 'fluent-crm'),
            'Select Lists that you want to show for contacts'                                        => __('Select Lists that you want to show for contacts', 'fluent-crm'),
            'Select Logs you want to delete'                                                         => __('Select Logs you want to delete', 'fluent-crm'),
            'Select New Automation Trigger'                                                          => __('Select New Automation Trigger', 'fluent-crm'),
            'Select Operator'                                                                        => __('Select Operator', 'fluent-crm'),
            'Select Option'                                                                          => __('Select Option', 'fluent-crm'),
            'Select Schedule time'                                                                   => __('Select Schedule time', 'fluent-crm'),
            'Select Sequence'                                                                        => __('Select Sequence', 'fluent-crm'),
            'Select Status'                                                                          => __('Select Status', 'fluent-crm'),
            'Select Subscribers'                                                                     => __('Select Subscribers', 'fluent-crm'),
            'Select Tag'                                                                             => __('Select Tag', 'fluent-crm'),
            'Select Tags'                                                                            => __('Select Tags', 'fluent-crm'),
            'Select Targeted Tags'                                                                   => __('Select Targeted Tags', 'fluent-crm'),
            'Select Template'                                                                        => __('Select Template', 'fluent-crm'),
            'Select User Roles'                                                                      => __('Select User Roles', 'fluent-crm'),
            'Select Value'                                                                           => __('Select Value', 'fluent-crm'),
            'Select Your CSV Delimiter'                                                              => __('Select Your CSV Delimiter', 'fluent-crm'),
            'Select Your Email Service Provider'                                                     => __('Select Your Email Service Provider', 'fluent-crm'),
            'Select a List'                                                                          => __('Select a List', 'fluent-crm'),
            'Select a Trigger'                                                                       => __('Select a Trigger', 'fluent-crm'),
            'Select a starter design to build your email'                                            => __('Select a starter design to build your email', 'fluent-crm'),
            'Select a template'                                                                      => __('Select a template', 'fluent-crm'),
            'Select automation conditions'                                                           => __('Select automation conditions', 'fluent-crm'),
            'Select by Roles'                                                                        => __('Select by Roles', 'fluent-crm'),
            'Select country'                                                                         => __('Select country', 'fluent-crm'),
            'Select custom contacts by advanced filters'                                             => __('Select custom contacts by advanced filters', 'fluent-crm'),
            'Select data source and operator first'                                                  => __('Select data source and operator first', 'fluent-crm'),
            'Select date and time'                                                                   => __('Select date and time', 'fluent-crm'),
            'Select lists'                                                                           => __('Select lists', 'fluent-crm'),
            'Select status'                                                                          => __('Select status', 'fluent-crm'),
            'Select tags'                                                                            => __('Select tags', 'fluent-crm'),
            'Select the fields that user can manage'                                                 => __('Select the fields that user can manage', 'fluent-crm'),
            'Select the trigger for this automation'                                                 => __('Select the trigger for this automation', 'fluent-crm'),
            'Select which day of the month to send email'                                            => __('Select which day of the month to send email', 'fluent-crm'),
            'Select which day you want to send email'                                                => __('Select which day you want to send email', 'fluent-crm'),
            'Select_Date_Time_Alert'                                                                 => __('Select Date & Time Range that you want to send the emails', 'fluent-crm'),
            'Selected emails are deleted'                                                            => __('Selected emails are deleted', 'fluent-crm'),
            'Semicolon Separated (;)'                                                                => __('Semicolon Separated (;)', 'fluent-crm'),
            'Send'                                                                                   => __('Send', 'fluent-crm'),
            'Send Custom Email'                                                                      => __('Send Custom Email', 'fluent-crm'),
            'Send Double Optin'                                                                      => __('Send Double Optin', 'fluent-crm'),
            'Send Double Optin Email'                                                                => __('Send Double Optin Email', 'fluent-crm'),
            'Send Double Optin Email for new contacts'                                               => __('Send Double Optin Email for new contacts', 'fluent-crm'),
            'Send Double Optin To Pending Contacts'                                                  => __('Send Double Optin To Pending Contacts', 'fluent-crm'),
            'Send Email'                                                                             => __('Send Email', 'fluent-crm'),
            'Send Emails Now'                                                                        => __('Send Emails Now', 'fluent-crm'),
            'Send HTTP '                                                                             => __('Send HTTP ', 'fluent-crm'),
            'Send a test email'                                                                      => __('Send a test email', 'fluent-crm'),
            'Send emails if'                                                                         => __('Send emails if', 'fluent-crm'),
            'Send the emails right now'                                                              => __('Send the emails right now', 'fluent-crm'),
            'Send to Draft'                                                                          => __('Send to Draft', 'fluent-crm'),
            'Send_Email_Auto_Info'                                                                   => __('Send Emails automatically to the selected day and time (if you disable this, emails will be on draft state and you can trigger emails after review)', 'fluent-crm'),
            'Send_Email_Daily_Weekly_Monthly'                                                        => __('Send emails daily / weekly / monthly automatically based in your published content as newsletter', 'fluent-crm'),
            'Sending'                                                                                => __('Sending', 'fluent-crm'),
            'Sending Conditions'                                                                     => __('Sending Conditions', 'fluent-crm'),
            'Sending Time Range'                                                                     => __('Sending Time Range', 'fluent-crm'),
            'Sending To Contacts'                                                                    => __('Sending To Contacts', 'fluent-crm'),
            'Sent'                                                                                   => __('Sent', 'fluent-crm'),
            'September'                                                                              => __('September', 'fluent-crm'),
            'Sequence'                                                                               => __('Sequence', 'fluent-crm'),
            'Sequence Subscribers'                                                                   => __('Sequence Subscribers', 'fluent-crm'),
            'Sequence Title'                                                                         => __('Sequence Title', 'fluent-crm'),
            'SequenceSubscribers.DeleteInfo'                                                         => __('Are you sure, you want to remove these subscribers from this sequence?', 'fluent-crm'),
            'Server Issue detected'                                                                  => __('Server Issue detected', 'fluent-crm'),
            'Set Campaign'                                                                           => __('Set Campaign', 'fluent-crm'),
            'Set Companies'                                                                          => __('Set Companies', 'fluent-crm'),
            'Set Condition'                                                                          => __('Set Condition', 'fluent-crm'),
            'Set Email Sequence'                                                                     => __('Set Email Sequence', 'fluent-crm'),
            'Set Email Subject & Body'                                                               => __('Set Email Subject & Body', 'fluent-crm'),
            'Set Lists'                                                                              => __('Set Lists', 'fluent-crm'),
            'Set Note Title'                                                                         => __('Set Note Title', 'fluent-crm'),
            'Set Property'                                                                           => __('Set Property', 'fluent-crm'),
            'Set Tag'                                                                                => __('Set Tag', 'fluent-crm'),
            'Set Tags'                                                                               => __('Set Tags', 'fluent-crm'),
            'Set User Role'                                                                          => __('Set User Role', 'fluent-crm'),
            'Set Webhook URL'                                                                        => __('Set Webhook URL', 'fluent-crm'),
            'Set after how many'                                                                     => __('Set after how many', 'fluent-crm'),
            'Set up your schedule'                                                                   => __('Set up your schedule', 'fluent-crm'),
            'Set_Date_Time_Label'                                                                    => __('Please set date and time to send this campaign', 'fluent-crm'),
            'Set_SMTP_Email_SS'                                                                      => __('SMTP/Email Service Settings', 'fluent-crm'),
            'Settings'                                                                               => __('Settings', 'fluent-crm'),
            'Settings Updated.'                                                                      => __('Settings Updated.', 'fluent-crm'),
            'SettingsTools.checkRestRequest_message'                                                 => __('Not Working. Please make sure your server has this request type enabled', 'fluent-crm'),
            'Setup'                                                                                  => __('Setup', 'fluent-crm'),
            'Setup.FluentCrm.Share_Essentials.desc'                                                  => __('Allow FluentCRM to collect non-sensitive diagnostic data and usage information.', 'fluent-crm'),
            'Setup.Install_FluentForm'                                                               => __('Install Fluent Forms Plugin for Lead collections Forms.', 'fluent-crm'),
            'Setup.Send_Marketing_tips'                                                              => __('We will send marketing tips and advanced usage of FluentCRM (Monthly)', 'fluent-crm'),
            'Setup.Subscribe_Newsletter'                                                             => __('You can subscribe to our bi-monthly newsletter where we will email you all about FluentCRM.', 'fluent-crm'),
            'Share Essentials'                                                                       => __('Share Essentials', 'fluent-crm'),
            'Shortcode'                                                                              => __('Shortcode', 'fluent-crm'),
            'Show Errors'                                                                            => __('Show Errors', 'fluent-crm'),
            'Show IF in Selected Tag'                                                                => __('Show IF in Selected Tag', 'fluent-crm'),
            'Show IF not in selected tag'                                                            => __('Show IF not in selected tag', 'fluent-crm'),
            'Show Individual Emails'                                                                 => __('Show Individual Emails', 'fluent-crm'),
            'Show Individual Recipients'                                                             => __('Show Individual Recipients', 'fluent-crm'),
            'Show Invalid Contacts'                                                                  => __('Show Invalid Contacts', 'fluent-crm'),
            'Show Report'                                                                            => __('Show Report', 'fluent-crm'),
            'Show Skipped Contacts'                                                                  => __('Show Skipped Contacts', 'fluent-crm'),
            'Show Subscribers'                                                                       => __('Show Subscribers', 'fluent-crm'),
            'Show if in tags:'                                                                       => __('Show if in tags:', 'fluent-crm'),
            'Show if not in tags:'                                                                   => __('Show if not in tags:', 'fluent-crm'),
            'Showing preview for current contact'                                                    => __('Showing preview for current contact', 'fluent-crm'),
            'Showing stats from'                                                                     => __('Showing stats from', 'fluent-crm'),
            'Skip'                                                                                   => __('Skip', 'fluent-crm'),
            'Skip if already in DB'                                                                  => __('Skip if already in DB', 'fluent-crm'),
            'Slug'                                                                                   => __('Slug', 'fluent-crm'),
            'Slug (Optional)'                                                                        => __('Slug (Optional)', 'fluent-crm'),
            'Smart Links'                                                                            => __('Smart Links', 'fluent-crm'),
            'Smart URL'                                                                              => __('Smart URL', 'fluent-crm'),
            'SmartLinks'                                                                             => __('SmartLinks', 'fluent-crm'),
            'Smartcode has been copied to your clipboard'                                            => __('Smartcode has been copied to your clipboard', 'fluent-crm'),
            'Smartcode:'                                                                             => __('Smartcode:', 'fluent-crm'),
            'SmtpSettings.Configured.failed.desc'                                                    => __('Looks like your FluentSMTP plugin is not configured yet', 'fluent-crm'),
            'SmtpSettings.Configured.title'                                                          => __('Your WordPress & FluentCRM is set to send using FluentSMTP Plugin', 'fluent-crm'),
            'SmtpSettings.FluentSMTP.title'                                                          => __('FluentSMTP - The ultimate SMTP & Email Service Connection Plugin for WordPress', 'fluent-crm'),
            'Social Links'                                                                           => __('Social Links', 'fluent-crm'),
            'Some_SmartCode_Not_Work_Alert'                                                          => __('Please note, some smart codes like footer links may not work on test email', 'fluent-crm'),
            'Something is wrong!'                                                                    => __('Something is wrong!', 'fluent-crm'),
            'Something went wrong!'                                                                  => __('Something went wrong!', 'fluent-crm'),
            'Something went wrong, when syncing data'                                                => __('Something went wrong, when syncing data', 'fluent-crm'),
            'Sorry no driver found for this import'                                                  => __('Sorry no driver found for this import', 'fluent-crm'),
            'Sorry no subscribers found based on your selection'                                     => __('Sorry no subscribers found based on your selection', 'fluent-crm'),
            'Sorry! No docs found'                                                                   => __('Sorry! No docs found', 'fluent-crm'),
            'Sorry! No subscribers found based on your selection'                                    => __('Sorry! No subscribers found based on your selection', 'fluent-crm'),
            'Sorry, No emails found in this automation'                                              => __('Sorry, No emails found in this automation', 'fluent-crm'),
            'Sorry, No subscribers found based on your filters'                                      => __('Sorry, No subscribers found based on your filters', 'fluent-crm'),
            'Sorry, the selected plugins could not be installed'                                     => __('Sorry, the selected plugins could not be installed', 'fluent-crm'),
            'Source'                                                                                 => __('Source', 'fluent-crm'),
            'SourceSelector.title'                                                                   => __('Select Source from where you want to import your contacts', 'fluent-crm'),
            'Start Range'                                                                            => __('Start Range', 'fluent-crm'),
            'Start Using SmartLinks'                                                                 => __('Start Using SmartLinks', 'fluent-crm'),
            'Start Writing Email Here'                                                               => __('Start Writing Email Here', 'fluent-crm'),
            'Start Writing Here'                                                                     => __('Start Writing Here', 'fluent-crm'),
            'Start date'                                                                             => __('Start date', 'fluent-crm'),
            'StartOver'                                                                              => __('StartOver', 'fluent-crm'),
            'Started At'                                                                             => __('Started At', 'fluent-crm'),
            'Starts With'                                                                            => __('Starts With', 'fluent-crm'),
            'State'                                                                                  => __('State', 'fluent-crm'),
            'Stats'                                                                                  => __('Stats', 'fluent-crm'),
            'Status'                                                                                 => __('Status', 'fluent-crm'),
            'Step 1'                                                                                 => __('Step 1', 'fluent-crm'),
            'Step 2'                                                                                 => __('Step 2', 'fluent-crm'),
            'Step Report'                                                                            => __('Step Report', 'fluent-crm'),
            'Subject'                                                                                => __('Subject', 'fluent-crm'),
            'Subject & Settings'                                                                     => __('Subject & Settings', 'fluent-crm'),
            'Subject Analytics'                                                                      => __('Subject Analytics', 'fluent-crm'),
            'Subject Test'                                                                           => __('Subject Test', 'fluent-crm'),
            'Subscribed'                                                                             => __('Subscribed', 'fluent-crm'),
            'Subscriber'                                                                             => __('Subscriber', 'fluent-crm'),
            'Subscriber Clicks'                                                                      => __('Subscriber Clicks', 'fluent-crm'),
            'Subscriber Fields'                                                                      => __('Subscriber Fields', 'fluent-crm'),
            'Subscribers'                                                                            => __('Subscribers', 'fluent-crm'),
            'Subscribers Growth'                                                                     => __('Subscribers Growth', 'fluent-crm'),
            'Subtract Options'                                                                       => __('Subtract Options', 'fluent-crm'),
            'Subtract Value'                                                                         => __('Subtract Value', 'fluent-crm'),
            'Success'                                                                                => __('Success', 'fluent-crm'),
            'Suggested text to include:'                                                             => __('Suggested text to include:', 'fluent-crm'),
            'Summary'                                                                                => __('Summary', 'fluent-crm'),
            'Sunday'                                                                                 => __('Sunday', 'fluent-crm'),
            'Super fast UI powered by VueJS'                                                         => __('Super fast UI powered by VueJS', 'fluent-crm'),
            'Support Tickets from'                                                                   => __('Support Tickets from', 'fluent-crm'),
            'Sync_New_Steps'                                                                         => __('Sync new steps to existing completed contacts', 'fluent-crm'),
            'Syncing...'                                                                             => __('Syncing...', 'fluent-crm'),
            'System Defined'                                                                         => __('System Defined', 'fluent-crm'),
            'Tag'                                                                                    => __('Tag', 'fluent-crm'),
            'Tag Name'                                                                               => __('Tag Name', 'fluent-crm'),
            'Tag_Tags_alLbmwtfyci'                                                                   => __('Tags are like Lists but more ways to filter your contacts inside a list', 'fluent-crm'),
            'Tags'                                                                                   => __('Tags', 'fluent-crm'),
            'Target Full URL'                                                                        => __('Target Full URL', 'fluent-crm'),
            'Target URL'                                                                             => __('Target URL', 'fluent-crm'),
            'Template Name'                                                                          => __('Template Name', 'fluent-crm'),
            'Template Title'                                                                         => __('Template Title', 'fluent-crm'),
            'Template_Export_Alert'                                                                  => __('Template export is only available on pro version of FluentCRM', 'fluent-crm'),
            'Test email successfully sent to'                                                        => __('Test email successfully sent to', 'fluent-crm'),
            'Text Color'                                                                             => __('Text Color', 'fluent-crm'),
            'Thank you again for configuring your own CRM in WordPress.'                             => __('Thank you again for configuring your own CRM in WordPress.', 'fluent-crm'),
            'The email address is required!'                                                         => __('The email address is required!', 'fluent-crm'),
            'The referrer: (e.g. google, newsletter)'                                                => __('The referrer: (e.g. google, newsletter)', 'fluent-crm'),
            'This Month'                                                                             => __('This Month', 'fluent-crm'),
            'This email footer text will be used to all your email'                                  => __('This email footer text will be used to all your email', 'fluent-crm'),
            'This email footer text will be used to this email only'                                 => __('This email footer text will be used to this email only', 'fluent-crm'),
            'This feature only available on FluentCRM Pro.'                                          => __('This feature only available on FluentCRM Pro.', 'fluent-crm'),
            'This is a pro feature'                                                                  => __('This is a pro feature', 'fluent-crm'),
            'This quarter'                                                                           => __('This quarter', 'fluent-crm'),
            'ThriveArchitect'                                                                        => __('ThriveArchitect', 'fluent-crm'),
            'ThriveThemes integration'                                                               => __('ThriveThemes integration', 'fluent-crm'),
            'Thursday'                                                                               => __('Thursday', 'fluent-crm'),
            'Tips'                                                                                   => __('Tips', 'fluent-crm'),
            'Title'                                                                                  => __('Title', 'fluent-crm'),
            'Title field is required'                                                                => __('Title field is required', 'fluent-crm'),
            'Title of the Recurring Campaign'                                                        => __('Title of the Recurring Campaign', 'fluent-crm'),
            'To'                                                                                     => __('To', 'fluent-crm'),
            'To use this feature you need FluentCRM Pro.'                                            => __('To use this feature you need FluentCRM Pro.', 'fluent-crm'),
            'Tomorrow'                                                                               => __('Tomorrow', 'fluent-crm'),
            'Tools'                                                                                  => __('Tools', 'fluent-crm'),
            'Top Selling Products'                                                                   => __('Top Selling Products', 'fluent-crm'),
            'Total Clicks'                                                                           => __('Total Clicks', 'fluent-crm'),
            'Total Contact'                                                                          => __('Total Contact', 'fluent-crm'),
            'Total Emails'                                                                           => __('Total Emails', 'fluent-crm'),
            'Total Found Result:'                                                                    => __('Total Found Result:', 'fluent-crm'),
            'Total Inserted:'                                                                        => __('Total Inserted:', 'fluent-crm'),
            'Total Order Count'                                                                      => __('Total Order Count', 'fluent-crm'),
            'Total Recipients'                                                                       => __('Total Recipients', 'fluent-crm'),
            'Total Updated:'                                                                         => __('Total Updated:', 'fluent-crm'),
            'Total:'                                                                                 => __('Total:', 'fluent-crm'),
            'Transfer Data From Other CRM'                                                           => __('Transfer Data From Other CRM', 'fluent-crm'),
            'Trigger'                                                                                => __('Trigger', 'fluent-crm'),
            'Trigger Title'                                                                          => __('Trigger Title', 'fluent-crm'),
            'TriggerChanger.instruction'                                                             => __('You may change the trigger for this automation. Once you change, Please reconfigure the trigger settings', 'fluent-crm'),
            'Trying fallback save. Please Wait...'                                                   => __('Trying fallback save. Please Wait...', 'fluent-crm'),
            'Tuesday'                                                                                => __('Tuesday', 'fluent-crm'),
            'TutorLMS Integration:'                                                                  => __('TutorLMS Integration:', 'fluent-crm'),
            'Twitter Handle URL'                                                                     => __('Twitter Handle URL', 'fluent-crm'),
            'Twitter Url'                                                                            => __('Twitter Url', 'fluent-crm'),
            'Type'                                                                                   => __('Type', 'fluent-crm'),
            'Type and Enter...'                                                                      => __('Type and Enter...', 'fluent-crm'),
            'URL'                                                                                    => __('URL', 'fluent-crm'),
            'URL after redirect'                                                                     => __('URL after redirect', 'fluent-crm'),
            'Uncheck this if you want to trigger automation events'                                  => __('Uncheck this if you want to trigger automation events', 'fluent-crm'),
            'Unique Clicks'                                                                          => __('Unique Clicks', 'fluent-crm'),
            'Unknown error'                                                                          => __('Unknown error', 'fluent-crm'),
            'Unsaved_Confirm_Msg'                                                                    => __('Do you really want to leave? you have unsaved changes!', 'fluent-crm'),
            'Unsubscribe'                                                                            => __('Unsubscribe', 'fluent-crm'),
            'Unsubscribed'                                                                           => __('Unsubscribed', 'fluent-crm'),
            'Unsubscribers'                                                                          => __('Unsubscribers', 'fluent-crm'),
            'Unsubscribes.instruction'                                                               => __('Awesome! No one unsubscribed from this campaign', 'fluent-crm'),
            'Update'                                                                                 => __('Update', 'fluent-crm'),
            'Update Companies'                                                                       => __('Update Companies', 'fluent-crm'),
            'Update Contact'                                                                         => __('Update Contact', 'fluent-crm'),
            'Update Custom Field'                                                                    => __('Update Custom Field', 'fluent-crm'),
            'Update Field'                                                                           => __('Update Field', 'fluent-crm'),
            'Update Note'                                                                            => __('Update Note', 'fluent-crm'),
            'Update Segment'                                                                         => __('Update Segment', 'fluent-crm'),
            'Update Settings'                                                                        => __('Update Settings', 'fluent-crm'),
            'Update Smart Link'                                                                      => __('Update Smart Link', 'fluent-crm'),
            'Update Subscribers'                                                                     => __('Update Subscribers', 'fluent-crm'),
            'Update if already in DB'                                                                => __('Update if already in DB', 'fluent-crm'),
            'Update info'                                                                            => __('Update info', 'fluent-crm'),
            'Updated At'                                                                             => __('Updated At', 'fluent-crm'),
            'Updating '                                                                              => __('Updating ', 'fluent-crm'),
            'Upgrade to FluentCRM Pro'                                                               => __('Upgrade to FluentCRM Pro', 'fluent-crm'),
            'Upgrade_To_Pro'                                                                         => __('To Activate this module please upgrade to pro', 'fluent-crm'),
            'Upload'                                                                                 => __('Upload', 'fluent-crm'),
            'Upload CSV'                                                                             => __('Upload CSV', 'fluent-crm'),
            'Upload Your CSV file'                                                                   => __('Upload Your CSV file', 'fluent-crm'),
            'Use Custom Email Footer'                                                                => __('Use Custom Email Footer', 'fluent-crm'),
            'Use Email Template'                                                                     => __('Use Email Template', 'fluent-crm'),
            'Use Global Email Footer'                                                                => __('Use Global Email Footer', 'fluent-crm'),
            'Use to differentiate ads'                                                               => __('Use to differentiate ads', 'fluent-crm'),
            'Use your dynamic segment to broadcast Email Campaigns'                                  => __('Use your dynamic segment to broadcast Email Campaigns', 'fluent-crm'),
            'Use_Please_dnctm'                                                                       => __('Please do not close this modal.', 'fluent-crm'),
            'Use_Some_otumyc'                                                                        => __('Some of the users matching your criteria', 'fluent-crm'),
            'User Email'                                                                             => __('User Email', 'fluent-crm'),
            'User Email Address'                                                                     => __('User Email Address', 'fluent-crm'),
            'User ID'                                                                                => __('User ID', 'fluent-crm'),
            'User Roles:'                                                                            => __('User Roles:', 'fluent-crm'),
            'User Signup On Form'                                                                    => __('User Signup On Form', 'fluent-crm'),
            'User data from database'                                                                => __('User data from database', 'fluent-crm'),
            'Value'                                                                                  => __('Value', 'fluent-crm'),
            'Verified Email Senders'                                                                 => __('Verified Email Senders', 'fluent-crm'),
            'Verify License'                                                                         => __('Verify License', 'fluent-crm'),
            'Vie_Add_aSE'                                                                            => __('Add another Sequence Email', 'fluent-crm'),
            'Vie_Emails_asatm'                                                                       => __('Emails are sending at this moment', 'fluent-crm'),
            'Vie_Get_swaaetts'                                                                       => __('Get started with adding an email to this sequence', 'fluent-crm'),
            'Vie_Please_msteisbyS'                                                                   => __('Please make sure this email is supported by your SMTP/SES', 'fluent-crm'),
            'Vie_Set_CFNaE'                                                                          => __('Set Custom From Name and Email', 'fluent-crm'),
            'Vie_The_ewbsboysdat'                                                                    => __('The emails will be sent based on your set date and time.', 'fluent-crm'),
            'Vie_This_chbs'                                                                          => __('This campaign has been scheduled.', 'fluent-crm'),
            'Vie_This_cino_sNEwbs'                                                                   => __('This campaign is now on "Paused" state. No Emails will be sent', 'fluent-crm'),
            'Vie_Your_easrn'                                                                         => __('Your emails are sending right now....', 'fluent-crm'),
            'View'                                                                                   => __('View', 'fluent-crm'),
            'View All'                                                                               => __('View All', 'fluent-crm'),
            'View Campaign Emails'                                                                   => __('View Campaign Emails', 'fluent-crm'),
            'View Company'                                                                           => __('View Company', 'fluent-crm'),
            'View Contacts'                                                                          => __('View Contacts', 'fluent-crm'),
            'View Emails'                                                                            => __('View Emails', 'fluent-crm'),
            'View Full Profile'                                                                      => __('View Full Profile', 'fluent-crm'),
            'View GridPane Article'                                                                  => __('View GridPane Article', 'fluent-crm'),
            'View Report'                                                                            => __('View Report', 'fluent-crm'),
            'View Reports'                                                                           => __('View Reports', 'fluent-crm'),
            'View Sequence'                                                                          => __('View Sequence', 'fluent-crm'),
            'View Sequence Emails'                                                                   => __('View Sequence Emails', 'fluent-crm'),
            'View Settings'                                                                          => __('View Settings', 'fluent-crm'),
            'View Subscribers'                                                                       => __('View Subscribers', 'fluent-crm'),
            'Visual_Email_Builder_Alert'                                                             => __('Visual Email Builder is a Pro feature. Please upgrade to pro to use this feature', 'fluent-crm'),
            'Visually design your email with Drag & Drop Builder'                                    => __('Visually design your email with Drag & Drop Builder', 'fluent-crm'),
            'WP Users'                                                                               => __('WP Users', 'fluent-crm'),
            'WPFusion Integration'                                                                   => __('WPFusion Integration', 'fluent-crm'),
            'WP_CLI_Help'                                                                            => __('You can also use WP CLI to sync the data if you have lots of customers.', 'fluent-crm'),
            'Wait '                                                                                  => __('Wait ', 'fluent-crm'),
            'Wait until '                                                                            => __('Wait until ', 'fluent-crm'),
            'Wait until next '                                                                       => __('Wait until next ', 'fluent-crm'),
            'Want to deactivate this license?'                                                       => __('Want to deactivate this license?', 'fluent-crm'),
            'Warning'                                                                                => __('Warning', 'fluent-crm'),
            'Warning default email change'                                                           => __('Are you sure? All of the Fluent CRM email will be sent from ', 'fluent-crm'),
            'WebHookSettings.Copy_Webhook_url'                                                       => __('Copy the webhook URL you want to send your POST request to.', 'fluent-crm'),
            'Webhook Info'                                                                           => __('Webhook Info', 'fluent-crm'),
            'Webhook Settings'                                                                       => __('Webhook Settings', 'fluent-crm'),
            'Website'                                                                                => __('Website', 'fluent-crm'),
            'Wednesday'                                                                              => __('Wednesday', 'fluent-crm'),
            'Weekly'                                                                                 => __('Weekly', 'fluent-crm'),
            'Weeks'                                                                                  => __('Weeks', 'fluent-crm'),
            'Welcome'                                                                                => __('Welcome', 'fluent-crm'),
            'Welcome to FluentCRM!'                                                                  => __('Welcome to FluentCRM!', 'fluent-crm'),
            'When you send the emails?'                                                              => __('When you send the emails?', 'fluent-crm'),
            'Which time you would like to schedule'                                                  => __('Which time you would like to schedule', 'fluent-crm'),
            'Will be applied to all the imported contacts'                                           => __('Will be applied to all the imported contacts', 'fluent-crm'),
            'Will be stored from your last saved email contents'                                     => __('Will be stored from your last saved email contents', 'fluent-crm'),
            'Wishlist Members.'                                                                      => __('Wishlist Members.', 'fluent-crm'),
            'WooCommerce Integration:'                                                               => __('WooCommerce Integration:', 'fluent-crm'),
            'WordPress Default'                                                                      => __('WordPress Default', 'fluent-crm'),
            'WordPress Users'                                                                        => __('WordPress Users', 'fluent-crm'),
            'Working'                                                                                => __('Working', 'fluent-crm'),
            'Year'                                                                                   => __('Year', 'fluent-crm'),
            'Year to Date'                                                                           => __('Year to Date', 'fluent-crm'),
            'Yes'                                                                                    => __('Yes', 'fluent-crm'),
            'Yes, Count me in!'                                                                      => __('Yes, Count me in!', 'fluent-crm'),
            'Yes, Delete and Reset All CRM Data'                                                     => __('Yes, Delete and Reset All CRM Data', 'fluent-crm'),
            'Yes, I want to delete Old Logs'                                                         => __('Yes, I want to delete Old Logs', 'fluent-crm'),
            'You can not reverse this action.'                                                       => __('You can not reverse this action.', 'fluent-crm'),
            'You cannot upload more than one file.'                                                  => __('You cannot upload more than one file.', 'fluent-crm'),
            'You have to add'                                                                        => __('You have to add', 'fluent-crm'),
            'You license key is valid and activated'                                                 => __('You license key is valid and activated', 'fluent-crm'),
            'You need pro version to use this feature'                                               => __('You need pro version to use this feature', 'fluent-crm'),
            'You should provide your business address'                                               => __('You should provide your business address', 'fluent-crm'),
            'Your Browser does not support JS copy. Please copy manually'                            => __('Your Browser does not support JS copy. Please copy manually', 'fluent-crm'),
            'Your Email Address'                                                                     => __('Your Email Address', 'fluent-crm'),
            'Your License Key'                                                                       => __('Your License Key', 'fluent-crm'),
            'Your Note Title'                                                                        => __('Your Note Title', 'fluent-crm'),
            'Your Selected logs will be deleted permanently'                                         => __('Your Selected logs will be deleted permanently', 'fluent-crm'),
            'Your URL Note'                                                                          => __('Your URL Note', 'fluent-crm'),
            'Your campaign email has been scheduled'                                                 => __('Your campaign email has been scheduled', 'fluent-crm'),
            'Your link title so you do not forget it'                                                => __('Your link title so you do not forget it', 'fluent-crm'),
            'Your target URL'                                                                        => __('Your target URL', 'fluent-crm'),
            'ZIP Code'                                                                               => __('ZIP Code', 'fluent-crm'),
            'Zip Code'                                                                               => __('Zip Code', 'fluent-crm'),
            '_Bl_Actions_battywtf'                                                                   => __('Actions blocks are tasks that you want to fire from your side for the target contact', 'fluent-crm'),
            '_Bl_These_aygityuwda'                                                                   => __('These are your goal/trigger item that your user will do and you can measure these steps or adding into this funnel', 'fluent-crm'),
            '_Bl_Use_tbtcspfysc'                                                                     => __('Use the blocks to create separate path for your segmented contacts', 'fluent-crm'),
            '_Ca_Please_utptutf'                                                                     => __('Please upgrade to pro to use this feature', 'fluent-crm'),
            '_Ca_failed_tstrTR'                                                                      => __('failed to send to recipients. Try Resending', 'fluent-crm'),
            '_Cr_Create_aAF'                                                                         => __('Create an Automation Funnel', 'fluent-crm'),
            '_Cr_Enable_DOC'                                                                         => __('Enable Double Opt-in Confirmation', 'fluent-crm'),
            '_Cr_Please_fuatf'                                                                       => __('Please fill up all the fields', 'fluent-crm'),
            '_Cr_Please_utfsuawau'                                                                   => __('Please use the following smart url and when a user click a link in an email, on your site or anywhere then your selected tags and lists will be applied.', 'fluent-crm'),
            '_Cr_This_fwbciFFaycc'                                                                   => __('This form will be created in Fluent Forms and you can customize anytime', 'fluent-crm'),
            '_Cr_Your_fhbcs'                                                                         => __('Your form has been created successfully', 'fluent-crm'),
            '_Cu_Estimated_Cboys'                                                                    => __('Estimated Contacts based on your selections:', 'fluent-crm'),
            '_In_Wfdc'                                                                               => __(' - Waiting for double-optin confirmation', 'fluent-crm'),
            '_In_Wfdoc'                                                                              => __(' - Waiting for double opt-in confirmation', 'fluent-crm'),
            '_In_Wfna'                                                                               => __(' - Waiting for next action', 'fluent-crm'),
            '_Su_All_SLashbaSttsE'                                                                   => __('All Selected List and subscribers has been added Successfully to this sequence. Emails will be sent as per your configuration', 'fluent-crm'),
            '_Su_Invalid_solatisi'                                                                   => __('Invalid selection of lists and tags in subscribers included.', 'fluent-crm'),
            '_Su_Please_ntsewbstt'                                                                   => __('Please note that, sequences emails will be scheduled to the contacts as per the current state', 'fluent-crm'),
            '_Su_Subscribers_hbas'                                                                   => __('Subscribers have been added successfully to this sequence.', 'fluent-crm'),
            '_Su_Subscribers_hbat'                                                                   => __('Subscribers has been added to this email sequence', 'fluent-crm'),
            'action'                                                                                 => __('action', 'fluent-crm'),
            'actions'                                                                                => __('actions', 'fluent-crm'),
            'after'                                                                                  => __('after', 'fluent-crm'),
            'all_admin_managers_vue'                                                                 => __('All Administrators automatically will get full access of FluentCRM', 'fluent-crm'),
            'and manage subscription/unsubscribe url is mandatory'                                   => __('and manage subscription/unsubscribe url is mandatory', 'fluent-crm'),
            'associate data with FluentCRM'                                                          => __('associate data with FluentCRM', 'fluent-crm'),
            'at'                                                                                     => __('at', 'fluent-crm'),
            'before'                                                                                 => __('before', 'fluent-crm'),
            'before days'                                                                            => __('before days', 'fluent-crm'),
            'benchmark'                                                                              => __('benchmark', 'fluent-crm'),
            'block_does_not_exist'                                                                   => __('Looks like settings is missing for this block. Maybe the block does not exist', 'fluent-crm'),
            'bounced'                                                                                => __('Bounced', 'fluent-crm'),
            'business_settings.address_help'                                                         => __('Your Business Address will be used in Emails. The address is required as per anti-spam rules', 'fluent-crm'),
            'business_settings.logo_help'                                                            => __('Your Business Logo, It will be used in Public Facing pages', 'fluent-crm'),
            'business_settings.name_help'                                                            => __('Your Business name will be used in Emails, Unsubscribe Pages and public front interfaces', 'fluent-crm'),
            'cancel'                                                                                 => __('cancel', 'fluent-crm'),
            'cancelled'                                                                              => __('cancelled', 'fluent-crm'),
            'cleanup_old_data_like_email_log'                                                        => __('You may cleanup old data like email logs, email click and open logs easily using this tool.', 'fluent-crm'),
            'click here'                                                                             => __('click here', 'fluent-crm'),
            'click to upload'                                                                        => __('click to upload', 'fluent-crm'),
            'click_rate_info'                                                                        => __('How many contacts clicked at least one of the links in the email', 'fluent-crm'),
            'compare to'                                                                             => __('compare to', 'fluent-crm'),
            'complained'                                                                             => __('Complained', 'fluent-crm'),
            'completed'                                                                              => __('completed', 'fluent-crm'),
            'condition'                                                                              => __('condition', 'fluent-crm'),
            'conditional'                                                                            => __('conditional', 'fluent-crm'),
            'confirm'                                                                                => __('confirm', 'fluent-crm'),
            'copy_webhook_keys_intro'                                                                => __('Copy the keys in the right column and paste it into the app you want to use for sending the POST request, so it will send the data into the contact field you want.', 'fluent-crm'),
            'create_as_many_dynamic_segment_introduction'                                            => __('You can create as many dynamic segment as you want and send email campaign only a selected segment. It will let you target specific audiences and increase your conversion rate.', 'fluent-crm'),
            'create_dynamic_segments_of_contacts_introduction'                                       => __('Create dynamic Segments of contacts by using dynamic contact properties and filter your target audience. Available in pro version of Fluent CRM', 'fluent-crm'),
            'create_some_tags'                                                                       => __('Tags are like Segment Lists but you it will let you filter your contacts in more targeted way. Let\'s Create Some tags.', 'fluent-crm'),
            'crm_managers_roles_and_permissions'                                                     => __('Add More Users to your FluentCRM and give them selected permissions to manage your marketing. For example, You can add user X to view only the reports and User Y to manage only email campaigns.', 'fluent-crm'),
            'csv.download_desc'                                                                      => __('Please make sure your csv file has unique headers. Otherwise, it may fail to import', 'fluent-crm'),
            'custom_contact_fields'                                                                  => __('You may also use these custom contact fields. Copy the keys in the right column and paste it into the app just like other contact fields.', 'fluent-crm'),
            'custom_segment.name_desc'                                                               => __('Please provide a name so you can identify easily', 'fluent-crm'),
            'customer'                                                                               => __('customer', 'fluent-crm'),
            'date'                                                                                   => __('date', 'fluent-crm'),
            'days'                                                                                   => __('days', 'fluent-crm'),
            'delete_all_companies_notice'                                                            => __('All the associate data of the selected companies will be deleted', 'fluent-crm'),
            'delete_all_contacts_notice'                                                             => __('All the associate data of the selected contacts will be deleted', 'fluent-crm'),
            'delete_all_crm_specific_data'                                                           => __('Use this tool only and only if you understand fully. This will delete all your CRM specific data from the system', 'fluent-crm'),
            'delete_all_funnels_notice'                                                              => __('All the associate data of the selected funnels will be deleted', 'fluent-crm'),
            'delete_all_templates_notice'                                                            => __('All selected templates will be deleted', 'fluent-crm'),
            'delete_old_log_data_permanently_notice'                                                 => __('Are you sure about this action? If you confirm, Your old log data will be deleted permanently!', 'fluent-crm'),
            'documentation'                                                                          => __('documentation', 'fluent-crm'),
            'does not equal'                                                                         => __('does not equal', 'fluent-crm'),
            'does not includes'                                                                      => __('does not includes', 'fluent-crm'),
            'dont_have_license_key'                                                                  => __('Don\'t have a license key?', 'fluent-crm'),
            'draft'                                                                                  => __('draft', 'fluent-crm'),
            'edd_integration_instruction'                                                            => __('Add and segment your EDD customers when someone purchase a product. You can also send a welcome email to your first-time customers with an automated thank you email.Segment your customers and run automatic funnels on product sales and track the conversion rate of your marketing campaigns.', 'fluent-crm'),
            'edit'                                                                                   => __('edit', 'fluent-crm'),
            'eg: Active Contacts'                                                                    => __('eg: Active Contacts', 'fluent-crm'),
            'eg: Weekly Post Updated'                                                                => __('eg: Weekly Post Updated', 'fluent-crm'),
            'email_settings_subheading'                                                              => __('(Please use the name and email as per your domain/SMTP settings. Email mismatch settings may not deliver emails as expected)', 'fluent-crm'),
            'email_will_be_sent_without_footer_contents'                                             => __('If you check this email will be sent without footer contents including unsubscribe link. Please use smartcode to include unsubscribe link', 'fluent-crm'),
            'ends with'                                                                              => __('ends with', 'fluent-crm'),
            'equal'                                                                                  => __('equal', 'fluent-crm'),
            'failed'                                                                                 => __('failed', 'fluent-crm'),
            'fluentSMTP_requires_to_configure_properly'                                              => __('FluentSMTP requires to configure properly. Please configure FluentSMTP to make your email delivery works.', 'fluent-crm'),
            'fluentcrtm_integrations_with_all_of_plugins'                                            => __('FluentCRM has integrations with all of your favorite plugins. This settings will be available based on your plugin installed. These integrations are only available on the', 'fluent-crm'),
            'fluentform_info'                                                                        => __('Fluent Forms is fast and very light-weight and works perfectly with FluentCRM.', 'fluent-crm'),
            'fluentsmtp.desc'                                                                        => __('Looks like you did not install FluentSMTP yet. FluentSMTP is the the ultimate SMTP & Email Service Connection Plugin for WordPress.', 'fluent-crm'),
            'follow this tutorial'                                                                   => __('follow this tutorial', 'fluent-crm'),
            'for migrating from'                                                                     => __('for migrating from', 'fluent-crm'),
            'for sending your WordPress emails. This section is for you'                             => __('for sending your WordPress emails. This section is for you', 'fluent-crm'),
            'force_update_contact_status'                                                            => __('Force update contact status. This will update all imported contact status regardless their previous status', 'fluent-crm'),
            'has been completed.'                                                                    => __('has been completed.', 'fluent-crm'),
            'if it\'s already exist?'                                                                => __('if it\'s already exist?', 'fluent-crm'),
            'import_your_exported_automation_json_file'                                              => __('You can import your exported automation JSON file here. Please upload your JSON file to get started', 'fluent-crm'),
            'importing_funnel_from_json_file'                                                        => __('Importing Funnel from JSON file is a pro feature. Please download and active FluentCRM Pro First', 'fluent-crm'),
            'in the date'                                                                            => __('in the date', 'fluent-crm'),
            'in your'                                                                                => __('in your', 'fluent-crm'),
            'in your email body for compliance'                                                      => __('in your email body for compliance', 'fluent-crm'),
            'includes'                                                                               => __('includes', 'fluent-crm'),
            'includes all of'                                                                        => __('includes all of', 'fluent-crm'),
            'includes in'                                                                            => __('includes in', 'fluent-crm'),
            'includes in any of'                                                                     => __('includes in any of', 'fluent-crm'),
            'includes none of'                                                                       => __('includes none of', 'fluent-crm'),
            'install_fluentSMTP'                                                                     => __('Install FluentSMTP Plugin (It\'s free)', 'fluent-crm'),
            'install_fluentcrm_pro'                                                                  => __('This automation trigger is available on Pro version of FluentCRM. Please download and install FluentCRM Pro', 'fluent-crm'),
            'install_fluentform'                                                                     => __('Thank you again for configuring your own CRM in WordPress. To collect lead via form we are suggesting to install', 'fluent-crm'),
            'keep blank for current time'                                                            => __('keep blank for current time', 'fluent-crm'),
            'lead'                                                                                   => __('lead', 'fluent-crm'),
            'learn_about_fluentSMTP'                                                                 => __('For FluentCRM users, we built a well-optimized SMTP/Amazon SES plugin. It will help you manage all your WordPress website emails, including FluentCRM emails.', 'fluent-crm'),
            'learndash_integration_instruction'                                                      => __('Run funnels and marketing automation for different LMS actions like course enrollment, course completed, lesson completed, Group Membership Enrollment, etc.', 'fluent-crm'),
            'lifterlms_integration_instruction'                                                      => __('Run funnels and marketing automations for different LMS actions like course enrollment, course completed, lesson completed etc.', 'fluent-crm'),
            'list'                                                                                   => __('list', 'fluent-crm'),
            'lists'                                                                                  => __('lists', 'fluent-crm'),
            'maybeRemove'                                                                            => __('May be remove', 'fluent-crm'),
            'n/a'                                                                                    => __('n/a', 'fluent-crm'),
            'no_form_submissions_found_for_this_subscriber'                                          => __('will be shown here, Currently no Form Submissions found for this subscriber', 'fluent-crm'),
            'no_html_support'                                                                        => __('No HTML will be supported for this email. Additionally email tracking and link analytics will not be available', 'fluent-crm'),
            'no_tickets_found_for_this_subscriber'                                                   => __('will be shown here, Currently no tickets found for this subscriber', 'fluent-crm'),
            'not includes in'                                                                        => __('not includes in', 'fluent-crm'),
            'not includes in any'                                                                    => __('not includes in any', 'fluent-crm'),
            'older than'                                                                             => __('older than', 'fluent-crm'),
            'open a support ticket'                                                                  => __('open a support ticket', 'fluent-crm'),
            'open_rate_info'                                                                         => __('Open rate is estimated based on pixel loads. it may not show the correct analytics as many email clients load or block tracking regardless of the actual email open', 'fluent-crm'),
            'or'                                                                                     => __('or', 'fluent-crm'),
            'order'                                                                                  => __('order', 'fluent-crm'),
            'pending'                                                                                => __('Pending', 'fluent-crm'),
            'permanently'                                                                            => __('permanently', 'fluent-crm'),
            'permission.desc'                                                                        => __('Please contact your site administrator to request the permission', 'fluent-crm'),
            'permission.title'                                                                       => __('Looks like you do not have permission to view the requested resource.', 'fluent-crm'),
            'preview_email_info'                                                                     => __('Preview Email may differ from actual email that was sent. Specially the dynamic data is shown as it\'s current state. You may check in FluentSMTP\'s log to see the actual email', 'fluent-crm'),
            'pro version of FluentCRM'                                                               => __('pro version of FluentCRM', 'fluent-crm'),
            'processing'                                                                             => __('processing', 'fluent-crm'),
            'published'                                                                              => __('published', 'fluent-crm'),
            'published within'                                                                       => __('published within', 'fluent-crm'),
            'quick_links.ff_desc'                                                                    => __('Fluent Forms is a free form plugin for WordPress that is integrated with FluentCRM', 'fluent-crm'),
            'read the doc for advanced usage'                                                        => __('read the doc for advanced usage', 'fluent-crm'),
            'recommended_texts_default_aligned'                                                      => __('It is recommended to keep the texts as default aligned. Your provided email design template will align the texts', 'fluent-crm'),
            'reset_db_conf_warning'                                                                  => __('Are you sure, you want to reset the CRM Database?', 'fluent-crm'),
            'reset_db_warning'                                                                       => __('All Your Fluent CRM Data (Contacts, Campaigns, Settings, Emails) will be deleted', 'fluent-crm'),
            'rest_api_sub_heading'                                                                   => __('Create REST API Key for specific CRM Manager to access and manage data over API', 'fluent-crm'),
            'scheduled'                                                                              => __('scheduled', 'fluent-crm'),
            'segment_your_contacts'                                                                  => __('Segment your contacts by advanced data points like contact\'s properties, custom fields, tags, purchase history etc.', 'fluent-crm'),
            'select_current_crm_software'                                                            => __('Select Your Current CRM / Email Marketing software', 'fluent-crm'),
            'send_double_optin_contacts_notice'                                                      => __('All the associate data of the selected contacts will get a double optin mail', 'fluent-crm'),
            'sent'                                                                                   => __('sent', 'fluent-crm'),
            'server_does_not_support'                                                                => __('Looks like your server does not support PUT and DELETE REST api Requests. Maybe It\'s blocking from your server firewall', 'fluent-crm'),
            'set_maximum_emails_will_be_sent_per_second'                                             => __('Set maximum emails will be sent per second. Set this number based on your email service provider', 'fluent-crm'),
            'setup_lists_to_segment_your_conntacts'                                                  => __('You can setup your lists to segment your contacts. For Example VIP Customers, Product Users, WordPress Users etc', 'fluent-crm'),
            'simple_enough_promo_dynamic_segment'                                                    => __('Simple enough to be quick to use, but powerful enough to really filter down your contacts into target segments', 'fluent-crm'),
            'starts with'                                                                            => __('starts with', 'fluent-crm'),
            'status'                                                                                 => __('status', 'fluent-crm'),
            'statuses'                                                                               => __('statuses', 'fluent-crm'),
            'step saved'                                                                             => __('step saved', 'fluent-crm'),
            'still_cant_find_the_answer'                                                             => __('first. If you still can\'t find the answer', 'fluent-crm'),
            'street, state, zip, country'                                                            => __('street, state, zip, country', 'fluent-crm'),
            'subscribed'                                                                             => __('Subscribed', 'fluent-crm'),
            'subscribed contacts'                                                                    => __('subscribed contacts', 'fluent-crm'),
            'subscribers_frontpage_email_campaign'                                                   => __('This will be used for your email campaign, Subscriber\'s front pages', 'fluent-crm'),
            'tag'                                                                                    => __('tag', 'fluent-crm'),
            'tags'                                                                                   => __('tags', 'fluent-crm'),
            'testEmail.instruction'                                                                  => __('Type custom email to send test or leave blank to send current user email', 'fluent-crm'),
            'thankyou_for_using_fluentcrm'                                                           => __('Thank you for choosing FluentCRM. An easier way to manage email marketing campaigns! This quick setup wizard will help you configure the basic settings.', 'fluent-crm'),
            'the email will be triggered from the starting date'                                     => __('the email will be triggered from the starting date', 'fluent-crm'),
            'this value will be skipped'                                                             => __('this value will be skipped', 'fluent-crm'),
            'time unit'                                                                              => __('time unit', 'fluent-crm'),
            'to enable this feature'                                                                 => __('to enable this feature', 'fluent-crm'),
            'to insert dynamic tags'                                                                 => __('to insert dynamic tags', 'fluent-crm'),
            'to insert post/page links'                                                              => __('to insert post/page links', 'fluent-crm'),
            'to make this feature work'                                                              => __('to make this feature work', 'fluent-crm'),
            'to re-sync the data again'                                                              => __('to re-sync the data again', 'fluent-crm'),
            'to see All Available Blocks'                                                            => __('to see All Available Blocks', 'fluent-crm'),
            'to see all the available blocks'                                                        => __('to see all the available blocks', 'fluent-crm'),
            'to see smart tags'                                                                      => __('to see smart tags', 'fluent-crm'),
            'total'                                                                                  => __('total', 'fluent-crm'),
            'transfer_data_from_current_ems_to_fluentcrm'                                            => __('Transfer your contacts, tags and associate data from your current email marketing software to FluentCRM in minutes', 'fluent-crm'),
            'tutorlms_integration_instruction'                                                       => __('Run funnels and marketing automations for different TutorLMS actions like course enrollment, course completed etc.', 'fluent-crm'),
            'type and press enter'                                                                   => __('type and press enter', 'fluent-crm'),
            'type days'                                                                              => __('type days', 'fluent-crm'),
            'unknown error. Please check your csv first'                                             => __('unknown error. Please check your csv first', 'fluent-crm'),
            'unsubscribed'                                                                           => __('Unsubscribed', 'fluent-crm'),
            'update_subscribers_data_notice'                                                         => __('Do you want to update the subscribers data if it\'s already exist?', 'fluent-crm'),
            'use_the_predefined_dynamic_segment_introduction'                                        => __('Use the Pre-Defined Dynamic Segment which will give your WordPress user lists or Ecommerce Customers or even Affiliates.', 'fluent-crm'),
            'using_gridpane'                                                                         => __('If you use GridPane, Please follow the following tutorial or contact with your hosting provider', 'fluent-crm'),
            'using_old_wordpress_version'                                                            => __('Looks like you are using Old version of WordPress. Use at-least version 5.4 to use Smart Email Editor powered by Block Editor', 'fluent-crm'),
            'verified_email_help'                                                                    => __('email as per your domain/SMTP settings. Email mismatch settings may not deliver emails as expected', 'fluent-crm'),
            'website url'                                                                            => __('Website URL', 'fluent-crm'),
            'welcome_to_fluentcrm_return_to_wordpress_dashboard'                                     => __('No time right now? If you don’t want to go through the wizard, you can skip and return to the WordPress dashboard. Come back anytime if you change your mind!', 'fluent-crm'),
            'what we collect'                                                                        => __('what we collect', 'fluent-crm'),
            'what_we_collect_infos'                                                                  => __('Server environment details (php, mysql, server, WordPress versions), crm usage, Site language, Number of active and inactive plugins, Site name and url, Your name and email address. No sensitive data is tracked.', 'fluent-crm'),
            'will be created automatically in FluentCRM'                                             => __('will be created automatically in FluentCRM', 'fluent-crm'),
            'will be deleted'                                                                        => __('will be deleted', 'fluent-crm'),
            'will be replaced with dynamic values.'                                                  => __('will be replaced with dynamic values.', 'fluent-crm'),
            'with API key'                                                                           => __('with API key', 'fluent-crm'),
            'with_fluentcrm_pro_integrate_with_other_plugins'                                        => __('With FluentCRM Pro, you can integrate with other plugins like E-commerce, Membership, LMS (25+ integrations) along with advanced reports, sequence (drip) emails, advanced automation and unlock lots of useful features.', 'fluent-crm'),
            'within days'                                                                            => __('within days', 'fluent-crm'),
            '__ENABLE_SYSTEM_LOG' => __('Enable System Log for debugging', 'fluent-crm'),
            'Abandon Carts - Reports'                                                                => __('Abandon Carts - Reports', 'fluent-crm'),
            'Search by Email'                                                                        => __('Search by Email', 'fluent-crm'),
            'Recovered Orders'                                                                       => __('Recovered Orders', 'fluent-crm'),
            'Lost Orders'                                                                            => __('Lost Orders', 'fluent-crm'),
            'Shipping'                                                                               => __('Shipping', 'fluent-crm'),
            'Total'                                                                                  => __('Total', 'fluent-crm'),
            'Popular pre-built funnel templates'                                                     => __('Popular pre-built funnel templates', 'fluent-crm'),
            'Back To Templates'                                                                      => __('Back To Templates', 'fluent-crm'),
            'Date-Time'                                                                              => __('Date-Time', 'fluent-crm'),
            'From'                                                                                   => __('From', 'fluent-crm'),
            'Resent Count'                                                                           => __('Resent Count', 'fluent-crm'),
            'Mailer'                                                                                 => __('Mailer', 'fluent-crm'),
            'Server Response'                                                                        => __('Server Response', 'fluent-crm'),
            'Email Headers'                                                                          => __('Email Headers', 'fluent-crm'),
            'Attachments'                                                                            => __('Attachments', 'fluent-crm'),
            'Status for New Contacts'                                                                => __('Status for New Contacts', 'fluent-crm'),
            'Cool-Off Period'                                                                        => __('Cool-Off Period', 'fluent-crm'),
            'Mark as Lost after'                                                                     => __('Mark as Lost after', 'fluent-crm'),
            'Enable Abandoned Cart Tracking for WooCommerce'                                          => __('Enable Abandoned Cart Tracking for WooCommerce', 'fluent-crm'),
            'Abandoned Cart Settings'                                                                => __('Abandoned Cart Settings', 'fluent-crm'),
            'Cart Abandoned Cut-off Time'                                                            => __('Cart Abandoned Cut-off Time', 'fluent-crm'),
            'Mark Cart as Recovered when WooCommerce Order Status Changes to:'                       => __('Mark Cart as Recovered when WooCommerce Order Status Changes to:', 'fluent-crm'),
            'No Shipping Address found'                                                              => __('No Shipping Address found', 'fluent-crm'),
            'Set up an automation'                                                                   => __('Set up an automation', 'fluent-crm'),
            'No active Abandoned Cart automation.'                                                   => __('No active Abandoned Cart automation.', 'fluent-crm'),
            'Site URL'                                                                               => __('Site URL', 'fluent-crm'),
            'Pause/Run'                                                                              => __('Pause/Run', 'fluent-crm'),
            'Export Campaign'                                                                        => __('Export Campaign', 'fluent-crm'),
            'Recurring_Campaign_Export_Alert'                                                        => __('Recurring Campaign export is only available on pro version of FluentCRM', 'fluent-crm'),
            'Campaign_Export_Alert'                                                        => __('Campaign export is only available on pro version of FluentCRM', 'fluent-crm'),
            'Import_Recurring_Campaign'                                                              => __('Import your exported recurring email campaigns JSON file here. Please upload your JSON file.', 'fluent-crm'),
            'Import_Email_Campaign'                                                              => __('Import your exported email campaigns JSON file here. Please upload your JSON file.', 'fluent-crm'),
            'Not_Import_Recurring_Campaigns_Alert'                                                   => __('Do not import recurring campaigns from untrusted sources.', 'fluent-crm'),
            'Not_Import_Email_Campaigns_Alert'                                                   => __('Do not import email campaigns from untrusted sources.', 'fluent-crm'),
            'Tax(es)'                                                                                => __('Tax(es)', 'fluent-crm'),
            'Order Comments'                                                                         => __('Order Comments', 'fluent-crm'),
            'Recovery URL'                                                                           => __('Recovery URL', 'fluent-crm'),
            'View Original Order'                                                                    => __('View Original Order', 'fluent-crm'),
            'All carts'                                                                              => __('All carts', 'fluent-crm'),
            'Draft Carts'                                                                            => __('Draft Carts', 'fluent-crm'),
            'Image'                                                                                  => __('Image', 'fluent-crm'),
            'In Progress'                                                                            => __('In Progress', 'fluent-crm'),
            'Recovered Carts'                                                                        => __('Recovered Carts', 'fluent-crm'),
            'Lost Carts'                                                                             => __('Lost Carts', 'fluent-crm'),
            'Opt-Out Carts'                                                                          => __('Opt-Out Carts', 'fluent-crm'),
            'Skipped Carts'                                                                          => __('Skipped Carts', 'fluent-crm'),
            'Love this Plugin?'                                                                      => __('Love this Plugin?', 'fluent-crm'),
            'Request_review_Desc'                                                                   => __('We would greatly appreciate it if you could leave a review for us in the WordPress plugin repository!', 'fluent-crm'),
            'Write a Review'                                                                         => __('Write a Review', 'fluent-crm'),
            'Please provide Remote URL'                                                             => __('Please provide Remote URL', 'fluent-crm'),
            'Send Test Webhook'                                                                     => __('Send Test Webhook', 'fluent-crm'),
            'Filter By Label'                                                                        => __('Filter By Label', 'fluent-crm'),
            'Manage Labels'                                                                          => __('Manage Labels', 'fluent-crm'),
            'Labels'                                                                                 => __('Labels', 'fluent-crm'),
            'Funnel Labels'                                                                          => __('Funnel Labels', 'fluent-crm'),
            'Create New Label'                                                                       => __('Create New Label', 'fluent-crm'),
            'Label Name'                                                                             => __('Label Name', 'fluent-crm'),
            'INACTIVE'                                                                               => __('INACTIVE', 'fluent-crm'),
            'Label name is required'                                                                 => __('Label name is required', 'fluent-crm'),
            'Automation Settings'                                                                    => __('Automation Settings', 'fluent-crm'),
            'Remove_Label_From_funnel_Message'                                                       => __('Are you sure to remove this label from the funnel?', 'fluent-crm'),
            'Edit Label'                                                                             => __('Edit Label', 'fluent-crm'),
            'Label Color'                                                                            => __('Label Color', 'fluent-crm'),
            'Back to Labels'                                                                         => __('Back to Labels', 'fluent-crm'),
            'Remove_Label_From_campaign_Message'                                                     => __('Are you sure to remove this label from the campaign?', 'fluent-crm'),
            'System Labels'                                                                          => __('System Labels', 'fluent-crm'),
            'Type User Email Address'                                                                => __('Type User Email Address', 'fluent-crm'),
            'No user found with your query.'                                                         => __('No user found with your query', 'fluent-crm'),
            'Cart Total'                                                                            => __('Cart Total', 'fluent-crm'),
            'Order Status'                                                                          => __('Order Status', 'fluent-crm'),
            'Time'                                                                                  => __('Time', 'fluent-crm'),
            'Auto Login'                                                                            => __('Auto Login', 'fluent-crm'),
            'Exporting_archived_campaign_Emails_alert'                                              => __('Exporting archived campaign emails is only available in the Pro version of FluentCRM', 'fluent-crm'),
            'Never Opened'                                                                          => __('Never Opened', 'fluent-crm'),
            'Never Clicked'                                                                         => __('Never Clicked', 'fluent-crm'),
            'Share Newsletter via URL'                                                              => __('Share Newsletter via URL', 'fluent-crm'),
            'Share_Newsletter_Desc'                                                                 => __('Share this URL with anyone to view this newsletter online.', 'fluent-crm'),
            'Copy'                                                                                  => __('Copy', 'fluent-crm'),
            'Search blocks, e.g., email, apply tags, etc.'                                          => __('Search blocks, e.g., email, apply tags, etc.', 'fluent-crm'),
            'No Blocks Found'                                                                       => __('No Blocks Found', 'fluent-crm'),
            'Leave it blank to display all campaigns'                                               => __('Leave it blank to display all campaigns', 'fluent-crm'),
            'Select Campaigns'                                                                      => __('Select Campaigns', 'fluent-crm'),
            'Update Custom Fields'                                                                  => __('Update Custom Fields', 'fluent-crm'),
            'Select Field'                                                                          => __('Select Field', 'fluent-crm'),
            'Flexible Contact Behavior Analytic'                                                    => __('Flexible Contact Behavior Analytic', 'fluent-crm'),
            'Frontend Showcase with Shortcode'                                                      => __('Frontend Showcase with Shortcode', 'fluent-crm'),
            'Business Contact Management'                                                           => __('Business Contact Management', 'fluent-crm'),
            'Advanced_Feature_And_Integrations_Desc'                                                => __('Enable/Disable FluentCrm\'s Advanced features or integrations.', 'fluent-crm'),
            'Plugins that will extend your FluentCrm Functionalities'                               => __('Plugins that will extend your FluentCrm Functionalities', 'fluent-crm'),
            'Sent By'                                                                               => __('Sent By', 'fluent-crm'),
            'Search shortcodes...'                                                                  => __('Search shortcodes...', 'fluent-crm'),
            'Click to edit'                                                                         => __('Click to edit', 'fluent-crm'),
            'Type new option'                                                                       => __('Type new option', 'fluent-crm'),
            'New Automation'                                                                        => __('New Automation', 'fluent-crm'),
            'Activities'                                                                            => __('Activities', 'fluent-crm'),
            'Label Title'                                                                           => __('Label Title', 'fluent-crm'),
            'Advanced Typography'                                                                   => __('Advanced Typography', 'fluent-crm'),
            'Advanced Spacing'                                                                      => __('Advanced Spacing', 'fluent-crm'),
            'Auto_Login_Label'                                                                      => __('Enable auto login (for connected wp user) when this smart links is being clicked from email', 'fluent-crm'),
            'Apply Tags when clicked (optional)'                                                    => __('Apply Tags when clicked (optional)', 'fluent-crm'),
            'Coupon Code Prefix'                                                                    => __('Coupon Code Prefix', 'fluent-crm'),
            'Coupon_Code_Prefix_help'                                                               => sprintf(__("FluentCRM will generate a random coupon code for each contact. Use a prefix to make something like '%s'.", 'fluent-crm'), 'YOUR_PREFIX-XYZHS4'),
            'Dynamic_Coupon_Usage'                                                                  => __('This dynamic coupon can be used in emails or other actions as:', 'fluent-crm'),
            'Configure from scratch'                                                                => __('Configure from scratch', 'fluent-crm'),
            'Use Existing Coupon as Template'                                                       => __('Use Existing Coupon as Template', 'fluent-crm'),
            'Select your existing Coupon Code'                                                      => __('Select your existing Coupon Code', 'fluent-crm'),
            'Dynamic_Coupon_Configuration'                                                          => __('The selected coupon configuration will be used when creating the dynamic coupon', 'fluent-crm'),
            'Percentage Discount'                                                                   => __('Percentage Discount', 'fluent-crm'),
            'Fixed Cart Discount'                                                                   => __('Fixed Cart Discount', 'fluent-crm'),
            'Fixed Product Discount'                                                                => __('Fixed Product Discount', 'fluent-crm'),
            'Discount Type'                                                                         => __('Discount Type', 'fluent-crm'),
            'Amount'                                                                                => __('Amount', 'fluent-crm'),
            'Coupon Expiry'                                                                         => __('Coupon Expiry', 'fluent-crm'),
            'Choose when the coupon will expire'                                                    => __('Choose when the coupon will expire', 'fluent-crm'),
            'Never Expires'                                                                         => __('Never Expires', 'fluent-crm'),
            'Fixed Date'                                                                            => __('Fixed Date', 'fluent-crm'),
            'Expire after x days of creation'                                                       => __('Expire after x days of creation', 'fluent-crm'),
            'Expiry Date'                                                                           => __('Expiry Date', 'fluent-crm'),
            'Expire after x days'                                                                   => __('Expire after x days', 'fluent-crm'),
            'Allow Free Shipping'                                                                   => __('Allow Free Shipping', 'fluent-crm'),
            'Free_Shipping_Info'                                                                    => __('Use "Yes" if the coupon grants free shipping. A free shipping method must be enabled in your shipping zone and be set to require "a valid free shipping coupon"', 'fluent-crm'),
            'Coupon settings will be inherited from the selected base coupon'                       => __('Coupon settings will be inherited from the selected base coupon', 'fluent-crm'),
            'Restrict the generated coupon to Contact Email Only'                                   => __('Restrict the generated coupon to Contact Email Only', 'fluent-crm'),
            'Restrictions & Limits'                                                                 => __('Restrictions & Limits', 'fluent-crm'),
            'Minimum Spend'                                                                         => __('Minimum Spend', 'fluent-crm'),
            'Minimum_Spend_Requirement'                                                             => __('This field allows you to set the minimum spend (subtotal) allowed to use the coupon.', 'fluent-crm'),
            'Maximum Spend'                                                                         => __('Maximum Spend', 'fluent-crm'),
            'Maximum_Spend_Limit'                                                                   => __('This field allows you to set the maximum spend (subtotal) allowed when using the coupon.', 'fluent-crm'),
            'Products'                                                                              => __('Products', 'fluent-crm'),
            'Eligible_Products_For_Discount' => __('Products that the coupon will be applied to, or that need to be in the cart in order for the "Fixed cart discount" to be applied.', 'fluent-crm'),
            'Exclude Products'                                                                      => __('Exclude Products', 'fluent-crm'),
            'Excluded_Products_For_Discount'                                                        => __('Products that the coupon will not be applied to, or that need to be in the cart in order for the "Fixed cart discount" to be applied.', 'fluent-crm'),
            'Product categories'                                                                    => __('Product categories', 'fluent-crm'),
            'Eligible_Product_Categories_For_Discount'                                              => __('Product categories that the coupon will be applied to, or that need to be in the cart in order for the "Fixed cart discount" to be applied.', 'fluent-crm'),
            'Exclude Product categories'                                                            => __('Exclude Product categories', 'fluent-crm'),
            'Excluded_Product_Categories_For_Discount'                                              => __('Product categories that the coupon will not be applied to, or that need to be in the cart in order for the "Fixed cart discount" to be applied.', 'fluent-crm'),
            'Individual_Coupon_Use_info'                                                            => __('Individual use only (Check this box if the coupon cannot be used in conjunction with other coupons.)', 'fluent-crm'),
            'Exclude_Sale_Items'                                                                    => __('Exclude sale items (Check this box if the coupon should not apply to items on sale.)', 'fluent-crm'),
            'Limits'                                                                                => __('Limits', 'fluent-crm'),
            'Usage limit per coupon'                                                                => __('Usage limit per coupon', 'fluent-crm'),
            'How many times this coupon can be used before it is void.'                             => __('How many times this coupon can be used before it is void.', 'fluent-crm'),
            'Unlimited Usage'                                                                       => __('Unlimited Usage', 'fluent-crm'),
            'Coupon_Max_items_For_Discount'                                                         => __('The maximum number of individual items this coupon can apply to when using product discounts. Leave blank to apply to all qualifying items in cart.', 'fluent-crm'),
            'Apply to all qualifying items in cart'                                                 => __('Apply to all qualifying items in cart', 'fluent-crm'),
            'Usage limit per user'                                                                  => __('Usage limit per user', 'fluent-crm'),
            'Coupon_Usage_Limit_Per_User'                                                           => __('How many times this coupon can be used by an individual user. Uses billing email for guests, and user ID for logged in users.', 'fluent-crm'),
            'Coupon_Inherited_Restrictions_And_Limits'                                              => __('Coupon Restrictions & Limits settings will be inherited from the selected base coupon', 'fluent-crm'),
            'Create from Scratch'                                                                   => __('Create from Scratch', 'fluent-crm'),
            'Copy Email'                                                                            => __('Copy Email', 'fluent-crm'),
            'Copy Phone Number'                                                                     => __('Copy Phone Number', 'fluent-crm'),
            'Copied'                                                                                => __('Copied', 'fluent-crm'),
            'Nothing to copy'                                                                       => __('Nothing to copy', 'fluent-crm'),
            'Built In Templates'                                                                    => __('Built In Templates', 'fluent-crm'),
            'No Built In Templates Found'                                                           => __('No Built In Templates Found', 'fluent-crm'),
            'See Built In Templates'                                                                => __('See Built In Templates', 'fluent-crm'),
            'Recover Carts & Pending Orders'                                                        => __('Recover abandoned carts and pending orders for WooCommerce to improve your store\'s revenue.', 'fluent-crm'),
            'Abandoned_Cart_requires_WooCommerce'                                                   => __('Abandoned Cart Feature is available for WooCommerce. Looks like you don\'t have WooCommerce Installed', 'fluent-crm'),
            'Minutes_before_cart_is_marked_recoverable'                                             => __('After how many minutes, the cart will be marked as recoverable', 'fluent-crm'),
            'AB_Cart_Mark_cart_as_lost_after_days'                                                  => __('Mark the cart as Lost if the order is not made within the given days', 'fluent-crm'),
            'AB_Cart_Exclude_customers_from_tracking_for_days_after_order'                          => __('Specify the number of days to exclude your customers from abandoned cart tracking in the case they placed an order.', 'fluent-crm'),
            'Status for your new contacts who are not exist in FluentCRM Database'                  => __('Status for your new contacts who are not exist in FluentCRM Database', 'fluent-crm'),
            'GDPR Consent'                                                                          => __('GDPR Consent', 'fluent-crm'),
            'GDPR Message'                                                                          => __('GDPR Message', 'fluent-crm'),
            'User'                                                                                  => __('User', 'fluent-crm'),
            'Disable cart tracking for selected user roles'                                         => __('Choose the user roles for which you want to disable cart tracking', 'fluent-crm'),
            'Disable Tracking For the User Roles'                                                   => __('Disable Tracking For the User Roles', 'fluent-crm'),
            'Track carts when a product is added to the cart for logged-in users'                   => __('Track carts when a product is added to the cart for logged-in users', 'fluent-crm'),
            'Select Roles'                                                                          => __('Select Roles', 'fluent-crm'),
            'Contact Tagging - Cart Abandoned'                                                      => __('Contact Tagging - Cart Abandoned', 'fluent-crm'),
            'Select Lists (Optional)'                                                               => __('Select Lists (Optional)', 'fluent-crm'),
            'Select Tags (Optional)'                                                                => __('Select Tags (Optional)', 'fluent-crm'),
            'Selected list(s) added when cart is abandoned. Removed on successful order.'           => __('Selected lists(s) will be added when cart is marked as abandoned (starting the target automation). These will be automatically removed on successful order', 'fluent-crm'),
            'Selected tag(s) added when cart is abandoned. Removed on successful order.'            => __('Selected tag(s) will be added when cart is marked as abandoned (starting the target automation). These will be automatically removed on successful order', 'fluent-crm'),
            'Contact Tagging - Cart Lost'                                                           => __('Contact Tagging - Cart Lost', 'fluent-crm'),
            'Selected list(s) added when cart is lost. Removed on successful order.'                => __('Selected lists(s) will be added when cart is lost. These will be automatically removed on successful order', 'fluent-crm'),
            'Add Lists on Cart Lost'                                                                => __('Add Lists on Cart Lost', 'fluent-crm'),
            'Add Tags on Cart Lost'                                                                 => __('Add Tags on Cart Lost', 'fluent-crm'),
            'Selected tag(s) added when cart is lost. Removed on successful order.'                 => __('Selected tag(s) will be added when cart is lost. These will be automatically removed on successful order', 'fluent-crm'),
            'Add Tags on Cart Abandoned'                                                            => __('Add Tags on Cart Abandoned', 'fluent-crm'),
            'Add Lists on Cart Abandoned'                                                           => __('Add Lists on Cart Abandoned', 'fluent-crm'),
            'Mark_Transactional'                                                                    => __('Mark this as Transactional Email', 'fluent-crm'),
            'Double Optin Settings'                                                                 => __('Double Optin Settings', 'fluent-crm'),
            'transaction_checkbox_note'                                                             => __('If enabled no email footer or unsubscribe link will be added to the email', 'fluent-crm'),
        ];
    }
}
