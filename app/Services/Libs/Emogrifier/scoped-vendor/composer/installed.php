<?php return array(
    'root' => array(
        'name' => '__root__',
        'pretty_version' => 'dev-master',
        'version' => 'dev-master',
        'reference' => 'ca1f0cfca7d137d392980237881ddb455c77e351',
        'type' => 'library',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        '__root__' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => 'ca1f0cfca7d137d392980237881ddb455c77e351',
            'type' => 'library',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'pelago/emogrifier' => array(
            'pretty_version' => 'v7.2.0',
            'version' => '7.2.0.0',
            'reference' => '727bdf7255b51798307f17dec52ff8a91f1c7de3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../pelago/emogrifier',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sabberworm/php-css-parser' => array(
            'pretty_version' => 'v8.6.0',
            'version' => '8.6.0.0',
            'reference' => 'd2fb94a9641be84d79c7548c6d39bbebba6e9a70',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sabberworm/php-css-parser',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/css-selector' => array(
            'pretty_version' => 'v7.1.1',
            'version' => '7.1.1.0',
            'reference' => '1c7cee86c6f812896af54434f8ce29c8d94f9ff4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/css-selector',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
