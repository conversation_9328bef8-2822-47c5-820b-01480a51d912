<?php

// autoload_real.php @generated by Composer

class FluentEmogComposerAutoloaderInit6ba88f1695515329cfc7e4b26033cc69
{
    private static $loader;

    public static function loadClassLoader($class)
    {
        if ('Composer\Autoload\ClassLoader' === $class) {
            require __DIR__ . '/ClassLoader.php';
        }
    }

    /**
     * @return \Composer\Autoload\ClassLoader
     */
    public static function getLoader()
    {
        if (null !== self::$loader) {
            return self::$loader;
        }

        spl_autoload_register(array('FluentEmogComposerAutoloaderInit6ba88f1695515329cfc7e4b26033cc69', 'loadClassLoader'), true, true);
        self::$loader = $loader = new \Composer\Autoload\ClassLoader(\dirname(__DIR__));
        spl_autoload_unregister(array('FluentEmogComposerAutoloaderInit6ba88f1695515329cfc7e4b26033cc69', 'loadClassLoader'));

        require __DIR__ . '/autoload_static.php';
        call_user_func(\Composer\Autoload\ComposerFluentStaticInit6ba88f1695515329cfc7e4b26033cc69::getInitializer($loader));

        $loader->register(true);

        return $loader;
    }
}
