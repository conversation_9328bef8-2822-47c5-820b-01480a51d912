{"packages": [{"name": "pelago/emogrifier", "version": "v7.2.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/MyIntervals/emogrifier.git", "reference": "727bdf7255b51798307f17dec52ff8a91f1c7de3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MyIntervals/emogrifier/zipball/727bdf7255b51798307f17dec52ff8a91f1c7de3", "reference": "727bdf7255b51798307f17dec52ff8a91f1c7de3", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "php": "~7.3.0 || ~7.4.0 || ~8.0.0 || ~8.1.0 || ~8.2.0 || ~8.3.0", "sabberworm/php-css-parser": "^8.4.0", "symfony/css-selector": "^4.4.23 || ^5.4.0 || ^6.0.0 || ^7.0.0"}, "require-dev": {"php-parallel-lint/php-parallel-lint": "1.3.2", "phpunit/phpunit": "9.6.11", "rawr/cross-data-providers": "2.4.0"}, "time": "2023-12-06T02:00:20+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "8.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Pelago\\Emogrifier\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}], "description": "Converts CSS styles into inline style attributes in your HTML code", "homepage": "https://www.myintervals.com/emogrifier.php", "keywords": ["css", "email", "pre-processing"], "support": {"issues": "https://github.com/MyIntervals/emogrifier/issues", "source": "https://github.com/MyIntervals/emogrifier"}, "install-path": "../pelago/emogrifier"}, {"name": "sabberworm/php-css-parser", "version": "v8.6.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/MyIntervals/PHP-CSS-Parser.git", "reference": "d2fb94a9641be84d79c7548c6d39bbebba6e9a70"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MyIntervals/PHP-CSS-Parser/zipball/d2fb94a9641be84d79c7548c6d39bbebba6e9a70", "reference": "d2fb94a9641be84d79c7548c6d39bbebba6e9a70", "shasum": ""}, "require": {"ext-iconv": "*", "php": ">=5.6.20"}, "require-dev": {"phpunit/phpunit": "^5.7.27"}, "suggest": {"ext-mbstring": "for parsing UTF-8 CSS"}, "time": "2024-07-01T07:33:21+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "9.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Sabberworm\\CSS\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Parser for CSS Files written in PHP", "homepage": "https://www.sabberworm.com/blog/2010/6/10/php-css-parser", "keywords": ["css", "parser", "stylesheet"], "support": {"issues": "https://github.com/MyIntervals/PHP-CSS-Parser/issues", "source": "https://github.com/MyIntervals/PHP-CSS-Parser/tree/v8.6.0"}, "install-path": "../sabberworm/php-css-parser"}, {"name": "symfony/css-selector", "version": "v7.1.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/css-selector.git", "reference": "1c7cee86c6f812896af54434f8ce29c8d94f9ff4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/css-selector/zipball/1c7cee86c6f812896af54434f8ce29c8d94f9ff4", "reference": "1c7cee86c6f812896af54434f8ce29c8d94f9ff4", "shasum": ""}, "require": {"php": ">=8.2"}, "time": "2024-05-31T14:57:53+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\CssSelector\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Converts CSS selectors to XPath expressions", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/css-selector/tree/v7.1.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/css-selector"}], "dev": true, "dev-package-names": []}