{"name": "league/csv", "type": "library", "description": "Csv data manipulation made easy in PHP", "keywords": ["csv", "import", "export", "read", "write", "filter"], "license": "MIT", "homepage": "http://csv.thephpleague.com", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/nyamsprod/", "role": "Developer"}], "support": {"forum": "https://groups.google.com/forum/#!forum/thephpleague", "issues": "https://github.com/thephpleague/csv/issues"}, "require": {"php": ">=5.4.0", "ext-mbstring": "*"}, "require-dev": {"phpunit/phpunit": "^4.0", "fabpot/php-cs-fixer": "^1.9"}, "autoload": {"psr-4": {"League\\Csv\\": "src"}}, "autoload-dev": {"psr-4": {"League\\Csv\\Test\\": "test", "lib\\": "examples\\lib"}}, "scripts": {"test": "vendor/bin/phpunit; vendor/bin/php-cs-fixer fix -v --diff --dry-run;"}, "extra": {"branch-alias": {"dev-master": "7.2-dev"}}}