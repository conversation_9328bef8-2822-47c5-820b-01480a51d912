(()=>{"use strict";const e=window.wp.components;window.React;const t="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzM2IiBoZWlnaHQ9IjM2OSIgdmlld0JveD0iMCAwIDMzNiAzNjkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHg9IjI0IiB5PSIyNCIgd2lkdGg9IjI4OCIgaGVpZ2h0PSIyMTMiIGZpbGw9IiNFREVFRjQiLz4KPHJlY3QgeD0iMjQiIHk9IjI2MyIgd2lkdGg9IjIwMyIgaGVpZ2h0PSIxNiIgcng9IjgiIGZpbGw9IiNFREVFRjQiLz4KPHJlY3QgeD0iMjQiIHk9IjI5NCIgd2lkdGg9IjUxIiBoZWlnaHQ9IjEyIiByeD0iNiIgZmlsbD0iI0VERUVGNCIvPgo8cmVjdCB4PSIyNCIgeT0iMzI5IiB3aWR0aD0iNjIiIGhlaWdodD0iMTQiIHJ4PSI3IiBmaWxsPSIjRURFRUY0Ii8+CjxyZWN0IHg9IjEiIHk9IjEiIHdpZHRoPSIzMzQiIGhlaWdodD0iMzY3IiByeD0iOSIgc3Ryb2tlPSIjRURFRUY0IiBzdHJva2Utd2lkdGg9IjIiLz4KPC9zdmc+Cg==",n="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNzExIiBoZWlnaHQ9IjUwOCIgdmlld0JveD0iMCAwIDcxMSA1MDgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHg9IjMwIiB5PSIzMCIgd2lkdGg9IjI5OCIgaGVpZ2h0PSIyMTIiIGZpbGw9IiNFREVFRjQiLz4KPHJlY3QgeD0iMzY1IiB5PSI0MiIgd2lkdGg9IjIxNiIgaGVpZ2h0PSIxNiIgcng9IjgiIGZpbGw9IiNFREVFRjQiLz4KPHJlY3QgeD0iMzY1IiB5PSI3OCIgd2lkdGg9IjI5NiIgaGVpZ2h0PSIxMiIgcng9IjYiIGZpbGw9IiNFREVFRjQiLz4KPHJlY3QgeD0iMzY1IiB5PSIxMDEiIHdpZHRoPSIxNDkiIGhlaWdodD0iMTIiIHJ4PSI2IiBmaWxsPSIjRURFRUY0Ii8+CjxyZWN0IHg9IjM2NSIgeT0iMTMzIiB3aWR0aD0iNjIiIGhlaWdodD0iMTQiIHJ4PSI3IiBmaWxsPSIjRURFRUY0Ii8+CjxyZWN0IHg9IjM2NSIgeT0iMTczIiB3aWR0aD0iMzE2IiBoZWlnaHQ9IjU2IiBmaWxsPSIjRURFRUY0Ii8+CjxyZWN0IHg9IjMwIiB5PSIyNjYiIHdpZHRoPSIyOTgiIGhlaWdodD0iMjEyIiBmaWxsPSIjRURFRUY0Ii8+CjxyZWN0IHg9IjM2NSIgeT0iMjc4IiB3aWR0aD0iMjE2IiBoZWlnaHQ9IjE2IiByeD0iOCIgZmlsbD0iI0VERUVGNCIvPgo8cmVjdCB4PSIzNjUiIHk9IjMxNCIgd2lkdGg9IjI5NiIgaGVpZ2h0PSIxMiIgcng9IjYiIGZpbGw9IiNFREVFRjQiLz4KPHJlY3QgeD0iMzY1IiB5PSIzMzciIHdpZHRoPSIxNDkiIGhlaWdodD0iMTIiIHJ4PSI2IiBmaWxsPSIjRURFRUY0Ii8+CjxyZWN0IHg9IjM2NSIgeT0iMzY5IiB3aWR0aD0iNjIiIGhlaWdodD0iMTQiIHJ4PSI3IiBmaWxsPSIjRURFRUY0Ii8+CjxyZWN0IHg9IjM2NSIgeT0iNDA5IiB3aWR0aD0iMzE2IiBoZWlnaHQ9IjU2IiBmaWxsPSIjRURFRUY0Ii8+CjxyZWN0IHg9IjEiIHk9IjEiIHdpZHRoPSI3MDkiIGhlaWdodD0iNTA2IiByeD0iOSIgc3Ryb2tlPSIjRURFRUY0IiBzdHJva2Utd2lkdGg9IjIiLz4KPC9zdmc+Cg==",a="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzM2IiBoZWlnaHQ9IjMzMiIgdmlld0JveD0iMCAwIDMzNiAzMzIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHg9IjI0IiB5PSIyNCIgd2lkdGg9IjI4OCIgaGVpZ2h0PSIyMTMiIHJ4PSIxMCIgZmlsbD0iI0VERUVGNCIvPgo8cmVjdCB4PSI2NiIgeT0iMjYzIiB3aWR0aD0iMjAzIiBoZWlnaHQ9IjE2IiByeD0iOCIgZmlsbD0iI0VERUVGNCIvPgo8cmVjdCB4PSIxNDIiIHk9IjI5NCIgd2lkdGg9IjUxIiBoZWlnaHQ9IjEyIiByeD0iNiIgZmlsbD0iI0VERUVGNCIvPgo8cmVjdCB4PSIxIiB5PSIxIiB3aWR0aD0iMzM0IiBoZWlnaHQ9IjMzMCIgcng9IjkiIHN0cm9rZT0iI0VERUVGNCIgc3Ryb2tlLXdpZHRoPSIyIi8+Cjwvc3ZnPgo=";function r(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=Array(t);n<t;n++)a[n]=e[n];return a}var l=wp.blockEditor,o=l.InspectorControls,i=l.PanelColorSettings,__=wp.i18n.__,c=wp.components,u=c.PanelBody,I=c.PanelRow,s=c.SelectControl,d=c.ToggleControl,m=c.TextControl,p=c.Dropdown,g=c.Button;const y=function(l){var c,y,f=l.attributes,b=f.selectedLayout,R=f.order,h=f.orderBy,v=f.taxonomies,j=f.taxType,w=f.selectedPostsPerPage,P=f.showDescription,C=f.showPrice,S=f.showButton,E=f.buttonText,N=f.titleColor,M=f.priceColor,B=f.buttonColor,x=f.buttonBG,H=f.descriptionColor,D=l.setAttributes,Z=t;"layout-2"===b?Z=n:"layout-3"===b&&(Z=a);var G=[{value:N,onChange:function(e){D({titleColor:e})},label:__("Title Color")}];"layout-2"===b&&!0===P&&G.push({value:H,onChange:function(e){D({descriptionColor:e})},label:__("Description Color")}),!0===C&&G.push({value:M,onChange:function(e){D({priceColor:e})},label:__("Price Color")}),"layout-3"!==b&&!0===S&&G.push({value:B,onChange:function(e){D({buttonColor:e})},label:__("Button Color")}),"layout-2"===b&&!0===S&&G.push({value:x,onChange:function(e){D({buttonBG:e})},label:__("Button Background")});var z,W,O=[{value:"all",label:"All"}];return null!==(c=v.product)&&void 0!==c&&c.terms&&null!==(z=v.product)&&void 0!==z&&z.terms.product_cat&&(null===(W=v.product)||void 0===W||W.terms.product_cat.map((function(e){return O.push(e)}))),React.createElement(o,null,React.createElement(u,{title:"General Settings",initialOpen:!0},React.createElement(I,null,React.createElement("div",{className:"fc-latest-products-settings"},null!==(y=v.product)&&void 0!==y&&y.terms?React.createElement(s,{value:j,options:O,placeholder:__("Select Taxonomy","fluent-crm"),label:__("Select Taxonomy","fluent-crm"),clearable:!0,onChange:function(e){D({taxType:e})}}):null,React.createElement(s,{label:__("Order by","fluent-crm"),options:[{label:__("Newest to Oldest","fluent-crm"),value:"date/desc"},{label:__("Oldest to Newest","fluent-crm"),value:"date/asc"},{label:__("Modified Ascending","fluent-crm"),value:"modified/asc"},{label:__("Modified Descending","fluent-crm"),value:"modified/desc"},{label:__("A → Z","fluent-crm"),value:"title/asc"},{label:__("Z → A","fluent-crm"),value:"title/desc"},{label:__("Menu Order","fluent-crm"),value:"menu_order/asc"},{label:__("Random","fluent-crm"),value:"rand/desc"}],value:"".concat(h,"/").concat(R),onChange:function(e){var t,n,a=(t=e.split("/"),n=2,function(e){if(Array.isArray(e))return e}(t)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var a,r,l,o,i=[],c=!0,u=!1;try{if(l=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(a=l.call(n)).done)&&(i.push(a.value),i.length!==t);c=!0);}catch(e){u=!0,r=e}finally{try{if(!c&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(u)throw r}}return i}}(t,n)||function(e,t){if(e){if("string"==typeof e)return r(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?r(e,t):void 0}}(t,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),l=a[0],o=a[1];o!==R&&D({order:o}),l!==h&&D({orderBy:l})}}),React.createElement(m,{type:"number",className:"fce-dimension-box",value:w,label:__("Show Products"),help:"e.g how many products you want to show",onChange:function(e){return D({selectedPostsPerPage:e})}}),React.createElement("div",{className:"show-setting-control-box select-layout"},React.createElement("p",null,"Select Layout"),React.createElement(p,{className:"show-setting-dropdown",contentClassName:"my-popover-content-classname",position:"bottom right",renderToggle:function(e){var t=e.isOpen,n=e.onToggle;return React.createElement(React.Fragment,null,React.createElement("img",{onClick:n,"aria-expanded":t,src:Z,alt:""}),React.createElement(g,{variant:"primary",onClick:n,"aria-expanded":t},React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:"24",height:"24","aria-hidden":"true",focusable:"false"},React.createElement("path",{d:"M13 19h-2v-2h2v2zm0-6h-2v-2h2v2zm0-6h-2V5h2v2z"}))))},renderContent:function(r){var l=r.isOpen,o=r.onToggle;return React.createElement("div",{className:"dropdown-render-content dropdown-render-selected-layout"},React.createElement("p",null,"Select Layout"),React.createElement(e.__experimentalRadioGroup,{label:"Layout",value:b,defaultChecked:b,onChange:function(e){return D({selectedLayout:e})}},React.createElement(e.__experimentalRadio,{value:"default",onClick:o,"aria-expanded":l},React.createElement("img",{src:t,alt:"Default"})),React.createElement(e.__experimentalRadio,{value:"layout-2",onClick:o,"aria-expanded":l},React.createElement("img",{src:n,alt:"Layout 2"})),React.createElement(e.__experimentalRadio,{value:"layout-3",onClick:o,"aria-expanded":l},React.createElement("img",{src:a,alt:"Layout 3"}))))}})),"layout-2"===b?React.createElement(d,{label:__("Show Description"),checked:P,onChange:function(){return D({showDescription:!P})}}):null,React.createElement(d,{label:__("Show Price"),checked:C,onChange:function(){return D({showPrice:!C})}}),"layout-3"!==b?React.createElement(React.Fragment,null,React.createElement(d,{label:__("Show Button"),checked:S,onChange:function(){return D({showButton:!S})}}),!0===S?React.createElement(m,{type:"text",value:E,label:__("Button Text"),onChange:function(e){return D({buttonText:e})}}):null):null))),React.createElement("div",{className:"fc-latest-products-content-color-settings"},React.createElement(i,{title:__("Customization"),colorSettings:G})))};function f(e){return f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},f(e)}function b(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function R(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?b(Object(n),!0).forEach((function(t){h(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):b(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function h(e,t,n){return(t=function(e){var t=function(e){if("object"!=f(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=f(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==f(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function v(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var a,r,l,o,i=[],c=!0,u=!1;try{if(l=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(a=l.call(n)).done)&&(i.push(a.value),i.length!==t);c=!0);}catch(e){u=!0,r=e}finally{try{if(!c&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(u)throw r}}return i}}(e,t)||function(e,t){if(e){if("string"==typeof e)return j(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?j(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function j(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=Array(t);n<t;n++)a[n]=e[n];return a}window.wp.i18n;var w=wp.components.Spinner,P=wp.element,C=P.useState,S=P.useEffect,E=wp.i18n,N=(E.__,E._n,function(e){var t=e.attributes,n=t.selectedLayout,a=t.order,r=t.orderBy,l=(t.taxonomies,t.taxType),o=t.selectedPostsPerPage,i=t.showDescription,c=t.showPrice,u=t.showButton,I=t.buttonText,s=t.titleColor,d=t.priceColor,m=t.buttonColor,p=t.buttonBG,g=t.descriptionColor,y=e.setAttributes,f=v(C([]),2),b=f[0],h=f[1],j=v(C(!1),2),P=j[0],E=j[1],N=wp.apiFetch,M=wp.url.addQueryArgs,B=v(C(!1),2),x=(B[0],B[1]);S((function(){H()}),[o,a,r,l]);var H=function(e){E(!0),N({path:M("fluent-crm/v2/campaigns-pro/products",R({per_page:o,order:a,orderby:r,taxType:l},e))}).then((function(e){h(e.products),y({taxonomies:e.taxonomies})})).catch((function(e){x(!0)})).finally((function(){E(!1)}))},D={color:s},Z={color:g},G={color:d},z="";return"layout-3"!==n&&!0===u&&(z={color:m}),"layout-2"===n&&(z={color:m,background:p}),[React.createElement("div",null,P?React.createElement("h2",null,React.createElement(w,null)):React.createElement("div",{className:"fc_woo_products template-"+n},b&&b.length?b.map((function(e,t){return React.createElement("div",{className:"fc_woo_product",key:t},React.createElement("div",{className:"fc_woo_product_img"},React.createElement("img",{src:e.image,alt:""})),React.createElement("div",{className:"fc_woo_product_info"},React.createElement("div",null,React.createElement("h3",{className:"title",style:D,dangerouslySetInnerHTML:{__html:e.name}}),"layout-2"===n&&!0===i?React.createElement("p",{className:"description",style:Z,dangerouslySetInnerHTML:{__html:e.short_description}}):null,!0===c?React.createElement("span",{className:"price",style:G,dangerouslySetInnerHTML:{__html:e.price_html?e.price_html:"Free"}}):null),"layout-3"!==n&&!0===u?React.createElement("span",{className:"add-to-cart-btn",style:z},I):null))})):React.createElement("div",{className:"fcw_products_not_found"},React.createElement("h2",null,"No Products found!"))))]}),M=wp.element.Fragment;const B=window.wp.blockEditor;wp.i18n.__;var x=wp.element.createElement,H=wp.i18n.__,D=wp.blocks.registerBlockType,Z=x("svg",{width:20,height:20},x("path",{d:"M0 0h24v24H0V0z",fill:"none"}),x("path",{fill:"#96588a",d:"M22 9.24l-7.19-.62L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21 12 17.27 18.18 21l-1.63-7.03L22 9.24zM12 15.4l-3.76 2.27 1-4.28-3.32-2.88 4.38-.38L12 6.1l1.71 4.04 4.38.38-3.32 2.88 1 4.28L12 15.4z"}));D("fluent-crm/products",{title:H("WooCommerce Products"),description:H("WooCommerce Products For your Email"),category:"layout",icon:Z,keywords:[H("card"),H("latest product"),H("latest products"),H("products")],supports:{align:["wide","full"],html:!0},attributes:{selectedLayout:{type:"string",default:"default"},selectedPostsPerPage:{type:"string",default:"3"},showDescription:{type:"boolean",default:!0},showPrice:{type:"boolean",default:!0},showButton:{type:"boolean",default:!0},taxonomies:{type:"array",default:[]},taxType:{type:"string",default:"all"},order:{type:"string",default:"desc"},orderBy:{type:"string",default:"date"},buttonText:{type:"string",default:"Buy Now"},titleColor:{type:"string",default:"#2a363d"},descriptionColor:{type:"string",default:""},priceColor:{type:"string",default:"#37454e"},buttonColor:{type:"string",default:""},buttonBG:{type:"string",default:"#2a363d"}},edit:function(e){return React.createElement(M,null,React.createElement("div",{className:"fluent-latest-posts-block"},React.createElement(N,{attributes:e.attributes,setAttributes:e.setAttributes})),React.createElement(y,{attributes:e.attributes,setAttributes:e.setAttributes}))},save:function(e){return React.createElement("div",null,React.createElement(React.Fragment,null,React.createElement(B.InnerBlocks.Content,null)))}})})();