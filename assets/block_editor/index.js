(()=>{var e,t={256:(e,t,r)=>{"use strict";var n=r(923);e.exports=function(e,t,r){r=r||{},9===t.nodeType&&(t=n.getWindow(t));var o=r.allowHorizontalScroll,i=r.onlyScrollIfNeeded,a=r.alignWithTop,c=r.alignWithLeft,l=r.offsetTop||0,u=r.offsetLeft||0,s=r.offsetBottom||0,f=r.offsetRight||0;o=void 0===o||o;var p=n.isWindow(t),d=n.offset(e),y=n.outerHeight(e),h=n.outerWidth(e),m=void 0,v=void 0,g=void 0,b=void 0,w=void 0,S=void 0,E=void 0,O=void 0,_=void 0,k=void 0;p?(E=t,k=n.height(E),_=n.width(E),O={left:n.scrollLeft(E),top:n.scrollTop(E)},w={left:d.left-O.left-u,top:d.top-O.top-l},S={left:d.left+h-(O.left+_)+f,top:d.top+y-(O.top+k)+s},b=O):(m=n.offset(t),v=t.clientHeight,g=t.clientWidth,b={left:t.scrollLeft,top:t.scrollTop},w={left:d.left-(m.left+(parseFloat(n.css(t,"borderLeftWidth"))||0))-u,top:d.top-(m.top+(parseFloat(n.css(t,"borderTopWidth"))||0))-l},S={left:d.left+h-(m.left+g+(parseFloat(n.css(t,"borderRightWidth"))||0))+f,top:d.top+y-(m.top+v+(parseFloat(n.css(t,"borderBottomWidth"))||0))+s}),w.top<0||S.top>0?!0===a?n.scrollTop(t,b.top+w.top):!1===a?n.scrollTop(t,b.top+S.top):w.top<0?n.scrollTop(t,b.top+w.top):n.scrollTop(t,b.top+S.top):i||((a=void 0===a||!!a)?n.scrollTop(t,b.top+w.top):n.scrollTop(t,b.top+S.top)),o&&(w.left<0||S.left>0?!0===c?n.scrollLeft(t,b.left+w.left):!1===c?n.scrollLeft(t,b.left+S.left):w.left<0?n.scrollLeft(t,b.left+w.left):n.scrollLeft(t,b.left+S.left):i||((c=void 0===c||!!c)?n.scrollLeft(t,b.left+w.left):n.scrollLeft(t,b.left+S.left)))}},875:(e,t,r)=>{"use strict";e.exports=r(256)},923:e=>{"use strict";var t=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol?"symbol":typeof e};function n(e,t){var r=e["page"+(t?"Y":"X")+"Offset"],n="scroll"+(t?"Top":"Left");if("number"!=typeof r){var o=e.document;"number"!=typeof(r=o.documentElement[n])&&(r=o.body[n])}return r}function o(e){return n(e)}function i(e){return n(e,!0)}function a(e){var t=function(e){var t,r=void 0,n=void 0,o=e.ownerDocument,i=o.body,a=o&&o.documentElement;return r=(t=e.getBoundingClientRect()).left,n=t.top,{left:r-=a.clientLeft||i.clientLeft||0,top:n-=a.clientTop||i.clientTop||0}}(e),r=e.ownerDocument,n=r.defaultView||r.parentWindow;return t.left+=o(n),t.top+=i(n),t}var c=new RegExp("^("+/[\-+]?(?:\d*\.|)\d+(?:[eE][\-+]?\d+|)/.source+")(?!px)[a-z%]+$","i"),l=/^(top|right|bottom|left)$/,u="currentStyle",s="runtimeStyle",f="left",p=void 0;function d(e,t){for(var r=0;r<e.length;r++)t(e[r])}function y(e){return"border-box"===p(e,"boxSizing")}"undefined"!=typeof window&&(p=window.getComputedStyle?function(e,t,r){var n="",o=e.ownerDocument,i=r||o.defaultView.getComputedStyle(e,null);return i&&(n=i.getPropertyValue(t)||i[t]),n}:function(e,t){var r=e[u]&&e[u][t];if(c.test(r)&&!l.test(t)){var n=e.style,o=n[f],i=e[s][f];e[s][f]=e[u][f],n[f]="fontSize"===t?"1em":r||0,r=n.pixelLeft+"px",n[f]=o,e[s][f]=i}return""===r?"auto":r});var h=["margin","border","padding"];function m(e,t,r){var n=0,o=void 0,i=void 0,a=void 0;for(i=0;i<t.length;i++)if(o=t[i])for(a=0;a<r.length;a++){var c;c="border"===o?o+r[a]+"Width":o+r[a],n+=parseFloat(p(e,c))||0}return n}function v(e){return null!=e&&e==e.window}var g={};function b(e,t,r){if(v(e))return"width"===t?g.viewportWidth(e):g.viewportHeight(e);if(9===e.nodeType)return"width"===t?g.docWidth(e):g.docHeight(e);var n="width"===t?["Left","Right"]:["Top","Bottom"],o="width"===t?e.offsetWidth:e.offsetHeight,i=(p(e),y(e)),a=0;(null==o||o<=0)&&(o=void 0,(null==(a=p(e,t))||Number(a)<0)&&(a=e.style[t]||0),a=parseFloat(a)||0),void 0===r&&(r=i?1:-1);var c=void 0!==o||i,l=o||a;if(-1===r)return c?l-m(e,["border","padding"],n):a;if(c){var u=2===r?-m(e,["border"],n):m(e,["margin"],n);return l+(1===r?0:u)}return a+m(e,h.slice(r),n)}d(["Width","Height"],(function(e){g["doc"+e]=function(t){var r=t.document;return Math.max(r.documentElement["scroll"+e],r.body["scroll"+e],g["viewport"+e](r))},g["viewport"+e]=function(t){var r="client"+e,n=t.document,o=n.body,i=n.documentElement[r];return"CSS1Compat"===n.compatMode&&i||o&&o[r]||i}}));var w={position:"absolute",visibility:"hidden",display:"block"};function S(e){var t=void 0,r=arguments;return 0!==e.offsetWidth?t=b.apply(void 0,r):function(e,n){var o={},i=e.style,a=void 0;for(a in n)n.hasOwnProperty(a)&&(o[a]=i[a],i[a]=n[a]);for(a in function(){t=b.apply(void 0,r)}.call(e),n)n.hasOwnProperty(a)&&(i[a]=o[a])}(e,w),t}function E(e,t,n){var o=n;if("object"!==(void 0===t?"undefined":r(t)))return void 0!==o?("number"==typeof o&&(o+="px"),void(e.style[t]=o)):p(e,t);for(var i in t)t.hasOwnProperty(i)&&E(e,i,t[i])}d(["width","height"],(function(e){var t=e.charAt(0).toUpperCase()+e.slice(1);g["outer"+t]=function(t,r){return t&&S(t,e,r?0:1)};var r="width"===e?["Left","Right"]:["Top","Bottom"];g[e]=function(t,n){return void 0===n?t&&S(t,e,-1):t?(p(t),y(t)&&(n+=m(t,["padding","border"],r)),E(t,e,n)):void 0}})),e.exports=t({getWindow:function(e){var t=e.ownerDocument||e;return t.defaultView||t.parentWindow},offset:function(e,t){if(void 0===t)return a(e);!function(e,t){"static"===E(e,"position")&&(e.style.position="relative");var r=a(e),n={},o=void 0,i=void 0;for(i in t)t.hasOwnProperty(i)&&(o=parseFloat(E(e,i))||0,n[i]=o+t[i]-r[i]);E(e,n)}(e,t)},isWindow:v,each:d,css:E,clone:function(e){var t={};for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r]);if(e.overflow)for(var r in e)e.hasOwnProperty(r)&&(t.overflow[r]=e.overflow[r]);return t},scrollLeft:function(e,t){if(v(e)){if(void 0===t)return o(e);window.scrollTo(t,i(e))}else{if(void 0===t)return e.scrollLeft;e.scrollLeft=t}},scrollTop:function(e,t){if(v(e)){if(void 0===t)return i(e);window.scrollTo(o(e),t)}else{if(void 0===t)return e.scrollTop;e.scrollTop=t}},viewportWidth:0,viewportHeight:0},g)},192:(e,t,r)=>{var n=r(580).Symbol;e.exports=n},154:e=>{e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length;++r<n&&!1!==t(e[r],r,e););return e}},384:(e,t,r)=>{var n=r(731),o=r(245),i=r(224),a=r(107),c=r(958),l=r(736),u=Object.prototype.hasOwnProperty;e.exports=function(e,t){var r=i(e),s=!r&&o(e),f=!r&&!s&&a(e),p=!r&&!s&&!f&&l(e),d=r||s||f||p,y=d?n(e.length,String):[],h=y.length;for(var m in e)!t&&!u.call(e,m)||d&&("length"==m||f&&("offset"==m||"parent"==m)||p&&("buffer"==m||"byteLength"==m||"byteOffset"==m)||c(m,h))||y.push(m);return y}},288:(e,t,r)=>{var n=r(140),o=r(768)(n);e.exports=o},86:(e,t,r)=>{var n=r(406)();e.exports=n},140:(e,t,r)=>{var n=r(86),o=r(165);e.exports=function(e,t){return e&&n(e,t,o)}},389:(e,t,r)=>{var n=r(192),o=r(468),i=r(859),a=n?n.toStringTag:void 0;e.exports=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":a&&a in Object(e)?o(e):i(e)}},701:(e,t,r)=>{var n=r(389),o=r(13);e.exports=function(e){return o(e)&&"[object Arguments]"==n(e)}},456:(e,t,r)=>{var n=r(389),o=r(577),i=r(13),a={};a["[object Float32Array]"]=a["[object Float64Array]"]=a["[object Int8Array]"]=a["[object Int16Array]"]=a["[object Int32Array]"]=a["[object Uint8Array]"]=a["[object Uint8ClampedArray]"]=a["[object Uint16Array]"]=a["[object Uint32Array]"]=!0,a["[object Arguments]"]=a["[object Array]"]=a["[object ArrayBuffer]"]=a["[object Boolean]"]=a["[object DataView]"]=a["[object Date]"]=a["[object Error]"]=a["[object Function]"]=a["[object Map]"]=a["[object Number]"]=a["[object Object]"]=a["[object RegExp]"]=a["[object Set]"]=a["[object String]"]=a["[object WeakMap]"]=!1,e.exports=function(e){return i(e)&&o(e.length)&&!!a[n(e)]}},77:(e,t,r)=>{var n=r(75),o=r(275),i=Object.prototype.hasOwnProperty;e.exports=function(e){if(!n(e))return o(e);var t=[];for(var r in Object(e))i.call(e,r)&&"constructor"!=r&&t.push(r);return t}},731:e=>{e.exports=function(e,t){for(var r=-1,n=Array(e);++r<e;)n[r]=t(r);return n}},770:e=>{e.exports=function(e){return function(t){return e(t)}}},747:(e,t,r)=>{var n=r(251);e.exports=function(e){return"function"==typeof e?e:n}},768:(e,t,r)=>{var n=r(935);e.exports=function(e,t){return function(r,o){if(null==r)return r;if(!n(r))return e(r,o);for(var i=r.length,a=t?i:-1,c=Object(r);(t?a--:++a<i)&&!1!==o(c[a],a,c););return r}}},406:e=>{e.exports=function(e){return function(t,r,n){for(var o=-1,i=Object(t),a=n(t),c=a.length;c--;){var l=a[e?c:++o];if(!1===r(i[l],l,i))break}return t}}},877:(e,t,r)=>{var n="object"==typeof r.g&&r.g&&r.g.Object===Object&&r.g;e.exports=n},468:(e,t,r)=>{var n=r(192),o=Object.prototype,i=o.hasOwnProperty,a=o.toString,c=n?n.toStringTag:void 0;e.exports=function(e){var t=i.call(e,c),r=e[c];try{e[c]=void 0;var n=!0}catch(e){}var o=a.call(e);return n&&(t?e[c]=r:delete e[c]),o}},958:e=>{var t=/^(?:0|[1-9]\d*)$/;e.exports=function(e,r){var n=typeof e;return!!(r=null==r?9007199254740991:r)&&("number"==n||"symbol"!=n&&t.test(e))&&e>-1&&e%1==0&&e<r}},75:e=>{var t=Object.prototype;e.exports=function(e){var r=e&&e.constructor;return e===("function"==typeof r&&r.prototype||t)}},275:(e,t,r)=>{var n=r(80)(Object.keys,Object);e.exports=n},420:(e,t,r)=>{e=r.nmd(e);var n=r(877),o=t&&!t.nodeType&&t,i=o&&e&&!e.nodeType&&e,a=i&&i.exports===o&&n.process,c=function(){try{return i&&i.require&&i.require("util").types||a&&a.binding&&a.binding("util")}catch(e){}}();e.exports=c},859:e=>{var t=Object.prototype.toString;e.exports=function(e){return t.call(e)}},80:e=>{e.exports=function(e,t){return function(r){return e(t(r))}}},580:(e,t,r)=>{var n=r(877),o="object"==typeof self&&self&&self.Object===Object&&self,i=n||o||Function("return this")();e.exports=i},976:(e,t,r)=>{e.exports=r(183)},183:(e,t,r)=>{var n=r(154),o=r(288),i=r(747),a=r(224);e.exports=function(e,t){return(a(e)?n:o)(e,i(t))}},251:e=>{e.exports=function(e){return e}},245:(e,t,r)=>{var n=r(701),o=r(13),i=Object.prototype,a=i.hasOwnProperty,c=i.propertyIsEnumerable,l=n(function(){return arguments}())?n:function(e){return o(e)&&a.call(e,"callee")&&!c.call(e,"callee")};e.exports=l},224:e=>{var t=Array.isArray;e.exports=t},935:(e,t,r)=>{var n=r(929),o=r(577);e.exports=function(e){return null!=e&&o(e.length)&&!n(e)}},107:(e,t,r)=>{e=r.nmd(e);var n=r(580),o=r(290),i=t&&!t.nodeType&&t,a=i&&e&&!e.nodeType&&e,c=a&&a.exports===i?n.Buffer:void 0,l=(c?c.isBuffer:void 0)||o;e.exports=l},929:(e,t,r)=>{var n=r(389),o=r(866);e.exports=function(e){if(!o(e))return!1;var t=n(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t}},577:e=>{e.exports=function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991}},866:e=>{e.exports=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}},13:e=>{e.exports=function(e){return null!=e&&"object"==typeof e}},736:(e,t,r)=>{var n=r(456),o=r(770),i=r(420),a=i&&i.isTypedArray,c=a?o(a):n;e.exports=c},165:(e,t,r)=>{var n=r(384),o=r(77),i=r(935);e.exports=function(e){return i(e)?n(e):o(e)}},290:e=>{e.exports=function(){return!1}},24:(e,t,r)=>{"use strict";var n={};r.r(n),r.d(n,{disableComplementaryArea:()=>k,enableComplementaryArea:()=>_,pinItem:()=>j,setDefaultComplementaryArea:()=>O,setFeatureDefaults:()=>T,setFeatureValue:()=>L,toggleFeature:()=>R,unpinItem:()=>x});var o={};r.r(o),r.d(o,{getActiveComplementaryArea:()=>P,isFeatureActive:()=>A,isItemPinned:()=>C});var i={};r.r(i),r.d(i,{getBlocks:()=>Fr,hasRedo:()=>Br,hasUndo:()=>Ir});var a={};r.r(a),r.d(a,{clearHistory:()=>Gr,fetchBlocksFromStorage:()=>Vr,persistBlocksToStorage:()=>zr,redo:()=>Hr,undo:()=>Mr,updateBlocks:()=>Wr});var c={};r.r(c),r.d(c,{getBlocks:()=>qr});const l=window.wp.components,u=window.wp.blockEditor,s=window.wp.element,f=e=>{let{isActive:t}=e;return(0,s.useEffect)((()=>{let e=!1;return document.body.classList.contains("sticky-menu")&&(e=!0,document.body.classList.remove("sticky-menu")),()=>{e&&document.body.classList.add("sticky-menu")}}),[]),(0,s.useEffect)((()=>(t?document.body.classList.add("is-fullscreen-mode"):document.body.classList.remove("is-fullscreen-mode"),()=>{t&&document.body.classList.remove("is-fullscreen-mode")})),[t]),null};function p(){return p=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},p.apply(null,arguments)}var d=r(73),y=r.n(d);const h=window.wp.i18n,m=window.wp.compose;function v(e){let{children:t,className:r,ariaLabel:n,motionProps:o={}}=e;const i=Object.keys(o).length?l.__unstableMotion.div:"div";return(0,s.createElement)(i,p({className:y()("interface-navigable-region",r),"aria-label":n,role:"region",tabIndex:"-1"},o),(0,s.createElement)("div",{className:"interface-navigable-region__stacker"},t))}const g=(0,s.forwardRef)((function(e,t){let{isDistractionFree:r,footer:n,header:o,editorNotices:i,sidebar:a,secondarySidebar:c,notices:u,content:f,drawer:d,actions:g,labels:b,className:w,shortcuts:S}=e;const E=(0,l.__unstableUseNavigateRegions)(S);!function(e){(0,s.useEffect)((()=>{const t=document&&document.querySelector(`html:not(.${e})`);if(t)return t.classList.toggle(e),()=>{t.classList.toggle(e)}}),[e])}("interface-interface-skeleton__html-container");const O={
/* translators: accessibility text for the nav bar landmark region. */
drawer:(0,h.__)("Drawer"),
/* translators: accessibility text for the top bar landmark region. */
header:(0,h.__)("Header"),
/* translators: accessibility text for the content landmark region. */
body:(0,h.__)("Content"),
/* translators: accessibility text for the secondary sidebar landmark region. */
secondarySidebar:(0,h.__)("Block Library"),
/* translators: accessibility text for the settings landmark region. */
sidebar:(0,h.__)("Settings"),
/* translators: accessibility text for the publish landmark region. */
actions:(0,h.__)("Publish"),
/* translators: accessibility text for the footer landmark region. */
footer:(0,h.__)("Footer"),...b},_={hidden:r?{opacity:0}:{opacity:1},hover:{opacity:1,transition:{type:"tween",delay:.2,delayChildren:.2}}};return(0,s.createElement)("div",p({},E,{ref:(0,m.useMergeRefs)([t,E.ref]),className:y()(w,"interface-interface-skeleton",E.className,!!n&&"has-footer")}),!!d&&(0,s.createElement)(v,{className:"interface-interface-skeleton__drawer",ariaLabel:O.drawer},d),(0,s.createElement)("div",{className:"interface-interface-skeleton__editor"},!!o&&r&&(0,s.createElement)(v,{className:"interface-interface-skeleton__header","aria-label":O.header,motionProps:{initial:r?"hidden":"hover",whileHover:"hover",variants:_,transition:{type:"tween",delay:.8}}},o),!!o&&!r&&(0,s.createElement)(v,{className:"interface-interface-skeleton__header",ariaLabel:O.header},o),r&&(0,s.createElement)("div",{className:"interface-interface-skeleton__header"},i),(0,s.createElement)("div",{className:"interface-interface-skeleton__body"},!!c&&(0,s.createElement)(v,{className:"interface-interface-skeleton__secondary-sidebar",ariaLabel:O.secondarySidebar},c),!!u&&(0,s.createElement)("div",{className:"interface-interface-skeleton__notices"},u),(0,s.createElement)(v,{className:"interface-interface-skeleton__content",ariaLabel:O.body},f),!!a&&(0,s.createElement)(v,{className:"interface-interface-skeleton__sidebar",ariaLabel:O.sidebar},a),!!g&&(0,s.createElement)(v,{className:"interface-interface-skeleton__actions",ariaLabel:O.actions},g))),!!n&&(0,s.createElement)(v,{className:"interface-interface-skeleton__footer",ariaLabel:O.footer},n))})),b=window.wp.data,w=window.wp.deprecated;var S=r.n(w);const E=window.wp.preferences,O=(e,t)=>({type:"SET_DEFAULT_COMPLEMENTARY_AREA",scope:e,area:t}),_=(e,t)=>r=>{let{registry:n,dispatch:o}=r;t&&(n.select(E.store).get(e,"isComplementaryAreaVisible")||n.dispatch(E.store).set(e,"isComplementaryAreaVisible",!0),o({type:"ENABLE_COMPLEMENTARY_AREA",scope:e,area:t}))},k=e=>t=>{let{registry:r}=t;r.select(E.store).get(e,"isComplementaryAreaVisible")&&r.dispatch(E.store).set(e,"isComplementaryAreaVisible",!1)},j=(e,t)=>r=>{let{registry:n}=r;if(!t)return;const o=n.select(E.store).get(e,"pinnedItems");!0!==(null==o?void 0:o[t])&&n.dispatch(E.store).set(e,"pinnedItems",{...o,[t]:!0})},x=(e,t)=>r=>{let{registry:n}=r;if(!t)return;const o=n.select(E.store).get(e,"pinnedItems");n.dispatch(E.store).set(e,"pinnedItems",{...o,[t]:!1})};function R(e,t){return function(r){let{registry:n}=r;S()("dispatch( 'core/interface' ).toggleFeature",{since:"6.0",alternative:"dispatch( 'core/preferences' ).toggle"}),n.dispatch(E.store).toggle(e,t)}}function L(e,t,r){return function(n){let{registry:o}=n;S()("dispatch( 'core/interface' ).setFeatureValue",{since:"6.0",alternative:"dispatch( 'core/preferences' ).set"}),o.dispatch(E.store).set(e,t,!!r)}}function T(e,t){return function(r){let{registry:n}=r;S()("dispatch( 'core/interface' ).setFeatureDefaults",{since:"6.0",alternative:"dispatch( 'core/preferences' ).setDefaults"}),n.dispatch(E.store).setDefaults(e,t)}}const P=(0,b.createRegistrySelector)((e=>(t,r)=>{var n;const o=e(E.store).get(r,"isComplementaryAreaVisible");if(void 0!==o)return o?null==t||null===(n=t.complementaryAreas)||void 0===n?void 0:n[r]:null})),C=(0,b.createRegistrySelector)((e=>(t,r,n)=>{var o;const i=e(E.store).get(r,"pinnedItems");return null===(o=null==i?void 0:i[n])||void 0===o||o})),A=(0,b.createRegistrySelector)((e=>(t,r,n)=>(S()("select( 'core/interface' ).isFeatureActive( scope, featureName )",{since:"6.0",alternative:"select( 'core/preferences' ).get( scope, featureName )"}),!!e(E.store).get(r,n)))),N=(0,b.combineReducers)({complementaryAreas:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"SET_DEFAULT_COMPLEMENTARY_AREA":{const{scope:r,area:n}=t;return e[r]?e:{...e,[r]:n}}case"ENABLE_COMPLEMENTARY_AREA":{const{scope:r,area:n}=t;return{...e,[r]:n}}}return e}}),F=(0,b.createReduxStore)("core/interface",{reducer:N,actions:n,selectors:o});(0,b.register)(F);const I=window.wp.keycodes,B=window.React,U=window.wp.primitives,D=(0,B.createElement)(U.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,B.createElement)(U.Path,{d:"M18.3 11.7c-.6-.6-1.4-.9-2.3-.9H6.7l2.9-3.3-1.1-1-4.5 5L8.5 16l1-1-2.7-2.7H16c.5 0 .9.2 1.3.5 1 1 1 3.4 1 4.5v.3h1.5v-.2c0-1.5 0-4.3-1.5-5.7z"}));var M=["hasUndo","undo"];function H(){return H=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},H.apply(null,arguments)}var G=(0,m.compose)([(0,b.withSelect)((function(e){return{hasUndo:e("historyUndoRedoStore").hasUndo()}})),(0,b.withDispatch)((function(e){return{undo:e("historyUndoRedoStore").undo}}))])((function(e){var t=e.hasUndo,r=e.undo,n=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.includes(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],t.includes(r)||{}.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,M);return React.createElement(l.Button,H({},n,{icon:D,label:(0,h.__)("Undo"),shortcut:I.displayShortcut.primary("z"),"aria-disabled":!t,onClick:t?r:void 0,className:"editor-history__undo"}))}));const W=G,V=(0,B.createElement)(U.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,B.createElement)(U.Path,{d:"M15.6 6.5l-1.1 1 2.9 3.3H8c-.9 0-1.7.3-2.3.9-1.4 1.5-1.4 4.2-1.4 5.6v.2h1.5v-.3c0-1.1 0-3.5 1-4.5.3-.3.7-.5 1.3-.5h9.2L14.5 15l1.1 1.1 4.6-4.6-4.6-5z"}));var z=["hasRedo","redo"];function K(){return K=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},K.apply(null,arguments)}var $=(0,m.compose)([(0,b.withSelect)((function(e){return{hasRedo:e("historyUndoRedoStore").hasRedo()}})),(0,b.withDispatch)((function(e){return{redo:e("historyUndoRedoStore").redo}}))])((function(e){var t=e.hasRedo,r=e.redo,n=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.includes(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],t.includes(r)||{}.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,z);return React.createElement(l.Button,K({},n,{icon:V,label:(0,h.__)("Redo"),shortcut:I.displayShortcut.primaryShift("z"),"aria-disabled":!t,onClick:t?r:void 0,className:"editor-history__redo"}))}));const Y=$;function q(){return React.createElement("div",{className:"history-undo-redo-header",role:"region","aria-label":(0,h.__)("History undo redo top bar."),tabIndex:"-1"},React.createElement(W,null),React.createElement(Y,null))}var J=(0,l.createSlotFill)("StandAloneBlockEditorSidebarInspector"),Q=J.Slot,X=J.Fill;function Z(){return React.createElement("div",{className:"fce-sidebar",role:"region","aria-label":(0,h.__)("advanced settings."),tabIndex:"-1"},React.createElement(l.Panel,null,React.createElement("div",{className:"components-panel__header"},React.createElement("h2",null,(0,h.__)("Block Settings")),React.createElement(q,null)),React.createElement(Q,{bubblesVirtually:!0})))}Z.InspectorFill=X;const ee=Z,te=(window.wp.formatLibrary,window.wp.blocks),re=window.wp.mediaUtils,ne=window.wp.keyboardShortcuts,oe=function(){var e=(0,b.useDispatch)("core/keyboard-shortcuts").registerShortcut,t=(0,b.useDispatch)("historyUndoRedoStore"),r=t.undo,n=t.redo;return(0,s.useEffect)((function(){e({name:"custom/editor/undo",category:"global",description:(0,h.__)("Undo your last changes."),keyCombination:{modifier:"primary",character:"z"}}),e({name:"custom/editor/redo",category:"global",description:(0,h.__)("Redo your last undo."),keyCombination:{modifier:"primaryShift",character:"z"}})}),[e]),(0,ne.useShortcut)("custom/editor/undo",(function(){r()})),(0,ne.useShortcut)("custom/editor/redo",(function(){n()})),null};var ie=["onError"];function ae(e){return ae="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ae(e)}function ce(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function le(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ce(Object(r),!0).forEach((function(t){ue(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ce(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function ue(e,t,r){return(t=function(e){var t=function(e){if("object"!=ae(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=ae(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==ae(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}const se=function(e){var t=e.settings,r=e.onChangeContent,n=(0,b.useSelect)((function(e){return e("historyUndoRedoStore").getBlocks()})),o=(0,b.useDispatch)("historyUndoRedoStore"),i=o.updateBlocks,a=o.clearHistory;t.__experimentalSetIsInserterOpened=function(e){e&&jQuery(".fce_inserter button.block-editor-inserter__toggle").trigger("click")};var c=(0,b.useSelect)((function(e){return!0}),[]),l=(0,s.useMemo)((function(){return c?le(le({},t),{},{mediaUpload:function(e){var r=e.onError,n=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.includes(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],t.includes(r)||{}.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,ie);(0,re.uploadMedia)(le({wpAllowedMimeTypes:t.allowedMimeTypes,onError:function(e){var t=e.message;return r(t)}},n))}}):t}),[c,t]);function f(e){i(e),null!=e&&e.length&&r((0,te.serialize)(e))}(0,s.useEffect)((function(){var e=n;null!=e&&e.length&&(a(),f(e))}),[]);var p,d=void 0!==ne.ShortcutProvider;return React.createElement("div",{className:"fce-block-editor"},(p=React.createElement(u.BlockEditorProvider,{value:n,onInput:f,onChange:function(e){i(e,!0),r((0,te.serialize)(e))},settings:l},React.createElement(u.BlockEditorKeyboardShortcuts.Register,null),React.createElement(u.BlockEditorKeyboardShortcuts,null),React.createElement(oe,null),React.createElement(ee.InspectorFill,null,React.createElement(u.BlockInspector,null)),React.createElement("div",{className:"editor-styles-wrapper"},React.createElement(u.BlockTools,null,React.createElement(u.WritingFlow,null,React.createElement(u.ObserveTyping,null,React.createElement("span",{className:"fce_inserter"},React.createElement(u.Inserter,null)),React.createElement(u.BlockList,{className:"fce-block-editor__block-list fc_editor_body"})))))),d?React.createElement(ne.ShortcutProvider,null,p):p))},fe=function(e){var t=e.settings,r=e.onChangeHandle;return React.createElement(React.Fragment,null,React.createElement(f,{isActive:!1}),React.createElement(l.SlotFillProvider,null,React.createElement(g,{className:"fluent-crm-editor",sidebar:React.createElement(ee,null),content:React.createElement(React.Fragment,null,React.createElement(se,{onChangeContent:r,settings:t}))}),React.createElement(u.BlockTools,null),React.createElement(l.Popover.Slot,null)))},pe=window.wp.domReady;var de=r.n(pe),ye=r(976),he=r.n(ye);function me(e){return function(e){if(Array.isArray(e))return ve(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return ve(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?ve(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ve(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}const ge={name:"crm_prefixes",className:"editor-autocompleters__crm",triggerPrefix:"@",options:function(e){console.log(e);var t=[],r=window.fcAdmin.globalSmartCodes;return window.fcrm_funnel_context_codes&&(r=[].concat(me(r),me(window.fcrm_funnel_context_codes))),window.fcAdmin.extendedSmartCodes&&(r=[].concat(me(r),me(window.fcAdmin.extendedSmartCodes))),he()(r,(function(e){he()(e.shortcodes,(function(r,n){t.push({code:n,title:r,prefix:e.key})}))})),t},isDebounced:!0,getOptionKeywords:function(e){return[e.title,e.code]},getOptionLabel:function(e){return[React.createElement("span",{key:"name",className:"editor-autocompleters__user-name"},e.title)]},getOptionCompletion:function(e){return e.code}},be=window.wp.hooks,we=window.wp.richText,Se=window.lodash;function Ee(e){return Ee="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ee(e)}function Oe(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,_e(n.key),n)}}function _e(e){var t=function(e){if("object"!=Ee(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=Ee(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==Ee(t)?t:t+""}function ke(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(ke=function(){return!!e})()}function je(e){return je=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},je(e)}function xe(e,t){return xe=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},xe(e,t)}var Re=window.wp,Le=function(e){return(0,Se.pick)(e,["sizes","mime","type","subtype","id","url","alt","link","caption"])},Te=function(e){return Re.media.query({order:"ASC",orderby:"post__in",post__in:e,posts_per_page:-1,query:!0,type:"image"})},Pe=function(e){function t(e){var r,n=e.allowedTypes,o=e.gallery,i=void 0!==o&&o,a=e.unstableFeaturedImageFlow,c=void 0!==a&&a,l=e.modalClass,u=e.multiple,s=void 0!==u&&u,f=e.title,p=void 0===f?(0,h.__)("Select or Upload Media"):f;if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),(r=function(e,t,r){return t=je(t),function(e,t){if(t&&("object"==Ee(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,ke()?Reflect.construct(t,r||[],je(e).constructor):t.apply(e,r))}(this,t,arguments)).openModal=r.openModal.bind(r),r.onOpen=r.onOpen.bind(r),r.onSelect=r.onSelect.bind(r),r.onUpdate=r.onUpdate.bind(r),r.onClose=r.onClose.bind(r),i)r.buildAndSetGalleryFrame();else{var d={title:p,multiple:s};n&&(d.library={type:n}),r.frame=Re.media(d)}return l&&r.frame.$el.addClass(l),c&&r.buildAndSetFeatureImageFrame(),r.initializeListeners(),r}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&xe(e,t)}(t,e),r=t,(n=[{key:"initializeListeners",value:function(){this.frame.on("select",this.onSelect),this.frame.on("update",this.onUpdate),this.frame.on("open",this.onOpen),this.frame.on("close",this.onClose)}},{key:"buildAndSetGalleryFrame",value:function(){var e=this.props,t=e.addToGallery,r=void 0!==t&&t,n=e.allowedTypes,o=e.multiple,i=void 0!==o&&o,a=e.value,c=void 0===a?null:a;if(c!==this.lastGalleryValue){var l;this.lastGalleryValue=c,this.frame&&this.frame.remove(),l=r?"gallery-library":c?"gallery-edit":"gallery",this.GalleryDetailsMediaFrame||(this.GalleryDetailsMediaFrame=Re.media.view.MediaFrame.Post.extend({createStates:function(){this.states.add([new Re.media.controller.Library({id:"gallery",title:Re.media.view.l10n.createGalleryTitle,priority:40,toolbar:"main-gallery",filterable:"uploaded",multiple:"add",editable:!1,library:Re.media.query((0,Se.defaults)({type:"image"},this.options.library))}),new Re.media.controller.GalleryEdit({library:this.options.selection,editing:this.options.editing,menu:"gallery",displaySettings:!1,multiple:!0}),new Re.media.controller.GalleryAdd])}}));var u=Te(c),s=new Re.media.model.Selection(u.models,{props:u.props.toJSON(),multiple:i});this.frame=new this.GalleryDetailsMediaFrame({mimeType:n,state:l,multiple:i,selection:s,editing:!!c}),Re.media.frame=this.frame,this.initializeListeners()}}},{key:"buildAndSetFeatureImageFrame",value:function(){var e=Re.media.view.MediaFrame.Select.extend({featuredImageToolbar:function(e){this.createSelectToolbar(e,{text:Re.media.view.l10n.setFeaturedImage,state:this.options.state})},createStates:function(){this.on("toolbar:create:featured-image",this.featuredImageToolbar,this),this.states.add([new Re.media.controller.FeaturedImage])}}),t=Te(this.props.value),r=new Re.media.model.Selection(t.models,{props:t.props.toJSON()});this.frame=new e({mimeType:this.props.allowedTypes,state:"featured-image",multiple:this.props.multiple,selection:r,editing:!!this.props.value}),Re.media.frame=this.frame}},{key:"componentWillUnmount",value:function(){this.frame.remove()}},{key:"onUpdate",value:function(e){var t=this.props,r=t.onSelect,n=t.multiple,o=void 0!==n&&n,i=this.frame.state(),a=e||i.get("selection");a&&a.models.length&&r(o?a.models.map((function(e){return Le(e.toJSON())})):Le(a.models[0].toJSON()))}},{key:"onSelect",value:function(){var e=this.props,t=e.onSelect,r=e.multiple,n=void 0!==r&&r,o=this.frame.state().get("selection").toJSON();t(n?o:o[0])}},{key:"onOpen",value:function(){if(this.updateCollection(),this.props.value){if(!this.props.gallery){var e=this.frame.state().get("selection");(0,Se.castArray)(this.props.value).forEach((function(t){e.add(Re.media.attachment(t))}))}Te((0,Se.castArray)(this.props.value)).more()}}},{key:"onClose",value:function(){var e=this.props.onClose;e&&e()}},{key:"updateCollection",value:function(){var e=this.frame.content.get();if(e&&e.collection){var t=e.collection;t.toArray().forEach((function(e){return e.trigger("destroy",e)})),t.mirroring._hasMore=!0,t.more()}}},{key:"openModal",value:function(){this.props.gallery&&this.props.value&&this.props.value.length>0&&this.buildAndSetGalleryFrame(),this.frame.open()}},{key:"render",value:function(){return this.props.render({open:this.openModal})}}])&&Oe(r.prototype,n),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,n}(s.Component);const Ce=Pe,Ae=window.wp.blockLibrary;var Ne=["colorScheme","contentMaxWidth","children"];function Fe(){return Fe=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Fe.apply(null,arguments)}const Ie=function(e){e.colorScheme,e.contentMaxWidth;var t=e.children,r=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.includes(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],t.includes(r)||{}.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,Ne);return React.createElement("div",Fe({className:"fc-cond-section"},r),React.createElement("div",{className:"fc-cond-blocks"},t))};var Be=wp.i18n,__=Be.__,_x=Be._x,Ue={title:__("Conditional Section"),description:__("Add a section that separates content, and put any other block into it."),category:"layout",icon:"welcome-widgets-menus",keywords:[_x("conditional"),_x("section")],supports:{align:["wide","full"],anchor:!0},attributes:{condition_type:{type:"string",default:"show_if_tag_exist"},tag_ids:{type:"array",default:[]}},edit:function(e){var t=e.attributes,r=e.setAttributes,n=t.condition_type,o=t.tag_ids,i=window.fcAdmin.available_tags,a=window.fcAdmin.addons&&window.fcAdmin.addons.fluentcampaign,c=function(e){if(a){var n=e.target,o=n.checked,i=n.value,c=jQuery.extend(!0,[],t.tag_ids);n.checked=o,o?-1==c.indexOf(i)&&(c.push(i),r({tag_ids:c})):(c.splice(c.indexOf(i),1),r({tag_ids:c}))}else alert("This is a pro version feature")};return React.createElement(s.Fragment,null,React.createElement(u.InspectorControls,null,React.createElement(l.PanelBody,{title:__("Conditional Settings")},React.createElement(l.SelectControl,{label:__("Condition Type"),value:n,onChange:function(e){return r({condition_type:e||"show_if_tag_exist"})},options:[{value:"show_if_tag_exist",label:"Show IF in selected tag"},{value:"show_if_tag_not_exist",label:"Show IF not in selected tag"}]}),React.createElement("div",{className:"fcrm-gb-multi-checkbox"},React.createElement("h4",null,"Select Targeted Tags"),React.createElement("ul",null,i.map((function(e){return React.createElement("label",{key:e.id,className:"checkbox"},e.title,React.createElement("input",{checked:-1!=o.indexOf(e.id),onChange:c,type:"checkbox",value:e.id}),React.createElement("span",{className:"checkmark"}))})))),a?React.createElement("div",{className:"fc_cd_info"},React.createElement("hr",null),React.createElement("b",null,"Tips:"),React.createElement("ul",null,React.createElement("li",null,"This will show/hide only if any tag is matched."),React.createElement("li",{style:{backgroundColor:"#ffffd7"}},"The yellow background in the content is only for editor and to identify the conditional contents"))):React.createElement("div",{style:{color:"red"}},React.createElement("b",null,"Pro Feature:"),React.createElement("p",null,"Conditional Feature will only work if you have FluentCRM pro activated. Please Install FluentCRM Pro First")))),React.createElement(Ie,{colorScheme:n,contentMaxWidth:o},React.createElement(u.InnerBlocks,null)))},save:function(e){var t=e.attributes,r=t.colorScheme,n=t.contentMaxWidth,o=t.attachmentId;return React.createElement(Ie,{colorScheme:r,contentMaxWidth:n,className:y()(o&&"has-background-image-".concat(o))},React.createElement(u.InnerBlocks.Content,null))}};function De(e){return De="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},De(e)}function Me(e,t,r){return(t=Ge(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function He(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Ge(n.key),n)}}function Ge(e){var t=function(e){if("object"!=De(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=De(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==De(t)?t:t+""}function We(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(We=function(){return!!e})()}function Ve(e){return Ve=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Ve(e)}function ze(e,t){return ze=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},ze(e,t)}var Ke=wp.i18n.__,$e=wp.element.Component,Ye=wp.components,qe=Ye.TextControl,Je=Ye.Button,Qe=Ye.Tooltip,Xe=function(e){function t(e){var r;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),(r=function(e,t,r){return t=Ve(t),function(e,t){if(t&&("object"==De(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,We()?Reflect.construct(t,r||[],Ve(e).constructor):t.apply(e,r))}(this,t,[e])).state={active:!1},r}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&ze(e,t)}(t,e),r=t,(n=[{key:"render",value:function(){var e=this,t=this.props,r=t.attributes,n=t.top,o=t.right,i=t.bottom,a=t.left,c=t.label,l=t.setAttributes;return React.createElement("div",{className:"fce-dimension-box-wrapper"},React.createElement("label",null," ",c," "),React.createElement("div",{className:"fce-dimension-box"},React.createElement(qe,{type:"number",className:"fce-dimension-box-top",value:r[n],Placeholder:"Top",onChange:function(t){e.state.active?l(Me(Me(Me(Me({},n,parseInt(t)),o,parseInt(t)),i,parseInt(t)),a,parseInt(t))):l(Me({},n,parseInt(t)))}}),React.createElement(qe,{type:"number",className:"fce-dimension-box-right",value:r[o],Placeholder:"Right",onChange:function(t){e.state.active?l(Me(Me(Me(Me({},n,parseInt(t)),o,parseInt(t)),i,parseInt(t)),a,parseInt(t))):l(Me({},o,parseInt(t)))}}),React.createElement(qe,{type:"number",className:"fce-dimension-box-bottom",value:r[i],Placeholder:"Bottom",onChange:function(t){e.state.active?l(Me(Me(Me(Me({},n,parseInt(t)),o,parseInt(t)),i,parseInt(t)),a,parseInt(t))):l(Me({},i,parseInt(t)))}}),React.createElement(qe,{type:"number",className:"fce-dimension-box-left",value:r[a],Placeholder:"Left",onChange:function(t){e.state.active?l(Me(Me(Me(Me({},n,parseInt(t)),o,parseInt(t)),i,parseInt(t)),a,parseInt(t))):l(Me({},a,parseInt(t)))}}),React.createElement(Qe,{text:Ke("Link values together","fluentcrm"),position:"top"},React.createElement(Je,{className:this.state.active?"fce-dimension-link-values-together active":"fce-dimension-link-values-together",isDefault:!0,onClick:function(){e.setState({active:!e.state.active},(function(){e.state.active&&l(Me(Me(Me(Me({},n,r[n]),o,r[n]),i,r[n]),a,r[n]))}))}},React.createElement("i",{className:"dashicons dashicons-admin-links"})))))}}])&&He(r.prototype,n),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,n}($e);function Ze(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var et=["fontFamily","lineHeight","marginTop","marginRight","marginBottom","marginLeft","paddingTop","paddingRight","paddingBottom","paddingLeft"];const tt=function(e,t){var r=(0,Se.pickBy)(e,(function(e,t){return!!e&&et.includes(t)})),n=Object.entries(r).reduce((function(e,t){var r,n,o=(n=2,function(e){if(Array.isArray(e))return e}(r=t)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,c=[],l=!0,u=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(c.push(n.value),c.length!==t);l=!0);}catch(e){u=!0,o=e}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(u)throw o}}return c}}(r,n)||function(e,t){if(e){if("string"==typeof e)return Ze(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Ze(e,t):void 0}}(r,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),i=o[0],a=o[1];return e[i]="number"==typeof a?"".concat(a,"px"):a,e}),{});return void 0!==e.lineHeight&&e.lineHeight&&(n.lineHeight=e.lineHeight+"px"),n};function rt(e){return rt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},rt(e)}var nt=["select"];function ot(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function it(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ot(Object(r),!0).forEach((function(t){at(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ot(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function at(e,t,r){return(t=function(e){var t=function(e){if("object"!=rt(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=rt(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==rt(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ct(){return ct=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ct.apply(null,arguments)}var lt=wp.i18n.__,ut=wp.blockEditor.InspectorControls,st=wp.compose,ft=st.createHigherOrderComponent,pt=st.compose,dt=wp.components,yt=dt.PanelBody,ht=dt.SelectControl,mt=dt.TextControl,vt=wp.element.Fragment,gt=wp.hooks.addFilter,bt=wp.data.withSelect,wt=["core/paragraph","core/heading","core/image"];gt("blocks.registerBlockType","fluentcrm/typography/attributes",(function(e,t){return wt.includes(t)?(void 0!==e.attributes&&(e.attributes=Object.assign(e.attributes,{fontFamily:{type:"string",default:""},lineHeight:{type:"number",default:""},paddingTop:{type:"number",default:""},paddingRight:{type:"number",default:""},paddingBottom:{type:"number",default:""},paddingLeft:{type:"number",default:""},marginTop:{type:"number",default:""},marginRight:{type:"number",default:""},marginBottom:{type:"number",default:""},marginLeft:{type:"number",default:""}})),e):e}),99999),gt("editor.BlockEdit","fluentcrm/typography",ft((function(e){return function(t){if(!wt.includes(t.name))return React.createElement(e,t);var r=t.attributes,n=t.name,o=t.setAttributes;return React.createElement(vt,null,React.createElement(e,t),React.createElement(ut,{key:"zzz-fluent-crm-typography"},React.createElement(yt,{initialOpen:!0,title:lt("core/image"===n?"Advanced Spacing":"Advanced Typography")},("core/heading"===n||"core/paragraph"===n)&&React.createElement(vt,null,React.createElement(ht,{label:lt("Font Family"),value:r.fontFamily,options:[{label:"Select a font family",value:""},{label:"Arial",value:"Arial, 'Helvetica Neue', Helvetica, sans-serif"},{label:"Comic Sans",value:"'Comic Sans MS', 'Marker Felt-Thin', Arial, sans-serif"},{label:"Courier New",value:"'Courier New', Courier, 'Lucida Sans Typewriter', 'Lucida Typewriter', monospace"},{label:"Georgia",value:"Georgia, Times, 'Times New Roman', serif"},{label:"Helvetica",value:"Helvetica , Arial, Verdana, sans-serif"},{label:"Lucida",value:"Lucida Sans Unicode', 'Lucida Grande', sans-serif"},{label:"Tahoma",value:"Tahoma, Verdana, Segoe, sans-serif"},{label:"Times New Roman",value:"'Times New Roman', Times, Baskerville, Georgia, serif"},{label:"Trebuchet MS",value:"'Trebuchet MS', 'Lucida Grande', 'Lucida Sans Unicode', 'Lucida Sans', Tahoma, sans-serif"},{label:"Verdana",value:"Verdana, Geneva, sans-serif"},{label:"Lato",value:"'Lato', 'Helvetica Neue', Helvetica, Arial, sans-serif"},{label:"Lora",value:"'Lora', Georgia, 'Times New Roman', serif"},{label:"Merriweather",value:"'Merriweather', Georgia, 'Times New Roman', serif"},{label:"Merriweather Sans",value:"'Merriweather Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif"},{label:"Noticia Text",value:"'Noticia Text', Georgia, 'Times New Roman', serif"},{label:"Open Sans",value:"'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif"},{label:"Roboto",value:"'Roboto', 'Helvetica Neue', Helvetica, Arial, sans-serif"},{label:"Source Sans Pro",value:"'Source Sans Pro', 'Helvetica Neue', Helvetica, Arial, sans-serif"}],onChange:function(e){return o({fontFamily:e})}}),React.createElement(mt,{label:lt("Line Height"),type:"number",value:r.lineHeight,onChange:function(e){return o({lineHeight:parseInt(e)})}})),React.createElement(Xe,ct({},t,{label:lt("Padding"),top:"paddingTop",right:"paddingRight",bottom:"paddingBottom",left:"paddingLeft"})),React.createElement(Xe,ct({},t,{label:lt("Margin"),top:"marginTop",right:"marginRight",bottom:"marginBottom",left:"marginLeft"})))))}}),"withControls"),99999);var St=pt(bt((function(e){return{selected:e("core/block-editor").getSelectedBlock(),select:e}})));gt("editor.BlockListBlock","fluentcrm/withTypographySettings",ft((function(e){return St((function(t){var r=t.select,n=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.includes(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],t.includes(r)||{}.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(t,nt),o=n.wrapperProps||{},i=r("core/block-editor").getBlock(n.clientId);return i?(wt.includes(i.name)&&i.attributes&&(o=it(it({},o),{},{style:it(it({},o.style||{}),tt(i.attributes,i.name))})),React.createElement(e,ct({},n,{wrapperProps:o}))):React.createElement(e,ct({},n,{wrapperProps:o}))}))}),"withTypographySettings"),999999),gt("blocks.getSaveContent.extraProps","fluentcrm/applyTypographySettings",(function(e,t,r){return wt.includes(t.name)?(void 0!==e.style?e.style=Object.assign(e.style,tt(r,t.name)):e.style=tt(r,t.name),e):e}),99999);const Et=window.wp.url,Ot=window.wp.dom;window.wp.apiFetch;var _t=r(875),kt=r.n(_t);const jt=window.wp.htmlEntities;function xt(e){return xt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},xt(e)}function Rt(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Lt(n.key),n)}}function Lt(e){var t=function(e){if("object"!=xt(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=xt(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==xt(t)?t:t+""}function Tt(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(Tt=function(){return!!e})()}function Pt(e){return Pt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Pt(e)}function Ct(e,t){return Ct=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Ct(e,t)}function At(){At=function(){return t};var e,t={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(e,t,r){e[t]=r.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",l=i.toStringTag||"@@toStringTag";function u(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,r){return e[t]=r}}function s(e,t,r,n){var i=t&&t.prototype instanceof v?t:v,a=Object.create(i.prototype),c=new T(n||[]);return o(a,"_invoke",{value:j(e,r,c)}),a}function f(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=s;var p="suspendedStart",d="suspendedYield",y="executing",h="completed",m={};function v(){}function g(){}function b(){}var w={};u(w,a,(function(){return this}));var S=Object.getPrototypeOf,E=S&&S(S(P([])));E&&E!==r&&n.call(E,a)&&(w=E);var O=b.prototype=v.prototype=Object.create(w);function _(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function k(e,t){function r(o,i,a,c){var l=f(e[o],e,i);if("throw"!==l.type){var u=l.arg,s=u.value;return s&&"object"==xt(s)&&n.call(s,"__await")?t.resolve(s.__await).then((function(e){r("next",e,a,c)}),(function(e){r("throw",e,a,c)})):t.resolve(s).then((function(e){u.value=e,a(u)}),(function(e){return r("throw",e,a,c)}))}c(l.arg)}var i;o(this,"_invoke",{value:function(e,n){function o(){return new t((function(t,o){r(e,n,t,o)}))}return i=i?i.then(o,o):o()}})}function j(t,r,n){var o=p;return function(i,a){if(o===y)throw Error("Generator is already running");if(o===h){if("throw"===i)throw a;return{value:e,done:!0}}for(n.method=i,n.arg=a;;){var c=n.delegate;if(c){var l=x(c,n);if(l){if(l===m)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===p)throw o=h,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=y;var u=f(t,r,n);if("normal"===u.type){if(o=n.done?h:d,u.arg===m)continue;return{value:u.arg,done:n.done}}"throw"===u.type&&(o=h,n.method="throw",n.arg=u.arg)}}}function x(t,r){var n=r.method,o=t.iterator[n];if(o===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,x(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),m;var i=f(o,t.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,m;var a=i.arg;return a?a.done?(r[t.resultName]=a.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,m):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,m)}function R(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function L(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function T(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(R,this),this.reset(!0)}function P(t){if(t||""===t){var r=t[a];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,i=function r(){for(;++o<t.length;)if(n.call(t,o))return r.value=t[o],r.done=!1,r;return r.value=e,r.done=!0,r};return i.next=i}}throw new TypeError(xt(t)+" is not iterable")}return g.prototype=b,o(O,"constructor",{value:b,configurable:!0}),o(b,"constructor",{value:g,configurable:!0}),g.displayName=u(b,l,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===g||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,b):(e.__proto__=b,u(e,l,"GeneratorFunction")),e.prototype=Object.create(O),e},t.awrap=function(e){return{__await:e}},_(k.prototype),u(k.prototype,c,(function(){return this})),t.AsyncIterator=k,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new k(s(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},_(O),u(O,l,"Generator"),u(O,a,(function(){return this})),u(O,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=P,T.prototype={constructor:T,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(L),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function o(n,o){return c.type="throw",c.arg=t,r.next=n,o&&(r.method="next",r.arg=e),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],c=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var l=n.call(a,"catchLoc"),u=n.call(a,"finallyLoc");if(l&&u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,m):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),m},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),L(r),m}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var o=n.arg;L(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:P(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),m}},t}function Nt(e,t,r,n,o,i,a){try{var c=e[i](a),l=c.value}catch(e){return void r(e)}c.done?t(l):Promise.resolve(l).then(n,o)}var Ft=function(e){return e.stopPropagation()},It=function(){var e,t=(e=At().mark((function e(t){var r;return At().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,window.FLUENTCRM.$get("smart-links",{search:t});case 2:return r=e.sent,e.abrupt("return",(0,Se.map)(r.action_links.data,(function(e){return{url:e.short_url,title:(0,jt.decodeEntities)(e.title),target_url:e.target_url}})));case 4:case"end":return e.stop()}}),e)})),function(){var t=this,r=arguments;return new Promise((function(n,o){var i=e.apply(t,r);function a(e){Nt(i,n,o,a,c,"next",e)}function c(e){Nt(i,n,o,a,c,"throw",e)}a(void 0)}))});return function(_x){return t.apply(this,arguments)}}(),Bt=function(e){function t(e){var r,n=e.autocompleteRef;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),(r=function(e,t,r){return t=Pt(t),function(e,t){if(t&&("object"==xt(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,Tt()?Reflect.construct(t,r||[],Pt(e).constructor):t.apply(e,r))}(this,t,arguments)).onChange=r.onChange.bind(r),r.onKeyDown=r.onKeyDown.bind(r),r.autocompleteRef=n||(0,s.createRef)(),r.inputRef=(0,s.createRef)(),r.updateSuggestions=(0,Se.throttle)(r.updateSuggestions.bind(r),200),r.suggestionNodes=[],r.state={suggestions:[],showSuggestions:!1,selectedSuggestion:null},r}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Ct(e,t)}(t,e),r=t,(n=[{key:"componentDidUpdate",value:function(){var e=this,t=this.state,r=t.showSuggestions,n=t.selectedSuggestion;r&&null!==n&&!this.scrollingIntoView&&(this.scrollingIntoView=!0,kt()(this.suggestionNodes[n],this.autocompleteRef.current,{onlyScrollIfNeeded:!0}),this.props.setTimeout((function(){e.scrollingIntoView=!1}),100))}},{key:"componentWillUnmount",value:function(){delete this.suggestionsRequest}},{key:"bindSuggestionNode",value:function(e){var t=this;return function(r){t.suggestionNodes[e]=r}}},{key:"updateSuggestions",value:function(e){var t=this;if(e.length<2||/^https?:/.test(e))this.setState({showSuggestions:!1,selectedSuggestion:null,loading:!1});else{this.setState({showSuggestions:!0,selectedSuggestion:null,loading:!0});var r=It(e);r.then((function(e){t.suggestionsRequest===r&&(t.setState({suggestions:e,loading:!1}),e.length?t.props.debouncedSpeak((0,h.sprintf)((0,h._n)("%d result found, use up and down arrow keys to navigate.","%d results found, use up and down arrow keys to navigate.",e.length),e.length),"assertive"):t.props.debouncedSpeak((0,h.__)("No results."),"assertive"))})).catch((function(){t.suggestionsRequest===r&&t.setState({loading:!1})})),this.suggestionsRequest=r}}},{key:"onChange",value:function(e){var t=e.target.value;this.props.onChange(t),this.updateSuggestions(t)}},{key:"onKeyDown",value:function(e){var t=this.state,r=t.showSuggestions,n=t.selectedSuggestion,o=t.suggestions,i=t.loading;if(r&&o.length&&!i){var a=this.state.suggestions[this.state.selectedSuggestion];switch(e.keyCode){case I.UP:e.stopPropagation(),e.preventDefault();var c=n?n-1:o.length-1;this.setState({selectedSuggestion:c});break;case I.DOWN:e.stopPropagation(),e.preventDefault();var l=null===n||n===o.length-1?0:n+1;this.setState({selectedSuggestion:l});break;case I.TAB:null!==this.state.selectedSuggestion&&(this.selectLink(a),this.props.speak((0,h.__)("Link selected.")));break;case I.ENTER:null!==this.state.selectedSuggestion&&(e.stopPropagation(),this.selectLink(a))}}else switch(e.keyCode){case I.UP:0!==e.target.selectionStart&&(e.stopPropagation(),e.preventDefault(),e.target.setSelectionRange(0,0));break;case I.DOWN:this.props.value.length!==e.target.selectionStart&&(e.stopPropagation(),e.preventDefault(),e.target.setSelectionRange(this.props.value.length,this.props.value.length))}}},{key:"selectLink",value:function(e){this.props.onChange(e.url,e),this.setState({selectedSuggestion:null,showSuggestions:!1})}},{key:"handleOnClick",value:function(e){this.selectLink(e),this.inputRef.current.focus()}},{key:"render",value:function(){var e=this,t=this.props,r=t.value,n=void 0===r?"":r,o=t.autoFocus,i=void 0===o||o,a=t.instanceId,c=t.className,u=this.state,s=u.showSuggestions,f=u.suggestions,p=u.selectedSuggestion,d=u.loading,m="block-editor-url-input-suggestions-".concat(a),v="block-editor-url-input-suggestion-".concat(a);return React.createElement("div",{className:y()("editor-url-input block-editor-url-input",c)},React.createElement("input",{autoFocus:i,type:"text","aria-label":(0,h.__)("URL"),required:!0,value:n,onChange:this.onChange,onInput:Ft,placeholder:(0,h.__)("Paste or type to search for your Smart Link"),onKeyDown:this.onKeyDown,role:"combobox","aria-expanded":s,"aria-autocomplete":"list","aria-owns":m,"aria-activedescendant":null!==p?"".concat(v,"-").concat(p):void 0,ref:this.inputRef}),d&&React.createElement(l.Spinner,null),s&&!!f.length&&React.createElement(l.Popover,{position:"bottom",noArrow:!0,focusOnMount:!1},React.createElement("div",{className:"editor-url-input__suggestions block-editor-url-input__suggestions",id:m,ref:this.autocompleteRef,role:"listbox"},f.map((function(t,r){return React.createElement("button",{key:t.id,role:"option",tabIndex:"-1",title:t.target_url,id:"".concat(v,"-").concat(r),ref:e.bindSuggestionNode(r),className:y()("editor-url-input__suggestion block-editor-url-input__suggestion",{"is-selected":r===p}),onClick:function(){return e.handleOnClick(t)},"aria-selected":r===p},t.title)})))))}}])&&Rt(r.prototype,n),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,n}(s.Component);const Ut=(0,m.compose)(m.withSafeTimeout,l.withSpokenMessages,m.withInstanceId,(0,b.withSelect)((function(e){var t=e("core/block-editor").getSettings,r=t().fetchLinkSuggestions;return r||(r=t().__experimentalFetchLinkSuggestions),{fetchLinkSuggestions:r}})))(Bt);function Dt(e){if(!e)return!1;var t=e.trim();if(!t)return!1;if(/^\S+:/.test(t)){var r=(0,Et.getProtocol)(t);if(!(0,Et.isValidProtocol)(r))return!1;if((0,Se.startsWith)(r,"http")&&!/^https?:\/\/[^\/\s]/i.test(t))return!1;var n=(0,Et.getAuthority)(t);if(!(0,Et.isValidAuthority)(n))return!1;var o=(0,Et.getPath)(t);if(o&&!(0,Et.isValidPath)(o))return!1;var i=(0,Et.getQueryString)(t);if(i&&!(0,Et.isValidQueryString)(i))return!1;var a=(0,Et.getFragment)(t);if(a&&!(0,Et.isValidFragment)(a))return!1}return!((0,Se.startsWith)(t,"#")&&!(0,Et.isValidFragment)(t))}function Mt(e){var t=e.url,r=e.opensInNewWindow,n=e.text,o=e.noFollow,i=e.isSponsored,a={type:"core/link",attributes:{url:t}};if(a.attributes.rel="",o&&(a.attributes.rel+="nofollow noindex "),i&&(a.attributes.rel+="sponsored "),r){
// translators: accessibility label for external links, where the argument is the link text
var c=(0,h.sprintf)((0,h.__)("%s (opens in a new tab)"),n);a.attributes.target="_blank",a.attributes.rel+="noreferrer noopener",a.attributes["aria-label"]=c}return""===a.attributes.rel&&delete a.attributes.rel,a}function Ht(e){return Ht="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ht(e)}var Gt=["isActive","addingLink","value"];function Wt(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Vt(n.key),n)}}function Vt(e){var t=function(e){if("object"!=Ht(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=Ht(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==Ht(t)?t:t+""}function zt(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(zt=function(){return!!e})()}function Kt(e){return Kt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Kt(e)}function $t(e,t){return $t=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},$t(e,t)}function Yt(){return Yt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Yt.apply(null,arguments)}var qt=function(e){return e.stopPropagation()};function Jt(e,t){return e.addingLink||t.editLink}var Qt=function(e){var t=e.value,r=e.onChangeInputValue,n=e.onKeyDown,o=e.submitLink,i=e.autocompleteRef;return React.createElement("form",{className:"editor-format-toolbar__link-container-content block-editor-format-toolbar__link-container-content",onKeyPress:qt,onKeyDown:n,onSubmit:o},React.createElement(Ut,{value:t,onChange:r,autocompleteRef:i}),React.createElement(l.IconButton,{icon:"editor-break",label:(0,h.__)("Insert Smart Link"),type:"submit"}))},Xt=function(e){var t=e.url,r=(0,Et.prependHTTP)(t),n=y()("editor-format-toolbar__link-container-value block-editor-format-toolbar__link-container-value",{"has-invalid-link":!Dt(r)});return t?React.createElement(l.ExternalLink,{className:n,href:t},(0,Et.filterURLForDisplay)((0,Et.safeDecodeURI)(t))):React.createElement("span",{className:n})},Zt=function(e){var t=e.isActive,r=e.addingLink,n=e.value,o=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.includes(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],t.includes(r)||{}.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,Gt),i=(0,s.useMemo)((function(){var e=window.getSelection(),t=e.rangeCount>0?e.getRangeAt(0):null;if(t){if(r)return(0,Ot.getRectangleFromRange)(t);var n=t.startContainer;for(n=n.nextElementSibling||n;n.nodeType!==window.Node.ELEMENT_NODE;)n=n.parentNode;var o=n.closest("a");return o?o.getBoundingClientRect():void 0}}),[t,r,n.start,n.end]);return i?React.createElement(u.URLPopover,Yt({anchorRect:i},o)):null},er=function(e){var t=e.url,r=e.editLink;return React.createElement("div",{className:"editor-format-toolbar__link-container-content block-editor-format-toolbar__link-container-content",onKeyPress:qt},React.createElement(Xt,{url:t}),React.createElement(l.IconButton,{icon:"edit",label:(0,h.__)("Edit"),onClick:r}))},tr=function(e){function t(){var e;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),(e=function(e,t,r){return t=Kt(t),function(e,t){if(t&&("object"==Ht(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,zt()?Reflect.construct(t,r||[],Kt(e).constructor):t.apply(e,r))}(this,t,arguments)).editLink=e.editLink.bind(e),e.submitLink=e.submitLink.bind(e),e.onKeyDown=e.onKeyDown.bind(e),e.onChangeInputValue=e.onChangeInputValue.bind(e),e.setNoFollow=e.setNoFollow.bind(e),e.setIsSponsored=e.setIsSponsored.bind(e),e.setLinkTarget=e.setLinkTarget.bind(e),e.onClickOutside=e.onClickOutside.bind(e),e.resetState=e.resetState.bind(e),e.autocompleteRef=(0,s.createRef)(),e.state={noFollow:!1,opensInNewWindow:!1,isSponsored:!1,inputValue:"",newLinkUrl:"",newLinkSlug:"",creatingLink:!1,createdLink:!1,createdLinkError:!1},e}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&$t(e,t)}(t,e),r=t,o=[{key:"getDerivedStateFromProps",value:function(e,t){var r=e.activeAttributes,n=r.url,o=r.target,i=(r.isSponsored,"_blank"===o);if(!Jt(e,t)){if(n!==t.inputValue)return{inputValue:n};if(i!==t.opensInNewWindow)return{opensInNewWindow:i}}return null}}],(n=[{key:"onKeyDown",value:function(e){[I.LEFT,I.DOWN,I.RIGHT,I.UP,I.BACKSPACE,I.ENTER].indexOf(e.keyCode)>-1&&e.stopPropagation()}},{key:"onChangeInputValue",value:function(e){this.setState({inputValue:e})}},{key:"setLinkTarget",value:function(e){var t=this.props,r=t.activeAttributes.url,n=void 0===r?"":r,o=t.value,i=t.onChange;if(this.setState({opensInNewWindow:e}),!Jt(this.props,this.state)){var a=(0,we.getTextContent)((0,we.slice)(o));i((0,we.applyFormat)(o,Mt({url:n,opensInNewWindow:e,text:a})))}}},{key:"setNoFollow",value:function(e){var t=this.props,r=t.activeAttributes.url,n=void 0===r?"":r,o=t.value,i=t.onChange;if(this.setState({noFollow:e}),!Jt(this.props,this.state)){var a=(0,we.getTextContent)((0,we.slice)(o));i((0,we.applyFormat)(o,Mt({url:n,opensInNewWindow,text:a,noFollow:e,isSponsored})))}}},{key:"setIsSponsored",value:function(e){var t=this.props,r=t.activeAttributes.url,n=void 0===r?"":r,o=t.value,i=t.onChange;if(this.setState({isSponsored:e}),!Jt(this.props,this.state)){var a=(0,we.getTextContent)((0,we.slice)(o));i((0,we.applyFormat)(o,Mt({url:n,opensInNewWindow,text:a,noFollow,isSponsored:e})))}}},{key:"editLink",value:function(e){this.setState({editLink:!0}),e.preventDefault()}},{key:"submitLink",value:function(e){var t=this.props,r=t.isActive,n=t.value,o=t.onChange,i=t.speak,a=this.state,c=a.inputValue,l=a.opensInNewWindow,u=a.noFollow,s=a.isSponsored,f=(0,Et.prependHTTP)(c),p=Mt({url:f,opensInNewWindow:l,text:(0,we.getTextContent)((0,we.slice)(n)),noFollow:u,isSponsored:s});if(e.preventDefault(),(0,we.isCollapsed)(n)&&!r){var d=(0,we.applyFormat)((0,we.create)({text:f}),p,0,f.length);o((0,we.insert)(n,d))}else o((0,we.applyFormat)(n,p));this.resetState(),Dt(f)?i(r?(0,h.__)("Link edited."):(0,h.__)("Link inserted."),"assertive"):i((0,h.__)("Warning: the link has been inserted but may have errors. Please test it."),"assertive")}},{key:"onClickOutside",value:function(e){var t=this.autocompleteRef.current;t&&t.contains(e.target)||this.resetState()}},{key:"resetState",value:function(){this.props.stopAddingLink(),this.setState({editLink:!1})}},{key:"render",value:function(){var e=this,t=this.props,r=t.isActive,n=t.activeAttributes.url,o=t.addingLink,i=t.value;if(!r&&!o)return null;var a=this.state,c=a.inputValue,u=a.noFollow,f=a.opensInNewWindow,p=a.isSponsored,d=(a.newLinkUrl,a.newLinkSlug,a.creatingLink,a.createdLink,a.createdLinkError,Jt(this.props,this.state));return React.createElement(Zt,{className:"pretty-link-inserter",value:i,isActive:r,addingLink:o,onClickOutside:this.onClickOutside,onClose:this.resetState,focusOnMount:!!d&&"firstElement",renderSettings:function(){return React.createElement(s.Fragment,null,React.createElement("div",null,React.createElement(l.ToggleControl,{label:(0,h.__)("Open in New Tab"),checked:f,onChange:e.setLinkTarget}),React.createElement(l.ToggleControl,{label:(0,h.__)("Nofollow"),checked:u,onChange:e.setNoFollow}),React.createElement(l.ToggleControl,{label:(0,h.__)("Sponsored Link"),checked:p,onChange:e.setIsSponsored})))}},d?React.createElement(Qt,{value:c,onChangeInputValue:this.onChangeInputValue,onKeyDown:this.onKeyDown,submitLink:this.submitLink,autocompleteRef:this.autocompleteRef}):React.createElement(er,{url:n,editLink:this.editLink}))}}])&&Wt(r.prototype,n),o&&Wt(r,o),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,n,o}(s.Component);const rr=(0,l.withSpokenMessages)(tr);function nr(e){return nr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},nr(e)}function or(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,ir(n.key),n)}}function ir(e){var t=function(e){if("object"!=nr(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=nr(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==nr(t)?t:t+""}function ar(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(ar=function(){return!!e})()}function cr(e){return cr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},cr(e)}function lr(e,t){return lr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},lr(e,t)}var ur="fluentcrm/smart-links",sr=(0,h.__)("SmartLinks"),fr={name:ur,title:sr,tagName:"a",className:"smart-link",attributes:{url:"href",target:"target"},edit:(0,l.withSpokenMessages)(function(e){function t(){var e;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),(e=function(e,t,r){return t=cr(t),function(e,t){if(t&&("object"==nr(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,ar()?Reflect.construct(t,r||[],cr(e).constructor):t.apply(e,r))}(this,t,arguments)).addLink=e.addLink.bind(e),e.stopAddingLink=e.stopAddingLink.bind(e),e.onRemoveFormat=e.onRemoveFormat.bind(e),e.state={addingLink:!1},e}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&lr(e,t)}(t,e),r=t,(n=[{key:"addLink",value:function(){var e=this.props,t=e.value,r=e.onChange,n=(0,we.getTextContent)((0,we.slice)(t));n&&(0,Et.isURL)(n)?r((0,we.applyFormat)(t,{type:ur,attributes:{url:n}})):this.setState({addingLink:!0})}},{key:"stopAddingLink",value:function(){this.setState({addingLink:!1})}},{key:"onRemoveFormat",value:function(){var e=this.props,t=e.value,r=e.onChange,n=e.speak;r((0,we.removeFormat)(t,ur)),n((0,h.__)("Link removed."),"assertive")}},{key:"render",value:function(){var e=this.props,t=e.isActive,r=e.activeAttributes,n=e.value,o=e.onChange;return React.createElement(React.Fragment,null,React.createElement(u.RichTextShortcut,{type:"primary",character:"p",onUse:this.addLink}),React.createElement(u.RichTextShortcut,{type:"primaryShift",character:"p",onUse:this.onRemoveFormat}),t&&React.createElement(u.RichTextToolbarButton,{icon:"star-filled",title:(0,h.__)("Unlink"),onClick:this.onRemoveFormat,isActive:t,shortcutType:"primaryShift",shortcutCharacter:"p"}),!t&&React.createElement(u.RichTextToolbarButton,{icon:"star-filled",title:sr,onClick:this.addLink,isActive:t,shortcutType:"primary",shortcutCharacter:"p"}),React.createElement(rr,{addingLink:this.state.addingLink,stopAddingLink:this.stopAddingLink,isActive:t,activeAttributes:r,value:n,onChange:o}))}}])&&or(r.prototype,n),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,n}(s.Component))};const pr="@@redux-undo/UNDO",dr="@@redux-undo/REDO",yr="@@redux-undo/JUMP_TO_FUTURE",hr="@@redux-undo/JUMP_TO_PAST",mr="@@redux-undo/JUMP",vr="@@redux-undo/CLEAR_HISTORY",gr={undo:()=>({type:pr}),redo:()=>({type:dr}),jumpToFuture:e=>({type:yr,index:e}),jumpToPast:e=>({type:hr,index:e}),jump:e=>({type:mr,index:e}),clearHistory:()=>({type:vr})};function br(e,t=[]){return Array.isArray(e)?e:"string"==typeof e?[e]:t}function wr(e,t,r,n=null){return{past:e,present:t,future:r,group:n,_latestUnfiltered:t,index:e.length,limit:e.length+r.length+1}}let Sr,Er;function Or(e,t,r){return[`%c${e}`,`color: ${t}; font-weight: bold`,r]}function _r(e){Sr&&(console.group?Er.next=Or("next history","#4CAF50",e):Er.next=["next history",e],function(){const{header:e,prev:t,next:r,action:n,msgs:o}=Er;console.group?(console.groupCollapsed(...e),console.log(...t),console.log(...n),console.log(...r),console.log(...o),console.groupEnd()):(console.log(...e),console.log(...t),console.log(...n),console.log(...r),console.log(...o))}())}function kr(...e){Sr&&(Er.msgs=Er.msgs.concat([...e,"\n"]))}function jr(e,t){const r=wr([],e,[]);return t&&(r._latestUnfiltered=null),r}function xr(e,t){if(t<0||t>=e.future.length)return e;const{past:r,future:n,_latestUnfiltered:o}=e;return wr([...r,o,...n.slice(0,t)],n[t],n.slice(t+1))}function Rr(e,t){if(t<0||t>=e.past.length)return e;const{past:r,future:n,_latestUnfiltered:o}=e,i=r.slice(0,t),a=[...r.slice(t+1),o,...n];return wr(i,r[t],a)}function Lr(e,t){return t>0?xr(e,t-1):t<0?Rr(e,e.past.length+t):e}var Tr="UPDATE_BLOCKS",Pr="PERSIST_BLOCKS",Cr="FETCH_BLOCKS_FROM_STORAGE",Ar="PERSIST_BLOCKS_TO_STORAGE";const Nr=function(e,t={}){!function(e){Sr=e}(t.debug);const r={limit:void 0,filter:()=>!0,groupBy:()=>null,undoType:pr,redoType:dr,jumpToPastType:hr,jumpToFutureType:yr,jumpType:mr,neverSkipReducer:!1,ignoreInitialState:!1,syncFilter:!1,...t,initTypes:br(t.initTypes,["@@redux-undo/INIT"]),clearHistoryType:br(t.clearHistoryType,[vr])},n=r.neverSkipReducer?(t,r,...n)=>({...t,present:e(t.present,r,...n)}):e=>e;let o;return(t=o,i={},...a)=>{!function(e,t){Er={header:[],prev:[],action:[],next:[],msgs:[]},Sr&&(console.group?(Er.header=["%credux-undo","font-style: italic","action",e.type],Er.action=Or("action","#03A9F4",e),Er.prev=Or("prev history","#9E9E9E",t)):(Er.header=["redux-undo action",e.type],Er.action=["action",e],Er.prev=["prev history",t]))}(i,t);let c,l=t;if(!o){if(kr("history is uninitialized"),void 0===t)return l=jr(e(t,{type:"@@redux-undo/CREATE_HISTORY"},...a),r.ignoreInitialState),kr("do not set initialState on probe actions"),_r(l),l;!function(e){return typeof e.present<"u"&&typeof e.future<"u"&&typeof e.past<"u"&&Array.isArray(e.future)&&Array.isArray(e.past)}(t)?(l=o=jr(t,r.ignoreInitialState),kr("initialHistory initialized: initialState is not a history",o)):(l=o=r.ignoreInitialState?t:wr(t.past,t.present,t.future),kr("initialHistory initialized: initialState is a history",o))}switch(i.type){case void 0:return l;case r.undoType:return c=Lr(l,-1),kr("perform undo"),_r(c),n(c,i,...a);case r.redoType:return c=Lr(l,1),kr("perform redo"),_r(c),n(c,i,...a);case r.jumpToPastType:return c=Rr(l,i.index),kr(`perform jumpToPast to ${i.index}`),_r(c),n(c,i,...a);case r.jumpToFutureType:return c=xr(l,i.index),kr(`perform jumpToFuture to ${i.index}`),_r(c),n(c,i,...a);case r.jumpType:return c=Lr(l,i.index),kr(`perform jump to ${i.index}`),_r(c),n(c,i,...a);case function(e,t){return t.indexOf(e)>-1?e:!e}(i.type,r.clearHistoryType):return c=jr(l.present,r.ignoreInitialState),kr("perform clearHistory"),_r(c),n(c,i,...a);default:if(c=e(l.present,i,...a),r.initTypes.some((e=>e===i.type)))return kr("reset history due to init action"),_r(o),o;if(l._latestUnfiltered===c)return l;if("function"==typeof r.filter&&!r.filter(i,c,l)){const e=wr(l.past,c,l.future,l.group);return r.syncFilter||(e._latestUnfiltered=l._latestUnfiltered),kr("filter ignored action, not storing it in past"),_r(e),e}const t=r.groupBy(i,c,l);if(null!=t&&t===l.group){const e=wr(l.past,c,l.future,l.group);return kr("groupBy grouped the action with the previous action"),_r(e),e}return l=function(e,t,r,n){const o=e.past.length+1;kr("inserting",t),kr("new free: ",r-o);const{past:i,_latestUnfiltered:a}=e,c=r&&r<=o,l=i.slice(c?1:0);return wr(null!=a?[...l,a]:l,t,[],n)}(l,c,r.limit,t),kr("inserted new state into history"),_r(l),l}}}((function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1?arguments[1]:void 0;switch(t.type){case Tr:case Pr:return{blocks:t.blocks}}return e}),{filter:function(e){const t=br(e);return e=>t.indexOf(e.type)>=0}(Pr)});var Fr=function(e){return e.present.blocks||[]},Ir=function(e){var t;return null===(t=e.past)||void 0===t?void 0:t.length},Br=function(e){var t;return null===(t=e.future)||void 0===t?void 0:t.length};function Ur(e){return Ur="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ur(e)}function Dr(){Dr=function(){return t};var e,t={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(e,t,r){e[t]=r.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",l=i.toStringTag||"@@toStringTag";function u(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,r){return e[t]=r}}function s(e,t,r,n){var i=t&&t.prototype instanceof v?t:v,a=Object.create(i.prototype),c=new T(n||[]);return o(a,"_invoke",{value:j(e,r,c)}),a}function f(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=s;var p="suspendedStart",d="suspendedYield",y="executing",h="completed",m={};function v(){}function g(){}function b(){}var w={};u(w,a,(function(){return this}));var S=Object.getPrototypeOf,E=S&&S(S(P([])));E&&E!==r&&n.call(E,a)&&(w=E);var O=b.prototype=v.prototype=Object.create(w);function _(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function k(e,t){function r(o,i,a,c){var l=f(e[o],e,i);if("throw"!==l.type){var u=l.arg,s=u.value;return s&&"object"==Ur(s)&&n.call(s,"__await")?t.resolve(s.__await).then((function(e){r("next",e,a,c)}),(function(e){r("throw",e,a,c)})):t.resolve(s).then((function(e){u.value=e,a(u)}),(function(e){return r("throw",e,a,c)}))}c(l.arg)}var i;o(this,"_invoke",{value:function(e,n){function o(){return new t((function(t,o){r(e,n,t,o)}))}return i=i?i.then(o,o):o()}})}function j(t,r,n){var o=p;return function(i,a){if(o===y)throw Error("Generator is already running");if(o===h){if("throw"===i)throw a;return{value:e,done:!0}}for(n.method=i,n.arg=a;;){var c=n.delegate;if(c){var l=x(c,n);if(l){if(l===m)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===p)throw o=h,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=y;var u=f(t,r,n);if("normal"===u.type){if(o=n.done?h:d,u.arg===m)continue;return{value:u.arg,done:n.done}}"throw"===u.type&&(o=h,n.method="throw",n.arg=u.arg)}}}function x(t,r){var n=r.method,o=t.iterator[n];if(o===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,x(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),m;var i=f(o,t.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,m;var a=i.arg;return a?a.done?(r[t.resultName]=a.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,m):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,m)}function R(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function L(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function T(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(R,this),this.reset(!0)}function P(t){if(t||""===t){var r=t[a];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,i=function r(){for(;++o<t.length;)if(n.call(t,o))return r.value=t[o],r.done=!1,r;return r.value=e,r.done=!0,r};return i.next=i}}throw new TypeError(Ur(t)+" is not iterable")}return g.prototype=b,o(O,"constructor",{value:b,configurable:!0}),o(b,"constructor",{value:g,configurable:!0}),g.displayName=u(b,l,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===g||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,b):(e.__proto__=b,u(e,l,"GeneratorFunction")),e.prototype=Object.create(O),e},t.awrap=function(e){return{__await:e}},_(k.prototype),u(k.prototype,c,(function(){return this})),t.AsyncIterator=k,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new k(s(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},_(O),u(O,l,"Generator"),u(O,a,(function(){return this})),u(O,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=P,T.prototype={constructor:T,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(L),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function o(n,o){return c.type="throw",c.arg=t,r.next=n,o&&(r.method="next",r.arg=e),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],c=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var l=n.call(a,"catchLoc"),u=n.call(a,"finallyLoc");if(l&&u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,m):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),m},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),L(r),m}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var o=n.arg;L(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:P(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),m}},t}function Mr(){return gr.undo()}function Hr(){return gr.redo()}function Gr(){return gr.clearHistory()}function Wr(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return Dr().mark((function r(){return Dr().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:if(!t){r.next=3;break}return r.next=3,zr(e);case 3:return r.abrupt("return",{type:t?Pr:Tr,blocks:e});case 4:case"end":return r.stop()}}),r)}))()}function Vr(){return{type:Cr}}function zr(e){return{type:Ar,blocks:e}}function Kr(e){return Kr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Kr(e)}function $r(){$r=function(){return t};var e,t={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(e,t,r){e[t]=r.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",l=i.toStringTag||"@@toStringTag";function u(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,r){return e[t]=r}}function s(e,t,r,n){var i=t&&t.prototype instanceof v?t:v,a=Object.create(i.prototype),c=new T(n||[]);return o(a,"_invoke",{value:j(e,r,c)}),a}function f(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=s;var p="suspendedStart",d="suspendedYield",y="executing",h="completed",m={};function v(){}function g(){}function b(){}var w={};u(w,a,(function(){return this}));var S=Object.getPrototypeOf,E=S&&S(S(P([])));E&&E!==r&&n.call(E,a)&&(w=E);var O=b.prototype=v.prototype=Object.create(w);function _(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function k(e,t){function r(o,i,a,c){var l=f(e[o],e,i);if("throw"!==l.type){var u=l.arg,s=u.value;return s&&"object"==Kr(s)&&n.call(s,"__await")?t.resolve(s.__await).then((function(e){r("next",e,a,c)}),(function(e){r("throw",e,a,c)})):t.resolve(s).then((function(e){u.value=e,a(u)}),(function(e){return r("throw",e,a,c)}))}c(l.arg)}var i;o(this,"_invoke",{value:function(e,n){function o(){return new t((function(t,o){r(e,n,t,o)}))}return i=i?i.then(o,o):o()}})}function j(t,r,n){var o=p;return function(i,a){if(o===y)throw Error("Generator is already running");if(o===h){if("throw"===i)throw a;return{value:e,done:!0}}for(n.method=i,n.arg=a;;){var c=n.delegate;if(c){var l=x(c,n);if(l){if(l===m)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===p)throw o=h,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=y;var u=f(t,r,n);if("normal"===u.type){if(o=n.done?h:d,u.arg===m)continue;return{value:u.arg,done:n.done}}"throw"===u.type&&(o=h,n.method="throw",n.arg=u.arg)}}}function x(t,r){var n=r.method,o=t.iterator[n];if(o===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,x(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),m;var i=f(o,t.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,m;var a=i.arg;return a?a.done?(r[t.resultName]=a.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,m):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,m)}function R(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function L(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function T(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(R,this),this.reset(!0)}function P(t){if(t||""===t){var r=t[a];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,i=function r(){for(;++o<t.length;)if(n.call(t,o))return r.value=t[o],r.done=!1,r;return r.value=e,r.done=!0,r};return i.next=i}}throw new TypeError(Kr(t)+" is not iterable")}return g.prototype=b,o(O,"constructor",{value:b,configurable:!0}),o(b,"constructor",{value:g,configurable:!0}),g.displayName=u(b,l,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===g||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,b):(e.__proto__=b,u(e,l,"GeneratorFunction")),e.prototype=Object.create(O),e},t.awrap=function(e){return{__await:e}},_(k.prototype),u(k.prototype,c,(function(){return this})),t.AsyncIterator=k,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new k(s(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},_(O),u(O,l,"Generator"),u(O,a,(function(){return this})),u(O,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=P,T.prototype={constructor:T,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(L),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function o(n,o){return c.type="throw",c.arg=t,r.next=n,o&&(r.method="next",r.arg=e),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],c=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var l=n.call(a,"catchLoc"),u=n.call(a,"finallyLoc");if(l&&u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,m):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),m},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),L(r),m}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var o=n.arg;L(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:P(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),m}},t}var Yr=$r().mark(qr);function qr(){var e,t;return $r().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,Vr();case 2:return e=r.sent,t=(0,te.parse)(e),r.next=7,Wr(t,!1);case 7:return r.abrupt("return",t);case 8:case"end":return r.stop()}}),Yr)}function Jr(e){return Jr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Jr(e)}function Qr(e,t,r){return(t=function(e){var t=function(e){if("object"!=Jr(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=Jr(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==Jr(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}const Xr=Qr(Qr({},Ar,(function(e){return new Promise((function(t,r){window.fceSettings.content=(0,te.serialize)(e.blocks),t(e.blocks)}))})),Cr,(function(){return new Promise((function(e,t){e(window.fceSettings.content||[])}))}));var Zr=(0,b.createReduxStore)("historyUndoRedoStore",{reducer:Nr,selectors:i,actions:a,controls:Xr,resolvers:c});(0,b.register)(Zr);var en=["name"];function tn(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var rn=function(){return Ce};de()((function(){(0,be.addFilter)("editor.Autocomplete.completers","fluentcrm/add_autocmplete",(function(e,t){return e=e.filter((function(e){return"users"!==e.name})),[].concat(function(e){if(Array.isArray(e))return tn(e)}(r=e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(r)||function(e,t){if(e){if("string"==typeof e)return tn(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?tn(e,t):void 0}}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),[ge]);var r})),(0,be.addFilter)("editor.MediaUpload","crm/media_uploader",rn),(0,Ae.registerCoreBlocks)(),(0,te.registerBlockType)("fluentcrm/conditional-group",Ue),(0,te.unregisterBlockVariation)("core/group","group-row"),(0,te.unregisterBlockVariation)("core/group","group-stack"),window.fcAdmin.has_smart_link&&[fr].forEach((function(e){var t=e.name,r=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.includes(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],t.includes(r)||{}.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,en);return(0,we.registerFormatType)(t,r)}))})),window.fluentCrmBootEmailEditor=function(e,t){var r=window.fceSettings||{};r.content=e,(0,b.dispatch)("historyUndoRedoStore").updateBlocks((0,te.parse)(e)),(0,s.render)(React.createElement(fe,{settings:r,onChangeHandle:t}),document.getElementById("fluentcrm_block_editor_x"))}},73:(e,t)=>{var r;!function(){"use strict";var n={}.hasOwnProperty;function o(){for(var e="",t=0;t<arguments.length;t++){var r=arguments[t];r&&(e=a(e,i(r)))}return e}function i(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return o.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var r in e)n.call(e,r)&&e[r]&&(t=a(t,r));return t}function a(e,t){return t?e?e+" "+t:e+t:e}e.exports?(o.default=o,e.exports=o):void 0===(r=function(){return o}.apply(t,[]))||(e.exports=r)}()}},r={};function n(e){var o=r[e];if(void 0!==o)return o.exports;var i=r[e]={id:e,loaded:!1,exports:{}};return t[e](i,i.exports,n),i.loaded=!0,i.exports}n.m=t,e=[],n.O=(t,r,o,i)=>{if(!r){var a=1/0;for(s=0;s<e.length;s++){r=e[s][0],o=e[s][1],i=e[s][2];for(var c=!0,l=0;l<r.length;l++)(!1&i||a>=i)&&Object.keys(n.O).every((e=>n.O[e](r[l])))?r.splice(l--,1):(c=!1,i<a&&(a=i));if(c){e.splice(s--,1);var u=o();void 0!==u&&(t=u)}}return t}i=i||0;for(var s=e.length;s>0&&e[s-1][2]>i;s--)e[s]=e[s-1];e[s]=[r,o,i]},n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),(()=>{var e={57:0,350:0};n.O.j=t=>0===e[t];var t=(t,r)=>{var o,i,a=r[0],c=r[1],l=r[2],u=0;if(a.some((t=>0!==e[t]))){for(o in c)n.o(c,o)&&(n.m[o]=c[o]);if(l)var s=l(n)}for(t&&t(r);u<a.length;u++)i=a[u],n.o(e,i)&&e[i]&&e[i][0](),e[i]=0;return n.O(s)},r=self.webpackChunkgetdave_sbe=self.webpackChunkgetdave_sbe||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})();var o=n.O(void 0,[350],(()=>n(24)));o=n.O(o)})();