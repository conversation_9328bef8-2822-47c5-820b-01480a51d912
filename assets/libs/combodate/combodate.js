/**
* Combodate - 1.0.7
* Dropdown date and time picker.
* Converts text input into dropdowns to pick day, month, year, hour, minute and second.
* Uses momentjs as datetime library http://momentjs.com.
* For i18n include corresponding file from https://github.com/timrwood/moment/tree/master/lang 
*
* Confusion at noon and midnight - see http://en.wikipedia.org/wiki/12-hour_clock#Confusion_at_noon_and_midnight
* In combodate: 
* 12:00 pm --> 12:00 (24-h format, midday)
* 12:00 am --> 00:00 (24-h format, midnight, start of day)
* 
* Differs from momentjs parse rules:
* 00:00 pm, 12:00 pm --> 12:00 (24-h format, day not change)
* 00:00 am, 12:00 am --> 00:00 (24-h format, day not change)
* 
* 
* Author: <PERSON><PERSON><PERSON>
* Project page: http://github.com/vitalets/combodate
* Copyright (c) 2012 Vitaliy Potapov. Released under MIT License.
**/
!function($){var a=function(a,b){if(this.$element=$(a),!this.$element.is("input")){$.error("Combodate should be applied to INPUT element");return}this.options=$.extend({},$.fn.combodate.defaults,b,this.$element.data()),this.init()};a.prototype={constructor:a,init:function(){this.map={day:["D","date"],month:["M","month"],year:["Y","year"],hour:["[Hh]","hours"],minute:["m","minutes"],second:["s","seconds"],ampm:["[Aa]",""]},this.$widget=$('<span class="combodate"></span>').html(this.getTemplate()),this.initCombos(),this.$widget.on("change","select",$.proxy(function(a){this.$element.val(this.getValue()).change(),this.options.smartDays&&($(a.target).is(".month")||$(a.target).is(".year"))&&this.fillCombo("day")},this)),this.$widget.find("select").css("width","auto"),this.$element.hide().after(this.$widget),this.setValue(this.$element.val()||this.options.value)},getTemplate:function(){var a=this.options.template,b=this.options.customClass;return $.each(this.map,function(e,b){b=b[0];var c=new RegExp(b+"+"),d=b.length>1?b.substring(1,2):b;a=a.replace(c,"{"+d+"}")}),a=a.replace(/ /g,"&nbsp;"),$.each(this.map,function(d,c){var e=(c=c[0]).length>1?c.substring(1,2):c;a=a.replace("{"+e+"}",'<select class="'+d+" "+b+'"></select>')}),a},initCombos:function(){for(var a in this.map){var b=this.$widget.find("."+a);this["$"+a]=b.length?b:null,this.fillCombo(a)}},fillCombo:function(c){var a=this["$"+c];if(a){var d=this["fill"+c.charAt(0).toUpperCase()+c.slice(1)](),e=a.val();a.empty();for(var b=0;b<d.length;b++)a.append('<option value="'+d[b][0]+'">'+d[b][1]+"</option>");a.val(e)}},fillCommon:function(a){var b,c=[];if("name"===this.options.firstItem){var d="function"==typeof(b=moment.relativeTime||moment.langData()._relativeTime)[a]?b[a](1,!0,a,!1):b[a];d=d.split(" ").reverse()[0],c.push(["",d])}else"empty"===this.options.firstItem&&c.push(["",""]);return c},fillDay:function(){var b,a,c=this.fillCommon("d"),g=-1!==this.options.template.indexOf("DD"),d=31;if(this.options.smartDays&&this.$month&&this.$year){var e=parseInt(this.$month.val(),10),f=parseInt(this.$year.val(),10);isNaN(e)||isNaN(f)||(d=moment([f,e]).daysInMonth())}for(a=1;a<=d;a++)b=g?this.leadZero(a):a,c.push([a,b]);return c},fillMonth:function(){var b,a,c=this.fillCommon("M"),d=-1!==this.options.template.indexOf("MMMM"),e=-1!==this.options.template.indexOf("MMM"),f=-1!==this.options.template.indexOf("MM");for(a=0;a<=11;a++)b=d?moment().date(1).month(a).format("MMMM"):e?moment().date(1).month(a).format("MMM"):f?this.leadZero(a+1):a+1,c.push([a,b]);return c},fillYear:function(){var b,a,c=[],d=-1!==this.options.template.indexOf("YYYY");for(a=this.options.maxYear;a>=this.options.minYear;a--)b=d?a:(a+"").substring(2),c[this.options.yearDescending?"push":"unshift"]([a,b]);return this.fillCommon("y").concat(c)},fillHour:function(){var b,a,c=this.fillCommon("h"),d=-1!==this.options.template.indexOf("h"),e=(this.options.template.indexOf("H"),-1!==this.options.template.toLowerCase().indexOf("hh")),f=d?12:23;for(a=d?1:0;a<=f;a++)b=e?this.leadZero(a):a,c.push([a,b]);return c},fillMinute:function(){var b,a,c=this.fillCommon("m"),d=-1!==this.options.template.indexOf("mm");for(a=0;a<=59;a+=this.options.minuteStep)b=d?this.leadZero(a):a,c.push([a,b]);return c},fillSecond:function(){var b,a,c=this.fillCommon("s"),d=-1!==this.options.template.indexOf("ss");for(a=0;a<=59;a+=this.options.secondStep)b=d?this.leadZero(a):a,c.push([a,b]);return c},fillAmpm:function(){var a=-1!==this.options.template.indexOf("a");return this.options.template.indexOf("A"),[["am",a?"am":"AM"],["pm",a?"pm":"PM"]]},getValue:function(c){var b,a={},e=this,d=!1;return($.each(this.map,function(b,c){if("ampm"!==b&&(a[b]=e["$"+b]?parseInt(e["$"+b].val(),10):"day"===b?1:0,isNaN(a[b])))return d=!0,!1}),d)?"":(this.$ampm&&(12===a.hour?a.hour="am"===this.$ampm.val()?0:12:a.hour="am"===this.$ampm.val()?a.hour:a.hour+12),b=moment([a.year,a.month,a.day,a.hour,a.minute,a.second]),this.highlight(b),null===(c=void 0===c?this.options.format:c))?b.isValid()?b:null:b.isValid()?b.format(c):""},setValue:function(b){if(b){var c="string"==typeof b?moment(b,this.options.format,!0):moment(b),d=this,a={};c.isValid()&&($.each(this.map,function(b,d){"ampm"!==b&&(a[b]=c[d[1]]())}),this.$ampm&&(a.hour>=12?(a.ampm="pm",a.hour>12&&(a.hour-=12)):(a.ampm="am",0===a.hour&&(a.hour=12))),$.each(a,function(a,b){d["$"+a]&&("minute"===a&&d.options.minuteStep>1&&d.options.roundTime&&(b=e(d["$"+a],b)),"second"===a&&d.options.secondStep>1&&d.options.roundTime&&(b=e(d["$"+a],b)),d["$"+a].val(b))}),this.options.smartDays&&this.fillCombo("day"),this.$element.val(c.format(this.options.format)).change())}function e(a,c){var b={};return a.children("option").each(function(f,e){var a,d=$(e).attr("value");""!==d&&(a=Math.abs(d-c),(void 0===b.distance||a<b.distance)&&(b={value:d,distance:a}))}),b.value}},highlight:function(a){a.isValid()?this.options.errorClass?this.$widget.removeClass(this.options.errorClass):this.$widget.find("select").css("border-color",this.borderColor):this.options.errorClass?this.$widget.addClass(this.options.errorClass):(this.borderColor||(this.borderColor=this.$widget.find("select").css("border-color")),this.$widget.find("select").css("border-color","red"))},leadZero:function(a){return a<=9?"0"+a:a},destroy:function(){this.$widget.remove(),this.$element.removeData("combodate").show()}},$.fn.combodate=function(d){var b,c=Array.apply(null,arguments);return(c.shift(),"getValue"===d&&this.length&&(b=this.eq(0).data("combodate")))?b.getValue.apply(b,c):this.each(function(){var e=$(this),b=e.data("combodate");b||e.data("combodate",b=new a(this,"object"==typeof d&&d)),"string"==typeof d&&"function"==typeof b[d]&&b[d].apply(b,c)})},$.fn.combodate.defaults={format:"DD-MM-YYYY HH:mm",template:"D / MMM / YYYY   H : mm",value:null,minYear:1970,maxYear:2015,yearDescending:!0,minuteStep:5,secondStep:1,firstItem:"empty",errorClass:null,customClass:"",roundTime:!0,smartDays:!1}}(window.jQuery)