/*!
 * Chart.BarFunnel.js
 * http://chartjs.org/
 * Version: 0.1.0
 *
 * Copyright 2016 <PERSON>
 * Released under the MIT license
 * https://github.com/chartjs/Chart.Zoom.js/blob/master/LICENSE.md
 */
!function e(t,r,o){function a(i,n){if(!r[i]){if(!t[i]){var d="function"==typeof require&&require;if(!n&&d)return d(i,!0);if(l)return l(i,!0);var s=new Error("Cannot find module '"+i+"'");throw s.code="MODULE_NOT_FOUND",s}var b=r[i]={exports:{}};t[i][0].call(b.exports,function(e){var r=t[i][1][e];return a(r?r:e)},b,b.exports,e,t,r,o)}return r[i].exports}for(var l="function"==typeof require&&require,i=0;i<o.length;i++)a(o[i]);return a}({1:[function(e,t,r){(function(e){var t=e.helpers;e.defaults.barFunnel={hover:{mode:"label"},region:{display:!0,borderColor:"#F6C85F",backgroundColor:"rgba(246, 200, 95, 0.2)"},elements:{rectangle:{stepLabel:{display:!0,fontSize:20}}},scales:{xAxes:[{type:"category",categoryPercentage:.8,barPercentage:.7,gridLines:{offsetGridLines:!0}}],yAxes:[{type:"linear"}]}},e.controllers.barFunnel=e.controllers.bar.extend({updateElement:function(e,r,o,a){var l,i=this.getMeta(),n=this.getScaleForId(i.xAxisID),d=this.getScaleForId(i.yAxisID);l=d.min<0&&d.max<0?d.getPixelForValue(d.max):d.min>0&&d.max>0?d.getPixelForValue(d.min):d.getPixelForValue(0);var s=this.chart.options,b=this.chart.options.elements.rectangle,p=e.custom||{},c=this.getDataset(),h=this.getRuler(this.index);t.extend(e,{_chart:this.chart.chart,_xScale:n,_yScale:d,_datasetIndex:this.index,_index:r,_model:{x:this.calculateBarX(r,this.index,h),y:o?l:this.calculateBarY(r,this.index),label:this.chart.data.labels[r],datasetLabel:c.label,base:o?l:this.calculateBarBase(this.index,r),width:this.calculateBarWidth(h),backgroundColor:p.backgroundColor?p.backgroundColor:t.getValueAtIndexOrDefault(c.backgroundColor,r,b.backgroundColor),borderSkipped:p.borderSkipped?p.borderSkipped:b.borderSkipped,borderColor:p.borderColor?p.borderColor:t.getValueAtIndexOrDefault(c.borderColor,r,b.borderColor),borderWidth:p.borderWidth?p.borderWidth:t.getValueAtIndexOrDefault(c.borderWidth,r,b.borderWidth),stepLabelColor:b.stepLabel.color?b.stepLabel.color:t.getValueAtIndexOrDefault(c.borderColor,r,b.borderColor),stepLabelFontSize:b.stepLabel.fontSize?b.stepLabel.fontSize:s.defaultFontSize},draw:function(){function e(e){return u[(f+e)%4]}var t=this._chart.ctx,o=this._view,a=this._chart.config.options,l=o.width/2,n=o.x-l,d=o.x+l,p=o.base-(o.base-o.y),h=o.borderWidth/2;o.borderWidth&&(n+=h,d-=h,p+=h),t.beginPath(),t.fillStyle=o.backgroundColor,t.strokeStyle=o.borderColor,t.lineWidth=o.borderWidth;var u=[[n,o.base],[n,p],[d,p],[d,o.base]],x=["bottom","left","top","right"],f=x.indexOf(o.borderSkipped,0);f===-1&&(f=0),t.moveTo.apply(t,e(0));for(var g=1;g<4;g++)t.lineTo.apply(t,e(g));if(t.fill(),o.borderWidth&&t.stroke(),b.stepLabel.display&&0!=r){var y=c.data[r]/c.data[0]*100;c.data[r]>0&&(t.font=o.stepLabelFontSize+"px "+a.defaultFontFamily,t.fillStyle=o.stepLabelColor,t.textAlign="center",t.fillText(y.toFixed(0)+"%",o.x,o.y-o.stepLabelFontSize))}if(s.region.display&&r<i.data.length-1){var C=i.data[r+1]._view,S=[[o.x+l,p],[C.x-l,C.base-(C.base-C.y-1)],[C.x-l,C.base],[o.x+l,o.base]];t.beginPath(),t.strokeStyle=s.region.borderColor,t.moveTo.apply(t,S[0]),t.lineTo.apply(t,S[1]),t.stroke(),t.beginPath(),t.strokeStyle="transparent",t.fillStyle=s.region.backgroundColor,t.moveTo.apply(t,S[1]),t.lineTo.apply(t,S[2]),t.lineTo.apply(t,S[3]),t.lineTo.apply(t,S[0]),t.fill(),t.stroke()}}}),e.pivot()}})}).call(this,Chart)},{}]},{},[1]);