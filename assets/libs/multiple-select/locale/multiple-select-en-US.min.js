/**
  * multiple-select - Multiple select is a jQuery plugin to select multiple elements with checkboxes :).
  *
  * @version v1.5.2
  * @homepage http://multiple-select.wenzhixin.net.cn
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],t):t((e=e||self).jQuery)}(this,(function(e){"use strict";(e=e&&e.hasOwnProperty("default")?e.default:e).fn.multipleSelect.locales["en-US"]={formatSelectAll:function(){return"[Select all]"},formatAllSelected:function(){return"All selected"},formatCountSelected:function(e,t){return e+" of "+t+" selected"},formatNoMatchesFound:function(){return"No matches found"}},e.extend(e.fn.multipleSelect.defaults,e.fn.multipleSelect.locales["en-US"])}));
