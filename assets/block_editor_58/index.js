(window.webpackJsonp_getdave_sbe=window.webpackJsonp_getdave_sbe||[]).push([[1],{75:function(e,t,n){}}]),function(e){function t(t){for(var r,a,c=t[0],l=t[1],u=t[2],f=0,p=[];f<c.length;f++)a=c[f],Object.prototype.hasOwnProperty.call(o,a)&&o[a]&&p.push(o[a][0]),o[a]=0;for(r in l)Object.prototype.hasOwnProperty.call(l,r)&&(e[r]=l[r]);for(s&&s(t);p.length;)p.shift()();return i.push.apply(i,u||[]),n()}function n(){for(var e,t=0;t<i.length;t++){for(var n=i[t],r=!0,c=1;c<n.length;c++){var l=n[c];0!==o[l]&&(r=!1)}r&&(i.splice(t--,1),e=a(a.s=n[0]))}return e}var r={},o={0:0},i=[];function a(t){if(r[t])return r[t].exports;var n=r[t]={i:t,l:!1,exports:{}};return e[t].call(n.exports,n,n.exports,a),n.l=!0,n.exports}a.m=e,a.c=r,a.d=function(e,t,n){a.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},a.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},a.t=function(e,t){if(1&t&&(e=a(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(a.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)a.d(n,r,function(t){return e[t]}.bind(null,r));return n},a.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return a.d(t,"a",t),t},a.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},a.p="";var c=window.webpackJsonp_getdave_sbe=window.webpackJsonp_getdave_sbe||[],l=c.push.bind(c);c.push=t,c=c.slice();for(var u=0;u<c.length;u++)t(c[u]);var s=l;i.push([79,1]),n()}([function(e,t){e.exports=window.wp.i18n},function(e,t){e.exports=window.wp.element},function(e,t){e.exports=window.wp.components},function(e,t){e.exports=window.wp.data},function(e,t){e.exports=window.wp.richText},function(e,t){e.exports=window.wp.url},function(e,t){e.exports=window.wp.blockEditor},function(e,t){e.exports=window.lodash},function(e,t){e.exports=window.wp.keycodes},function(e,t,n){var r;!function(){"use strict";var n={}.hasOwnProperty;function o(){for(var e=[],t=0;t<arguments.length;t++){var r=arguments[t];if(r){var i=typeof r;if("string"===i||"number"===i)e.push(r);else if(Array.isArray(r)){if(r.length){var a=o.apply(null,r);a&&e.push(a)}}else if("object"===i)if(r.toString===Object.prototype.toString)for(var c in r)n.call(r,c)&&r[c]&&e.push(c);else e.push(r.toString())}}return e.join(" ")}e.exports?(o.default=o,e.exports=o):void 0===(r=function(){return o}.apply(t,[]))||(e.exports=r)}()},function(e,t){e.exports=window.wp.blocks},function(e,t){e.exports=window.wp.compose},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ActionTypes",{enumerable:!0,get:function(){return o.ActionTypes}}),Object.defineProperty(t,"ActionCreators",{enumerable:!0,get:function(){return o.ActionCreators}}),Object.defineProperty(t,"parseActions",{enumerable:!0,get:function(){return i.parseActions}}),Object.defineProperty(t,"isHistory",{enumerable:!0,get:function(){return i.isHistory}}),Object.defineProperty(t,"includeAction",{enumerable:!0,get:function(){return i.includeAction}}),Object.defineProperty(t,"excludeAction",{enumerable:!0,get:function(){return i.excludeAction}}),Object.defineProperty(t,"combineFilters",{enumerable:!0,get:function(){return i.combineFilters}}),Object.defineProperty(t,"groupByActionTypes",{enumerable:!0,get:function(){return i.groupByActionTypes}}),Object.defineProperty(t,"newHistory",{enumerable:!0,get:function(){return i.newHistory}}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return a.default}});var r,o=n(26),i=n(27),a=(r=n(76))&&r.__esModule?r:{default:r}},function(e,t){e.exports=window.wp.primitives},function(e,t){e.exports=window.wp.keyboardShortcuts},function(e,t){e.exports=window.wp.hooks},function(e,t,n){var r=n(19),o=n(54),i=n(55),a=r?r.toStringTag:void 0;e.exports=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":a&&a in Object(e)?o(e):i(e)}},function(e,t){e.exports=function(e){return null!=e&&"object"==typeof e}},function(e,t,n){e.exports=n(42)},function(e,t,n){var r=n(20).Symbol;e.exports=r},function(e,t,n){var r=n(21),o="object"==typeof self&&self&&self.Object===Object&&self,i=r||o||Function("return this")();e.exports=i},function(e,t,n){(function(t){var n="object"==typeof t&&t&&t.Object===Object&&t;e.exports=n}).call(this,n(53))},function(e,t){var n=Array.isArray;e.exports=n},function(e,t){e.exports=function(e){return e.webpackPolyfill||(e.deprecate=function(){},e.paths=[],e.children||(e.children=[]),Object.defineProperty(e,"loaded",{enumerable:!0,get:function(){return e.l}}),Object.defineProperty(e,"id",{enumerable:!0,get:function(){return e.i}}),e.webpackPolyfill=1),e}},function(e,t){e.exports=function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991}},function(e,t,n){var r=n(67),o=n(24);e.exports=function(e){return null!=e&&o(e.length)&&!r(e)}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ActionCreators=t.ActionTypes=void 0;var r={UNDO:"@@redux-undo/UNDO",REDO:"@@redux-undo/REDO",JUMP_TO_FUTURE:"@@redux-undo/JUMP_TO_FUTURE",JUMP_TO_PAST:"@@redux-undo/JUMP_TO_PAST",JUMP:"@@redux-undo/JUMP",CLEAR_HISTORY:"@@redux-undo/CLEAR_HISTORY"};t.ActionTypes=r;var o={undo:function(){return{type:r.UNDO}},redo:function(){return{type:r.REDO}},jumpToFuture:function(e){return{type:r.JUMP_TO_FUTURE,index:e}},jumpToPast:function(e){return{type:r.JUMP_TO_PAST,index:e}},jump:function(e){return{type:r.JUMP,index:e}},clearHistory:function(){return{type:r.CLEAR_HISTORY}}};t.ActionCreators=o},function(e,t,n){"use strict";function r(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return Array.isArray(e)?e:"string"==typeof e?[e]:t}Object.defineProperty(t,"__esModule",{value:!0}),t.parseActions=r,t.isHistory=function(e){return void 0!==e.present&&void 0!==e.future&&void 0!==e.past&&Array.isArray(e.future)&&Array.isArray(e.past)},t.includeAction=function(e){var t=r(e);return function(e){return t.indexOf(e.type)>=0}},t.excludeAction=function(e){var t=r(e);return function(e){return t.indexOf(e.type)<0}},t.combineFilters=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.reduce((function(e,t){return function(n,r,o){return e(n,r,o)&&t(n,r,o)}}),(function(){return!0}))},t.groupByActionTypes=function(e){var t=r(e);return function(e){return t.indexOf(e.type)>=0?e.type:null}},t.newHistory=function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;return{past:e,present:t,future:n,group:r,_latestUnfiltered:t,index:e.length,limit:e.length+n.length+1}}},function(e,t){function n(t){return"function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?(e.exports=n=function(e){return typeof e},e.exports.default=e.exports,e.exports.__esModule=!0):(e.exports=n=function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.default=e.exports,e.exports.__esModule=!0),n(t)}e.exports=n,e.exports.default=e.exports,e.exports.__esModule=!0},function(e,t){e.exports=window.wp.mediaUtils},function(e,t){e.exports=window.wp.domReady},function(e,t){e.exports=window.wp.blockLibrary},function(e,t){e.exports=window.wp.dom},function(e,t,n){"use strict";e.exports=n(73)},function(e,t){e.exports=window.wp.htmlEntities},,,,,,,function(e,t){e.exports=window.wp.formatLibrary},function(e,t,n){var r=n(43),o=n(44),i=n(70),a=n(22);e.exports=function(e,t){return(a(e)?r:o)(e,i(t))}},function(e,t){e.exports=function(e,t){for(var n=-1,r=null==e?0:e.length;++n<r&&!1!==t(e[n],n,e););return e}},function(e,t,n){var r=n(45),o=n(69)(r);e.exports=o},function(e,t,n){var r=n(46),o=n(48);e.exports=function(e,t){return e&&r(e,t,o)}},function(e,t,n){var r=n(47)();e.exports=r},function(e,t){e.exports=function(e){return function(t,n,r){for(var o=-1,i=Object(t),a=r(t),c=a.length;c--;){var l=a[e?c:++o];if(!1===n(i[l],l,i))break}return t}}},function(e,t,n){var r=n(49),o=n(63),i=n(25);e.exports=function(e){return i(e)?r(e):o(e)}},function(e,t,n){var r=n(50),o=n(51),i=n(22),a=n(56),c=n(58),l=n(59),u=Object.prototype.hasOwnProperty;e.exports=function(e,t){var n=i(e),s=!n&&o(e),f=!n&&!s&&a(e),p=!n&&!s&&!f&&l(e),d=n||s||f||p,y=d?r(e.length,String):[],b=y.length;for(var m in e)!t&&!u.call(e,m)||d&&("length"==m||f&&("offset"==m||"parent"==m)||p&&("buffer"==m||"byteLength"==m||"byteOffset"==m)||c(m,b))||y.push(m);return y}},function(e,t){e.exports=function(e,t){for(var n=-1,r=Array(e);++n<e;)r[n]=t(n);return r}},function(e,t,n){var r=n(52),o=n(17),i=Object.prototype,a=i.hasOwnProperty,c=i.propertyIsEnumerable,l=r(function(){return arguments}())?r:function(e){return o(e)&&a.call(e,"callee")&&!c.call(e,"callee")};e.exports=l},function(e,t,n){var r=n(16),o=n(17);e.exports=function(e){return o(e)&&"[object Arguments]"==r(e)}},function(e,t){var n;n=function(){return this}();try{n=n||new Function("return this")()}catch(e){"object"==typeof window&&(n=window)}e.exports=n},function(e,t,n){var r=n(19),o=Object.prototype,i=o.hasOwnProperty,a=o.toString,c=r?r.toStringTag:void 0;e.exports=function(e){var t=i.call(e,c),n=e[c];try{e[c]=void 0;var r=!0}catch(e){}var o=a.call(e);return r&&(t?e[c]=n:delete e[c]),o}},function(e,t){var n=Object.prototype.toString;e.exports=function(e){return n.call(e)}},function(e,t,n){(function(e){var r=n(20),o=n(57),i=t&&!t.nodeType&&t,a=i&&"object"==typeof e&&e&&!e.nodeType&&e,c=a&&a.exports===i?r.Buffer:void 0,l=(c?c.isBuffer:void 0)||o;e.exports=l}).call(this,n(23)(e))},function(e,t){e.exports=function(){return!1}},function(e,t){var n=/^(?:0|[1-9]\d*)$/;e.exports=function(e,t){var r=typeof e;return!!(t=null==t?9007199254740991:t)&&("number"==r||"symbol"!=r&&n.test(e))&&e>-1&&e%1==0&&e<t}},function(e,t,n){var r=n(60),o=n(61),i=n(62),a=i&&i.isTypedArray,c=a?o(a):r;e.exports=c},function(e,t,n){var r=n(16),o=n(24),i=n(17),a={};a["[object Float32Array]"]=a["[object Float64Array]"]=a["[object Int8Array]"]=a["[object Int16Array]"]=a["[object Int32Array]"]=a["[object Uint8Array]"]=a["[object Uint8ClampedArray]"]=a["[object Uint16Array]"]=a["[object Uint32Array]"]=!0,a["[object Arguments]"]=a["[object Array]"]=a["[object ArrayBuffer]"]=a["[object Boolean]"]=a["[object DataView]"]=a["[object Date]"]=a["[object Error]"]=a["[object Function]"]=a["[object Map]"]=a["[object Number]"]=a["[object Object]"]=a["[object RegExp]"]=a["[object Set]"]=a["[object String]"]=a["[object WeakMap]"]=!1,e.exports=function(e){return i(e)&&o(e.length)&&!!a[r(e)]}},function(e,t){e.exports=function(e){return function(t){return e(t)}}},function(e,t,n){(function(e){var r=n(21),o=t&&!t.nodeType&&t,i=o&&"object"==typeof e&&e&&!e.nodeType&&e,a=i&&i.exports===o&&r.process,c=function(){try{return i&&i.require&&i.require("util").types||a&&a.binding&&a.binding("util")}catch(e){}}();e.exports=c}).call(this,n(23)(e))},function(e,t,n){var r=n(64),o=n(65),i=Object.prototype.hasOwnProperty;e.exports=function(e){if(!r(e))return o(e);var t=[];for(var n in Object(e))i.call(e,n)&&"constructor"!=n&&t.push(n);return t}},function(e,t){var n=Object.prototype;e.exports=function(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||n)}},function(e,t,n){var r=n(66)(Object.keys,Object);e.exports=r},function(e,t){e.exports=function(e,t){return function(n){return e(t(n))}}},function(e,t,n){var r=n(16),o=n(68);e.exports=function(e){if(!o(e))return!1;var t=r(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t}},function(e,t){e.exports=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}},function(e,t,n){var r=n(25);e.exports=function(e,t){return function(n,o){if(null==n)return n;if(!r(n))return e(n,o);for(var i=n.length,a=t?i:-1,c=Object(n);(t?a--:++a<i)&&!1!==o(c[a],a,c););return n}}},function(e,t,n){var r=n(71);e.exports=function(e){return"function"==typeof e?e:r}},function(e,t){e.exports=function(e){return e}},function(e,t){e.exports=window.wp.apiFetch},function(e,t,n){"use strict";var r=n(74);e.exports=function(e,t,n){n=n||{},9===t.nodeType&&(t=r.getWindow(t));var o=n.allowHorizontalScroll,i=n.onlyScrollIfNeeded,a=n.alignWithTop,c=n.alignWithLeft,l=n.offsetTop||0,u=n.offsetLeft||0,s=n.offsetBottom||0,f=n.offsetRight||0;o=void 0===o||o;var p=r.isWindow(t),d=r.offset(e),y=r.outerHeight(e),b=r.outerWidth(e),m=void 0,v=void 0,g=void 0,h=void 0,O=void 0,w=void 0,j=void 0,S=void 0,_=void 0,k=void 0;p?(j=t,k=r.height(j),_=r.width(j),S={left:r.scrollLeft(j),top:r.scrollTop(j)},O={left:d.left-S.left-u,top:d.top-S.top-l},w={left:d.left+b-(S.left+_)+f,top:d.top+y-(S.top+k)+s},h=S):(m=r.offset(t),v=t.clientHeight,g=t.clientWidth,h={left:t.scrollLeft,top:t.scrollTop},O={left:d.left-(m.left+(parseFloat(r.css(t,"borderLeftWidth"))||0))-u,top:d.top-(m.top+(parseFloat(r.css(t,"borderTopWidth"))||0))-l},w={left:d.left+b-(m.left+g+(parseFloat(r.css(t,"borderRightWidth"))||0))+f,top:d.top+y-(m.top+v+(parseFloat(r.css(t,"borderBottomWidth"))||0))+s}),O.top<0||w.top>0?!0===a?r.scrollTop(t,h.top+O.top):!1===a?r.scrollTop(t,h.top+w.top):O.top<0?r.scrollTop(t,h.top+O.top):r.scrollTop(t,h.top+w.top):i||((a=void 0===a||!!a)?r.scrollTop(t,h.top+O.top):r.scrollTop(t,h.top+w.top)),o&&(O.left<0||w.left>0?!0===c?r.scrollLeft(t,h.left+O.left):!1===c?r.scrollLeft(t,h.left+w.left):O.left<0?r.scrollLeft(t,h.left+O.left):r.scrollLeft(t,h.left+w.left):i||((c=void 0===c||!!c)?r.scrollLeft(t,h.left+O.left):r.scrollLeft(t,h.left+w.left)))}},function(e,t,n){"use strict";var r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol?"symbol":typeof e};function i(e,t){var n=e["page"+(t?"Y":"X")+"Offset"],r="scroll"+(t?"Top":"Left");if("number"!=typeof n){var o=e.document;"number"!=typeof(n=o.documentElement[r])&&(n=o.body[r])}return n}function a(e){return i(e)}function c(e){return i(e,!0)}function l(e){var t=function(e){var t,n=void 0,r=void 0,o=e.ownerDocument,i=o.body,a=o&&o.documentElement;return n=(t=e.getBoundingClientRect()).left,r=t.top,{left:n-=a.clientLeft||i.clientLeft||0,top:r-=a.clientTop||i.clientTop||0}}(e),n=e.ownerDocument,r=n.defaultView||n.parentWindow;return t.left+=a(r),t.top+=c(r),t}var u=new RegExp("^("+/[\-+]?(?:\d*\.|)\d+(?:[eE][\-+]?\d+|)/.source+")(?!px)[a-z%]+$","i"),s=/^(top|right|bottom|left)$/,f="left",p=void 0;function d(e,t){for(var n=0;n<e.length;n++)t(e[n])}function y(e){return"border-box"===p(e,"boxSizing")}"undefined"!=typeof window&&(p=window.getComputedStyle?function(e,t,n){var r="",o=e.ownerDocument,i=n||o.defaultView.getComputedStyle(e,null);return i&&(r=i.getPropertyValue(t)||i[t]),r}:function(e,t){var n=e.currentStyle&&e.currentStyle[t];if(u.test(n)&&!s.test(t)){var r=e.style,o=r[f],i=e.runtimeStyle[f];e.runtimeStyle[f]=e.currentStyle[f],r[f]="fontSize"===t?"1em":n||0,n=r.pixelLeft+"px",r[f]=o,e.runtimeStyle[f]=i}return""===n?"auto":n});var b=["margin","border","padding"];function m(e,t,n){var r={},o=e.style,i=void 0;for(i in t)t.hasOwnProperty(i)&&(r[i]=o[i],o[i]=t[i]);for(i in n.call(e),t)t.hasOwnProperty(i)&&(o[i]=r[i])}function v(e,t,n){var r=0,o=void 0,i=void 0,a=void 0;for(i=0;i<t.length;i++)if(o=t[i])for(a=0;a<n.length;a++){var c;c="border"===o?o+n[a]+"Width":o+n[a],r+=parseFloat(p(e,c))||0}return r}function g(e){return null!=e&&e==e.window}var h={};function O(e,t,n){if(g(e))return"width"===t?h.viewportWidth(e):h.viewportHeight(e);if(9===e.nodeType)return"width"===t?h.docWidth(e):h.docHeight(e);var r="width"===t?["Left","Right"]:["Top","Bottom"],o="width"===t?e.offsetWidth:e.offsetHeight,i=(p(e),y(e)),a=0;(null==o||o<=0)&&(o=void 0,(null==(a=p(e,t))||Number(a)<0)&&(a=e.style[t]||0),a=parseFloat(a)||0),void 0===n&&(n=i?1:-1);var c=void 0!==o||i,l=o||a;if(-1===n)return c?l-v(e,["border","padding"],r):a;if(c){var u=2===n?-v(e,["border"],r):v(e,["margin"],r);return l+(1===n?0:u)}return a+v(e,b.slice(n),r)}d(["Width","Height"],(function(e){h["doc"+e]=function(t){var n=t.document;return Math.max(n.documentElement["scroll"+e],n.body["scroll"+e],h["viewport"+e](n))},h["viewport"+e]=function(t){var n="client"+e,r=t.document,o=r.body,i=r.documentElement[n];return"CSS1Compat"===r.compatMode&&i||o&&o[n]||i}}));var w={position:"absolute",visibility:"hidden",display:"block"};function j(e){var t=void 0,n=arguments;return 0!==e.offsetWidth?t=O.apply(void 0,n):m(e,w,(function(){t=O.apply(void 0,n)})),t}function S(e,t,n){var r=n;if("object"!==(void 0===t?"undefined":o(t)))return void 0!==r?("number"==typeof r&&(r+="px"),void(e.style[t]=r)):p(e,t);for(var i in t)t.hasOwnProperty(i)&&S(e,i,t[i])}d(["width","height"],(function(e){var t=e.charAt(0).toUpperCase()+e.slice(1);h["outer"+t]=function(t,n){return t&&j(t,e,n?0:1)};var n="width"===e?["Left","Right"]:["Top","Bottom"];h[e]=function(t,r){return void 0===r?t&&j(t,e,-1):t?(p(t),y(t)&&(r+=v(t,["padding","border"],n)),S(t,e,r)):void 0}})),e.exports=r({getWindow:function(e){var t=e.ownerDocument||e;return t.defaultView||t.parentWindow},offset:function(e,t){if(void 0===t)return l(e);!function(e,t){"static"===S(e,"position")&&(e.style.position="relative");var n=l(e),r={},o=void 0,i=void 0;for(i in t)t.hasOwnProperty(i)&&(o=parseFloat(S(e,i))||0,r[i]=o+t[i]-n[i]);S(e,r)}(e,t)},isWindow:g,each:d,css:S,clone:function(e){var t={};for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n]);if(e.overflow)for(var n in e)e.hasOwnProperty(n)&&(t.overflow[n]=e.overflow[n]);return t},scrollLeft:function(e,t){if(g(e)){if(void 0===t)return a(e);window.scrollTo(t,c(e))}else{if(void 0===t)return e.scrollLeft;e.scrollLeft=t}},scrollTop:function(e,t){if(g(e)){if(void 0===t)return c(e);window.scrollTo(a(e),t)}else{if(void 0===t)return e.scrollTop;e.scrollTop=t}},viewportWidth:0,viewportHeight:0},h)},,function(e,t,n){"use strict";function r(e){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};o.set(t.debug);var n,r=l({limit:void 0,filter:function(){return!0},groupBy:function(){return null},undoType:i.ActionTypes.UNDO,redoType:i.ActionTypes.REDO,jumpToPastType:i.ActionTypes.JUMP_TO_PAST,jumpToFutureType:i.ActionTypes.JUMP_TO_FUTURE,jumpType:i.ActionTypes.JUMP,neverSkipReducer:!1,ignoreInitialState:!1,syncFilter:!1},t,{initTypes:(0,a.parseActions)(t.initTypes,["@@redux-undo/INIT"]),clearHistoryType:(0,a.parseActions)(t.clearHistoryType,[i.ActionTypes.CLEAR_HISTORY])}),c=r.neverSkipReducer?function(t,n){for(var r=arguments.length,o=new Array(r>2?r-2:0),i=2;i<r;i++)o[i-2]=arguments[i];return l({},t,{present:e.apply(void 0,[t.present,n].concat(o))})}:function(e){return e};return function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:n,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};o.start(i,t);for(var l,u=t,s=arguments.length,v=new Array(s>2?s-2:0),g=2;g<s;g++)v[g-2]=arguments[g];if(!n){if(o.log("history is uninitialized"),void 0===t){var h={type:"@@redux-undo/CREATE_HISTORY"},O=e.apply(void 0,[t,h].concat(v));return u=f(O,r.ignoreInitialState),o.log("do not set initialState on probe actions"),o.end(u),u}(0,a.isHistory)(t)?(u=n=r.ignoreInitialState?t:(0,a.newHistory)(t.past,t.present,t.future),o.log("initialHistory initialized: initialState is a history",n)):(u=n=f(t,r.ignoreInitialState),o.log("initialHistory initialized: initialState is not a history",n))}switch(i.type){case void 0:return u;case r.undoType:return l=b(u,-1),o.log("perform undo"),o.end(l),c.apply(void 0,[l,i].concat(v));case r.redoType:return l=b(u,1),o.log("perform redo"),o.end(l),c.apply(void 0,[l,i].concat(v));case r.jumpToPastType:return l=y(u,i.index),o.log("perform jumpToPast to ".concat(i.index)),o.end(l),c.apply(void 0,[l,i].concat(v));case r.jumpToFutureType:return l=d(u,i.index),o.log("perform jumpToFuture to ".concat(i.index)),o.end(l),c.apply(void 0,[l,i].concat(v));case r.jumpType:return l=b(u,i.index),o.log("perform jump to ".concat(i.index)),o.end(l),c.apply(void 0,[l,i].concat(v));case m(i.type,r.clearHistoryType):return l=f(u.present,r.ignoreInitialState),o.log("perform clearHistory"),o.end(l),c.apply(void 0,[l,i].concat(v));default:if(l=e.apply(void 0,[u.present,i].concat(v)),r.initTypes.some((function(e){return e===i.type})))return o.log("reset history due to init action"),o.end(n),n;if(u._latestUnfiltered===l)return u;var w="function"==typeof r.filter&&!r.filter(i,l,u);if(w){var j=(0,a.newHistory)(u.past,l,u.future,u.group);return r.syncFilter||(j._latestUnfiltered=u._latestUnfiltered),o.log("filter ignored action, not storing it in past"),o.end(j),j}var S=r.groupBy(i,l,u);if(null!=S&&S===u.group){var _=(0,a.newHistory)(u.past,l,u.future,u.group);return o.log("groupBy grouped the action with the previous action"),o.end(_),_}return u=p(u,l,r.limit,S),o.log("inserted new state into history"),o.end(u),u}}};var o=function(e){if(e&&e.__esModule)return e;if(null===e||"object"!==r(e)&&"function"!=typeof e)return{default:e};var t=function(){if("function"!=typeof WeakMap)return null;var e=new WeakMap;return function(){return e},e}();if(t&&t.has(e))return t.get(e);var n={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i)){var a=o?Object.getOwnPropertyDescriptor(e,i):null;a&&(a.get||a.set)?Object.defineProperty(n,i,a):n[i]=e[i]}return n.default=e,t&&t.set(e,n),n}(n(77)),i=n(26),a=n(27);function c(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function l(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?c(Object(n),!0).forEach((function(t){u(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):c(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function u(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function s(e){return function(e){if(Array.isArray(e)){for(var t=0,n=new Array(e.length);t<e.length;t++)n[t]=e[t];return n}}(e)||function(e){if(Symbol.iterator in Object(e)||"[object Arguments]"===Object.prototype.toString.call(e))return Array.from(e)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}function f(e,t){var n=(0,a.newHistory)([],e,[]);return t&&(n._latestUnfiltered=null),n}function p(e,t,n,r){var i=e.past.length+1;o.log("inserting",t),o.log("new free: ",n-i);var c=e.past,l=e._latestUnfiltered,u=n&&n<=i,f=c.slice(u?1:0),p=null!=l?[].concat(s(f),[l]):f;return(0,a.newHistory)(p,t,[],r)}function d(e,t){if(t<0||t>=e.future.length)return e;var n=e.past,r=e.future,o=e._latestUnfiltered,i=[].concat(s(n),[o],s(r.slice(0,t))),c=r[t],l=r.slice(t+1);return(0,a.newHistory)(i,c,l)}function y(e,t){if(t<0||t>=e.past.length)return e;var n=e.past,r=e.future,o=e._latestUnfiltered,i=n.slice(0,t),c=[].concat(s(n.slice(t+1)),[o],s(r)),l=n[t];return(0,a.newHistory)(i,l,c)}function b(e,t){return t>0?d(e,t-1):t<0?y(e,e.past.length+t):e}function m(e,t){return t.indexOf(e)>-1?e:!e}},function(e,t,n){"use strict";function r(e){return function(e){if(Array.isArray(e)){for(var t=0,n=new Array(e.length);t<e.length;t++)n[t]=e[t];return n}}(e)||function(e){if(Symbol.iterator in Object(e)||"[object Arguments]"===Object.prototype.toString.call(e))return Array.from(e)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}var o,i;Object.defineProperty(t,"__esModule",{value:!0}),t.set=function(e){o=e},t.start=function(e,t){i={header:[],prev:[],action:[],next:[],msgs:[]},o&&(console.group?(i.header=["%credux-undo","font-style: italic","action",e.type],i.action=u("action",c,e),i.prev=u("prev history",a,t)):(i.header=["redux-undo action",e.type],i.action=["action",e],i.prev=["prev history",t]))},t.end=function(e){var t,n,a,c,s,f,p,d,y,b,m,v,g,h,O,w;o&&(console.group?i.next=u("next history",l,e):i.next=["next history",e],v=(m=i).header,g=m.prev,h=m.next,O=m.action,w=m.msgs,console.group?((t=console).groupCollapsed.apply(t,r(v)),(n=console).log.apply(n,r(g)),(a=console).log.apply(a,r(O)),(c=console).log.apply(c,r(h)),(s=console).log.apply(s,r(w)),console.groupEnd()):((f=console).log.apply(f,r(v)),(p=console).log.apply(p,r(g)),(d=console).log.apply(d,r(O)),(y=console).log.apply(y,r(h)),(b=console).log.apply(b,r(w))))},t.log=function(){if(o){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];i.msgs=i.msgs.concat([].concat(t,["\n"]))}};var a="#9E9E9E",c="#03A9F4",l="#4CAF50";function u(e,t,n){return["%c".concat(e),"color: ".concat(t,"; font-weight: bold"),n]}},function(e,t,n){},function(e,t,n){"use strict";n.r(t);var r={};n.r(r),n.d(r,"enableComplementaryArea",(function(){return v})),n.d(r,"disableComplementaryArea",(function(){return g})),n.d(r,"pinItem",(function(){return O})),n.d(r,"unpinItem",(function(){return w}));var o={};n.r(o),n.d(o,"getActiveComplementaryArea",(function(){return j})),n.d(o,"isItemPinned",(function(){return S}));var i={};n.r(i),n.d(i,"getBlocks",(function(){return On})),n.d(i,"hasUndo",(function(){return wn})),n.d(i,"hasRedo",(function(){return jn}));var a={};n.r(a),n.d(a,"undo",(function(){return _n})),n.d(a,"redo",(function(){return kn})),n.d(a,"clearHistory",(function(){return Rn})),n.d(a,"updateBlocks",(function(){return En})),n.d(a,"fetchBlocksFromStorage",(function(){return xn})),n.d(a,"persistBlocksToStorage",(function(){return Tn}));var c={};n.r(c),n.d(c,"getBlocks",(function(){return Cn}));var l=n(2),u=n(3);function s(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var f=n(7);function p(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function d(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?p(Object(n),!0).forEach((function(t){s(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):p(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var y=Object(u.combineReducers)({singleEnableItems:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0,n=t.type,r=t.itemType,o=t.scope,i=t.item;return"SET_SINGLE_ENABLE_ITEM"===n&&r&&o?d({},e,s({},r,d({},e[r],s({},o,i||null)))):e},multipleEnableItems:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0,n=t.type,r=t.itemType,o=t.scope,i=t.item,a=t.isEnable;if("SET_MULTIPLE_ENABLE_ITEM"!==n||!r||!o||!i||Object(f.get)(e,[r,o,i])===a)return e;var c=e[r]||{},l=c[o]||{};return d({},e,s({},r,d({},c,s({},o,d({},l,s({},i,a||!1))))))}}),b=Object(u.combineReducers)({enableItems:y});function m(e,t,n){return{type:"SET_SINGLE_ENABLE_ITEM",itemType:e,scope:t,item:n}}function v(e,t){return m("complementaryArea",e,t)}function g(e){return m("complementaryArea",e,void 0)}function h(e,t,n,r){return{type:"SET_MULTIPLE_ENABLE_ITEM",itemType:e,scope:t,item:n,isEnable:r}}function O(e,t){return h("pinnedItems",e,t,!0)}function w(e,t){return h("pinnedItems",e,t,!1)}function j(e,t){return function(e,t,n){return Object(f.get)(e.enableItems.singleEnableItems,["complementaryArea",n])}(e,0,t)}function S(e,t,n){return!1!==function(e,t,n,r){return Object(f.get)(e.enableItems.multipleEnableItems,["pinnedItems",n,r])}(e,0,t,n)}function _(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function k(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}Object(u.registerStore)("core/interface",{reducer:b,actions:r,selectors:o,persist:["enableItems"]});var R=n(28),E=n.n(R);function x(e,t){if(t&&("object"===E()(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function T(e){return(T=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function P(e,t){return(P=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}var A=n(1);function C(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}var L=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&P(e,t)}(i,e);var t,n,r,o=(t=i,function(){var e,n=T(t);if(C()){var r=T(this).constructor;e=Reflect.construct(n,arguments,r)}else e=n.apply(this,arguments);return x(this,e)});function i(){return _(this,i),o.apply(this,arguments)}return n=i,(r=[{key:"componentDidMount",value:function(){this.isSticky=!1,this.sync(),document.body.classList.contains("sticky-menu")&&(this.isSticky=!0,document.body.classList.remove("sticky-menu"))}},{key:"componentWillUnmount",value:function(){this.isSticky&&document.body.classList.add("sticky-menu")}},{key:"componentDidUpdate",value:function(e){this.props.isActive!==e.isActive&&this.sync()}},{key:"sync",value:function(){this.props.isActive?document.body.classList.add("is-fullscreen-mode"):document.body.classList.remove("is-fullscreen-mode")}},{key:"render",value:function(){return null}}])&&k(n.prototype,r),i}(A.Component),I=n(9),F=n.n(I),N=n(0);function B(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}var U=Object(l.navigateRegions)((function(e){var t=e.footer,n=e.header,r=e.sidebar,o=e.leftSidebar,i=e.content,a=e.actions,c=e.labels,l=e.className;!function(e){Object(A.useEffect)((function(){var t=document&&document.querySelector("html:not(.".concat(e,")"));if(t)return t.classList.toggle(e),function(){t.classList.toggle(e)}}),[e])}("interface-interface-skeleton__html-container");var u=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?B(Object(n),!0).forEach((function(t){s(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):B(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},{
/* translators: accessibility text for the top bar landmark region. */
header:Object(N.__)("Header"),
/* translators: accessibility text for the content landmark region. */
body:Object(N.__)("Content"),
/* translators: accessibility text for the left sidebar landmark region. */
leftSidebar:Object(N.__)("Left sidebar"),
/* translators: accessibility text for the settings landmark region. */
sidebar:Object(N.__)("Settings"),
/* translators: accessibility text for the publish landmark region. */
actions:Object(N.__)("Publish"),
/* translators: accessibility text for the footer landmark region. */
footer:Object(N.__)("Footer")},{},c);return Object(A.createElement)("div",{className:F()(l,"interface-interface-skeleton")},!!n&&Object(A.createElement)("div",{className:"interface-interface-skeleton__header",role:"region","aria-label":u.header,tabIndex:"-1"},n),Object(A.createElement)("div",{className:"interface-interface-skeleton__body"},!!o&&Object(A.createElement)("div",{className:"interface-interface-skeleton__left-sidebar",role:"region","aria-label":u.leftSidebar,tabIndex:"-1"},o),Object(A.createElement)("div",{className:"interface-interface-skeleton__content",role:"region","aria-label":u.body,tabIndex:"-1"},i),!!r&&Object(A.createElement)("div",{className:"interface-interface-skeleton__sidebar",role:"region","aria-label":u.sidebar,tabIndex:"-1"},r),!!a&&Object(A.createElement)("div",{className:"interface-interface-skeleton__actions",role:"region","aria-label":u.actions,tabIndex:"-1"},a)),!!t&&Object(A.createElement)("div",{className:"interface-interface-skeleton__footer",role:"region","aria-label":u.footer,tabIndex:"-1"},t))})),D=n(11),M=n(8),H=n(13),W=Object(A.createElement)(H.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},Object(A.createElement)(H.Path,{d:"M18.3 11.7c-.6-.6-1.4-.9-2.3-.9H6.7l2.9-3.3-1.1-1-4.5 5L8.5 16l1-1-2.7-2.7H16c.5 0 .9.2 1.3.5 1 1 1 3.4 1 4.5v.3h1.5v-.2c0-1.5 0-4.3-1.5-5.7z"})),V=["hasUndo","undo"];function G(){return(G=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var K=Object(D.compose)([Object(u.withSelect)((function(e){return{hasUndo:e("historyUndoRedoStore").hasUndo()}})),Object(u.withDispatch)((function(e){return{undo:e("historyUndoRedoStore").undo}}))])((function(e){var t=e.hasUndo,n=e.undo,r=function(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}(e,V);return React.createElement(l.Button,G({},r,{icon:W,label:Object(N.__)("Undo"),shortcut:M.displayShortcut.primary("z"),"aria-disabled":!t,onClick:t?n:void 0,className:"editor-history__undo"}))})),J=Object(A.createElement)(H.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},Object(A.createElement)(H.Path,{d:"M15.6 6.5l-1.1 1 2.9 3.3H8c-.9 0-1.7.3-2.3.9-1.4 1.5-1.4 4.2-1.4 5.6v.2h1.5v-.3c0-1.1 0-3.5 1-4.5.3-.3.7-.5 1.3-.5h9.2L14.5 15l1.1 1.1 4.6-4.6-4.6-5z"})),z=["hasRedo","redo"];function q(){return(q=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var Y=Object(D.compose)([Object(u.withSelect)((function(e){return{hasRedo:e("historyUndoRedoStore").hasRedo()}})),Object(u.withDispatch)((function(e){return{redo:e("historyUndoRedoStore").redo}}))])((function(e){var t=e.hasRedo,n=e.redo,r=function(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}(e,z);return React.createElement(l.Button,q({},r,{icon:J,label:Object(N.__)("Redo"),shortcut:M.displayShortcut.primaryShift("z"),"aria-disabled":!t,onClick:t?n:void 0,className:"editor-history__redo"}))}));function $(){return React.createElement("div",{className:"history-undo-redo-header",role:"region","aria-label":Object(N.__)("History undo redo top bar."),tabIndex:"-1"},React.createElement(K,null),React.createElement(Y,null))}var Q=Object(l.createSlotFill)("StandAloneBlockEditorSidebarInspector"),X=Q.Slot,Z=Q.Fill;function ee(){return React.createElement("div",{className:"fce-sidebar",role:"region","aria-label":Object(N.__)("advanced settings."),tabIndex:"-1"},React.createElement(l.Panel,null,React.createElement("div",{className:"components-panel__header"},React.createElement("h2",null,Object(N.__)("Block Settings")),React.createElement($,null)),React.createElement(X,{bubblesVirtually:!0})))}ee.InspectorFill=Z;var te=ee,ne=(n(41),n(10)),re=n(29),oe=n(6),ie=n(14),ae=function(){var e=Object(u.useDispatch)("core/keyboard-shortcuts").registerShortcut,t=Object(u.useDispatch)("historyUndoRedoStore"),n=t.undo,r=t.redo;return Object(A.useEffect)((function(){e({name:"custom/editor/undo",category:"global",description:Object(N.__)("Undo your last changes."),keyCombination:{modifier:"primary",character:"z"}}),e({name:"custom/editor/redo",category:"global",description:Object(N.__)("Redo your last undo."),keyCombination:{modifier:"primaryShift",character:"z"}})}),[e]),Object(ie.useShortcut)("custom/editor/undo",(function(){n()})),Object(ie.useShortcut)("custom/editor/redo",(function(){r()})),null},ce=["onError"];function le(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ue(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?le(Object(n),!0).forEach((function(t){se(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):le(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function se(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var fe=function(e){var t=e.settings,n=e.onChangeContent,r=Object(u.useSelect)((function(e){return e("historyUndoRedoStore").getBlocks()})),o=Object(u.useDispatch)("historyUndoRedoStore"),i=o.updateBlocks,a=o.clearHistory;t.__experimentalSetIsInserterOpened=function(e){e&&jQuery(".fce_inserter button.block-editor-inserter__toggle").trigger("click")};var c=Object(u.useSelect)((function(e){return!0}),[]),l=Object(A.useMemo)((function(){return c?ue(ue({},t),{},{mediaUpload:function(e){var n=e.onError,r=function(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}(e,ce);Object(re.uploadMedia)(ue({wpAllowedMimeTypes:t.allowedMimeTypes,onError:function(e){var t=e.message;return n(t)}},r))}}):t}),[c,t]);function s(e){i(e),null!=e&&e.length&&n(Object(ne.serialize)(e))}Object(A.useEffect)((function(){var e=r;null!=e&&e.length&&(a(),s(e))}),[]);var f,p=void 0!==ie.ShortcutProvider;return React.createElement("div",{className:"fce-block-editor"},(f=React.createElement(oe.BlockEditorProvider,{value:r,onInput:s,onChange:function(e){i(e,!0),n(Object(ne.serialize)(e))},settings:l},React.createElement(oe.BlockEditorKeyboardShortcuts.Register,null),React.createElement(oe.BlockEditorKeyboardShortcuts,null),React.createElement(ae,null),React.createElement(te.InspectorFill,null,React.createElement(oe.BlockInspector,null)),React.createElement("div",{className:"editor-styles-wrapper"},React.createElement(oe.WritingFlow,null,React.createElement(oe.ObserveTyping,null,React.createElement("span",{className:"fce_inserter"},React.createElement(oe.Inserter,null)),React.createElement(oe.BlockList,{className:"fce-block-editor__block-list fc_editor_body"}))))),p?React.createElement(ie.ShortcutProvider,null,f):f))},pe=function(e){var t=e.settings,n=e.onChangeHandle;return React.createElement(React.Fragment,null,React.createElement(L,{isActive:!1}),React.createElement(l.SlotFillProvider,null,React.createElement(l.DropZoneProvider,null,React.createElement(l.FocusReturnProvider,null,React.createElement(U,{sidebar:React.createElement(te,null),content:React.createElement(React.Fragment,null,React.createElement(fe,{onChangeContent:n,settings:t}))}),React.createElement(l.Popover.Slot,null)))))};function de(e){return(de="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ye(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function be(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function me(e,t){return(me=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function ve(e,t){if(t&&("object"===de(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function ge(e){return(ge=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var he=wp.i18n.__,Oe=wp.element.Component,we=wp.components,je=we.TextControl,Se=we.Button,_e=we.Tooltip,ke=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&me(e,t)}(a,e);var t,n,r,o,i=(r=a,o=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=ge(r);if(o){var n=ge(this).constructor;e=Reflect.construct(t,arguments,n)}else e=t.apply(this,arguments);return ve(this,e)});function a(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,a),(t=i.call(this,e)).state={active:!1},t}return t=a,(n=[{key:"render",value:function(){var e=this,t=this.props,n=t.attributes,r=t.top,o=t.right,i=t.bottom,a=t.left,c=t.label,l=t.setAttributes;return React.createElement("div",{className:"fce-dimension-box-wrapper"},React.createElement("label",null," ",c," "),React.createElement("div",{className:"fce-dimension-box"},React.createElement(je,{type:"number",className:"fce-dimension-box-top",value:n[r],Placeholder:"Top",onChange:function(t){var n;e.state.active?l((ye(n={},r,parseInt(t)),ye(n,o,parseInt(t)),ye(n,i,parseInt(t)),ye(n,a,parseInt(t)),n)):l(ye({},r,parseInt(t)))}}),React.createElement(je,{type:"number",className:"fce-dimension-box-right",value:n[o],Placeholder:"Right",onChange:function(t){var n;e.state.active?l((ye(n={},r,parseInt(t)),ye(n,o,parseInt(t)),ye(n,i,parseInt(t)),ye(n,a,parseInt(t)),n)):l(ye({},o,parseInt(t)))}}),React.createElement(je,{type:"number",className:"fce-dimension-box-bottom",value:n[i],Placeholder:"Bottom",onChange:function(t){var n;e.state.active?l((ye(n={},r,parseInt(t)),ye(n,o,parseInt(t)),ye(n,i,parseInt(t)),ye(n,a,parseInt(t)),n)):l(ye({},i,parseInt(t)))}}),React.createElement(je,{type:"number",className:"fce-dimension-box-left",value:n[a],Placeholder:"Left",onChange:function(t){var n;e.state.active?l((ye(n={},r,parseInt(t)),ye(n,o,parseInt(t)),ye(n,i,parseInt(t)),ye(n,a,parseInt(t)),n)):l(ye({},a,parseInt(t)))}}),React.createElement(_e,{text:he("Link values together","fluentcrm"),position:"top"},React.createElement(Se,{className:this.state.active?"fce-dimension-link-values-together active":"fce-dimension-link-values-together",isDefault:!0,onClick:function(){e.setState({active:!e.state.active},(function(){var t;e.state.active&&l((ye(t={},r,n[r]),ye(t,o,n[r]),ye(t,i,n[r]),ye(t,a,n[r]),t))}))}},React.createElement("i",{className:"dashicons dashicons-admin-links"})))))}}])&&be(t.prototype,n),a}(Oe),Re=["fontFamily","lineHeight","marginTop","marginRight","marginBottom","marginLeft","paddingTop","paddingRight","paddingBottom","paddingLeft"],Ee=function(e,t){var n=Object(f.pickBy)(e,(function(e,t){return!!e&&Re.includes(t)}));return void 0!==e.lineHeight&&e.lineHeight&&(n.lineHeight=e.lineHeight+"px"),n},xe=["select"];function Te(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Pe(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Te(Object(n),!0).forEach((function(t){Ae(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Te(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Ae(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Ce(){return(Ce=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var Le=wp.i18n.__,Ie=wp.blockEditor.InspectorControls,Fe=wp.compose,Ne=Fe.createHigherOrderComponent,Be=Fe.compose,Ue=wp.components,De=Ue.PanelBody,Me=Ue.SelectControl,He=Ue.TextControl,We=wp.element.Fragment,Ve=wp.hooks.addFilter,Ge=wp.data.withSelect,Ke=["core/paragraph","core/heading","core/image"];Ve("blocks.registerBlockType","fluentcrm/typography/attributes",(function(e,t){return Ke.includes(t)?(void 0!==e.attributes&&(e.attributes=Object.assign(e.attributes,{fontFamily:{type:"string",default:""},lineHeight:{type:"number",default:""},paddingTop:{type:"number",default:""},paddingRight:{type:"number",default:""},paddingBottom:{type:"number",default:""},paddingLeft:{type:"number",default:""},marginTop:{type:"number",default:""},marginRight:{type:"number",default:""},marginBottom:{type:"number",default:""},marginLeft:{type:"number",default:""}})),e):e})),Ve("editor.BlockEdit","fluentcrm/typography",Ne((function(e){return function(t){if(!Ke.includes(t.name))return React.createElement(e,t);var n=t.attributes,r=t.name,o=t.setAttributes;return React.createElement(We,null,React.createElement(e,t),React.createElement(Ie,null,React.createElement(De,{initialOpen:!1,title:Le("core/image"===r?"Advanced Spacing":"Advanced Typography")},("core/heading"===r||"core/paragraph"===r)&&React.createElement(We,null,React.createElement(Me,{label:Le("Font Family"),value:n.fontFamily,options:[{label:"Select a font family",value:""},{label:"Arial",value:"Arial, 'Helvetica Neue', Helvetica, sans-serif"},{label:"Comic Sans",value:"'Comic Sans MS', 'Marker Felt-Thin', Arial, sans-serif"},{label:"Courier New",value:"'Courier New', Courier, 'Lucida Sans Typewriter', 'Lucida Typewriter', monospace"},{label:"Georgia",value:"Georgia, Times, 'Times New Roman', serif"},{label:"Helvetica",value:"Helvetica , Arial, Verdana, sans-serif"},{label:"Lucida",value:"Lucida Sans Unicode', 'Lucida Grande', sans-serif"},{label:"Tahoma",value:"Tahoma, Verdana, Segoe, sans-serif"},{label:"Times New Roman",value:"'Times New Roman', Times, Baskerville, Georgia, serif"},{label:"Trebuchet MS",value:"'Trebuchet MS', 'Lucida Grande', 'Lucida Sans Unicode', 'Lucida Sans', Tahoma, sans-serif"},{label:"Verdana",value:"Verdana, Geneva, sans-serif"},{label:"Lato",value:"'Lato', 'Helvetica Neue', Helvetica, Arial, sans-serif"},{label:"Lora",value:"'Lora', Georgia, 'Times New Roman', serif"},{label:"Merriweather",value:"'Merriweather', Georgia, 'Times New Roman', serif"},{label:"Merriweather Sans",value:"'Merriweather Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif"},{label:"Noticia Text",value:"'Noticia Text', Georgia, 'Times New Roman', serif"},{label:"Open Sans",value:"'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif"},{label:"Roboto",value:"'Roboto', 'Helvetica Neue', Helvetica, Arial, sans-serif"},{label:"Source Sans Pro",value:"'Source Sans Pro', 'Helvetica Neue', Helvetica, Arial, sans-serif"}],onChange:function(e){return o({fontFamily:e})}}),React.createElement(He,{label:Le("Line Height"),type:"number",value:n.lineHeight,onChange:function(e){return o({lineHeight:parseInt(e)})}})),React.createElement(ke,Ce({},t,{label:Le("Padding"),top:"paddingTop",right:"paddingRight",bottom:"paddingBottom",left:"paddingLeft"})),React.createElement(ke,Ce({},t,{label:Le("Margin"),top:"marginTop",right:"marginRight",bottom:"marginBottom",left:"marginLeft"})))))}}),"withControls"));var Je=Be(Ge((function(e){return{selected:e("core/block-editor").getSelectedBlock(),select:e}})));Ve("editor.BlockListBlock","fluentcrm/withTypographySettings",Ne((function(e){return Je((function(t){var n=t.select,r=function(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}(t,xe),o=r.wrapperProps,i=n("core/block-editor").getBlock(r.rootClientId||r.clientId),a=n("core/block-editor").getBlockName(r.rootClientId||r.clientId);return Ke.includes(a)&&i.attributes&&(o=Pe(Pe({},o),{},{style:Ee(i.attributes)})),React.createElement(e,Ce({},r,{wrapperProps:o}))}))}),"withTypographySettings")),Ve("blocks.getSaveContent.extraProps","fluentcrm/applyTypographySettings",(function(e,t,n){return Ke.includes(t.name)?(void 0!==e.style?e.style=Object.assign(e.style,Ee(n,t.name)):e.style=Ee(n,t.name),e):e}));var ze=n(30),qe=n.n(ze),Ye=n(18),$e=n.n(Ye),Qe={name:"crm_prefixes",className:"editor-autocompleters__crm",triggerPrefix:"@",options:function(e){var t=[];return $e()(window.fcAdmin.globalSmartCodes,(function(e){$e()(e.shortcodes,(function(n,r){t.push({code:r,title:n,prefix:e.key})}))})),t},isDebounced:!0,getOptionKeywords:function(e){return[e.title,e.code]},getOptionLabel:function(e){return[React.createElement("span",{key:"name",className:"editor-autocompleters__user-name"},e.title)]},getOptionCompletion:function(e){return e.code}},Xe=n(15),Ze=n(4);function et(e){return(et="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function tt(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function nt(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function rt(e,t){return(rt=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function ot(e,t){if(t&&("object"===et(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return it(e)}function it(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function at(e){return(at=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var ct=window.wp,lt=function(e){return Object(f.pick)(e,["sizes","mime","type","subtype","id","url","alt","link","caption"])},ut=function(e){return ct.media.query({order:"ASC",orderby:"post__in",post__in:e,posts_per_page:-1,query:!0,type:"image"})},st=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&rt(e,t)}(a,e);var t,n,r,o,i=(r=a,o=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=at(r);if(o){var n=at(this).constructor;e=Reflect.construct(t,arguments,n)}else e=t.apply(this,arguments);return ot(this,e)});function a(e){var t,n=e.allowedTypes,r=e.gallery,o=void 0!==r&&r,c=e.unstableFeaturedImageFlow,l=void 0!==c&&c,u=e.modalClass,s=e.multiple,f=void 0!==s&&s,p=e.title,d=void 0===p?Object(N.__)("Select or Upload Media"):p;if(tt(this,a),(t=i.apply(this,arguments)).openModal=t.openModal.bind(it(t)),t.onOpen=t.onOpen.bind(it(t)),t.onSelect=t.onSelect.bind(it(t)),t.onUpdate=t.onUpdate.bind(it(t)),t.onClose=t.onClose.bind(it(t)),o)t.buildAndSetGalleryFrame();else{var y={title:d,multiple:f};n&&(y.library={type:n}),t.frame=ct.media(y)}return u&&t.frame.$el.addClass(u),l&&t.buildAndSetFeatureImageFrame(),t.initializeListeners(),t}return t=a,(n=[{key:"initializeListeners",value:function(){this.frame.on("select",this.onSelect),this.frame.on("update",this.onUpdate),this.frame.on("open",this.onOpen),this.frame.on("close",this.onClose)}},{key:"buildAndSetGalleryFrame",value:function(){var e=this.props,t=e.addToGallery,n=void 0!==t&&t,r=e.allowedTypes,o=e.multiple,i=void 0!==o&&o,a=e.value,c=void 0===a?null:a;if(c!==this.lastGalleryValue){var l;this.lastGalleryValue=c,this.frame&&this.frame.remove(),l=n?"gallery-library":c?"gallery-edit":"gallery",this.GalleryDetailsMediaFrame||(this.GalleryDetailsMediaFrame=ct.media.view.MediaFrame.Post.extend({createStates:function(){this.states.add([new ct.media.controller.Library({id:"gallery",title:ct.media.view.l10n.createGalleryTitle,priority:40,toolbar:"main-gallery",filterable:"uploaded",multiple:"add",editable:!1,library:ct.media.query(Object(f.defaults)({type:"image"},this.options.library))}),new ct.media.controller.GalleryEdit({library:this.options.selection,editing:this.options.editing,menu:"gallery",displaySettings:!1,multiple:!0}),new ct.media.controller.GalleryAdd])}}));var u=ut(c),s=new ct.media.model.Selection(u.models,{props:u.props.toJSON(),multiple:i});this.frame=new this.GalleryDetailsMediaFrame({mimeType:r,state:l,multiple:i,selection:s,editing:!!c}),ct.media.frame=this.frame,this.initializeListeners()}}},{key:"buildAndSetFeatureImageFrame",value:function(){var e=ct.media.view.MediaFrame.Select.extend({featuredImageToolbar:function(e){this.createSelectToolbar(e,{text:ct.media.view.l10n.setFeaturedImage,state:this.options.state})},createStates:function(){this.on("toolbar:create:featured-image",this.featuredImageToolbar,this),this.states.add([new ct.media.controller.FeaturedImage])}}),t=ut(this.props.value),n=new ct.media.model.Selection(t.models,{props:t.props.toJSON()});this.frame=new e({mimeType:this.props.allowedTypes,state:"featured-image",multiple:this.props.multiple,selection:n,editing:!!this.props.value}),ct.media.frame=this.frame}},{key:"componentWillUnmount",value:function(){this.frame.remove()}},{key:"onUpdate",value:function(e){var t=this.props,n=t.onSelect,r=t.multiple,o=void 0!==r&&r,i=this.frame.state(),a=e||i.get("selection");a&&a.models.length&&n(o?a.models.map((function(e){return lt(e.toJSON())})):lt(a.models[0].toJSON()))}},{key:"onSelect",value:function(){var e=this.props,t=e.onSelect,n=e.multiple,r=void 0!==n&&n,o=this.frame.state().get("selection").toJSON();t(r?o:o[0])}},{key:"onOpen",value:function(){if(this.updateCollection(),this.props.value){if(!this.props.gallery){var e=this.frame.state().get("selection");Object(f.castArray)(this.props.value).forEach((function(t){e.add(ct.media.attachment(t))}))}ut(Object(f.castArray)(this.props.value)).more()}}},{key:"onClose",value:function(){var e=this.props.onClose;e&&e()}},{key:"updateCollection",value:function(){var e=this.frame.content.get();if(e&&e.collection){var t=e.collection;t.toArray().forEach((function(e){return e.trigger("destroy",e)})),t.mirroring._hasMore=!0,t.more()}}},{key:"openModal",value:function(){this.props.gallery&&this.props.value&&this.props.value.length>0&&this.buildAndSetGalleryFrame(),this.frame.open()}},{key:"render",value:function(){return this.props.render({open:this.openModal})}}])&&nt(t.prototype,n),a}(A.Component),ft=n(31),pt=["colorScheme","contentMaxWidth","children"];function dt(){return(dt=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var yt=function(e){e.colorScheme,e.contentMaxWidth;var t=e.children,n=function(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}(e,pt);return React.createElement("div",dt({className:"fc-cond-section"},n),React.createElement("div",{className:"fc-cond-blocks"},t))},bt=(wp.element.createElement,wp.element.Fragment),mt=wp.components,vt=mt.PanelBody,gt=mt.SelectControl,ht=(mt.Notice,mt.CheckboxControl,mt.BaseControl,mt.IconButton,wp.editor),Ot=ht.InspectorControls,wt=ht.InnerBlocks,jt=wp.i18n,St=jt.__,_t=jt._x,kt={title:St("Conditional Section"),description:St("Add a section that separates content, and put any other block into it."),category:"layout",icon:"welcome-widgets-menus",keywords:[_t("conditional"),_t("section")],supports:{align:["wide","full"],anchor:!0},attributes:{condition_type:{type:"string"},tag_ids:{type:"array",default:[]}},edit:function(e){var t=e.attributes,n=e.setAttributes,r=t.condition_type,o=t.tag_ids,i=window.fcAdmin.available_tags,a=window.fcAdmin.addons&&window.fcAdmin.addons.fluentcampaign,c=function(e){if(a){var r=e.target,o=r.checked,i=r.value,c=jQuery.extend(!0,[],t.tag_ids);r.checked=o,o?-1==c.indexOf(i)&&(c.push(i),n({tag_ids:c})):(c.splice(c.indexOf(i),1),n({tag_ids:c}))}else alert("This is a pro version feature")};return React.createElement(bt,null,React.createElement(Ot,null,React.createElement(vt,{title:St("Conditional Settings")},React.createElement(gt,{label:St("Condition Type"),value:r,onChange:function(e){return n({condition_type:e||"show_if_tag_exist"})},options:[{value:"show_if_tag_exist",label:"Show IF in selected tag"},{value:"show_if_tag_not_exist",label:"Show IF not in selected tag"}]}),React.createElement("div",{className:"fcrm-gb-multi-checkbox"},React.createElement("h4",null,"Select Targeted Tags"),React.createElement("ul",null,i.map((function(e){return React.createElement("label",{className:"checkbox"},e.title,React.createElement("input",{checked:-1!=o.indexOf(e.id),onChange:c,type:"checkbox",value:e.id}),React.createElement("span",{className:"checkmark"}))})))),a?React.createElement("div",{className:"fc_cd_info"},React.createElement("hr",null),React.createElement("b",null,"Tips:"),React.createElement("ul",null,React.createElement("li",null,"This will show/hide only if any tag is matched."),React.createElement("li",{style:{backgroundColor:"#ffffd7"}},"The yellow background in the content is only for editor and to identify the conditional contents"))):React.createElement("div",{style:{color:"red"}},React.createElement("b",null,"Pro Feature:"),React.createElement("p",null,"Conditional Feature will only work if you have FluentCRM pro activated. Please Install FluentCRM Pro First")))),React.createElement(yt,{colorScheme:r,contentMaxWidth:o},React.createElement(wt,null)))},save:function(e){var t=e.attributes,n=t.colorScheme,r=t.contentMaxWidth,o=t.attachmentId;return React.createElement(yt,{colorScheme:n,contentMaxWidth:r,className:F()(o&&"has-background-image-".concat(o))},React.createElement(wt.Content,null))}},Rt=n(5),Et=n(32),xt=(n(72),n(33)),Tt=n.n(xt),Pt=n(34);function At(e){return(At="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Ct(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Lt(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function It(e,t){return(It=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function Ft(e,t){if(t&&("object"===At(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Nt(e)}function Nt(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Bt(e){return(Bt=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function Ut(e,t,n,r,o,i,a){try{var c=e[i](a),l=c.value}catch(e){return void n(e)}c.done?t(l):Promise.resolve(l).then(r,o)}var Dt=function(e){return e.stopPropagation()},Mt=function(){var e,t=(e=regeneratorRuntime.mark((function e(t){var n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,window.FLUENTCRM.$get("smart-links",{search:t});case 2:return n=e.sent,e.abrupt("return",Object(f.map)(n.action_links.data,(function(e){return{url:e.short_url,title:Object(Pt.decodeEntities)(e.title),target_url:e.target_url}})));case 4:case"end":return e.stop()}}),e)})),function(){var t=this,n=arguments;return new Promise((function(r,o){var i=e.apply(t,n);function a(e){Ut(i,r,o,a,c,"next",e)}function c(e){Ut(i,r,o,a,c,"throw",e)}a(void 0)}))});return function(e){return t.apply(this,arguments)}}(),Ht=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&It(e,t)}(a,e);var t,n,r,o,i=(r=a,o=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=Bt(r);if(o){var n=Bt(this).constructor;e=Reflect.construct(t,arguments,n)}else e=t.apply(this,arguments);return Ft(this,e)});function a(e){var t,n=e.autocompleteRef;return Ct(this,a),(t=i.apply(this,arguments)).onChange=t.onChange.bind(Nt(t)),t.onKeyDown=t.onKeyDown.bind(Nt(t)),t.autocompleteRef=n||Object(A.createRef)(),t.inputRef=Object(A.createRef)(),t.updateSuggestions=Object(f.throttle)(t.updateSuggestions.bind(Nt(t)),200),t.suggestionNodes=[],t.state={suggestions:[],showSuggestions:!1,selectedSuggestion:null},t}return t=a,(n=[{key:"componentDidUpdate",value:function(){var e=this,t=this.state,n=t.showSuggestions,r=t.selectedSuggestion;n&&null!==r&&!this.scrollingIntoView&&(this.scrollingIntoView=!0,Tt()(this.suggestionNodes[r],this.autocompleteRef.current,{onlyScrollIfNeeded:!0}),this.props.setTimeout((function(){e.scrollingIntoView=!1}),100))}},{key:"componentWillUnmount",value:function(){delete this.suggestionsRequest}},{key:"bindSuggestionNode",value:function(e){var t=this;return function(n){t.suggestionNodes[e]=n}}},{key:"updateSuggestions",value:function(e){var t=this;if(e.length<2||/^https?:/.test(e))this.setState({showSuggestions:!1,selectedSuggestion:null,loading:!1});else{this.setState({showSuggestions:!0,selectedSuggestion:null,loading:!0});var n=Mt(e);n.then((function(e){t.suggestionsRequest===n&&(t.setState({suggestions:e,loading:!1}),e.length?t.props.debouncedSpeak(Object(N.sprintf)(Object(N._n)("%d result found, use up and down arrow keys to navigate.","%d results found, use up and down arrow keys to navigate.",e.length),e.length),"assertive"):t.props.debouncedSpeak(Object(N.__)("No results."),"assertive"))})).catch((function(){t.suggestionsRequest===n&&t.setState({loading:!1})})),this.suggestionsRequest=n}}},{key:"onChange",value:function(e){var t=e.target.value;this.props.onChange(t),this.updateSuggestions(t)}},{key:"onKeyDown",value:function(e){var t=this.state,n=t.showSuggestions,r=t.selectedSuggestion,o=t.suggestions,i=t.loading;if(n&&o.length&&!i){var a=this.state.suggestions[this.state.selectedSuggestion];switch(e.keyCode){case M.UP:e.stopPropagation(),e.preventDefault();var c=r?r-1:o.length-1;this.setState({selectedSuggestion:c});break;case M.DOWN:e.stopPropagation(),e.preventDefault();var l=null===r||r===o.length-1?0:r+1;this.setState({selectedSuggestion:l});break;case M.TAB:null!==this.state.selectedSuggestion&&(this.selectLink(a),this.props.speak(Object(N.__)("Link selected.")));break;case M.ENTER:null!==this.state.selectedSuggestion&&(e.stopPropagation(),this.selectLink(a))}}else switch(e.keyCode){case M.UP:0!==e.target.selectionStart&&(e.stopPropagation(),e.preventDefault(),e.target.setSelectionRange(0,0));break;case M.DOWN:this.props.value.length!==e.target.selectionStart&&(e.stopPropagation(),e.preventDefault(),e.target.setSelectionRange(this.props.value.length,this.props.value.length))}}},{key:"selectLink",value:function(e){this.props.onChange(e.url,e),this.setState({selectedSuggestion:null,showSuggestions:!1})}},{key:"handleOnClick",value:function(e){this.selectLink(e),this.inputRef.current.focus()}},{key:"render",value:function(){var e=this,t=this.props,n=t.value,r=void 0===n?"":n,o=t.autoFocus,i=void 0===o||o,a=t.instanceId,c=t.className,u=this.state,s=u.showSuggestions,f=u.suggestions,p=u.selectedSuggestion,d=u.loading,y="block-editor-url-input-suggestions-".concat(a),b="block-editor-url-input-suggestion-".concat(a);return React.createElement("div",{className:F()("editor-url-input block-editor-url-input",c)},React.createElement("input",{autoFocus:i,type:"text","aria-label":Object(N.__)("URL"),required:!0,value:r,onChange:this.onChange,onInput:Dt,placeholder:Object(N.__)("Paste or type to search for your Pretty Link"),onKeyDown:this.onKeyDown,role:"combobox","aria-expanded":s,"aria-autocomplete":"list","aria-owns":y,"aria-activedescendant":null!==p?"".concat(b,"-").concat(p):void 0,ref:this.inputRef}),d&&React.createElement(l.Spinner,null),s&&!!f.length&&React.createElement(l.Popover,{position:"bottom",noArrow:!0,focusOnMount:!1},React.createElement("div",{className:"editor-url-input__suggestions block-editor-url-input__suggestions",id:y,ref:this.autocompleteRef,role:"listbox"},f.map((function(t,n){return React.createElement("button",{key:t.id,role:"option",tabIndex:"-1",title:t.target_url,id:"".concat(b,"-").concat(n),ref:e.bindSuggestionNode(n),className:F()("editor-url-input__suggestion block-editor-url-input__suggestion",{"is-selected":n===p}),onClick:function(){return e.handleOnClick(t)},"aria-selected":n===p},t.title)})))))}}])&&Lt(t.prototype,n),a}(A.Component),Wt=Object(D.compose)(D.withSafeTimeout,l.withSpokenMessages,D.withInstanceId,Object(u.withSelect)((function(e){return{fetchLinkSuggestions:(0,e("core/block-editor").getSettings)().__experimentalFetchLinkSuggestions}})))(Ht);function Vt(e){if(!e)return!1;var t=e.trim();if(!t)return!1;if(/^\S+:/.test(t)){var n=Object(Rt.getProtocol)(t);if(!Object(Rt.isValidProtocol)(n))return!1;if(Object(f.startsWith)(n,"http")&&!/^https?:\/\/[^\/\s]/i.test(t))return!1;var r=Object(Rt.getAuthority)(t);if(!Object(Rt.isValidAuthority)(r))return!1;var o=Object(Rt.getPath)(t);if(o&&!Object(Rt.isValidPath)(o))return!1;var i=Object(Rt.getQueryString)(t);if(i&&!Object(Rt.isValidQueryString)(i))return!1;var a=Object(Rt.getFragment)(t);if(a&&!Object(Rt.isValidFragment)(a))return!1}return!(Object(f.startsWith)(t,"#")&&!Object(Rt.isValidFragment)(t))}function Gt(e){var t=e.url,n=e.opensInNewWindow,r=e.text,o=e.noFollow,i=e.isSponsored,a={type:"core/link",attributes:{url:t}};if(a.attributes.rel="",o&&(a.attributes.rel+="nofollow noindex "),i&&(a.attributes.rel+="sponsored "),n){
// translators: accessibility label for external links, where the argument is the link text
var c=Object(N.sprintf)(Object(N.__)("%s (opens in a new tab)"),r);a.attributes.target="_blank",a.attributes.rel+="noreferrer noopener",a.attributes["aria-label"]=c}return""===a.attributes.rel&&delete a.attributes.rel,a}function Kt(e){return(Kt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}n(75);var Jt=["isActive","addingLink","value"];function zt(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function qt(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function Yt(e,t){return(Yt=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function $t(e,t){if(t&&("object"===Kt(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Qt(e)}function Qt(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Xt(e){return(Xt=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function Zt(){return(Zt=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var en=function(e){return e.stopPropagation()};function tn(e,t){return e.addingLink||t.editLink}var nn=function(e){var t=e.value,n=e.onChangeInputValue,r=e.onKeyDown,o=e.submitLink,i=e.autocompleteRef;return React.createElement("form",{className:"editor-format-toolbar__link-container-content block-editor-format-toolbar__link-container-content",onKeyPress:en,onKeyDown:r,onSubmit:o},React.createElement(Wt,{value:t,onChange:n,autocompleteRef:i}),React.createElement(l.IconButton,{icon:"editor-break",label:Object(N.__)("Insert Pretty Link"),type:"submit"}))},rn=function(e){var t=e.url,n=Object(Rt.prependHTTP)(t),r=F()("editor-format-toolbar__link-container-value block-editor-format-toolbar__link-container-value",{"has-invalid-link":!Vt(n)});return t?React.createElement(l.ExternalLink,{className:r,href:t},Object(Rt.filterURLForDisplay)(Object(Rt.safeDecodeURI)(t))):React.createElement("span",{className:r})},on=function(e){var t=e.isActive,n=e.addingLink,r=e.value,o=function(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}(e,Jt),i=Object(A.useMemo)((function(){var e=window.getSelection(),t=e.rangeCount>0?e.getRangeAt(0):null;if(t){if(n)return Object(Et.getRectangleFromRange)(t);var r=t.startContainer;for(r=r.nextElementSibling||r;r.nodeType!==window.Node.ELEMENT_NODE;)r=r.parentNode;var o=r.closest("a");return o?o.getBoundingClientRect():void 0}}),[t,n,r.start,r.end]);return i?React.createElement(oe.URLPopover,Zt({anchorRect:i},o)):null},an=function(e){var t=e.url,n=e.editLink;return React.createElement("div",{className:"editor-format-toolbar__link-container-content block-editor-format-toolbar__link-container-content",onKeyPress:en},React.createElement(rn,{url:t}),React.createElement(l.IconButton,{icon:"edit",label:Object(N.__)("Edit"),onClick:n}))},cn=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&Yt(e,t)}(c,e);var t,n,r,o,i,a=(o=c,i=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=Xt(o);if(i){var n=Xt(this).constructor;e=Reflect.construct(t,arguments,n)}else e=t.apply(this,arguments);return $t(this,e)});function c(){var e;return zt(this,c),(e=a.apply(this,arguments)).editLink=e.editLink.bind(Qt(e)),e.submitLink=e.submitLink.bind(Qt(e)),e.onKeyDown=e.onKeyDown.bind(Qt(e)),e.onChangeInputValue=e.onChangeInputValue.bind(Qt(e)),e.setNoFollow=e.setNoFollow.bind(Qt(e)),e.setIsSponsored=e.setIsSponsored.bind(Qt(e)),e.setLinkTarget=e.setLinkTarget.bind(Qt(e)),e.onClickOutside=e.onClickOutside.bind(Qt(e)),e.resetState=e.resetState.bind(Qt(e)),e.autocompleteRef=Object(A.createRef)(),e.state={noFollow:!1,opensInNewWindow:!1,isSponsored:!1,inputValue:"",newLinkUrl:"",newLinkSlug:"",creatingLink:!1,createdLink:!1,createdLinkError:!1},e}return t=c,r=[{key:"getDerivedStateFromProps",value:function(e,t){var n=e.activeAttributes,r=n.url,o=n.target,i=(n.isSponsored,"_blank"===o);if(!tn(e,t)){if(r!==t.inputValue)return{inputValue:r};if(i!==t.opensInNewWindow)return{opensInNewWindow:i}}return null}}],(n=[{key:"onKeyDown",value:function(e){[M.LEFT,M.DOWN,M.RIGHT,M.UP,M.BACKSPACE,M.ENTER].indexOf(e.keyCode)>-1&&e.stopPropagation()}},{key:"onChangeInputValue",value:function(e){this.setState({inputValue:e})}},{key:"setLinkTarget",value:function(e){var t=this.props,n=t.activeAttributes.url,r=void 0===n?"":n,o=t.value,i=t.onChange;if(this.setState({opensInNewWindow:e}),!tn(this.props,this.state)){var a=Object(Ze.getTextContent)(Object(Ze.slice)(o));i(Object(Ze.applyFormat)(o,Gt({url:r,opensInNewWindow:e,text:a})))}}},{key:"setNoFollow",value:function(e){var t=this.props,n=t.activeAttributes.url,r=void 0===n?"":n,o=t.value,i=t.onChange;if(this.setState({noFollow:e}),!tn(this.props,this.state)){var a=Object(Ze.getTextContent)(Object(Ze.slice)(o));i(Object(Ze.applyFormat)(o,Gt({url:r,opensInNewWindow:opensInNewWindow,text:a,noFollow:e,isSponsored:isSponsored})))}}},{key:"setIsSponsored",value:function(e){var t=this.props,n=t.activeAttributes.url,r=void 0===n?"":n,o=t.value,i=t.onChange;if(this.setState({isSponsored:e}),!tn(this.props,this.state)){var a=Object(Ze.getTextContent)(Object(Ze.slice)(o));i(Object(Ze.applyFormat)(o,Gt({url:r,opensInNewWindow:opensInNewWindow,text:a,noFollow:noFollow,isSponsored:e})))}}},{key:"editLink",value:function(e){this.setState({editLink:!0}),e.preventDefault()}},{key:"submitLink",value:function(e){var t=this.props,n=t.isActive,r=t.value,o=t.onChange,i=t.speak,a=this.state,c=a.inputValue,l=a.opensInNewWindow,u=a.noFollow,s=a.isSponsored,f=Object(Rt.prependHTTP)(c),p=Gt({url:f,opensInNewWindow:l,text:Object(Ze.getTextContent)(Object(Ze.slice)(r)),noFollow:u,isSponsored:s});if(e.preventDefault(),Object(Ze.isCollapsed)(r)&&!n){var d=Object(Ze.applyFormat)(Object(Ze.create)({text:f}),p,0,f.length);o(Object(Ze.insert)(r,d))}else o(Object(Ze.applyFormat)(r,p));this.resetState(),Vt(f)?i(n?Object(N.__)("Link edited."):Object(N.__)("Link inserted."),"assertive"):i(Object(N.__)("Warning: the link has been inserted but may have errors. Please test it."),"assertive")}},{key:"onClickOutside",value:function(e){var t=this.autocompleteRef.current;t&&t.contains(e.target)||this.resetState()}},{key:"resetState",value:function(){this.props.stopAddingLink(),this.setState({editLink:!1})}},{key:"render",value:function(){var e=this,t=this.props,n=t.isActive,r=t.activeAttributes.url,o=t.addingLink,i=t.value;if(!n&&!o)return null;var a=this.state,c=a.inputValue,u=a.noFollow,s=a.opensInNewWindow,f=a.isSponsored,p=(a.newLinkUrl,a.newLinkSlug,a.creatingLink,a.createdLink,a.createdLinkError,tn(this.props,this.state));return React.createElement(on,{className:"pretty-link-inserter",value:i,isActive:n,addingLink:o,onClickOutside:this.onClickOutside,onClose:this.resetState,focusOnMount:!!p&&"firstElement",renderSettings:function(){return React.createElement(A.Fragment,null,React.createElement("div",null,React.createElement(l.ToggleControl,{label:Object(N.__)("Open in New Tab"),checked:s,onChange:e.setLinkTarget}),React.createElement(l.ToggleControl,{label:Object(N.__)("Nofollow"),checked:u,onChange:e.setNoFollow}),React.createElement(l.ToggleControl,{label:Object(N.__)("Sponsored Link"),checked:f,onChange:e.setIsSponsored})))}},p?React.createElement(nn,{value:c,onChangeInputValue:this.onChangeInputValue,onKeyDown:this.onKeyDown,submitLink:this.submitLink,autocompleteRef:this.autocompleteRef}):React.createElement(an,{url:r,editLink:this.editLink}))}}])&&qt(t.prototype,n),r&&qt(t,r),c}(A.Component),ln=Object(l.withSpokenMessages)(cn);function un(e){return(un="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function sn(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function fn(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function pn(e,t){return(pn=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function dn(e,t){if(t&&("object"===un(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return yn(e)}function yn(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function bn(e){return(bn=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var mn=Object(N.__)("SmartLinks"),vn={name:"fluentcrm/smart-links",title:mn,tagName:"a",className:"smart-link",attributes:{url:"href",target:"target"},edit:Object(l.withSpokenMessages)(function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&pn(e,t)}(a,e);var t,n,r,o,i=(r=a,o=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=bn(r);if(o){var n=bn(this).constructor;e=Reflect.construct(t,arguments,n)}else e=t.apply(this,arguments);return dn(this,e)});function a(){var e;return sn(this,a),(e=i.apply(this,arguments)).addLink=e.addLink.bind(yn(e)),e.stopAddingLink=e.stopAddingLink.bind(yn(e)),e.onRemoveFormat=e.onRemoveFormat.bind(yn(e)),e.state={addingLink:!1},e}return t=a,(n=[{key:"addLink",value:function(){var e=this.props,t=e.value,n=e.onChange,r=Object(Ze.getTextContent)(Object(Ze.slice)(t));r&&Object(Rt.isURL)(r)?n(Object(Ze.applyFormat)(t,{type:"fluentcrm/smart-links",attributes:{url:r}})):this.setState({addingLink:!0})}},{key:"stopAddingLink",value:function(){this.setState({addingLink:!1})}},{key:"onRemoveFormat",value:function(){var e=this.props,t=e.value,n=e.onChange,r=e.speak;n(Object(Ze.removeFormat)(t,"fluentcrm/smart-links")),r(Object(N.__)("Link removed."),"assertive")}},{key:"render",value:function(){var e=this.props,t=e.isActive,n=e.activeAttributes,r=e.value,o=e.onChange;return React.createElement(React.Fragment,null,React.createElement(oe.RichTextShortcut,{type:"primary",character:"p",onUse:this.addLink}),React.createElement(oe.RichTextShortcut,{type:"primaryShift",character:"p",onUse:this.onRemoveFormat}),t&&React.createElement(oe.RichTextToolbarButton,{icon:"star-filled",title:Object(N.__)("Unlink"),onClick:this.onRemoveFormat,isActive:t,shortcutType:"primaryShift",shortcutCharacter:"p"}),!t&&React.createElement(oe.RichTextToolbarButton,{icon:"star-filled",title:mn,onClick:this.addLink,isActive:t,shortcutType:"primary",shortcutCharacter:"p"}),React.createElement(ln,{addingLink:this.state.addingLink,stopAddingLink:this.stopAddingLink,isActive:t,activeAttributes:n,value:r,onChange:o}))}}])&&fn(t.prototype,n),a}(A.Component))},gn=n(12),hn=n.n(gn)()((function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"UPDATE_BLOCKS":case"PERSIST_BLOCKS":var n=t.blocks;return{blocks:n}}return e}),{filter:Object(gn.includeAction)("PERSIST_BLOCKS")}),On=function(e){return e.present.blocks||[]},wn=function(e){var t;return null===(t=e.past)||void 0===t?void 0:t.length},jn=function(e){var t;return null===(t=e.future)||void 0===t?void 0:t.length},Sn=regeneratorRuntime.mark(En);function _n(){return gn.ActionCreators.undo()}function kn(){return gn.ActionCreators.redo()}function Rn(){return gn.ActionCreators.clearHistory()}function En(e){var t,n=arguments;return regeneratorRuntime.wrap((function(r){for(;;)switch(r.prev=r.next){case 0:if(!(t=n.length>1&&void 0!==n[1]&&n[1])){r.next=4;break}return r.next=4,Tn(e);case 4:return r.abrupt("return",{type:t?"PERSIST_BLOCKS":"UPDATE_BLOCKS",blocks:e});case 5:case"end":return r.stop()}}),Sn)}function xn(){return{type:"FETCH_BLOCKS_FROM_STORAGE"}}function Tn(e){return{type:"PERSIST_BLOCKS_TO_STORAGE",blocks:e}}var Pn,An=regeneratorRuntime.mark(Cn);function Cn(){var e,t;return regeneratorRuntime.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,{type:"FETCH_BLOCKS_FROM_STORAGE"};case 2:return e=n.sent,t=Object(ne.parse)(e),n.next=7,En(t,!1);case 7:return n.abrupt("return",t);case 8:case"end":return n.stop()}}),An)}function Ln(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var In=(Ln(Pn={},"PERSIST_BLOCKS_TO_STORAGE",(function(e){return new Promise((function(t,n){window.fceSettings.content=Object(ne.serialize)(e.blocks),t(e.blocks)}))})),Ln(Pn,"FETCH_BLOCKS_FROM_STORAGE",(function(){return new Promise((function(e,t){e(window.fceSettings.content||[])}))})),Pn),Fn=Object(u.createReduxStore)("historyUndoRedoStore",{reducer:hn,selectors:i,actions:a,controls:In,resolvers:c});Object(u.register)(Fn),n(78);var Nn=["name"];function Bn(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var Un=function(){return st};qe()((function(){Object(Xe.addFilter)("editor.Autocomplete.completers","fluentcrm/add_autocmplete",(function(e,t){return e=e.filter((function(e){return"users"!==e.name})),[].concat(function(e){if(Array.isArray(e))return Bn(e)}(n=e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(n)||function(e,t){if(e){if("string"==typeof e)return Bn(e,void 0);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Bn(e,void 0):void 0}}(n)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),[Qe]);var n})),Object(Xe.addFilter)("blocks.registerBlockType","fluentcrm/remove_drop_cap",(function(e,t){if("core/paragraph"!==t)return e;var n=Object.assign({},e);return n.supports&&n.supports.__experimentalFeatures&&n.supports.__experimentalFeatures.typography&&n.supports.__experimentalFeatures.typography.dropCap&&(n.supports.__experimentalFeatures.typography.dropCap=!1),n})),Object(Xe.addFilter)("editor.MediaUpload","crm/media_uploader",Un),Object(ft.registerCoreBlocks)(),Object(ne.registerBlockType)("fluentcrm/conditional-group",kt),window.fcAdmin.has_smart_link&&[vn].forEach((function(e){var t=e.name,n=function(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}(e,Nn);return Object(Ze.registerFormatType)(t,n)}))})),window.fluentCrmBootEmailEditor=function(e,t){var n=window.fceSettings||{};n.content=e,Object(u.dispatch)("historyUndoRedoStore").updateBlocks(Object(ne.parse)(e)),Object(A.render)(React.createElement(pe,{settings:n,onChangeHandle:t}),document.getElementById("fluentcrm_block_editor_x"))}}]);