(()=>{"use strict";var e={2462:(e,r,a)=>{a.d(r,{Z:()=>c});var n=a(1519),t=a.n(n)()((function(e){return e[1]}));t.push([e.id,"@keyframes fc_spin{0%{transform:rotate(0deg)}to{transform:rotate(1turn)}}li#wp-admin-bar-fc_global_search>a{background:#7757e6}li#wp-admin-bar-fc_global_search *{box-sizing:border-box;margin:0;overflow:hidden;padding:0}li#wp-admin-bar-fc_global_search .fc_search_container{clear:both;display:none;position:relative}li#wp-admin-bar-fc_global_search .fc_search_container.fc_show{background:#fff;border:1px solid #7757e6;box-shadow:0 8px 2px 3px #f1f1f1;display:block;min-width:400px;padding:10px 20px;position:absolute;right:0}li#wp-admin-bar-fc_global_search .fc_search_container input{border-radius:7px;padding:4px 10px;width:100%}li#wp-admin-bar-fc_global_search .fc_search_container .fc_quick_links_wrapper{background:#f7f5f5;clear:both;display:block;margin:10px -20px -10px!important;overflow:hidden;padding:0 20px 20px}li#wp-admin-bar-fc_global_search .fc_search_container .fc_quick_links_wrapper h4{color:#000;font-size:16px;margin:0;padding:0}li#wp-admin-bar-fc_global_search .fc_search_container .fc_quick_links_wrapper ul.fc_quick_links{clear:both;display:block;list-style:disc;margin:0;overflow:hidden;padding:0;width:100%}li#wp-admin-bar-fc_global_search .fc_search_container .fc_quick_links_wrapper ul.fc_quick_links li{display:block;width:50%}li#wp-admin-bar-fc_global_search .fc_search_container .fc_quick_links_wrapper ul.fc_quick_links li a{margin:0;padding:0}li#wp-admin-bar-fc_global_search .fc_search_container div#fc_search_result_wrapper{clear:both;display:block;width:100%}li#wp-admin-bar-fc_global_search .fc_search_container div#fc_search_result_wrapper.fc_has_results{padding:10px 0}li#wp-admin-bar-fc_global_search .fc_search_container div#fc_search_result_wrapper .fc_result_lists{list-style:disc}li#wp-admin-bar-fc_global_search .fc_search_container div#fc_search_result_wrapper.fc_loading .fc_result_lists{animation:fc_spin 2s linear infinite;border:5px solid #f3f3f3;border-radius:50%;border-top-color:#3498db;opacity:0;text-align:center}li#wp-admin-bar-fc_global_search .fc_search_container .fc_load_more{display:none;text-align:center}li#wp-admin-bar-fc_global_search .fc_search_container .fc_load_more.fc_has_more{display:block}li#wp-admin-bar-fc_global_search .fc_search_container .fc_load_more button#fc_load_more_result{background:#fff;border:1px solid grey;border-radius:6px;cursor:pointer;height:auto;line-height:16px;margin:0;padding:6px 15px}li#wp-admin-bar-fc_global_search .fc_search_container .fc_load_more button#fc_load_more_result:hover{background:#000;color:#fff}html[dir=rtl] li#wp-admin-bar-fc_global_search .fc_search_container.fc_show{left:0;right:auto}",""]);const c=t},1519:e=>{e.exports=function(e){var r=[];return r.toString=function(){return this.map((function(r){var a=e(r);return r[2]?"@media ".concat(r[2]," {").concat(a,"}"):a})).join("")},r.i=function(e,a,n){"string"==typeof e&&(e=[[null,e,""]]);var t={};if(n)for(var c=0;c<this.length;c++){var i=this[c][0];null!=i&&(t[i]=!0)}for(var o=0;o<e.length;o++){var s=[].concat(e[o]);n&&t[s[0]]||(a&&(s[2]?s[2]="".concat(a," and ").concat(s[2]):s[2]=a),r.push(s))}},r}},3379:(e,r,a)=>{var n,t=function(){return void 0===n&&(n=Boolean(window&&document&&document.all&&!window.atob)),n},c=function(){var e={};return function(r){if(void 0===e[r]){var a=document.querySelector(r);if(window.HTMLIFrameElement&&a instanceof window.HTMLIFrameElement)try{a=a.contentDocument.head}catch(e){a=null}e[r]=a}return e[r]}}(),i=[];function o(e){for(var r=-1,a=0;a<i.length;a++)if(i[a].identifier===e){r=a;break}return r}function s(e,r){for(var a={},n=[],t=0;t<e.length;t++){var c=e[t],s=r.base?c[0]+r.base:c[0],l=a[s]||0,f="".concat(s," ").concat(l);a[s]=l+1;var u=o(f),_={css:c[1],media:c[2],sourceMap:c[3]};-1!==u?(i[u].references++,i[u].updater(_)):i.push({identifier:f,updater:b(_,r),references:1}),n.push(f)}return n}function l(e){var r=document.createElement("style"),n=e.attributes||{};if(void 0===n.nonce){var t=a.nc;t&&(n.nonce=t)}if(Object.keys(n).forEach((function(e){r.setAttribute(e,n[e])})),"function"==typeof e.insert)e.insert(r);else{var i=c(e.insert||"head");if(!i)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");i.appendChild(r)}return r}var f,u=(f=[],function(e,r){return f[e]=r,f.filter(Boolean).join("\n")});function _(e,r,a,n){var t=a?"":n.media?"@media ".concat(n.media," {").concat(n.css,"}"):n.css;if(e.styleSheet)e.styleSheet.cssText=u(r,t);else{var c=document.createTextNode(t),i=e.childNodes;i[r]&&e.removeChild(i[r]),i.length?e.insertBefore(c,i[r]):e.appendChild(c)}}function d(e,r,a){var n=a.css,t=a.media,c=a.sourceMap;if(t?e.setAttribute("media",t):e.removeAttribute("media"),c&&"undefined"!=typeof btoa&&(n+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(c))))," */")),e.styleSheet)e.styleSheet.cssText=n;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(n))}}var p=null,h=0;function b(e,r){var a,n,t;if(r.singleton){var c=h++;a=p||(p=l(r)),n=_.bind(null,a,c,!1),t=_.bind(null,a,c,!0)}else a=l(r),n=d.bind(null,a,r),t=function(){!function(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e)}(a)};return n(e),function(r){if(r){if(r.css===e.css&&r.media===e.media&&r.sourceMap===e.sourceMap)return;n(e=r)}else t()}}e.exports=function(e,r){(r=r||{}).singleton||"boolean"==typeof r.singleton||(r.singleton=t());var a=s(e=e||[],r);return function(e){if(e=e||[],"[object Array]"===Object.prototype.toString.call(e)){for(var n=0;n<a.length;n++){var t=o(a[n]);i[t].references--}for(var c=s(e,r),l=0;l<a.length;l++){var f=o(a[l]);0===i[f].references&&(i[f].updater(),i.splice(f,1))}a=c}}}}},r={};function a(n){var t=r[n];if(void 0!==t)return t.exports;var c=r[n]={id:n,exports:{}};return e[n](c,c.exports,a),c.exports}a.n=e=>{var r=e&&e.__esModule?()=>e.default:()=>e;return a.d(r,{a:r}),r},a.d=(e,r)=>{for(var n in r)a.o(r,n)&&!a.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:r[n]})},a.o=(e,r)=>Object.prototype.hasOwnProperty.call(e,r),a.nc=void 0,(()=>{var e=a(3379),r=a.n(e),n=a(2462),t={insert:"head",singleton:!1};r()(n.Z,t);n.Z.locals;var c=function e(r,a,n){var t,c=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;if("string"==typeof r?t=document.createElement(r):r instanceof HTMLElement&&(t=r),a)for(var i in a)t.setAttribute(i,a[i]);return(n||0==n)&&e.append(t,n,c),t};function i(e){return window.fc_bar_vars.trans[e]||e}c.append=function(e,r,a){e instanceof HTMLTextAreaElement||e instanceof HTMLInputElement?r instanceof Text||"string"==typeof r||"number"==typeof r?e.value=r:r instanceof Array?r.forEach((function(r){c.append(e,r)})):"function"==typeof r&&c.append(e,r()):r instanceof HTMLElement||r instanceof Text?e.appendChild(r):"string"==typeof r||"number"==typeof r?a?e.innerHTML+=r:e.appendChild(document.createTextNode(r)):r instanceof Array?r.forEach((function(r){c.append(e,r)})):"function"==typeof r&&c.append(e,r())},{init:function(){this.initButton(),window.fc_bar_vars.edit_user_vars&&window.fc_bar_vars.edit_user_vars.crm_profile_url&&this.maybeUserProfile(window.fc_bar_vars.edit_user_vars)},current_page:1,initButton:function(){var e=this,r=document.getElementById("wp-admin-bar-fc_global_search"),a=jQuery("#wp-admin-bar-fc_global_search"),n=this.getSearchDom();n.append(c("div",{class:"fc_load_more"},[c("button",{id:"fc_load_more_result"},i("Load More"))])),n.append(this.getQuickLinks()),r.append(n),a.on("mouseenter",(function(){var e=a.find(".fc_search_container");e.addClass("fc_show"),e.hasClass("fc_show")&&e.find("input").focus()})).on("mouseleave",(function(){a.find(".fc_search_container").removeClass("fc_show")})),jQuery("#fc_search_input").on("keypress",(function(r){if(e.current_page=1,13==r.which){var a=jQuery.trim(jQuery(this).val());jQuery("#fc_search_input").attr("data-searched")!==a&&(r.preventDefault(),e.current_page=1,e.performSearch(a))}})).on("keyup",(function(e){jQuery.trim(jQuery(this).val())||(jQuery("#fc_search_input").attr("data-searched",""),jQuery("#fc_search_result_wrapper").html('<p class="fc_no_result">'+i("Type and press enter")+"</p>").removeClass("fc_has_results").removeClass("fc_has_more"))})),jQuery("#fc_load_more_result").on("click",(function(r){r.preventDefault(),e.current_page++,e.performSearch(jQuery("#fc_search_input").val())}))},getSearchDom:function(){return c("div",{class:"fc_search_container"},[c("div",{class:"fc_search_box"},[c("input",{type:"search",placeholder:i("Search Contacts"),autocomplete:"off",id:"fc_search_input",autocorrect:"off",autocapitalize:"none",spellcheck:"false"})]),c("div",{id:"fc_search_result_wrapper"},i("Type to search contacts"))])},getQuickLinks:function(){var e=[];return jQuery.each(window.fc_bar_vars.links,(function(r,a){e.push(c("li",{},[c("a",{href:a.url},a.title)]))})),c("div",{class:"fc_quick_links_wrapper"},[c("h4",{},i("Quick Links")),c("ul",{class:"fc_quick_links"},e)])},performSearch:function(e){var r=this;if(!e)return"";jQuery("#fc_search_result_wrapper").addClass("fc_loading"),this.$get("subscribers",{per_page:10,page:this.current_page,search:e,sort_by:"id",sort_type:"DESC"}).then((function(a){r.pushSearchResult(a.subscribers.data,parseInt(a.subscribers.current_page)<a.subscribers.last_page),jQuery("#fc_search_input").attr("data-searched",e)})).catch((function(e){})).finally((function(){jQuery("#fc_search_result_wrapper").removeClass("fc_loading")}))},pushSearchResult:function(e){var r=arguments.length>1&&void 0!==arguments[1]&&arguments[1],a=jQuery("#fc_search_result_wrapper");if(e.length){var n=[];jQuery.each(e,(function(e,r){n.push(c("li",{},[c("a",{href:window.fc_bar_vars.subscriber_base+r.id+"?t="+r.hash},r.full_name+" - "+r.email)]))}));var t=c("ul",{class:"fc_result_lists"},n);a.html(t).addClass("fc_has_results"),r?jQuery(".fc_load_more").addClass("fc_has_more"):jQuery(".fc_load_more").removeClass("fc_has_more")}else a.html('<p class="fc_no_result">'+i("Sorry no contact found")+"</p>").removeClass("fc_has_results").removeClass("fc_has_more")},$get:function(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a="".concat(window.fc_bar_vars.rest.url,"/").concat(e);return new Promise((function(e,n){window.jQuery.ajax({url:a,type:"GET",data:r,beforeSend:function(e){e.setRequestHeader("X-WP-Nonce",window.fc_bar_vars.rest.nonce)}}).then((function(r){return e(r)})).catch((function(e){return n(e.responseJSON)}))}))},maybeUserProfile:function(e){window.jQuery('<a style="background: #7757e6;color: white;border-color: #7757e6;" class="page-title-action" href="'+e.crm_profile_url+'">View CRM Profile</a>').insertBefore("#profile-page > .wp-header-end")}}.init()})()})();