jQuery(document).ready((function(e){var r=e(".fluentcrm_public_pref_form");r.length&&(r.find("input[name=reason]").on("change",(function(){"other"===r.find("input[name=reason]:checked").val()?r.find("#fluentcrm_other_reason_wrapper").show():r.find("#fluentcrm_other_reason_wrapper").hide()})),r.on("submit",(function(r){r.preventDefault();var n=e(this).serialize();e(".fluentcrm_form_responses").html(""),e.post(window.fluentcrm_public_pref.ajaxurl,n).then((function(r){e(".fluentcrm_un_form_wrapper").html('<div class="fluentcrm_success">'+r.data.message+"</div>"),r.data.redirect_url&&(window.location.href=r.data.redirect_url)})).fail((function(r){var n="Sorry! Something is wrong. Please try again";r.responseJSON&&r.responseJSON.data&&r.responseJSON.data.message&&(n=r.responseJSON.data.message),e(".fluentcrm_form_responses").html('<div class="fluentcrm_error">'+n+"</div>")})).always((function(){}))})),"yes"==window.fluentcrm_public_pref.auto_unsubscribe&&setTimeout((function(){e("#fluentcrm_unsubscribe_submit").click()}),500));var n=e("#fc_pref_form");n.length&&n.on("submit",(function(r){r.preventDefault();var n=e(this).serialize(),s=e("#fluentcrm_preferences_submit");e(".fluentcrm_form_responses").html(""),s.attr("disabled",!0).addClass("fc_btn_loading"),e.post(window.fluentcrm_sub_pref.ajaxurl,n).then((function(r){e(".fluentcrm_form_responses").html('<div class="fluentcrm_success">'+r.data.message+"</div>")})).fail((function(r){var n="Sorry! Something is wrong. Please try again";r.responseJSON&&r.responseJSON.data&&r.responseJSON.data.message&&(n=r.responseJSON.data.message),e(".fluentcrm_form_responses").html('<div class="fluentcrm_error">'+n+"</div>")})).always((function(){s.attr("disabled",!1).removeClass("fc_btn_loading")}))}))}));