msgid ""
msgstr ""
"Project-Id-Version: FluentCRM - Marketing Automation For WordPress\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-11-14 13:14+0000\n"
"PO-Revision-Date: 2024-10-16 12:28+0000\n"
"Last-Translator: admin\n"
"Language-Team: Hebrew\n"
"Language: he_IL\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Loco https://localise.biz/\n"
"X-Loco-Version: 2.5.2; wp-5.6.2"

#: app/Services/ExternalIntegrations/MailComplaince/Webhook.php:58
msgid " at "
msgstr ""

#: app/Services/CrmMigrator/ActiveCampaignMigrator.php:312
msgid " contacts have been imported so far."
msgstr ""

#: app/Services/CrmMigrator/DripMigrator.php:153
#: app/Services/CrmMigrator/MailChimpMigrator.php:169
msgid " contacts will be imported"
msgstr ""

#: app/Services/CrmMigrator/MailerLiteMigrator.php:145
msgid " groups and associate contacts will be imported from MailerLite"
msgstr ""

#: app/Services/CrmMigrator/ActiveCampaignMigrator.php:153
msgid " lists and associate contacts  will be imported"
msgstr ""

#: app/Services/ExternalIntegrations/MailComplaince/Webhook.php:227
msgid " status was set from PostMark Webhook API. Reason: "
msgstr ""

#: app/Services/ExternalIntegrations/MailComplaince/Webhook.php:92
#: app/Services/ExternalIntegrations/MailComplaince/Webhook.php:128
msgid " status was set from SendGrid Webhook API. Reason: "
msgstr ""

#: app/Services/ExternalIntegrations/MailComplaince/Webhook.php:181
msgid " status was set from Sparkpost Webhook API. Reason: "
msgstr ""

#: app/Services/CrmMigrator/ConvertKitMigrator.php:148
msgid " tags and associate contacts will be imported from ConvertKit"
msgstr ""

#: app/Services/CrmMigrator/MailerLiteMigrator.php:271
#: app/Services/CrmMigrator/ConvertKitMigrator.php:257
msgid " tags have been imported so far"
msgstr ""

#: app/Services/ExternalIntegrations/MailComplaince/Webhook.php:58
msgid " was set by mailgun webhook api with event name: "
msgstr ""

#: app/Http/Controllers/SettingsController.php:68
#: app/Http/Controllers/TemplateController.php:227
msgid ""
"##crm.manage_subscription_url## or ##crm.unsubscribe_url## string is "
"required for compliance. Please include unsubscription or manage "
"subscription link"
msgstr ""

#: app/Hooks/Handlers/PurchaseHistory.php:127
#, php-format
msgid "%1$s for %2$s item"
msgid_plural "%1$s for %2$s items"
msgstr[0] ""
msgstr[1] ""

#: app/Http/Controllers/SubscriberController.php:1151
msgid "%d subscribers has been attached to the selected automation funnel"
msgstr ""

#: app/Http/Controllers/SubscriberController.php:1030
msgid "%d subscribers has been attached to the selected company"
msgstr ""

#: app/Http/Controllers/SubscriberController.php:983
msgid "%d subscribers has been attached to the selected email sequence"
msgstr ""

#: app/Http/Controllers/SubscriberController.php:1077
msgid "%d subscribers has been detached from the selected company"
msgstr ""

#: app/Services/CrmMigrator/MailChimpMigrator.php:69
msgid "(Contacts count "
msgstr ""

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:253
msgid ""
"(Optional) The selected tags will be removed from the contact (if exist)"
msgstr ""

#: app/Http/Controllers/CampaignController.php:727
msgid ", The dynamic tags may not replaced in test email"
msgstr ""

#: app/Services/ExternalIntegrations/MailComplaince/Webhook.php:92
#: app/Services/ExternalIntegrations/MailComplaince/Webhook.php:128
#: app/Services/ExternalIntegrations/MailComplaince/Webhook.php:181
#: app/Services/ExternalIntegrations/MailComplaince/Webhook.php:227
msgid ". Recorded at: "
msgstr ""

#: app/Http/Controllers/FunnelController.php:537
msgid "[Copy] "
msgstr ""

#: app/Http/Controllers/CampaignController.php:1171
#: app/Http/Controllers/TemplateController.php:175
#, fuzzy
#| msgid "Duplicate"
msgid "[Duplicate] "
msgstr "לְשַׁכְפֵּל"

#: app/Services/Funnel/BaseBenchMark.php:78
msgid ""
"[Essential Point] Select IF this step is required for processing further "
"actions"
msgstr "[נקודה חיונית] בחר אם שלב זה נדרש לעיבוד פעולות נוספות"

#: app/Services/Funnel/BaseBenchMark.php:74
msgid "[Optional Point] This is an optional trigger point"
msgstr "[נקודה אופציונלית] זו נקודת טריגר אופציונלית"

#: app/Hooks/Handlers/ExternalPages.php:1069
#, php-format
msgid ""
"A confirmation email has been sent to %s. Please confirm your email address "
"to resubscribe"
msgstr ""
"דוא\"ל קונפורמציה נשלח אל %s. אנא אשר את כתובת הדוא\"ל שלך כדי להירשם מחדש"

#: app/Hooks/Handlers/ExternalPages.php:1048
#, php-format
msgid ""
"A confirmation email has been sent to %s. Please confirm your email address "
"to resubscribe with changed email address"
msgstr ""
"דוא\"ל קונפורמציה נשלח אל %s. אנא אשר את כתובת הדוא\"ל שלך כדי להירשם מחדש "
"עם כתובת הדוא\"ל שהשתנתה"

#: app/Services/CrmMigrator/MailerLiteMigrator.php:33
msgid "Account -> Integrations -> Developer API"
msgstr ""

#: app/Services/CrmMigrator/ConvertKitMigrator.php:34
msgid "Account -> Settings -> Advanced"
msgstr ""

#: app/Services/CrmMigrator/DripMigrator.php:36
msgid "Account ID"
msgstr ""

#: app/Hooks/Handlers/PurchaseHistory.php:162
#: app/Hooks/Handlers/PurchaseHistory.php:399
msgid "Actions"
msgstr ""

#: app/Services/Helper.php:1335
#, fuzzy
#| msgid "Action"
msgid "Active"
msgstr "פעולה"

#: app/Services/Stats.php:53
msgid "Active Automations"
msgstr ""

#: app/Services/Stats.php:18
msgid "Active Contacts"
msgstr ""

#: app/Http/Controllers/DocsController.php:94
msgid "Active Fluent Connect"
msgstr ""

#: app/Http/Controllers/DocsController.php:67
msgid "Active Fluent Forms"
msgstr ""

#: app/Http/Controllers/DocsController.php:76
msgid "Active Fluent SMTP"
msgstr ""

#: app/Http/Controllers/DocsController.php:85
msgid "Active Fluent Support"
msgstr ""

#: app/Services/CrmMigrator/ActiveCampaignMigrator.php:31
msgid "ActiveCampaign API Token"
msgstr ""

#: app/Http/Controllers/SettingsController.php:573
msgid "Activity Logs"
msgstr ""

#: app/Services/AutoSubscribe.php:258
msgid "Add a subscription box to WooCommerce Checkout Form"
msgstr ""

#: app/Services/Funnel/Actions/ApplyCompanyAction.php:23
msgid "Add contact to the selected company"
msgstr ""

#: app/Services/Funnel/Actions/ApplyListAction.php:23
#, fuzzy
#| msgid "Add this contact to the selected lists"
msgid "Add contact to the selected lists"
msgstr "הוסף איש קשר זה לרשימות שנבחרו"

#: app/Services/Funnel/Actions/ApplyTagAction.php:23
msgid "Add this contact to the selected Tags"
msgstr "הוסף איש קשר זה לתגיות שנבחרו"

#: app/Hooks/Handlers/AdminMenu.php:199 app/Hooks/Handlers/AdminMenu.php:200
#: app/Hooks/Handlers/AdminMenu.php:1308 app/Hooks/Handlers/AdminMenu.php:1309
msgid "Addons"
msgstr ""

#: app/Hooks/Handlers/PrefFormHandler.php:55
msgid "Address Information"
msgstr ""

#: app/Models/Company.php:60 app/Models/Subscriber.php:725
#: app/Services/Helper.php:167 app/Services/Helper.php:918
#: app/Hooks/Handlers/PrefFormHandler.php:48
#: app/Hooks/Handlers/PrefFormHandler.php:433
#: app/Services/CrmMigrator/BaseMigrator.php:36
#: app/Services/Funnel/FunnelHelper.php:152
msgid "Address Line 1"
msgstr "שורת כתובת 1"

#: app/Models/Company.php:61 app/Models/Subscriber.php:726
#: app/Services/Helper.php:168 app/Services/Helper.php:923
#: app/Hooks/Handlers/PrefFormHandler.php:49
#: app/Hooks/Handlers/PrefFormHandler.php:444
#: app/Services/CrmMigrator/BaseMigrator.php:37
#: app/Services/Funnel/FunnelHelper.php:156
msgid "Address Line 2"
msgstr "שורת כתובת 2"

#: app/Services/Helper.php:207
msgid "Admin Email"
msgstr "דוא\"ל מנהל"

#: app/Services/Helper.php:1320
msgid "Affiliate ID (Pro Required)"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:25
msgid "Afghanistan"
msgstr "אפגניסטן"

#: app/Http/Controllers/SettingsController.php:133
msgid "After Confirmation Actions"
msgstr ""

#: app/Http/Controllers/SettingsController.php:153
#: app/Http/Controllers/SettingsController.php:154
msgid "After Confirmation Message"
msgstr "לאחר הודעת האישור"

#: app/Http/Controllers/SettingsController.php:219
msgid "After Confirmation Message is required"
msgstr "לאחר נדרשת הודעת אישור"

#: app/Http/Controllers/SettingsController.php:138
msgid "After Confirmation Type"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:33
msgid "Albania"
msgstr "אלבניה"

#: app/Hooks/Handlers/CountryNames.php:37
msgid "Algeria"
msgstr "אלג'יריה"

#: app/Http/Controllers/ImporterController.php:153
msgid "All"
msgstr "את כל"

#: app/Hooks/Handlers/AdminMenu.php:356
msgid "All Campaigns"
msgstr "כל הקמפיינים"

#: app/Hooks/CLI/Commands.php:26 app/Hooks/Handlers/AdminMenu.php:304
msgid "All Contacts"
msgstr "כל אנשי הקשר"

#: app/Http/Controllers/SubscriberController.php:909
msgid "All contacts has been processed"
msgstr ""

#: app/Hooks/CLI/Commands.php:42 app/Hooks/Handlers/AdminMenu.php:380
msgid "All Emails"
msgstr "כל המיילים"

#: app/Http/Controllers/SettingsController.php:306
msgid "All FluentCRM Database Tables have been resetted"
msgstr "כל טבלאות מסדי הנתונים של FluentCRM אופסו מחדש"

#: app/Http/Controllers/SystemLogController.php:39
msgid "All logs has been deleted"
msgstr ""

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:218
msgid ""
"Allow FluentCRM integration conditionally based on your submission values"
msgstr "אפשר שילוב FluentCRM מותנה בהתבסס על ערכי ההגשה שלך"

#: app/Http/Controllers/SettingsController.php:321
msgid "Amazon SES"
msgstr ""

#: app/Http/Controllers/SettingsController.php:324
msgid "Amazon SES Bounce Handler URL"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:41
msgid "American Samoa"
msgstr "סמואה האמריקנית"

#: app/Services/Funnel/Triggers/UserRegistrationTrigger.php:51
msgid "An Automated double-optin email will be sent for new subscribers"
msgstr "דוא\"ל אוטומטי עם כניסה אופטית יישלח למנויים חדשים"

#: app/Hooks/Handlers/CountryNames.php:45
msgid "Andorra"
msgstr "אנדורה"

#: app/Hooks/Handlers/CountryNames.php:49
msgid "Angola"
msgstr "אנגולה"

#: app/Hooks/Handlers/CountryNames.php:53
msgid "Anguilla"
msgstr "אנגווילה"

#: app/Hooks/Handlers/CountryNames.php:57
msgid "Antarctica"
msgstr "אנטארקטיקה"

#: app/Hooks/Handlers/CountryNames.php:61
msgid "Antigua and Barbuda"
msgstr "אנטיגואה וברבודה"

#: app/Services/CrmMigrator/ActiveCampaignMigrator.php:37
#: app/Services/CrmMigrator/ActiveCampaignMigrator.php:38
msgid "API Access URL"
msgstr ""

#: app/Services/CrmMigrator/MailerLiteMigrator.php:29
#: app/Services/CrmMigrator/ConvertKitMigrator.php:30
#: app/Services/CrmMigrator/MailChimpMigrator.php:32
msgid "API Key"
msgstr ""

#: app/Http/Controllers/SettingsController.php:845
msgid "API Key has been successfully created"
msgstr ""

#: app/Http/Controllers/SettingsController.php:704
msgid "API Key has been successfully deleted"
msgstr ""

#: app/Services/CrmMigrator/ConvertKitMigrator.php:37
msgid "API Secret"
msgstr ""

#: app/Services/CrmMigrator/ActiveCampaignMigrator.php:30
#: app/Services/CrmMigrator/DripMigrator.php:29
msgid "API Token"
msgstr ""

#: app/Services/CrmMigrator/Api/ConvertKit.php:61
msgid "API_Error"
msgstr ""

#: app/Services/Funnel/Actions/ApplyCompanyAction.php:22
msgid "Apply Company"
msgstr ""

#: app/Services/Funnel/Actions/ApplyCompanyAction.php:34
msgid "Apply Company to the contact"
msgstr ""

#: app/Services/Funnel/Actions/ApplyListAction.php:22
msgid "Apply List"
msgstr "החל רשימה"

#: app/Services/Funnel/Actions/ApplyListAction.php:34
msgid "Apply List to the contact"
msgstr "החל רשימת איש הקשר"

#: app/Services/Funnel/Actions/ApplyTagAction.php:22
msgid "Apply Tag"
msgstr "החל את התג"

#: app/Services/Funnel/Actions/ApplyTagAction.php:34
msgid "Apply Tag to the contact"
msgstr "החל את תג על איש הקשר"

#: app/Hooks/Handlers/CountryNames.php:65
msgid "Argentina"
msgstr "ארגנטינה"

#: app/Hooks/Handlers/CountryNames.php:69
msgid "Armenia"
msgstr "אַרְמֶנִיָה"

#: app/Hooks/Handlers/CountryNames.php:73
msgid "Aruba"
msgstr "ארובה"

#: app/Services/AutoSubscribe.php:40 app/Services/AutoSubscribe.php:128
#: app/Services/AutoSubscribe.php:286
msgid "Assign List"
msgstr "רשימת הקצאות"

#: app/Services/RoleBasedTagging.php:46
msgid "Assign or Remove tags when a contact assign to a user role."
msgstr ""

#: app/Services/AutoSubscribe.php:54 app/Services/AutoSubscribe.php:141
#: app/Services/AutoSubscribe.php:299
msgid "Assign Tags"
msgstr "הקצה תגים"

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:135
msgid ""
"Associate your FluentCRM merge tags to the appropriate Fluent Form fields by "
"selecting the appropriate form field from the list."
msgstr ""
"שייך את תגי המיזוג של FluentCRM לשדות ה- Fluent Form המתאימים על ידי בחירת "
"שדה הטופס המתאים מהרשימה."

#: app/Hooks/Handlers/CountryNames.php:77
msgid "Australia"
msgstr "אוֹסטְרַלִיָה"

#: app/Hooks/Handlers/CountryNames.php:81
msgid "Austria"
msgstr "אוֹסְטְרֵיָה"

#: app/Services/AutoSubscribe.php:211
msgid "Auto Sync User Data and Contact Data"
msgstr "סנכרון אוטומטי של נתוני משתמשים ונתוני קשר"

#: app/Services/AutoSubscribe.php:29
msgid "Automatically add your new user signups as subscriber in FluentCRM"
msgstr "הוסף באופן אוטומטי את הרשמות המשתמשים החדשות שלך כמנוי ב- FluentCRM"

#: app/Services/AutoSubscribe.php:107
msgid "Automatically add your site commenter as subscriber in FluentCRM"
msgstr "הוסף באופן אוטומטי את מגיב האתר שלך כמנוי ב- FluentCRM"

#: app/Services/AutoSubscribe.php:265
msgid ""
"Automatically fill WooCommerce Checkout field value with current contact data"
msgstr ""

#: app/Services/AutoSubscribe.php:212
msgid "Automatically Sync your WP User Data and Fluent CRM Contact Data"
msgstr ""
"סנכרן באופן אוטומטי את נתוני המשתמש שלך ב- WP ונתוני איש הקשר CRM שוטפים"

#: app/Services/Helper.php:1085
msgid "Automation Activity -"
msgstr ""

#: app/Services/PermissionManager.php:95
msgid "Automation Delete"
msgstr ""

#: app/Services/PermissionManager.php:83
msgid "Automation Read"
msgstr "קריאת אוטומציה"

#: app/Services/PermissionManager.php:88
msgid "Automation Write/Edit/Delete"
msgstr "אוטומציה כתיבה / עריכה / מחיקה"

#: app/Services/Stats.php:107 app/Hooks/CLI/Commands.php:38
#: app/Hooks/Handlers/AdminMenu.php:167 app/Hooks/Handlers/AdminMenu.php:168
#: app/Hooks/Handlers/AdminMenu.php:400 app/Hooks/Handlers/AdminMenu.php:1287
#: app/Hooks/Handlers/AdminMenu.php:1288
msgid "Automations"
msgstr "אוטומציות"

#: app/Hooks/Handlers/CountryNames.php:85
msgid "Azerbaijan"
msgstr "אזרבייג'ן"

#: app/Hooks/Handlers/CountryNames.php:89
msgid "Bahamas"
msgstr "איי בהאמה"

#: app/Hooks/Handlers/CountryNames.php:93
msgid "Bahrain"
msgstr "בחריין"

#: app/Hooks/Handlers/CountryNames.php:97
msgid "Bangladesh"
msgstr "בנגלדש"

#: app/Hooks/Handlers/CountryNames.php:101
msgid "Barbados"
msgstr "ברבדוס"

#: app/Services/CrmMigrator/ActiveCampaignMigrator.php:153
#: app/Services/CrmMigrator/DripMigrator.php:153
#: app/Services/CrmMigrator/MailChimpMigrator.php:169
msgid "Based on your selections "
msgstr ""

#: app/Services/CrmMigrator/MailerLiteMigrator.php:145
#: app/Services/CrmMigrator/ConvertKitMigrator.php:148
msgid "Based on your selections, "
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:105
msgid "Belarus"
msgstr "בלארוס"

#: app/Hooks/Handlers/CountryNames.php:113
#, fuzzy
#| msgid "Palau"
msgid "Belau"
msgstr "בלאו"

#: app/Hooks/Handlers/CountryNames.php:109
msgid "Belgium"
msgstr "בלגיה"

#: app/Hooks/Handlers/CountryNames.php:117
msgid "Belize"
msgstr "בליז"

#: app/Services/Funnel/BaseBenchMark.php:69
msgid "Benchmark type"
msgstr "סוג מידוד"

#: app/Hooks/Handlers/CountryNames.php:121
msgid "Benin"
msgstr "בנין"

#: app/Hooks/Handlers/CountryNames.php:125
msgid "Bermuda"
msgstr "ברמודה"

#: app/Hooks/Handlers/CountryNames.php:129
msgid "Bhutan"
msgstr "בהוטן"

#: app/Services/Helper.php:481
#, fuzzy
#| msgid "Back"
msgid "Black"
msgstr "חזור"

#: app/Hooks/Handlers/CountryNames.php:133
msgid "Bolivia"
msgstr "בוליביה"

#: app/Hooks/Handlers/CountryNames.php:137
msgid "Bonaire, Saint Eustatius and Saba"
msgstr "בונייר, סנט אוסטאטיוס וסאבא"

#: app/Hooks/Handlers/CountryNames.php:141
msgid "Bosnia and Herzegovina"
msgstr "בוסניה והרצגובינה"

#: app/Hooks/Handlers/CountryNames.php:145
msgid "Botswana"
msgstr "בוצואנה"

#: app/Functions/helpers.php:502 app/Functions/helpers.php:549
msgid "Bounced"
msgstr "הקפיץ"

#: app/Hooks/Handlers/CountryNames.php:149
msgid "Bouvet Island"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:153
msgid "Brazil"
msgstr "בְּרָזִיל"

#: app/Hooks/Handlers/CountryNames.php:157
msgid "British Indian Ocean Territory"
msgstr "הטריטוריה הבריטית באוקיאנוס ההודי"

#: app/Hooks/Handlers/AdminMenu.php:306
msgid "Browse all your subscribers and customers"
msgstr ""

#: app/Hooks/Handlers/AdminMenu.php:318
msgid "Browse and Manage contact business/companies"
msgstr ""

#: app/Hooks/Handlers/AdminMenu.php:326
msgid "Browse and Manage your lists associate with contact"
msgstr ""

#: app/Hooks/Handlers/AdminMenu.php:332
msgid "Browse and Manage your tags associate with contact"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:161
msgid "Brunei"
msgstr "ברוניי"

#: app/Hooks/Handlers/CountryNames.php:165
msgid "Bulgaria"
msgstr "בולגריה"

#: app/Hooks/Handlers/CountryNames.php:169
msgid "Burkina Faso"
msgstr "בורקינה פאסו"

#: app/Hooks/Handlers/CountryNames.php:173
msgid "Burundi"
msgstr "בורונדי"

#: app/Services/Helper.php:206
msgid "Business Address"
msgstr "כתובת עסקית"

#: app/Services/Helper.php:205
msgid "Business Name"
msgstr "שם עסק"

#: app/Functions/helpers.php:612
msgid "Call"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:177
msgid "Cambodia"
msgstr "קמבודיה"

#: app/Hooks/Handlers/CountryNames.php:181
msgid "Cameroon"
msgstr "קמרון"

#: app/Services/Helper.php:1067
msgid "Campaign Email -"
msgstr ""

#: app/Http/Controllers/CampaignController.php:1197
msgid "Campaign has been successfully duplicated"
msgstr "הקמפיין שוכפל בהצלחה"

#: app/Http/Controllers/CampaignController.php:1110
msgid "Campaign has been successfully marked as paused"
msgstr "הקמפיין סומן בהשהייה"

#: app/Http/Controllers/CampaignController.php:1136
msgid "Campaign has been successfully resumed"
msgstr "הקמפיין התחדש בהצלחה"

#: app/Http/Controllers/CampaignController.php:1238
msgid "Campaign has been successfully un-scheduled"
msgstr ""

#: app/Http/Controllers/CampaignController.php:1162
msgid "Campaign has been updated"
msgstr "הקמפיין עודכן"

#: app/Http/Controllers/CampaignController.php:446
msgid "Campaign status is not in draft status. Please reload the page"
msgstr ""

#: app/Services/Stats.php:25 app/Hooks/CLI/Commands.php:34
#: app/Hooks/Handlers/AdminMenu.php:118 app/Hooks/Handlers/AdminMenu.php:119
msgid "Campaigns"
msgstr "קמפיינים"

#: app/Hooks/Handlers/CountryNames.php:185
msgid "Canada"
msgstr "קנדה"

#: app/Hooks/Handlers/CountryNames.php:189
msgid "Cape Verde"
msgstr "קייפ ורדה"

#: app/Hooks/Handlers/CountryNames.php:193
msgid "Cayman Islands"
msgstr "איי קיימן"

#: app/Hooks/Handlers/CountryNames.php:197
msgid "Central African Republic"
msgstr "הרפובליקה המרכז - אפריקאית"

#: app/Hooks/Handlers/CountryNames.php:201
msgid "Chad"
msgstr "צ'אד"

#: app/Services/AutoSubscribe.php:275 app/Services/AutoSubscribe.php:277
msgid "Checkbox Label for Checkout checkbox"
msgstr ""

#: app/Services/AutoSubscribe.php:117 app/Services/AutoSubscribe.php:119
msgid "Checkbox Label for Comment Form"
msgstr "תווית תיבת סימון לטופס תגובה"

#: app/Models/CustomContactField.php:70
msgid "Checkboxes"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:205
msgid "Chile"
msgstr "צ'ילה"

#: app/Hooks/Handlers/CountryNames.php:209
msgid "China"
msgstr "סין"

#: app/Hooks/Handlers/CountryNames.php:213
msgid "Christmas Island"
msgstr "אי חג המולד"

#: app/Models/Company.php:63 app/Models/Subscriber.php:727
#: app/Services/Helper.php:169 app/Services/Helper.php:928
#: app/Hooks/Handlers/PrefFormHandler.php:50
#: app/Hooks/Handlers/PrefFormHandler.php:455
#: app/Services/CrmMigrator/BaseMigrator.php:39
#: app/Services/Funnel/FunnelHelper.php:164
msgid "City"
msgstr "עִיר"

#: app/Services/Helper.php:325
msgid "Classic Editor"
msgstr ""

#: app/Models/CampaignUrlMetric.php:130
msgid "Click Rate (%d)"
msgstr ""

#: app/Models/CampaignUrlMetric.php:141
msgid "Click To Open Rate"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:217
msgid "Cocos (Keeling) Islands"
msgstr "איי קוקוס (קילינג)"

#: app/Http/Controllers/DocsController.php:68
msgid ""
"Collect leads and build any type of forms, accept payments, connect with "
"your CRM with the Fastest Contact Form Builder Plugin for WordPress"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:221
msgid "Colombia"
msgstr "קולומביה"

#: app/Http/Controllers/CompanyController.php:288
#: app/Http/Controllers/SubscriberController.php:176
msgid "Column is not valid"
msgstr ""

#: app/Services/AutoSubscribe.php:106
msgid "Comment Form Subscription Settings"
msgstr "הגדרות מנוי לטופס תגובה"

#: app/Hooks/Handlers/CountryNames.php:225
msgid "Comoros"
msgstr "קומורו"

#: app/Hooks/Handlers/AdminMenu.php:87 app/Hooks/Handlers/AdminMenu.php:88
#: app/Hooks/Handlers/AdminMenu.php:316
msgid "Companies"
msgstr ""

#: app/Http/Controllers/CompanyController.php:358
msgid "Companies selection is required"
msgstr ""

#: app/Services/Helper.php:1125
msgid "Company"
msgstr ""

#: app/Services/Helper.php:1135
msgid "Company - Industry"
msgstr ""

#: app/Services/Helper.php:1145
msgid "Company - Type"
msgstr ""

#: app/Services/Helper.php:182
msgid "Company Address"
msgstr ""

#: app/Http/Controllers/CompanyController.php:436
msgid "Company Category has been updated for the selected companies"
msgstr ""

#: app/Models/Company.php:55
msgid "Company Description"
msgstr ""

#: app/Models/Company.php:58
msgid "Company Email"
msgstr ""

#: app/Http/Controllers/CompanyController.php:232
msgid "Company has been created successfully"
msgstr ""

#: app/Http/Controllers/CompanyController.php:345
msgid "Company has been deleted successfully"
msgstr ""

#: app/Http/Controllers/CompanyController.php:162
msgid "Company has been successfully detached"
msgstr ""

#: app/Http/Controllers/CompanyController.php:260
msgid "Company has been updated"
msgstr ""

#: app/Services/Helper.php:181
msgid "Company Industry"
msgstr ""

#: app/Models/Company.php:56
msgid "Company Logo URL"
msgstr ""

#: app/Services/Helper.php:180
msgid "Company Name"
msgstr ""

#: app/Models/Company.php:51
msgid "Company Name *"
msgstr ""

#: app/Models/Company.php:59
msgid "Company Phone"
msgstr ""

#: app/Http/Controllers/CompanyController.php:333
msgid "Company successfully updated"
msgstr ""

#: app/Http/Controllers/CompanyController.php:416
msgid "Company Type has been updated for the selected companies"
msgstr ""

#: app/Functions/helpers.php:503 app/Functions/helpers.php:550
msgid "Complained"
msgstr "התלונן"

#: app/Hooks/CLI/Commands.php:132
msgid "Completed"
msgstr "הושלם"

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:217
msgid "Conditional Logics"
msgstr "לוגיקה מותנית"

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:52
msgid "Configuration required!"
msgstr "תצורה נדרשת!"

#: app/Services/Libs/Parser/ShortcodeParser.php:252
msgid "Confirm Subscription"
msgstr ""

#: app/Hooks/Handlers/ExternalPages.php:414
msgid "Confirm your unsubscribe Request"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:229
msgid "Congo (Brazzaville)"
msgstr "קונגו (בראזוויל)"

#: app/Hooks/Handlers/CountryNames.php:233
msgid "Congo (Kinshasa)"
msgstr "קונגו (קינשאסה)"

#: app/Http/Controllers/DocsController.php:95
msgid ""
"Connect FluentCRM with ThriveCart and create, segment contact and run "
"automation on ThriveCart purchase events."
msgstr ""

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:36
msgid ""
"Connect FluentCRM with WP Fluent Forms and subscribe a contact when a form "
"is submitted."
msgstr ""
"חבר את FluentCRM עם WP Forms Fluent Forms והירשם כמנוי לאיש קשר כשמוגש טופס."

#: app/Services/Helper.php:158 app/Services/Helper.php:896
msgid "Contact"
msgstr "איש קשר"

#: app/Services/Helper.php:1047
msgid "Contact Activities"
msgstr ""

#: app/Services/Funnel/Benchmarks/ListAppliedBenchmark.php:69
msgid "contact added in all of the selected lists"
msgstr "איש קשר נוסף בכל הרשימות שנבחרו"

#: app/Services/Funnel/Benchmarks/TagAppliedBenchmark.php:69
msgid "contact added in all of the selected Tags"
msgstr "איש קשר נוסף בכל התגים שנבחרו"

#: app/Services/Funnel/Benchmarks/ListAppliedBenchmark.php:65
msgid "contact added in any of the selected Lists"
msgstr "איש קשר נוסף בכל אחת מהרשימות שנבחרו"

#: app/Services/Funnel/Benchmarks/TagAppliedBenchmark.php:65
msgid "contact added in any of the selected Tags"
msgstr "איש קשר נוסף בכל אחד מהתגיות שנבחרו"

#: app/Http/Controllers/SubscriberController.php:765
msgid "Contact Already Subscribed"
msgstr "איש הקשר כבר רשום"

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:376
msgid ""
"Contact creation has been skipped because contact already exist in the "
"database"
msgstr "יצירת המגע דלגה כיוון שכבר קיים איש קשר במסד הנתונים"

#: app/Services/Helper.php:164
msgid "Contact Email"
msgstr "דוא\"ל ליצירת קשר"

#: app/Services/Funnel/Triggers/FluentFormSubmissionTrigger.php:85
msgid "Contact Field (CRM)"
msgstr "שדה קשר (CRM)"

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:460
msgid "Contact has been created in FluentCRM. Contact ID: "
msgstr "איש קשר נוצר ב- FluentCRM. מזהה איש קשר:"

#: app/Http/Controllers/SubscriberController.php:348
msgid "contact has been successfully updated."
msgstr ""

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:511
msgid "Contact has been updated in FluentCRM. Contact ID: "
msgstr "איש הקשר עודכן ב- FluentCRM. מזהה איש קשר:"

#: app/Services/Helper.php:165
msgid "Contact ID"
msgstr "תעודת זהות ליצירת קשר"

#: app/Services/Funnel/Benchmarks/RemoveFromListBenchmark.php:69
msgid "contact removed from all of the selected lists"
msgstr "איש קשר הוסר מכל הרשימות שנבחרו"

#: app/Services/Funnel/Benchmarks/RemoveFromListBenchmark.php:65
msgid "contact removed from any of the selected Lists"
msgstr "איש קשר הוסר מכל אחת מהרשימות שנבחרו"

#: app/Services/Helper.php:997 app/Hooks/Handlers/EventTrackingHandler.php:256
msgid "Contact Segment"
msgstr ""

#: app/Services/Stats.php:87
msgid "Contact Segments"
msgstr "פלחי קשר"

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:174
msgid "Contact Tags"
msgstr "תגי קשר"

#: app/Services/PermissionManager.php:42
msgid "Contact Tags/List/Companies/Segment Create or Update"
msgstr ""

#: app/Services/PermissionManager.php:49
msgid "Contact Tags/List/Companies/Segment Delete"
msgstr ""

#: app/Http/Controllers/SubscriberController.php:1174
msgid "Contact Type has been updated for the selected subscribers"
msgstr ""

#: app/Services/Funnel/Actions/WaitTimeAction.php:68
msgid "Contact's Next Date of Birth"
msgstr ""

#: app/Services/Helper.php:1817 app/Hooks/Handlers/AdminMenu.php:76
#: app/Hooks/Handlers/AdminMenu.php:77 app/Hooks/Handlers/AdminMenu.php:298
#: app/Hooks/Handlers/AdminMenu.php:1240 app/Hooks/Handlers/AdminMenu.php:1241
msgid "Contacts"
msgstr "אנשי קשר"

#: app/Services/PermissionManager.php:21
msgid "Contacts Add/Update/Import"
msgstr ""

#: app/Services/Funnel/BaseBenchMark.php:89
msgid ""
"Contacts can enter directly to this sequence point. If you enable this then "
"any contact meet with goal will enter in this goal point."
msgstr ""

#: app/Services/PermissionManager.php:28
msgid "Contacts Delete"
msgstr ""

#: app/Services/PermissionManager.php:35
msgid "Contacts Export"
msgstr ""

#: app/Services/PermissionManager.php:16
msgid "Contacts Read"
msgstr "אנשי קשר נקראו"

#: app/Services/CrmMigrator/ConvertKitMigrator.php:31
msgid "ConvertKit API Key"
msgstr ""

#: app/Services/CrmMigrator/ConvertKitMigrator.php:38
msgid "ConvertKit API Secret"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:237
msgid "Cook Islands"
msgstr "איי קוק"

#: app/Hooks/Handlers/CountryNames.php:241
msgid "Costa Rica"
msgstr "קוסטה ריקה"

#: app/Models/Company.php:65 app/Models/Subscriber.php:730
#: app/Services/Helper.php:172 app/Services/Helper.php:943
#: app/Hooks/Handlers/PrefFormHandler.php:53
#: app/Services/CrmMigrator/BaseMigrator.php:41
msgid "Country"
msgstr "מדינה"

#: app/Services/Funnel/FunnelHelper.php:172
msgid "country"
msgstr "מדינה"

#: app/Services/Stats.php:173
msgid "Create a Campaign"
msgstr ""

#: app/Services/Stats.php:187
msgid "Create a Form"
msgstr "צור טופס"

#: app/Services/Stats.php:159
msgid "Create a Tag"
msgstr ""

#: app/Services/Stats.php:180
msgid "Create an Automation"
msgstr ""

#: app/Hooks/Handlers/AdminMenu.php:376
msgid "Create email templates to use as a starting point in your emails"
msgstr ""

#: app/Hooks/Handlers/AdminMenu.php:370
msgid "Create Multiple Emails and Send in order as a Drip Email Campaign"
msgstr ""

#: app/Services/Helper.php:984
msgid "Created At"
msgstr ""

#: app/Services/Funnel/Triggers/FluentFormSubmissionTrigger.php:30
#: app/Services/Funnel/Actions/ApplyCompanyAction.php:21
#: app/Services/Funnel/Actions/DetachTagAction.php:21
#: app/Services/Funnel/Actions/DetachListAction.php:21
#: app/Services/Funnel/Actions/ApplyTagAction.php:21
#: app/Services/Funnel/Actions/ApplyListAction.php:21
#: app/Services/Funnel/Actions/WaitTimeAction.php:22
#: app/Services/Funnel/Actions/DetachCompanyAction.php:21
msgid "CRM"
msgstr ""

#. Description of the plugin
msgid "CRM and Email Newsletter Plugin for WordPress"
msgstr "תוסף CRM  לניוזלטר ודואר אלקטרוני עבור WordPress"

#: app/Services/PermissionManager.php:11
msgid "CRM Dashboard"
msgstr "לוח מחוונים של CRM"

#: app/Hooks/Handlers/CountryNames.php:245
msgid "Croatia"
msgstr "קרואטיה"

#: app/Http/Controllers/ImporterController.php:25
msgid "CSV File"
msgstr "קובץ CSV"

#: app/Hooks/Handlers/CountryNames.php:249
msgid "Cuba"
msgstr "קובה"

#: app/Hooks/Handlers/CountryNames.php:253
msgid "Cura&ccedil;ao"
msgstr "קוראסאו"

#: app/Models/CustomCompanyField.php:29
msgid "Custom Company Data"
msgstr ""

#: app/Services/Helper.php:210
msgid "Custom Date Format (Any PHP Date Format)"
msgstr ""

#: app/Models/CustomEmailCampaign.php:26
msgid "Custom Email"
msgstr ""

#: app/Services/Funnel/Actions/SendEmailAction.php:75
msgid "Custom Email Addresses"
msgstr "כתובות דוא\"ל מותאמות אישית"

#: app/Http/Controllers/SubscriberController.php:815
msgid "Custom Email has been successfully sent"
msgstr "דוא\"ל מותאם אישית נשלח בהצלחה"

#: app/Http/Controllers/SubscriberController.php:779
msgid "Custom Email to Contact"
msgstr "דוא\"ל מותאם אישית ליצירת קשר"

#: app/Services/Helper.php:196 app/Services/Helper.php:1200
#, fuzzy
#| msgid "Custom Field Slug"
msgid "Custom Fields"
msgstr "שדה שבלול מותאם אישית"

#: app/Models/CustomContactField.php:191
msgid "Custom Profile Data"
msgstr ""

#: app/Functions/helpers.php:577
#: app/Http/Controllers/CampaignAnalyticsController.php:155
#: app/Http/Controllers/CampaignAnalyticsController.php:173
msgid "Customer"
msgstr ""

#: app/Hooks/CLI/Commands.php:144
msgid "Customer Counts"
msgstr ""

#: app/Hooks/Handlers/PurchaseHistory.php:43
#: app/Hooks/Handlers/PurchaseHistory.php:76
#: app/Hooks/Handlers/PurchaseHistory.php:478
msgid "Customer Summary"
msgstr ""

#: app/Services/Helper.php:486
msgid "Cyan bluish gray"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:257
msgid "Cyprus"
msgstr "קַפרִיסִין"

#: app/Hooks/Handlers/CountryNames.php:261
msgid "Czechia (Czech Republic)"
msgstr "צ'כיה (צ'כיה)"

#: app/Hooks/Handlers/AdminMenu.php:67 app/Hooks/Handlers/AdminMenu.php:68
#: app/Hooks/Handlers/AdminMenu.php:290 app/Hooks/Handlers/AdminMenu.php:1233
#: app/Hooks/Handlers/AdminMenu.php:1234
msgid "Dashboard"
msgstr "לוח בקרה"

#: app/Models/CustomContactField.php:75
#: app/Http/Controllers/CampaignAnalyticsController.php:106
#: app/Http/Controllers/CampaignAnalyticsController.php:157
#: app/Http/Controllers/CampaignAnalyticsController.php:175
#: app/Hooks/Handlers/PurchaseHistory.php:147
#: app/Hooks/Handlers/PurchaseHistory.php:383
msgid "Date"
msgstr "תַאֲרִיך"

#: app/Models/CustomContactField.php:80
msgid "Date and Time"
msgstr ""

#: app/Services/Helper.php:175 app/Services/Helper.php:974
#: app/Services/Helper.php:989 app/Hooks/Handlers/PrefFormHandler.php:47
#: app/Hooks/Handlers/PrefFormHandler.php:396
msgid "Date of Birth"
msgstr "תאריך לידה"

#: app/Models/Subscriber.php:734
msgid "Date of Birth (Y-m-d Format only)"
msgstr ""

#: app/Services/Helper.php:1872
msgid "Date Time"
msgstr ""

#: app/Services/Funnel/Actions/WaitTimeAction.php:114
msgid "Days"
msgstr "ימים"

#: app/Services/AutoSubscribe.php:226
msgid "Delete FluentCRM contact on WP User delete"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:265
msgid "Denmark"
msgstr "דנמרק"

#: app/Services/Helper.php:1887
msgid "Description"
msgstr ""

#: app/Http/Controllers/SettingsController.php:127
msgid "Design Template"
msgstr "תבנית עיצוב"

#: fluent-crm.php:46
msgid "Developer Docs"
msgstr ""

#: app/Http/Controllers/SettingsController.php:260
msgid ""
"Development mode is not activated. So you can not use this feature. You can "
"define \"FLUENTCRM_IS_DEV_FEATURES\" in your wp-config to enable this feature"
msgstr ""
"מצב הפיתוח אינו מופעל. אז אתה לא יכול להשתמש בתכונה זו. אתה יכול להגדיר "
"\"FLUENTCRM_IS_DEV_FEATURES\" ב- wp-config שלך כדי לאפשר תכונה זו"

#: app/Hooks/Handlers/CountryNames.php:269
msgid "Djibouti"
msgstr "ג'יבוטי"

#: app/Services/AutoSubscribe.php:325
msgid "Do not show the checkbox if current user already in subscribed state"
msgstr ""

#: app/Services/AutoSubscribe.php:167
msgid "Do not show the checkbox if current user already subscribed state"
msgstr "אל תציג את תיבת הסימון אם המשתמש הנוכחי כבר רשום"

#: fluent-crm.php:44
msgid "Docs & FAQs"
msgstr ""

#: app/Services/Stats.php:117
msgid "Documentations"
msgstr "תיעודים"

#: app/Hooks/Handlers/CountryNames.php:273
msgid "Dominica"
msgstr "דומיניקה"

#: app/Hooks/Handlers/CountryNames.php:277
msgid "Dominican Republic"
msgstr "הרפובליקה הדומיניקנית"

#: app/Services/AutoSubscribe.php:68 app/Services/AutoSubscribe.php:178
#: app/Services/AutoSubscribe.php:336
msgid "Double Opt-In"
msgstr "הצטרפות כפולה"

#: app/Http/Controllers/SettingsController.php:237
msgid "Double Opt-in settings has been updated"
msgstr "הגדרות ההצטרפות הכפולה עודכנו"

#: app/Http/Controllers/SubscriberController.php:772
msgid "Double OptIn email has been sent"
msgstr "דוא\"ל OptIn כפול נשלח"

#: app/Http/Controllers/SubscriberController.php:940
msgid "Double optin sent to selected contacts"
msgstr ""

#: app/Http/Controllers/SettingsController.php:120
msgid "Double-Optin Email Body"
msgstr "גוף דואר אופטי כפול"

#: app/Services/CrmMigrator/DripMigrator.php:37
msgid "Drip Account ID"
msgstr ""

#: app/Services/CrmMigrator/DripMigrator.php:30
msgid "Drip API Token"
msgstr ""

#: app/Services/Helper.php:209
msgid "Dynamic Date (ex: +2 days from now)"
msgstr ""

#: app/Services/Helper.php:1343
msgid "Earnings (Pro Required)"
msgstr ""

#: app/Services/Helper.php:460
msgid "Easy Digital Downloads"
msgstr "הורדות דיגיטליות קלות"

#: app/Hooks/Handlers/CountryNames.php:281
msgid "Ecuador"
msgstr "אקוודור"

#: app/Services/Helper.php:1264
#, fuzzy
#| msgid "Add"
msgid "EDD"
msgstr "הוספה"

#: app/Services/Helper.php:459
msgid "EDD Purchase History"
msgstr "היסטוריית הרכישות של EDD"

#: app/Hooks/Handlers/CountryNames.php:285
msgid "Egypt"
msgstr "מִצְרַיִם"

#: app/Hooks/Handlers/CountryNames.php:289
msgid "El Salvador"
msgstr "אל סלבדור"

#: app/Http/Controllers/SettingsController.php:363
msgid "Elastic Email"
msgstr ""

#: app/Http/Controllers/SettingsController.php:366
msgid "Elastic Email Bounce Handler Webhook URL"
msgstr ""

#: app/Models/Subscriber.php:723 app/Functions/helpers.php:613
#: app/Services/Helper.php:914 app/Hooks/Handlers/PrefFormHandler.php:45
#: app/Hooks/Handlers/PrefFormHandler.php:362
#: app/Services/Funnel/FunnelHelper.php:138
#: app/Services/Funnel/Actions/SendEmailAction.php:27
msgid "Email"
msgstr "אימייל"

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:142
msgid "Email Address"
msgstr "כתובת דוא\"ל"

#: app/Services/CrmMigrator/MailerLiteMigrator.php:22
#: app/Services/CrmMigrator/ConvertKitMigrator.php:22
msgid "Email Address and First name will be mapped automatically"
msgstr ""

#: app/Services/CrmMigrator/ActiveCampaignMigrator.php:27
#: app/Services/CrmMigrator/DripMigrator.php:26
msgid "Email and main contact fields will be mapped automatically"
msgstr ""

#: app/Http/Controllers/SettingsController.php:121
msgid "Email Body"
msgstr "גוף דוא\"ל"

#: app/Http/Controllers/SettingsController.php:218
msgid "Email Body is required"
msgstr "יש צורך בגוף דוא\"ל"

#: app/Http/Controllers/SettingsController.php:230
msgid "Email Body need to contains activation link"
msgstr "גוף הדוא\"ל צריך להכיל קישור הפעלה"

#: app/Services/Stats.php:92
msgid "Email Campaigns"
msgstr "קמפיינים בדוא\"ל"

#: app/Http/Controllers/SettingsController.php:555
#: app/Http/Controllers/SettingsController.php:564
msgid "Email clicks"
msgstr "קליקים בדוא\"ל"

#: app/views/external/confirmation.php:8
msgid "Email Confirmation"
msgstr "אישור דואר אלקטרוני"

#: app/Http/Controllers/SettingsController.php:128
msgid "Email Design Template for this double-optin email"
msgstr "תבנית לעיצוב דוא\"ל עבור דוא\"ל זה עם כניסה אופטית"

#: app/Http/Controllers/SettingsController.php:546
msgid "Email History Logs"
msgstr "יומני היסטוריית דוא\"ל"

#: app/Hooks/Handlers/ExternalPages.php:1021
msgid "Email is not valid. Please provide a valid email"
msgstr "הדוא\"ל אינו תקף. אנא שלחו אימייל תקף"

#: app/views/external/manage_subscription_request_form.php:43
#: app/views/external/unsubscribe_request_form.php:43
msgid "Email me the link"
msgstr ""

#: app/Http/Controllers/SettingsController.php:115
msgid "Email Pre Header"
msgstr ""

#: app/Services/Libs/Parser/ShortcodeParser.php:239
msgid "Email Preference"
msgstr ""

#: app/Http/Controllers/CampaignController.php:522
msgid "Email Sending will be started soon"
msgstr ""

#: app/Models/FunnelCampaign.php:91
msgid "Email Sent From Funnel"
msgstr ""

#: app/Services/Funnel/Actions/SendEmailAction.php:238
msgid "Email Sent From Funnel: "
msgstr ""

#: app/Services/Helper.php:1103
msgid "Email Sequence Activity -"
msgstr ""

#: app/Services/Stats.php:97 app/Hooks/Handlers/AdminMenu.php:136
#: app/Hooks/Handlers/AdminMenu.php:137 app/Hooks/Handlers/AdminMenu.php:368
msgid "Email Sequences"
msgstr "סדרות מיילים"

#: app/Http/Controllers/SettingsController.php:109
msgid "Email Subject"
msgstr "נושא האימייל"

#: app/Http/Controllers/SettingsController.php:217
msgid "Email Subject is required"
msgstr "נושא הדוא\"ל נדרש"

#: app/Services/Stats.php:46 app/Hooks/Handlers/AdminMenu.php:145
#: app/Hooks/Handlers/AdminMenu.php:146 app/Hooks/Handlers/AdminMenu.php:374
msgid "Email Templates"
msgstr "תבניות אימייל"

#: app/Services/PermissionManager.php:68
msgid "Email Templates Manage"
msgstr "ניהול תבניות דוא\"ל"

#: app/Services/Helper.php:110 app/Hooks/Handlers/AdminMenu.php:348
#: app/Hooks/Handlers/AdminMenu.php:1247 app/Hooks/Handlers/AdminMenu.php:1248
msgid "Emails"
msgstr "דוא\"ל"

#: app/Services/PermissionManager.php:73
msgid "Emails Delete"
msgstr ""

#: app/Services/PermissionManager.php:56
msgid "Emails Read"
msgstr "דואר אלקטרוני נקרא"

#: app/Services/Stats.php:32
msgid "Emails Sent"
msgstr "הודעות דוא\"ל נשלחו"

#: app/Services/PermissionManager.php:61
msgid "Emails Write/Send"
msgstr ""

#: app/Models/Company.php:66
msgid "Employees Number"
msgstr ""

#: app/Services/AutoSubscribe.php:313
msgid "Enable auto checked status on checkout page checkbox"
msgstr ""

#: app/Services/AutoSubscribe.php:155
msgid "Enable auto checked status on Comment Form subscription"
msgstr "אפשר סטטוס שנבדק אוטומטית במינוי טופס תגובה"

#: app/Services/AutoSubscribe.php:114
msgid ""
"Enable Create new contacts in FluentCRM when a visitor add a comment in your "
"comment form"
msgstr ""
"אפשר יצירת אנשי קשר חדשים ב- FluentCRM כאשר מבקר מוסיף תגובה בטופס התגובה שלך"

#: app/Services/AutoSubscribe.php:34
msgid ""
"Enable Create new contacts in FluentCRM when users register in WordPress"
msgstr "אפשר ליצור אנשי קשר חדשים ב- FluentCRM כאשר משתמשים נרשמים בוורדפרס"

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:204
#, fuzzy
#| msgid "Enable Double Option for new contacts"
msgid "Enable Double opt-in for new contacts"
msgstr "אפשר אפשרות כפולה לאנשי קשר חדשים"

#: app/Services/AutoSubscribe.php:69 app/Services/AutoSubscribe.php:179
#: app/Services/AutoSubscribe.php:337
msgid "Enable Double-Optin Email Confirmation"
msgstr "הפעל אישור אימייל כפול אופטי"

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:183
msgid "Enable Dynamic Tag Selection"
msgstr "אפשר בחירת תגים דינמית"

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:210
msgid ""
"Enable Force Subscribe if contact is not in subscribed status (Existing "
"contact only)"
msgstr ""

#: app/Services/RoleBasedTagging.php:51
msgid "Enable Role Based Tag Mapping"
msgstr ""

#: app/Services/AutoSubscribe.php:272
msgid "Enable Subscription Checkbox to WooCommerce Checkout Page"
msgstr ""

#: app/Services/AutoSubscribe.php:219
msgid "Enable Sync between WP User Data and Fluent CRM Contact Data"
msgstr "אפשר סנכרון בין נתוני משתמש WP ונתוני קשר שוטפים של CRM"

#: app/Http/Controllers/SettingsController.php:175
msgid "Enable Tag based double optin redirect"
msgstr ""

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:265
msgid "Enable This feed"
msgstr "אפשר עדכון זה"

#: app/Services/Helper.php:1406 app/Services/Helper.php:1462
msgid "Enrollment Categories (Pro Required)"
msgstr ""

#: app/Services/Helper.php:1388 app/Services/Helper.php:1445
msgid "Enrollment Courses (Pro Required)"
msgstr ""

#: app/Services/Helper.php:1396
msgid "Enrollment Groups (Pro Required)"
msgstr ""

#: app/Services/Helper.php:1453
msgid "Enrollment Memberships (Pro Required)"
msgstr ""

#: app/Services/Helper.php:1415 app/Services/Helper.php:1471
msgid "Enrollment Tags (Pro Required)"
msgstr ""

#: app/Services/Reporting.php:138
msgid "Entrance"
msgstr "כְּנִיסָה"

#: app/Hooks/Handlers/CountryNames.php:293
msgid "Equatorial Guinea"
msgstr "גיניאה המשוונית"

#: app/Hooks/Handlers/CountryNames.php:297
msgid "Eritrea"
msgstr "אריתריאה"

#: app/Hooks/Handlers/CountryNames.php:301
msgid "Estonia"
msgstr "אסטוניה"

#: app/Hooks/Handlers/CountryNames.php:305
msgid "Ethiopia"
msgstr "אֶתִיוֹפִּיָה"

#: app/Http/Controllers/SubscriberController.php:1426
msgid "Event has been tracked"
msgstr ""

#: app/Hooks/Handlers/EventTrackingHandler.php:293
msgid "Event Key"
msgstr ""

#: app/Hooks/Handlers/EventTrackingHandler.php:308
msgid "Event Occurrence Count"
msgstr ""

#: app/Hooks/Handlers/EventTrackingHandler.php:360
msgid "Event Title"
msgstr ""

#: app/Http/Controllers/SubscriberController.php:1391
msgid "Event Tracker is not enabled"
msgstr ""

#: app/Hooks/Handlers/EventTrackingHandler.php:30
#: app/Hooks/Handlers/EventTrackingHandler.php:217
#: app/Hooks/Handlers/EventTrackingHandler.php:235
#: app/Hooks/Handlers/EventTrackingHandler.php:251
msgid "Event Tracking"
msgstr ""

#: app/Hooks/Handlers/EventTrackingHandler.php:333
msgid "Event Value"
msgstr ""

#: app/Functions/helpers.php:625
msgid "Facebook Post"
msgstr ""

#: app/Models/Company.php:68
msgid "Facebook URL"
msgstr ""

#: app/Hooks/Handlers/ExternalPages.php:99
msgid "failed"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:309
msgid "Falkland Islands"
msgstr "איי פוקלנד"

#: app/Hooks/Handlers/CountryNames.php:313
msgid "Faroe Islands"
msgstr "איי פרו"

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:117
msgid "Feed Name"
msgstr "שם העדכון"

#: app/Functions/helpers.php:623
msgid "Feedback"
msgstr ""

#: app/Http/Controllers/CustomContactFieldsController.php:35
#: app/Http/Controllers/CompanyController.php:730
msgid "Fields saved successfully!"
msgstr "שדות נשמרו בהצלחה!"

#: app/Hooks/Handlers/CountryNames.php:317
msgid "Fiji"
msgstr "פיג'י"

#: app/Hooks/Handlers/AdminMenu.php:382
msgid "Find all the emails that are being sent or scheduled by FluentCRM"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:321
msgid "Finland"
msgstr "פינלנד"

#: app/Services/Helper.php:1382 app/Services/Helper.php:1439
msgid "First Enrollment Date (Pro Required)"
msgstr ""

#: app/Models/Subscriber.php:720 app/Services/Helper.php:162
#: app/Services/Helper.php:904 app/Hooks/Handlers/PrefFormHandler.php:42
#: app/Hooks/Handlers/PrefFormHandler.php:321
#: app/views/external/manage_subscription_form.php:14
#: app/views/external/manage_subscription_form.php:16
#: app/Services/CrmMigrator/BaseMigrator.php:25
#: app/Services/Funnel/FunnelHelper.php:130
#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:148
msgid "First Name"
msgstr "שם פרטי"

#: app/Services/Helper.php:1233 app/Services/Helper.php:1287
msgid "First Order Date (Pro Required)"
msgstr ""

#: app/Http/Controllers/SetupController.php:89
#: app/Http/Controllers/DocsController.php:89
msgid "Fluent Connect"
msgstr ""

#: config/app.php:6
#, fuzzy
#| msgid "Fluent CRM"
msgid "Fluent Crm"
msgstr "מערכת דיוור"

#: app/Hooks/Handlers/Cleanup.php:192
msgid "Fluent CRM Data"
msgstr ""

#: app/Http/Controllers/SetupController.php:159
#: app/Http/Controllers/DocsController.php:62
#: app/Hooks/Handlers/FormSubmissions.php:23
msgid "Fluent Forms"
msgstr "טפסים שוטפים"

#: app/Http/Controllers/SetupController.php:57
msgid "Fluent Forms has been installed and activated"
msgstr "Fluent Forms הותקנו והופעלו"

#: app/Http/Controllers/DocsController.php:71
msgid "Fluent SMTP"
msgstr ""

#: app/Http/Controllers/SetupController.php:112
#: app/Http/Controllers/DocsController.php:80
msgid "Fluent Support"
msgstr ""

#: app/Http/Controllers/SetupController.php:120
msgid "Fluent Support plugin has been installed and activated successfully"
msgstr ""

#: app/Http/Controllers/SetupController.php:97
msgid "FluentConnect plugin has been installed and activated successfully"
msgstr ""

#: app/Hooks/Handlers/AdminMenu.php:56 app/Hooks/Handlers/AdminMenu.php:57
#: app/Services/ExternalIntegrations/BricksBuilderIntegration.php:21
#: app/views/emails/plain/Template.php:95
#: app/views/emails/classic/Template.php:93
#: app/views/emails/raw_classic/Template.php:37
#: app/views/emails/simple/Template.php:92
#: app/views/emails/web_preview/Template.php:14
#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:27
msgid "FluentCRM"
msgstr "מערכת דיוור"

#. Name of the plugin
#, fuzzy
#| msgid "FluentCRM - Marketing Automation For WordPress (BETA)"
msgid "FluentCRM - Marketing Automation For WordPress"
msgstr "FluentCRM - אוטומציה שיווקית עבור וורדפרס"

#: app/views/admin/setup_wizard.php:6
msgid "FluentCRM - Setup Wizard"
msgstr "אשף ההתקנה של מערכת דיוור"

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:354
msgid "FluentCRM API called skipped because no valid email available"
msgstr "ממשק ה- API של FluentCRM התקשר, כי אין דוא\"ל חוקי זמין"

#: app/Hooks/Handlers/Cleanup.php:168
msgid "FluentCRM Data"
msgstr ""

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:137
#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:167
msgid "FluentCRM Field"
msgstr "שדה FluentCRM"

#: app/Http/Controllers/FormsController.php:196
msgid "FluentCRM Integration Feed"
msgstr "עדכון אינטגרציה FluentCRM"

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:54
msgid ""
"FluentCRM is not configured yet! Please configure your FluentCRM api first"
msgstr ""
"FluentCRM עדיין לא מוגדר! אנא הגדר תחילה את ממשק ה- API של FluentCRM שלך"

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:124
msgid "FluentCRM List"
msgstr "רשימת FluentCRM"

#: app/Services/ExternalIntegrations/BricksBuilderIntegration.php:67
msgid "FluentCRM Lists"
msgstr ""

#: app/Services/ExternalIntegrations/FluentForm/FluentFormInit.php:53
msgid "FluentCRM Profile"
msgstr ""

#: app/Services/ExternalIntegrations/BricksBuilderIntegration.php:47
msgid "FluentCRM Tags"
msgstr ""

#: app/Services/Stats.php:131 app/Http/Controllers/SetupController.php:170
msgid "FluentSMTP"
msgstr "FluentSMTP"

#: app/Http/Controllers/SetupController.php:74
msgid "FluentSMTP plugin has been installed and activated successfully"
msgstr "תוסף FluentSMTP הותקן והופעל בהצלחה"

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:138
#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:168
#: app/Services/Funnel/Triggers/FluentFormSubmissionTrigger.php:86
msgid "Form Field"
msgstr "שדה טופס"

#: app/Http/Controllers/FormsController.php:236
msgid "Form has been created"
msgstr "נוצר טופס"

#: app/Services/Helper.php:126
msgid "Form Submissions"
msgstr "הגשת טפסים"

#: app/Hooks/Handlers/FormSubmissions.php:22
msgid "Form Submissions (Fluent Forms)"
msgstr "הגשת טפסים (Fluent Forms)"

#: app/Services/Stats.php:102 app/Hooks/Handlers/AdminMenu.php:156
#: app/Hooks/Handlers/AdminMenu.php:157 app/Hooks/Handlers/AdminMenu.php:392
#: app/Hooks/Handlers/AdminMenu.php:1280 app/Hooks/Handlers/AdminMenu.php:1281
msgid "Forms"
msgstr "טפסים"

#: app/Hooks/Handlers/CountryNames.php:325
msgid "France"
msgstr "צָרְפַת"

#: app/Hooks/Handlers/CountryNames.php:329
msgid "French Guiana"
msgstr "גיאנה הצרפתית"

#: app/Hooks/Handlers/CountryNames.php:333
msgid "French Polynesia"
msgstr "פולינזיה הצרפתית"

#: app/Hooks/Handlers/CountryNames.php:337
msgid "French Southern Territories"
msgstr "השטחים הדרומיים הצרפתיים"

#: app/Models/Subscriber.php:722 app/Services/Helper.php:160
#: app/Services/CrmMigrator/BaseMigrator.php:27
#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:156
msgid "Full Name"
msgstr "שם מלא"

#: app/Http/Controllers/CampaignAnalyticsController.php:75
#, php-format
msgctxt "full name"
msgid "%1$s %2$s"
msgstr ""

#: app/Hooks/Handlers/AdminMenu.php:813
msgid "Full Size"
msgstr ""

#: app/Http/Controllers/FunnelController.php:454
msgid "Funnel already have the same status"
msgstr ""

#: app/Models/FunnelCampaign.php:30
msgid "Funnel Campaign Holder"
msgstr ""

#: app/Http/Controllers/FunnelController.php:968
msgid "Funnel has been created from template"
msgstr ""

#: app/Http/Controllers/FunnelController.php:145
msgid "Funnel has been created. Please configure now"
msgstr "משפך נוצר. אנא הגדר כעת"

#: app/Http/Controllers/FunnelController.php:162
msgid "Funnel has been deleted"
msgstr "משפך נמחק"

#: app/Http/Controllers/FunnelController.php:614
msgid "Funnel has been successfully cloned"
msgstr "המשפך שובט בהצלחה"

#: app/Http/Controllers/FunnelController.php:627
msgid "Funnel has been successfully imported"
msgstr "משפך יובא בהצלחה"

#: app/Http/Controllers/FunnelController.php:829
msgid "Funnel status need to be published"
msgstr ""

#: app/Http/Controllers/FunnelController.php:200
msgid "Funnel Trigger has been successfully updated"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:341
msgid "Gabon"
msgstr "גבון"

#: app/Hooks/Handlers/CountryNames.php:345
msgid "Gambia"
msgstr "גמביה"

#: app/Services/Helper.php:203
msgid "General"
msgstr "כללי"

#: app/Services/Helper.php:900
msgid "General Properties"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:349
msgid "Georgia"
msgstr "ג'ורג'יה"

#: app/Hooks/Handlers/CountryNames.php:353
msgid "Germany"
msgstr "גֶרמָנִיָה"

#: app/views/external/manage_subscription_request_form.php:32
msgid "Get Email Subscription Management Link"
msgstr ""

#: app/Hooks/Handlers/AdminMenu.php:425
msgid "Get Pro"
msgstr ""

#: fluent-crm.php:45
msgid "Get Support"
msgstr ""

#: app/views/external/unsubscribe_request_form.php:32
msgid "Get Unsubscribe Link"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:357
msgid "Ghana"
msgstr "גאנה"

#: app/Hooks/Handlers/CountryNames.php:361
msgid "Gibraltar"
msgstr "גיברלטר"

#: app/Hooks/Handlers/CountryNames.php:365
msgid "Greece"
msgstr "יָוָן"

#: app/Hooks/Handlers/CountryNames.php:369
msgid "Greenland"
msgstr "גרינלנד"

#: app/Hooks/Handlers/CountryNames.php:373
msgid "Grenada"
msgstr "גרנדה"

#: app/Hooks/Handlers/CountryNames.php:377
msgid "Guadeloupe"
msgstr "גוואדלופ"

#: app/Hooks/Handlers/CountryNames.php:381
msgid "Guam"
msgstr "גואם"

#: app/Hooks/Handlers/CountryNames.php:385
msgid "Guatemala"
msgstr "גואטמלה"

#: app/Hooks/Handlers/CountryNames.php:389
msgid "Guernsey"
msgstr "גרנזי"

#: app/Hooks/Handlers/CountryNames.php:393
msgid "Guinea"
msgstr "גינאה"

#: app/Hooks/Handlers/CountryNames.php:397
msgid "Guinea-Bissau"
msgstr "גינאה ביסאו"

#: app/Hooks/Handlers/CountryNames.php:401
msgid "Guyana"
msgstr "גיאנה"

#: app/Hooks/Handlers/CountryNames.php:405
msgid "Haiti"
msgstr "האיטי"

#: app/Http/Controllers/SubscriberController.php:840
msgid "Handled could not be found."
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:409
msgid "Heard Island and McDonald Islands"
msgstr ""

#: app/Hooks/Handlers/AdminMenu.php:221 app/Hooks/Handlers/AdminMenu.php:222
#: app/Hooks/Handlers/AdminMenu.php:1315 app/Hooks/Handlers/AdminMenu.php:1316
msgid "Help"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:413
msgid "Honduras"
msgstr "הונדורס"

#: app/Hooks/Handlers/CountryNames.php:417
msgid "Hong Kong"
msgstr ""

#: app/Services/Funnel/Actions/WaitTimeAction.php:118
msgid "Hours"
msgstr "שעה (ות"

#. URI of the plugin
#. Author URI of the plugin
msgid "https://fluentcrm.com"
msgstr "https://fluentcrm.com"

#: app/Hooks/Handlers/CountryNames.php:421
msgid "Hungary"
msgstr "הונגריה"

#: app/Hooks/Handlers/ExternalPages.php:488
msgid "I never signed up for this email list"
msgstr "מעולם לא נרשמתי לרשימת הדוא\"ל הזו"

#: app/Hooks/Handlers/ExternalPages.php:487
msgid "I no longer want to receive these emails"
msgstr "אני כבר לא רוצה לקבל את המיילים האלה"

#: app/Hooks/Handlers/CountryNames.php:425
msgid "Iceland"
msgstr "אִיסלַנד"

#: app/Http/Controllers/CampaignAnalyticsController.php:103
#: app/Http/Controllers/SubscriberController.php:737
msgid "ID"
msgstr "תְעוּדַת זֶהוּת"

#: app/Services/Funnel/Triggers/UserRegistrationTrigger.php:75
msgid "If Contact Already Exist?"
msgstr "אם איש קשר כבר קיים?"

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:157
msgid ""
"If First Name & Last Name is not available full name will be used to get "
"first name and last name"
msgstr ""
"אם שם פרטי ושם משפחה אינם זמינים שם מלא ישמש לשם קבלת שם פרטי ושם משפחה"

#: app/Services/Funnel/Actions/WaitTimeAction.php:201
msgid ""
"If no value is found in the contact's custom field or past date then it will "
"wait only 1 minute by default"
msgstr ""

#: app/Services/Funnel/Actions/SendEmailAction.php:108
msgid ""
"If schedule date is past in the runtime then email will be sent immediately"
msgstr "אם תאריך לוח הזמנים עבר בזמן הריצה, הדוא\"ל יישלח מיד"

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:244
msgid ""
"If you check any of the events then this feed will only run to the selected "
"events"
msgstr ""

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:212
msgid ""
"If you enable this then contact will forcefully subscribed no matter in "
"which status that contact had"
msgstr ""

#: app/Services/Funnel/Triggers/FluentFormSubmissionTrigger.php:181
msgid ""
"If you enable this then this will run only once per customer otherwise, It "
"will delete the existing automation flow and start new"
msgstr ""
"אם תפעיל זאת, פעולה זו תפעל פעם אחת בלבד לכל לקוח אחרת, היא תמחק את זרימת "
"האוטומציה הקיימת ותתחיל חדשה"

#: app/Services/Funnel/BaseBenchMark.php:81
msgid ""
"If you select [Optional Point] it will work as an Optional Trigger otherwise,"
" it will wait for full-fill this action"
msgstr ""
"אם תבחר באפשרות [נקודה אופציונלית] זה יעבוד כמפעיל אופציונלי אחרת, הוא ימתין "
"למילוי מלא של פעולה זו"

#: app/Http/Controllers/ImporterController.php:300
#, php-format
msgid ""
"Import %s members by member groups and member types then segment by "
"associate tags. This is a pro feature. Please upgrade to activate this "
"feature"
msgstr ""

#: app/Services/Stats.php:166
msgid "Import Contacts"
msgstr ""

#: app/Http/Controllers/ImporterController.php:246
msgid ""
"Import LearnDash students by course and groups then segment by associate "
"tags. This is a pro feature. Please upgrade to activate this feature"
msgstr ""

#: app/Http/Controllers/ImporterController.php:309
msgid ""
"Import LearnPress students by course then segment by associate tags. This is "
"a pro feature. Please upgrade to activate this feature"
msgstr ""

#: app/Http/Controllers/ImporterController.php:237
msgid ""
"Import LifterLMS students by course and groups then segment by associate "
"tags. This is a pro feature. Please upgrade to activate this feature"
msgstr ""

#: app/Http/Controllers/ImporterController.php:264
msgid ""
"Import Paid Membership Pro members by membership levels then segment by "
"associate tags. This is a pro feature. Please upgrade to activate this "
"feature"
msgstr ""

#: app/Http/Controllers/ImporterController.php:282
msgid ""
"Import Restrict Content Pro members by membership levels then segment by "
"associate tags. This is a pro feature. Please upgrade to activate this "
"feature"
msgstr ""

#: app/Http/Controllers/ImporterController.php:255
msgid ""
"Import TutorLMS students by course then segment by associate tags. This is a "
"pro feature. Please upgrade to activate this feature"
msgstr ""

#: app/Http/Controllers/ImporterController.php:158
msgid "Import Users Now"
msgstr "ייבא משתמשים עכשיו"

#: app/Http/Controllers/ImporterController.php:273
msgid ""
"Import Wishlist members by membership levels then segment by associate tags. "
"This is a pro feature. Please upgrade to activate this feature"
msgstr ""

#: app/Services/CrmMigrator/MailerLiteMigrator.php:271
#: app/Services/CrmMigrator/ConvertKitMigrator.php:257
msgid "Importer is running now. "
msgstr ""

#: app/Services/Helper.php:1336
msgid "Inactive"
msgstr ""

#: app/Services/ExternalIntegrations/BricksBuilderIntegration.php:52
#: app/Services/ExternalIntegrations/BricksBuilderIntegration.php:72
msgid "includes in"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:429
msgid "India"
msgstr "הוֹדוּ"

#: app/Hooks/Handlers/CountryNames.php:433
msgid "Indonesia"
msgstr "אִינדוֹנֵזִיָה"

#: app/Models/Company.php:54
msgid "Industry"
msgstr ""

#: app/Http/Controllers/FormsController.php:251
msgid "Inline Opt-in Form"
msgstr "טופס הצטרפות מוטבע"

#: app/Http/Controllers/DocsController.php:94
msgid "Install Fluent Connect"
msgstr ""

#: app/Http/Controllers/DocsController.php:67
msgid "Install Fluent Forms"
msgstr ""

#: app/Http/Controllers/DocsController.php:76
msgid "Install Fluent SMTP"
msgstr ""

#: app/Http/Controllers/DocsController.php:85
msgid "Install Fluent Support"
msgstr ""

#: app/Http/Controllers/SetupController.php:44
msgid "Installation has been completed"
msgstr "ההתקנה הושלמה"

#: app/Http/Controllers/SubscriberController.php:866
msgid "Invalid Advanced Filters"
msgstr ""

#: app/Http/Controllers/SubscriberController.php:1115
msgid "Invalid Automation Funnel ID"
msgstr ""

#: app/Http/Controllers/FunnelController.php:528
#: app/Http/Controllers/TemplateController.php:309
msgid "invalid bulk action"
msgstr ""

#: app/Http/Controllers/SubscriberController.php:991
#: app/Http/Controllers/SubscriberController.php:1038
msgid "Invalid Company ID"
msgstr ""

#: app/Hooks/Handlers/FunnelHandler.php:332
msgid "Invalid Data"
msgstr ""

#: app/Http/Controllers/WebhookBounceController.php:67
msgid "Invalid Data or Security Code"
msgstr ""

#: app/Http/Controllers/SubscriberController.php:953
msgid "Invalid Email Sequence ID"
msgstr ""

#: app/Http/Controllers/CampaignController.php:501
msgid "Invalid schedule date"
msgstr ""

#: app/Hooks/Handlers/ExternalPages.php:770
msgid "Invalid Webhook Hash"
msgstr ""

#: app/Hooks/Handlers/ExternalPages.php:761
msgid "Invalid Webhook URL"
msgstr ""

#: app/Functions/helpers.php:620
msgid "Invoice: Paid"
msgstr ""

#: app/Functions/helpers.php:619
msgid "Invoice: Part Paid"
msgstr ""

#: app/Functions/helpers.php:621
msgid "Invoice: Refunded"
msgstr ""

#: app/Functions/helpers.php:618
msgid "Invoice: Sent"
msgstr ""

#: app/Models/Subscriber.php:731
msgid "IP Address"
msgstr "כתובת ה - IP"

#: app/Hooks/Handlers/CountryNames.php:437
msgid "Iran"
msgstr "איראן"

#: app/Hooks/Handlers/CountryNames.php:441
msgid "Iraq"
msgstr "עִירַאק"

#: app/Hooks/Handlers/CountryNames.php:445
msgid "Ireland"
msgstr "אירלנד"

#: app/Services/ExternalIntegrations/BricksBuilderIntegration.php:55
#: app/Services/ExternalIntegrations/BricksBuilderIntegration.php:75
msgid "is"
msgstr ""

#: app/Services/Helper.php:1310
msgid "Is Affiliate (Pro Required)"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:449
msgid "Isle of Man"
msgstr "האי מאן"

#: app/Hooks/Handlers/CountryNames.php:453
msgid "Israel"
msgstr "ישראל"

#: app/Hooks/Handlers/CountryNames.php:457
msgid "Italy"
msgstr "אִיטַלִיָה"

#: app/Hooks/Handlers/CountryNames.php:461
msgid "Ivory Coast"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:465
msgid "Jamaica"
msgstr "ג'מייקה"

#: app/Hooks/Handlers/CountryNames.php:469
msgid "Japan"
msgstr "יפן"

#: app/Hooks/Handlers/CountryNames.php:473
msgid "Jersey"
msgstr "ג'רזי"

#: app/Hooks/Handlers/CountryNames.php:477
msgid "Jordan"
msgstr "יַרדֵן"

#: app/Hooks/Handlers/CountryNames.php:481
msgid "Kazakhstan"
msgstr "קזחסטן"

#: app/Services/Helper.php:1875
msgid "keep blank for current time"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:485
msgid "Kenya"
msgstr "קניה"

#: app/Hooks/Handlers/CountryNames.php:489
msgid "Kiribati"
msgstr "קיריבטי"

#: app/Hooks/Handlers/CountryNames.php:497
msgid "Kosovo"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:493
msgid "Kuwait"
msgstr "כווית"

#: app/Hooks/Handlers/CountryNames.php:501
msgid "Kyrgyzstan"
msgstr "קירגיזסטן"

#: app/Hooks/Handlers/CountryNames.php:505
msgid "Laos"
msgstr "לאוס"

#: app/Services/Helper.php:551 app/Hooks/Handlers/AdminMenu.php:812
msgid "Large"
msgstr ""

#: app/Services/Helper.php:557
msgid "Larger"
msgstr ""

#: app/Services/Helper.php:979
msgid "Last Activity"
msgstr ""

#: app/Services/Helper.php:1062
msgid "Last Email Clicked"
msgstr ""

#: app/Services/Helper.php:1056
msgid "Last Email Open"
msgstr ""

#: app/Services/Helper.php:1051
msgid "Last Email Sent"
msgstr ""

#: app/Services/Helper.php:1376 app/Services/Helper.php:1433
msgid "Last Enrollment Date (Pro Required)"
msgstr ""

#: app/Models/Subscriber.php:721 app/Services/Helper.php:163
#: app/Services/Helper.php:909 app/Hooks/Handlers/PrefFormHandler.php:43
#: app/Hooks/Handlers/PrefFormHandler.php:336
#: app/views/external/manage_subscription_form.php:20
#: app/views/external/manage_subscription_form.php:21
#: app/Services/CrmMigrator/BaseMigrator.php:26
#: app/Services/Funnel/FunnelHelper.php:134
#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:152
msgid "Last Name"
msgstr "שם משפחה"

#: app/Services/Helper.php:1227 app/Services/Helper.php:1281
msgid "Last Order Date (Pro Required)"
msgstr ""

#: app/Services/Helper.php:1361
msgid "Last Payout Date (Pro Required)"
msgstr ""

#: app/Services/Helper.php:211
msgid "Latest Post Title (Published)"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:509
msgid "Latvia"
msgstr "לטביה"

#: app/Functions/helpers.php:576
msgid "Lead"
msgstr ""

#: app/Services/Helper.php:1371 app/Http/Controllers/ImporterController.php:243
msgid "LearnDash"
msgstr ""

#: app/Http/Controllers/ImporterController.php:306
msgid "LearnPress"
msgstr ""

#: app/Services/Funnel/Triggers/UserRegistrationTrigger.php:86
msgid "Leave blank to run for all user roles"
msgstr "השאר ריק כדי להפעיל את כל תפקידי המשתמש"

#: app/Hooks/Handlers/CountryNames.php:513
msgid "Lebanon"
msgstr "לבנון"

#: app/Hooks/Handlers/CountryNames.php:517
msgid "Lesotho"
msgstr "לסוטו"

#: app/Hooks/Handlers/CountryNames.php:521
msgid "Liberia"
msgstr "ליבריה"

#: app/Hooks/Handlers/CountryNames.php:525
msgid "Libya"
msgstr "לוב"

#: app/Hooks/Handlers/CountryNames.php:529
msgid "Liechtenstein"
msgstr "ליכטנשטיין"

#: app/Functions/helpers.php:1030
msgid "Lifetime Value"
msgstr ""

#: app/Services/Helper.php:1428 app/Http/Controllers/ImporterController.php:234
msgid "LifterLMS"
msgstr ""

#: app/Services/Helper.php:511
msgid "Light green cyan"
msgstr ""

#: app/Models/Company.php:67
msgid "LinkedIn URL"
msgstr ""

#: app/Services/Funnel/Benchmarks/ListAppliedBenchmark.php:23
#: app/Services/Funnel/Benchmarks/ListAppliedBenchmark.php:48
msgid "List Applied"
msgstr "הרשימה הוחלה"

#: app/Services/Funnel/Benchmarks/RemoveFromListBenchmark.php:23
msgid "List Removed"
msgstr "הרשימה הוסרה"

#: app/Services/Funnel/Benchmarks/RemoveFromListBenchmark.php:48
msgid "List Removed From Contact"
msgstr "הרשימה הוסרה מאיש קשר"

#: app/Services/Helper.php:1027 app/Hooks/CLI/Commands.php:158
#: app/Hooks/CLI/Commands.php:372 app/Hooks/CLI/Commands.php:580
#: app/Hooks/Handlers/AdminMenu.php:97 app/Hooks/Handlers/AdminMenu.php:98
#: app/Hooks/Handlers/AdminMenu.php:324
#: app/Hooks/Handlers/EventTrackingHandler.php:277
msgid "Lists"
msgstr "רשימות"

#: app/Hooks/Handlers/CountryNames.php:533
msgid "Lithuania"
msgstr "ליטא"

#: app/Hooks/Handlers/AdminBar.php:77
msgid "Load More"
msgstr ""

#: app/Http/Controllers/SettingsController.php:655
msgid "Logs older than %d days have been deleted successfully"
msgstr "יומנים מעל גיל% d ימים נמחקו בהצלחה"

#: app/views/external/unsubscribe_request_form.php:33
msgid ""
"Looks like we could not determine your info. Please fill up the form and get "
"unsubscribe link via email."
msgstr ""

#: app/views/external/manage_subscription_request_form.php:33
msgid ""
"Looks like we could not determine your info. Please fill up the form and get "
"your email subscription form link via email."
msgstr ""

#: app/Hooks/Handlers/ExternalPages.php:398
msgid "Looks like you are already unsubscribed"
msgstr ""

#: app/Http/Controllers/CsvController.php:69
msgid ""
"Looks like your csv has same name header multiple times. Please fix your csv "
"first and remove any duplicate header column"
msgstr ""

#: app/Services/Helper.php:506
msgid "Luminous vivid amber"
msgstr ""

#: app/Services/Helper.php:501
msgid "Luminous vivid orange"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:537
msgid "Luxembourg"
msgstr "לוקסמבורג"

#: app/Hooks/Handlers/CountryNames.php:541
msgid "Macao"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:549
msgid "Madagascar"
msgstr "מדגסקר"

#: app/Services/CrmMigrator/MailChimpMigrator.php:33
msgid "MailChimp API Key"
msgstr ""

#: app/Services/CrmMigrator/MailerLiteMigrator.php:30
msgid "MailerLite API Key"
msgstr ""

#: app/Http/Controllers/SettingsController.php:328
msgid "Mailgun"
msgstr ""

#: app/Http/Controllers/SettingsController.php:331
msgid "Mailgun Bounce Handler Webhook URL"
msgstr ""

#: app/Hooks/Handlers/PrefFormHandler.php:56
#: app/views/external/manage_subscription_form.php:26
msgid "Mailing List Groups"
msgstr "קבוצות רשימת תפוצה"

#: app/Hooks/Handlers/CountryNames.php:553
msgid "Malawi"
msgstr "מלאווי"

#: app/Hooks/Handlers/CountryNames.php:557
msgid "Malaysia"
msgstr "מלזיה"

#: app/Hooks/Handlers/CountryNames.php:561
msgid "Maldives"
msgstr "האיים המלדיביים"

#: app/Hooks/Handlers/CountryNames.php:565
msgid "Mali"
msgstr "מאלי"

#: app/Hooks/Handlers/CountryNames.php:569
msgid "Malta"
msgstr "מלטה"

#: app/Services/PermissionManager.php:102
msgid "Manage CRM Settings"
msgstr ""

#: app/Services/PermissionManager.php:78
msgid "Manage Forms"
msgstr "נהל טפסים"

#: app/Services/Helper.php:216
msgid "Manage Subscription Hyperlink HTML"
msgstr ""

#: app/Services/Helper.php:213
msgid "Manage Subscription URL"
msgstr "נהל את כתובת האתר למנוי"

#: app/Hooks/Handlers/AdminMenu.php:338
msgid "Manage your dynamic contact segments"
msgstr ""

#: app/Services/Funnel/Triggers/FluentFormSubmissionTrigger.php:90
msgid "Map Other Data"
msgstr "מיפוי נתונים אחרים"

#: app/Services/Funnel/Triggers/FluentFormSubmissionTrigger.php:82
msgid "Map Primary Data"
msgstr "מפה נתונים ראשוניים"

#: app/Services/RoleBasedTagging.php:57
msgid "Map Role and associate tags"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:573
msgid "Marshall Islands"
msgstr "איי מרשל"

#: app/Hooks/Handlers/CountryNames.php:577
msgid "Martinique"
msgstr "מרטיניק"

#: app/Hooks/Handlers/CountryNames.php:581
msgid "Mauritania"
msgstr "מאוריטניה"

#: app/Hooks/Handlers/CountryNames.php:585
msgid "Mauritius"
msgstr "מאוריציוס"

#: app/Hooks/CLI/Commands.php:54
msgid "Max Rune Time"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:589
msgid "Mayotte"
msgstr "מיוט"

#: app/Services/Helper.php:545 app/Hooks/Handlers/AdminMenu.php:811
msgid "Medium"
msgstr ""

#: app/Functions/helpers.php:614
msgid "Meeting"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:593
msgid "Mexico"
msgstr "מקסיקו"

#: app/Hooks/Handlers/CountryNames.php:597
msgid "Micronesia"
msgstr "מיקרונזיה"

#: app/Services/CrmMigrator/ConvertKitMigrator.php:14
msgid "Migrate your ConvertKit contacts and associate to FluentCRM"
msgstr ""

#: app/Services/CrmMigrator/MailerLiteMigrator.php:14
msgid "Migrate your MailerLite contacts and associate to FluentCRM"
msgstr ""

#: app/Services/Funnel/Actions/WaitTimeAction.php:122
msgid "Minutes"
msgstr "דקות"

#: app/Hooks/Handlers/CountryNames.php:601
msgid "Moldova"
msgstr "מולדובה"

#: app/Hooks/Handlers/CountryNames.php:605
msgid "Monaco"
msgstr "מונקו"

#: app/Hooks/Handlers/CountryNames.php:609
msgid "Mongolia"
msgstr "מונגוליה"

#: app/Hooks/Handlers/CountryNames.php:613
msgid "Montenegro"
msgstr "מונטנגרו"

#: app/Hooks/Handlers/CountryNames.php:617
msgid "Montserrat"
msgstr "מונטסראט"

#: app/Hooks/Handlers/CountryNames.php:621
msgid "Morocco"
msgstr "מָרוֹקוֹ"

#: app/Hooks/Handlers/CountryNames.php:625
msgid "Mozambique"
msgstr "מוזמביק"

#: app/Models/CustomContactField.php:45
msgid "Multi Line Text"
msgstr ""

#: app/Models/CustomContactField.php:60
msgid "Multiple Select choice"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:629
msgid "Myanmar"
msgstr ""

#: app/Models/Subscriber.php:719 app/Services/Helper.php:161
#: app/Services/CrmMigrator/BaseMigrator.php:24
#: app/Services/Funnel/FunnelHelper.php:148
msgid "Name Prefix"
msgstr "קידומת שם"

#: app/Services/Helper.php:962
msgid "Name Prefix (Title)"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:633
msgid "Namibia"
msgstr "נמיביה"

#: app/Hooks/Handlers/CountryNames.php:637
msgid "Nauru"
msgstr "נאורו"

#: app/Services/Funnel/Benchmarks/RemoveFromTagBenchmark.php:69
msgid "Need all selected tags removed from the contact"
msgstr "צריך להסיר את כל התגים שנבחרו מאיש הקשר"

#: app/Hooks/Handlers/CountryNames.php:641
msgid "Nepal"
msgstr "נפאל"

#: app/Hooks/Handlers/CountryNames.php:645
msgid "Netherlands"
msgstr "הולנד"

#: app/Hooks/Handlers/CountryNames.php:649
msgid "New Caledonia"
msgstr "קלדוניה החדשה"

#: app/Services/Funnel/Triggers/FluentFormSubmissionTrigger.php:73
msgid "New Fluent Forms Submission Funnel"
msgstr "משפך הגשת טפסים שוטפים חדשים"

#: app/Services/Funnel/Triggers/FluentFormSubmissionTrigger.php:31
msgid "New Form Submission (Fluent Forms)"
msgstr "הגשת טופס חדש (טפסים שוטפים)"

#: app/Services/Funnel/Triggers/UserRegistrationTrigger.php:23
msgid "New User Sign Up"
msgstr "הרשמה למשתמש חדש"

#: app/Services/Funnel/Triggers/UserRegistrationTrigger.php:39
msgid "New User Sign Up Funnel"
msgstr "משפך הרשמה למשתמש חדש"

#: app/Hooks/Handlers/CountryNames.php:653
msgid "New Zealand"
msgstr "ניו זילנד"

#: app/Http/Controllers/ImporterController.php:157
msgid "Next [Review Data]"
msgstr "הבא [נתוני סקירה]"

#: app/Hooks/Handlers/CountryNames.php:657
msgid "Nicaragua"
msgstr "ניקרגואה"

#: app/Hooks/Handlers/CountryNames.php:661
msgid "Niger"
msgstr "ניז'ר"

#: app/Hooks/Handlers/CountryNames.php:665
msgid "Nigeria"
msgstr "ניגריה"

#: app/Hooks/Handlers/CountryNames.php:669
msgid "Niue"
msgstr "ניואה"

#: app/Services/Helper.php:1314
msgid "No"
msgstr "לא"

#: app/Hooks/Handlers/ExternalPages.php:1163
msgid "No Action found"
msgstr ""

#: app/Http/Controllers/FunnelController.php:681
msgid "No Corresponding report found"
msgstr "לא נמצא דו\"ח מקביל"

#: app/Http/Controllers/CampaignController.php:664
msgid ""
"No subscriber found to send test. Please add atleast one contact as "
"subscribed status"
msgstr ""

#: app/Http/Controllers/SubscriberController.php:971
#: app/Http/Controllers/SubscriberController.php:1009
#: app/Http/Controllers/SubscriberController.php:1056
#: app/Http/Controllers/SubscriberController.php:1133
msgid "No valid active subscribers found for this chunk"
msgstr ""

#: app/Http/Controllers/SubscriberController.php:1014
#: app/Http/Controllers/SubscriberController.php:1061
msgid "No valid active subscribers found for this company"
msgstr ""

#: app/Http/Controllers/SubscriberController.php:1138
msgid "No valid active subscribers found for this funnel"
msgstr ""

#: app/Http/Controllers/SubscriberController.php:975
msgid "No valid active subscribers found for this sequence"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:673
msgid "Norfolk Island"
msgstr "האי נורפולק"

#: app/Hooks/Handlers/CountryNames.php:681
msgid "North Korea"
msgstr "צפון קוריאה"

#: app/Hooks/Handlers/CountryNames.php:545
msgid "North Macedonia"
msgstr "צפון מקדוניה"

#: app/Hooks/Handlers/CountryNames.php:677
msgid "Northern Mariana Islands"
msgstr "איי מריאנה הצפוניים"

#: app/Hooks/Handlers/CountryNames.php:685
msgid "Norway"
msgstr "נורווגיה"

#: app/Services/ExternalIntegrations/BricksBuilderIntegration.php:53
#: app/Services/ExternalIntegrations/BricksBuilderIntegration.php:73
msgid "not includes"
msgstr ""

#: app/Functions/helpers.php:611
msgid "Note"
msgstr "פתק"

#: app/Http/Controllers/CompanyController.php:651
msgid "Note has been successfully added"
msgstr ""

#: app/Http/Controllers/SubscriberController.php:644
msgid "Note successfully added"
msgstr "ההערה נוספה בהצלחה"

#: app/Http/Controllers/CompanyController.php:709
#: app/Http/Controllers/SubscriberController.php:704
msgid "Note successfully deleted"
msgstr "ההערה נמחקה בהצלחה"

#: app/Http/Controllers/CompanyController.php:690
#: app/Http/Controllers/SubscriberController.php:685
msgid "Note successfully updated"
msgstr "ההערה עודכנה בהצלחה"

#: app/Services/Helper.php:142 app/Services/Helper.php:1822
msgid "Notes & Activities"
msgstr "הערות ופעילויות"

#: app/Models/CustomContactField.php:50
msgid "Numeric Field"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:689
msgid "Oman"
msgstr "עומאן"

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:234
msgid "On Payment Refund"
msgstr ""

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:232
msgid "On Subscription Active"
msgstr ""

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:233
msgid "On Subscription Cancel"
msgstr ""

#: app/Services/CrmMigrator/MailerLiteMigrator.php:23
msgid "Only Selected Groups will be imported from MailerLite"
msgstr ""

#: app/Services/CrmMigrator/ConvertKitMigrator.php:23
msgid "Only Selected tags will be imported from ConvertKit"
msgstr ""

#: app/Hooks/Handlers/WpQueryLogger.php:45
msgid "Oops! You are not able to see query logs."
msgstr ""

#: app/Models/CampaignUrlMetric.php:120
msgid "Open Rate (%d)"
msgstr ""

#: app/Http/Controllers/SettingsController.php:114
msgid "Optin Email Pre Header"
msgstr ""

#: app/Http/Controllers/SettingsController.php:108
msgid "Optin Email Subject"
msgstr "נושא הדוא\"ל של אופטין"

#: app/views/external/manage_subscription_form.php:39
msgid "or"
msgstr ""

#: app/Hooks/Handlers/PurchaseHistory.php:141
#: app/Hooks/Handlers/PurchaseHistory.php:377
msgid "Order"
msgstr ""

#: app/Hooks/Handlers/ExternalPages.php:491
msgid "Other (fill in reason below)"
msgstr "אחר (מלא את הסיבה למטה)"

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:164
msgid "Other Fields"
msgstr "תחומים אחרים"

#: app/Services/Helper.php:105
msgid "Overview"
msgstr "סקירה כללית"

#: app/Models/Company.php:52
msgid "Owner Email"
msgstr ""

#: app/Models/Company.php:53
msgid "Owner Name"
msgstr ""

#: app/Http/Controllers/ImporterController.php:261
msgid "Paid Membership Pro"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:693
msgid "Pakistan"
msgstr "פקיסטן"

#: app/Services/Helper.php:521
msgid "Pale cyan blue"
msgstr ""

#: app/Services/Helper.php:496
msgid "Pale pink"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:697
msgid "Palestinian Territory"
msgstr "טריטוריה פלסטינית"

#: app/Hooks/Handlers/CountryNames.php:701
msgid "Panama"
msgstr "פנמה"

#: app/Hooks/Handlers/CountryNames.php:705
msgid "Papua New Guinea"
msgstr "פפואה גינאה החדשה"

#: app/Hooks/Handlers/CountryNames.php:709
msgid "Paraguay"
msgstr "פרגוואי"

#: app/Services/Helper.php:467
msgid "Paymattic"
msgstr ""

#: app/Services/Helper.php:466
msgid "Paymattic Purchase History"
msgstr ""

#: app/Functions/helpers.php:499 app/Functions/helpers.php:546
#: app/Services/Helper.php:1337
msgid "Pending"
msgstr "ממתין ל"

#: app/Services/Stats.php:65
msgid "Pending Emails"
msgstr ""

#: app/Http/Controllers/SettingsController.php:335
msgid "PepiPost"
msgstr ""

#: app/Http/Controllers/SettingsController.php:338
msgid "PepiPost Bounce Handler Webhook URL"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:713
msgid "Peru"
msgstr "פרו"

#: app/Hooks/Handlers/CountryNames.php:717
msgid "Philippines"
msgstr "הפיליפינים"

#: app/Models/Subscriber.php:732 app/Services/Helper.php:952
#: app/Hooks/Handlers/PrefFormHandler.php:377
#: app/Services/CrmMigrator/BaseMigrator.php:28
#: app/Services/Funnel/FunnelHelper.php:176
msgid "Phone"
msgstr "טלפון"

#: app/Services/Helper.php:173
msgid "Phone Number"
msgstr "מספר טלפון"

#: app/Hooks/Handlers/PrefFormHandler.php:46
msgid "Phone/Mobile"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:721
msgid "Pitcairn"
msgstr "פיטקארן"

#: app/Services/Helper.php:311
msgid "Plain Centered"
msgstr "ממוקד רגיל"

#: app/Services/Helper.php:318
msgid "Plain Left"
msgstr ""

#: app/Http/Controllers/ImporterController.php:148
msgid "Please check the user roles that you want to import as contact"
msgstr ""

#: app/Http/Controllers/FormsController.php:184
msgid "Please check your inbox to confirm your subscription"
msgstr "אנא בדוק את תיבת הדואר הנכנס שלך כדי לאשר את המנוי שלך"

#: app/Hooks/Handlers/WpQueryLogger.php:37
msgid ""
"Please enable query logging by calling enableQueryLog() before queries ran."
msgstr ""

#: app/Hooks/Handlers/PrefFormHandler.php:218
msgid "Please fill up all required fields"
msgstr ""

#: app/Services/Funnel/Actions/WaitTimeAction.php:135
msgid ""
"Please input date and time and this step will be executed after that time "
"(TimeZone will be as per your WordPress Date Time Zone)"
msgstr ""

#: app/Hooks/Handlers/ExternalPages.php:324
msgid "Please let us know a reason"
msgstr "אנא ציין סיבה"

#: app/Http/Controllers/SettingsController.php:367
msgid ""
"Please paste this URL into your Elastic Email's Webhook settings to enable "
"Bounce Handling with FluentCRM"
msgstr ""

#: app/Http/Controllers/SettingsController.php:332
msgid ""
"Please paste this URL into your Mailgun's Webhook settings to enable Bounce "
"Handling with FluentCRM"
msgstr ""

#: app/Http/Controllers/SettingsController.php:339
msgid ""
"Please paste this URL into your PepiPost's Webhook settings to enable Bounce "
"Handling with FluentCRM"
msgstr ""

#: app/Http/Controllers/SettingsController.php:374
msgid ""
"Please paste this URL into your Postal Server's Webhook settings to enable "
"Bounce Handling with FluentCRM. Please select only MessageBounced & "
"MessageDeliveryFailed event"
msgstr ""

#: app/Http/Controllers/SettingsController.php:346
msgid ""
"Please paste this URL into your PostMark's Webhook settings to enable Bounce "
"Handling with FluentCRM"
msgstr ""

#: app/Http/Controllers/SettingsController.php:353
msgid ""
"Please paste this URL into your SendGrid's Webhook settings to enable Bounce "
"Handling with FluentCRM"
msgstr ""

#: app/Http/Controllers/SettingsController.php:360
msgid ""
"Please paste this URL into your SparkPost's Webhook settings to enable "
"Bounce Handling with FluentCRM"
msgstr ""

#: app/Hooks/Handlers/ExternalPages.php:384
#: app/Hooks/Handlers/ExternalPages.php:438
msgid "Please provide a valid email address"
msgstr ""

#: app/Http/Controllers/SubscriberController.php:1210
msgid "Please provide bulk options"
msgstr ""

#: app/Http/Controllers/CampaignController.php:868
msgid "Please provide campaign IDs"
msgstr ""

#: app/Http/Controllers/SettingsController.php:134
msgid "Please provide details after a contact confirm double option from email"
msgstr ""

#: app/Services/Funnel/Actions/SendEmailAction.php:54
msgid "Please provide email details that you want to send"
msgstr "אנא ספק פרטי דוא\"ל שברצונך לשלוח"

#: app/Http/Controllers/FunnelController.php:476
msgid "Please provide funnel IDs"
msgstr ""

#: app/Http/Controllers/FunnelController.php:421
msgid "Please provide funnel subscriber IDs"
msgstr ""

#: app/Http/Controllers/SettingsController.php:166
msgid "Please provide redirect URL after confirmation"
msgstr ""

#: app/Http/Controllers/FunnelController.php:484
#: app/Http/Controllers/CompanyController.php:380
#: app/Http/Controllers/TemplateController.php:279
#: app/Http/Controllers/SubscriberController.php:1084
msgid "Please select status"
msgstr ""

#: app/Http/Controllers/SettingsController.php:139
msgid "Please select what will happen once a contact confirm double-optin "
msgstr ""

#: app/views/external/unsubscribe.php:64
msgid "Please specify"
msgstr "אנא פרט"

#: app/Services/Funnel/Triggers/UserRegistrationTrigger.php:76
msgid ""
"Please specify what will happen if the subscriber already exist in the "
"database"
msgstr "אנא ציין מה יקרה אם המנוי כבר קיים במסד הנתונים"

#: app/Hooks/actions.php:177
msgid "Please update FluentCRM Pro to latest version"
msgstr ""

#: app/Http/Controllers/SettingsController.php:325
msgid "Please use this bounce handler url in your Amazon SES + SNS settings"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:725
msgid "Poland"
msgstr "פּוֹלִין"

#: app/Hooks/Handlers/CountryNames.php:729
msgid "Portugal"
msgstr "פּוֹרטוּגָל"

#: app/Models/Company.php:62 app/Models/Subscriber.php:729
#: app/Services/Helper.php:171 app/Services/Helper.php:938
#: app/Services/CrmMigrator/BaseMigrator.php:38
#: app/Services/Funnel/FunnelHelper.php:160
msgid "Postal Code"
msgstr "מיקוד"

#: app/Http/Controllers/SettingsController.php:370
msgid "Postal Server"
msgstr ""

#: app/Http/Controllers/SettingsController.php:373
msgid "Postal Server Bounce Handler Webhook URL"
msgstr ""

#: app/Http/Controllers/SettingsController.php:342
msgid "PostMark"
msgstr ""

#: app/Http/Controllers/SettingsController.php:345
msgid "PostMark Bounce Handler Webhook URL"
msgstr ""

#: app/views/emails/plain/Template.php:95
#: app/views/emails/classic/Template.php:93
#: app/views/emails/raw_classic/Template.php:37
#: app/views/emails/simple/Template.php:92
#: app/views/emails/web_preview/Template.php:14
msgid "Powered By"
msgstr ""

#: app/Models/Subscriber.php:738
msgid "Primary Company"
msgstr ""

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:134
msgid "Primary Fields"
msgstr "שדות ראשוניים"

#: app/views/admin/menu_page.php:15
msgid "Pro"
msgstr "מִקצוֹעָן"

#: app/Http/Controllers/UsersController.php:79 app/Hooks/CLI/Commands.php:136
msgid "Processing"
msgstr ""

#: app/Http/Controllers/SettingsController.php:122
msgid "Provide Email Body for the double-optin"
msgstr "ספק גוף דואר אלקטרוני לאופציה כפולה"

#: app/Http/Controllers/SubscriberController.php:292
#: app/Http/Controllers/SubscriberController.php:373
msgid "Provided email already assigned to another subscriber."
msgstr "דוא\"ל שסופק כבר הוקצה למנוי אחר."

#: app/Http/Controllers/ListsController.php:199
msgid "Provided Lists have been successfully created"
msgstr "רשימות מסופקות נוצרו בהצלחה"

#: app/Hooks/Handlers/CountryNames.php:733
msgid "Puerto Rico"
msgstr "פוארטו ריקו"

#: app/Services/Helper.php:118
msgid "Purchase History"
msgstr "היסטוריית רכישות"

#: app/Hooks/Handlers/PurchaseHistory.php:489
msgid "Purchased Products"
msgstr ""

#: app/Services/Helper.php:1239 app/Services/Helper.php:1293
msgid "Purchased Products (Pro Required)"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:737
msgid "Qatar"
msgstr "קטאר"

#: app/Hooks/Handlers/AdminBar.php:75
msgid "Quick Links"
msgstr "קישורים מהירים"

#: app/Functions/helpers.php:616
msgid "Quote: Accepted"
msgstr ""

#: app/Functions/helpers.php:617
msgid "Quote: Refused"
msgstr ""

#: app/Functions/helpers.php:615
msgid "Quote: Sent"
msgstr ""

#: app/Models/CustomContactField.php:65
msgid "Radio Choice"
msgstr ""

#: app/Services/Helper.php:334
msgid "Raw HTML"
msgstr "HTML גולמי"

#: app/Http/Controllers/CampaignController.php:275
msgid "Recipient settings has been updated"
msgstr ""

#: app/Hooks/Handlers/AdminMenu.php:127 app/Hooks/Handlers/AdminMenu.php:128
#: app/Hooks/Handlers/AdminMenu.php:362
msgid "Recurring Campaigns"
msgstr ""

#: app/Http/Controllers/SettingsController.php:147
msgid "Redirect to an URL"
msgstr ""

#: app/Http/Controllers/SettingsController.php:164
#: app/Http/Controllers/SettingsController.php:165
msgid "Redirect URL"
msgstr ""

#: app/Services/Helper.php:1355
msgid "Registration Date (Pro Required)"
msgstr ""

#: app/Services/Funnel/Actions/DetachCompanyAction.php:34
msgid "Remove Contact from the Selected Company"
msgstr ""

#: app/Services/Funnel/Actions/DetachListAction.php:34
msgid "Remove Contact from the Selected Lists"
msgstr "הסר איש קשר מהרשימות שנבחרו"

#: app/Services/Funnel/Actions/DetachTagAction.php:34
msgid "Remove Contact from the Selected Tags"
msgstr "הסר איש קשר מהתגיות שנבחרו"

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:251
msgid "Remove Contact Tags"
msgstr ""

#: app/Services/Funnel/Actions/DetachCompanyAction.php:22
msgid "Remove From Company"
msgstr ""

#: app/Services/Funnel/Actions/DetachListAction.php:22
msgid "Remove From List"
msgstr "הסר מהרשימה"

#: app/Services/Funnel/Actions/DetachTagAction.php:22
msgid "Remove From Tag"
msgstr "הסר מהתג"

#: app/Services/Funnel/Actions/DetachCompanyAction.php:23
msgid "Remove this contact from the selected company"
msgstr ""

#: app/Services/Funnel/Actions/DetachListAction.php:23
msgid "Remove this contact from the selected lists"
msgstr "הסר איש קשר זה מהרשימות שנבחרו"

#: app/Services/Funnel/Actions/DetachTagAction.php:23
msgid "Remove this contact from the selected Tags"
msgstr "הסר איש קשר זה מהתגיות שנבחרו"

#: app/Hooks/Handlers/AdminMenu.php:190 app/Hooks/Handlers/AdminMenu.php:191
#: app/Hooks/Handlers/AdminMenu.php:411 app/Hooks/Handlers/AdminMenu.php:1294
#: app/Hooks/Handlers/AdminMenu.php:1295
msgid "Reports"
msgstr "דיווחים"

#: app/views/external/manage_subscription_request_form.php:13
msgid "Request Manage Subscription"
msgstr ""

#: app/views/external/unsubscribe_request_form.php:13
msgid "Request Unsubscribe"
msgstr ""

#: app/Http/Controllers/ImporterController.php:279
msgid "Restrict Content Pro"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:741
#, fuzzy
#| msgid "Réunion"
msgid "Reunion"
msgstr "איחוד"

#: app/Models/Campaign.php:658 app/Models/CampaignUrlMetric.php:164
msgid "Revenue"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:745
msgid "Romania"
msgstr "רומניה"

#: app/Services/Funnel/Benchmarks/RemoveFromTagBenchmark.php:65
msgid "Run if any selected tag removed from a contact"
msgstr "הפעל אם תג כלשהו שנבחר הוסר מאיש קשר"

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:241
msgid "Run only on events"
msgstr ""

#: app/Services/Funnel/BaseTrigger.php:56
msgid ""
"Run the automation actions even contact status is not in subscribed status"
msgstr ""

#: app/Services/Funnel/Triggers/FluentFormSubmissionTrigger.php:180
msgid ""
"Run this automation only once per contact. If unchecked then it will over-"
"write existing flow"
msgstr ""
"הפעל אוטומציה זו פעם אחת בלבד לכל איש קשר. אם לא מסומן, הוא יכתוב יתר על "
"המידה את הזרימה הקיימת"

#: app/Services/Funnel/Benchmarks/RemoveFromTagBenchmark.php:60
#: app/Services/Funnel/Benchmarks/RemoveFromListBenchmark.php:60
#: app/Services/Funnel/Benchmarks/ListAppliedBenchmark.php:60
#: app/Services/Funnel/Benchmarks/TagAppliedBenchmark.php:60
msgid "Run When"
msgstr "הפעל מתי"

#: app/Hooks/Handlers/CountryNames.php:749
msgid "Russia"
msgstr "רוּסִיָה"

#: app/Hooks/Handlers/CountryNames.php:753
msgid "Rwanda"
msgstr "רואנדה"

#: app/Hooks/Handlers/CountryNames.php:793
msgid "S&atilde;o Tom&eacute; and Pr&iacute;ncipe"
msgstr "סאו טומה ופרינסיפה"

#: app/Hooks/Handlers/CountryNames.php:757
msgid "Saint Barth&eacute;lemy"
msgstr "סנט ברתלמי"

#: app/Hooks/Handlers/CountryNames.php:761
msgid "Saint Helena"
msgstr "סנט הלנה"

#: app/Hooks/Handlers/CountryNames.php:765
msgid "Saint Kitts and Nevis"
msgstr "סנט קיטס ונוויס"

#: app/Hooks/Handlers/CountryNames.php:769
msgid "Saint Lucia"
msgstr "סנט לוסיה"

#: app/Hooks/Handlers/CountryNames.php:777
#, fuzzy
#| msgid "Saint Martin (Dutch)"
msgid "Saint Martin (Dutch part)"
msgstr "סן מרטין (חלק הולנדי)"

#: app/Hooks/Handlers/CountryNames.php:773
#, fuzzy
#| msgid "Saint Martin (French)"
msgid "Saint Martin (French part)"
msgstr "סן מרטין (חלק צרפתי)"

#: app/Hooks/Handlers/CountryNames.php:781
msgid "Saint Pierre and Miquelon"
msgstr "סן פייר ומיקלון"

#: app/Hooks/Handlers/CountryNames.php:785
msgid "Saint Vincent and the Grenadines"
msgstr "וינסנט הקדוש ו ה - גרנידיים"

#: app/Hooks/Handlers/CountryNames.php:1009
msgid "Samoa"
msgstr "סמואה"

#: app/Hooks/Handlers/CountryNames.php:789
msgid "San Marino"
msgstr "סן מרינו"

#: app/Hooks/Handlers/CountryNames.php:797
msgid "Saudi Arabia"
msgstr "ערב הסעודית"

#: app/Services/Funnel/Actions/SendEmailAction.php:104
msgid "Schedule Date and Time"
msgstr "קבע תאריך ושעה"

#: app/Services/Funnel/Actions/SendEmailAction.php:90
msgid "Schedule this email to a specific date"
msgstr "קבעו דוא\"ל זה לתאריך מסוים"

#: app/Http/Controllers/SettingsController.php:491
#: app/Http/Controllers/SettingsController.php:513
msgid "Scheduled Automation Tasks"
msgstr "משימות אוטומציה מתוזמנות"

#: app/Http/Controllers/SettingsController.php:482
#: app/Http/Controllers/SettingsController.php:514
msgid "Scheduled Email Processing"
msgstr ""

#: app/Http/Controllers/SettingsController.php:512
msgid "Scheduled Email Sending"
msgstr "שליחת דוא\"ל מתוזמנת"

#: app/Http/Controllers/SettingsController.php:473
msgid "Scheduled Email Sending Tasks"
msgstr ""

#: app/Hooks/CLI/Commands.php:50
#, fuzzy
#| msgid "Schedule the emails"
msgid "Scheduled Emails"
msgstr "תזמן את המיילים"

#: app/Hooks/Handlers/AdminBar.php:72 app/Hooks/Handlers/AdminBar.php:84
msgid "Search Contacts"
msgstr "חפש אנשי קשר"

#: app/Hooks/Handlers/AdminMenu.php:336
msgid "Segments"
msgstr "פלחים"

#: app/Services/Funnel/Benchmarks/RemoveFromTagBenchmark.php:56
msgid "Select a Tag"
msgstr "בחר תג"

#: app/Services/AutoSubscribe.php:44 app/Services/AutoSubscribe.php:131
#: app/Services/AutoSubscribe.php:289
msgid "Select Assign List"
msgstr "בחר הקצה רשימה"

#: app/Services/AutoSubscribe.php:58 app/Services/AutoSubscribe.php:144
#: app/Services/AutoSubscribe.php:302
msgid "Select Assign Tag"
msgstr "בחר הקצה תג"

#: app/Models/CustomContactField.php:55
msgid "Select choice"
msgstr ""

#: app/Services/Funnel/Actions/ApplyCompanyAction.php:40
#: app/Services/Funnel/Actions/ApplyCompanyAction.php:41
#: app/Services/Funnel/Actions/DetachCompanyAction.php:40
#: app/Services/Funnel/Actions/DetachCompanyAction.php:41
msgid "Select Company"
msgstr ""

#: app/Services/Funnel/Actions/DetachCompanyAction.php:35
msgid "Select Company that you want to remove from targeted Contact"
msgstr ""

#: app/Services/Funnel/Triggers/FluentFormSubmissionTrigger.php:93
#: app/Services/Funnel/Triggers/FluentFormSubmissionTrigger.php:95
msgid "Select Contact Property"
msgstr "בחר נכס איש קשר"

#: app/Services/Funnel/Actions/WaitTimeAction.php:199
msgid "Select Contact's Custom Field"
msgstr ""

#: app/Hooks/Handlers/PrefFormHandler.php:486
msgid "Select Country"
msgstr ""

#: app/Services/Funnel/Actions/WaitTimeAction.php:134
#, fuzzy
#| msgid "Select date and time"
msgid "Select Date & Time"
msgstr "בחר תאריך ושעה"

#: app/Services/Funnel/Actions/SendEmailAction.php:107
msgid "Select Date and Time"
msgstr "בחר תאריך ושעה"

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:125
msgid "Select FluentCRM List"
msgstr "בחר FluentCRM List"

#: app/Services/Funnel/Triggers/FluentFormSubmissionTrigger.php:94
msgid "Select Form Field"
msgstr "בחר שדה טופס"

#: app/Services/Funnel/Triggers/FluentFormSubmissionTrigger.php:96
msgid "Select Form Property"
msgstr ""

#: app/Services/Funnel/Benchmarks/RemoveFromListBenchmark.php:56
#: app/Services/Funnel/Benchmarks/ListAppliedBenchmark.php:56
#: app/Services/Funnel/Actions/DetachListAction.php:42
#: app/Services/Funnel/Actions/ApplyListAction.php:42
msgid "Select List"
msgstr "בחר רשימה"

#: app/Services/ExternalIntegrations/BricksBuilderIntegration.php:81
#: app/Services/Funnel/Benchmarks/RemoveFromListBenchmark.php:55
#: app/Services/Funnel/Benchmarks/ListAppliedBenchmark.php:55
#: app/Services/Funnel/Actions/DetachListAction.php:41
#: app/Services/Funnel/Actions/ApplyListAction.php:41
msgid "Select Lists"
msgstr "בחר רשימות"

#: app/Services/Funnel/Actions/DetachListAction.php:35
msgid "Select Lists that you want to remove from targeted Contact"
msgstr "בחר רשימות שברצונך להסיר מאיש קשר ממוקד"

#: app/Services/Funnel/Triggers/UserRegistrationTrigger.php:84
msgid "Select Roles"
msgstr "בחר תפקידים"

#: app/Services/Funnel/Triggers/FluentFormSubmissionTrigger.php:113
#: app/Services/Funnel/Triggers/UserRegistrationTrigger.php:47
msgid "Select Status"
msgstr "בחר סטטוס"

#: app/Services/Funnel/Actions/DetachTagAction.php:42
#: app/Services/Funnel/Actions/ApplyTagAction.php:43
msgid "Select Tag"
msgstr "בחר תג"

#: app/Services/ExternalIntegrations/BricksBuilderIntegration.php:61
#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:175
#: app/Services/Funnel/Benchmarks/RemoveFromTagBenchmark.php:55
#: app/Services/Funnel/Benchmarks/TagAppliedBenchmark.php:56
#: app/Services/Funnel/Benchmarks/TagAppliedBenchmark.php:57
#: app/Services/Funnel/Actions/DetachTagAction.php:41
#: app/Services/Funnel/Actions/ApplyTagAction.php:42
msgid "Select Tags"
msgstr "בחר תגים"

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:252
msgid "Select Tags (remove from contact)"
msgstr ""

#: app/Services/Funnel/Actions/DetachTagAction.php:35
msgid "Select Tags that you want to remove from targeted Contact"
msgstr "בחר תגים שברצונך להסיר מאיש קשר ממוקד"

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:126
msgid "Select the FluentCRM List you would like to add your contacts to."
msgstr "בחר את רשימת FluentCRM שאליה תרצה להוסיף את אנשי הקשר שלך."

#: app/Services/AutoSubscribe.php:132
msgid ""
"Select the list that will be assigned for comment will be made in comment "
"forms"
msgstr "בחר את הרשימה שתוקצה לתגובה שתעשה בטפסי תגובה"

#: app/Services/AutoSubscribe.php:45
msgid ""
"Select the list that will be assigned for new user registration in your site"
msgstr "בחר את הרשימה שתוקצה לרישום משתמשים חדשים באתר שלך"

#: app/Services/AutoSubscribe.php:290
msgid "Select the list that will be assigned when checkbox checked"
msgstr ""

#: app/Services/AutoSubscribe.php:145
msgid ""
"Select the tags that will be assigned for new comment will be made in "
"comment forms"
msgstr "בחר את התגיות שיוקצו לתגובה חדשה שייעשו בטפסי תגובה"

#: app/Services/AutoSubscribe.php:59
msgid ""
"Select the tags that will be assigned for new user registration in your site"
msgstr "בחר את התגים שיוקצו לרישום משתמשים חדש באתר שלך"

#: app/Services/AutoSubscribe.php:303
msgid "Select the tags that will be assigned when checkbox checked"
msgstr ""

#: app/Http/Controllers/ImporterController.php:147
msgid "Select User Roles"
msgstr "בחר תפקידים של משתמשים"

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:165
msgid ""
"Select which Fluent Form fields pair with their<br /> respective FlunentCRM "
"fields."
msgstr ""

#: app/Services/Funnel/Actions/ApplyCompanyAction.php:35
#: app/Services/Funnel/Actions/ApplyListAction.php:35
msgid "Select which list will be added to the contact"
msgstr "בחר איזו רשימה תתווסף לאיש הקשר"

#: app/Services/Funnel/Triggers/UserRegistrationTrigger.php:83
msgid "Select which roles registration will run this automation Funnel"
msgstr "בחר אילו תפקידים רישום יפעיל משפך אוטומציה זה"

#: app/Services/Funnel/Actions/ApplyTagAction.php:35
msgid "Select which tag will be added to the contact"
msgstr "בחר איזה תג יתווסף לאיש הקשר"

#: app/Services/Funnel/Triggers/FluentFormSubmissionTrigger.php:78
msgid "Select your form"
msgstr "בחר את הטופס שלך"

#: app/Http/Controllers/SubscriberController.php:1187
msgid "Selected Action is not valid"
msgstr ""

#: app/Http/Controllers/CompanyController.php:441
#: app/Http/Controllers/SubscriberController.php:1225
msgid "Selected bulk action has been successfully completed"
msgstr ""

#: app/Http/Controllers/CampaignController.php:881
msgid "Selected Campaigns has been deleted permanently"
msgstr ""

#: app/Http/Controllers/CompanyController.php:147
msgid "Selected Companies has been attached successfully"
msgstr ""

#: app/Http/Controllers/CompanyController.php:374
msgid "Selected Companies has been deleted permanently"
msgstr ""

#: app/Http/Controllers/SubscriberController.php:933
msgid "Selected Contacts has been deleted permanently"
msgstr ""

#: app/Http/Controllers/SettingsController.php:526
msgid "Selected CRON Event successfully ran"
msgstr "אירוע CRON שנבחר רץ בהצלחה"

#: app/Http/Controllers/CampaignController.php:434
msgid "Selected emails are deleted"
msgstr "הודעות האימייל שנבחרו נמחקות"

#: app/Http/Controllers/ReportingController.php:90
#: app/Http/Controllers/SubscriberController.php:579
msgid "Selected emails has been deleted"
msgstr "הודעות האימייל שנבחרו נמחקו"

#: app/Http/Controllers/FunnelController.php:522
msgid "Selected Funnels has been deleted permanently"
msgstr ""

#: app/Http/Controllers/ListsController.php:234
msgid "Selected Lists has been removed permanently"
msgstr ""

#: app/Http/Controllers/SubscriberController.php:220
msgid "Selected Subscriber has been deleted successfully"
msgstr ""

#: app/Http/Controllers/SubscriberController.php:236
msgid "Selected Subscribers has been deleted"
msgstr "מנויים שנבחרו נמחקו"

#: app/Http/Controllers/FunnelController.php:436
msgid "Selected subscribers has been removed from this automation funnels"
msgstr ""

#: app/Http/Controllers/TagsController.php:244
msgid "Selected Tags has been removed permanently"
msgstr ""

#: app/Http/Controllers/TemplateController.php:304
msgid "Selected Templates has been deleted permanently"
msgstr ""

#: app/Services/Funnel/Actions/SendEmailAction.php:29
msgid "Send a custom Email to your subscriber or custom email address"
msgstr "שלח דוא\"ל מותאם אישית למנוי שלך או לכתובת הדוא\"ל המותאמת אישית שלך"

#: app/Hooks/Handlers/AdminMenu.php:364
msgid ""
"Send automated daily or weekly emails of your dynamic data like new blog "
"posts"
msgstr ""

#: app/Services/Funnel/Actions/SendEmailAction.php:28
#: app/Services/Funnel/Actions/SendEmailAction.php:53
msgid "Send Custom Email"
msgstr "שלח דוא\"ל בהתאמה אישית"

#: app/Hooks/Handlers/AdminMenu.php:358
msgid ""
"Send Email Broadcast to your selected subscribers by tags, lists or segment"
msgstr ""

#: app/Services/Funnel/Actions/SendEmailAction.php:59
msgid "Send Email to"
msgstr "שלח דוא\"ל אל"

#: app/Hooks/CLI/Commands.php:46
#, fuzzy
#| msgid "Send Email"
msgid "Send Emails"
msgstr "שלח אימייל"

#: app/Services/Funnel/Actions/SendEmailAction.php:67
msgid "Send to Custom Email Address"
msgstr "שלח לכתובת דוא\"ל מותאמת אישית"

#: app/Services/Funnel/Actions/SendEmailAction.php:74
msgid "Send To Email Addresses (If Custom)"
msgstr "שלח לכתובות דוא\"ל (אם מותאם אישית)"

#: app/Services/Funnel/Actions/SendEmailAction.php:63
msgid "Send To the contact"
msgstr "שלח לאיש הקשר"

#: app/Http/Controllers/SettingsController.php:349
msgid "SendGrid"
msgstr ""

#: app/Http/Controllers/SettingsController.php:352
msgid "SendGrid Bounce Handler Webhook URL"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:801
msgid "Senegal"
msgstr "סנגל"

#: app/Http/Controllers/FunnelController.php:278
#: app/Hooks/Handlers/FunnelHandler.php:231
msgid "Sequence successfully updated"
msgstr "הרצף עודכן בהצלחה"

#: app/Hooks/Handlers/CountryNames.php:805
msgid "Serbia"
msgstr "סרביה"

#: app/Hooks/Handlers/AdminMenu.php:265
#, php-format
msgid "Server-Side Cron Job is not enabled %1sView Documentation%2s."
msgstr ""

#: app/Services/Funnel/Actions/SendEmailAction.php:117
msgid "Set Custom From Name and Email"
msgstr ""

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:55
msgid "Set FluentCRM"
msgstr "הגדר FluentCRM"

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:185
msgid "Set Tag"
msgstr "הגדר תג"

#: app/Services/Stats.php:112 app/Hooks/Handlers/AdminMenu.php:181
#: app/Hooks/Handlers/AdminMenu.php:182 app/Hooks/Handlers/AdminMenu.php:417
#: app/Hooks/Handlers/AdminMenu.php:1301 app/Hooks/Handlers/AdminMenu.php:1302
msgid "Settings"
msgstr "הגדרות"

#: app/Http/Controllers/SettingsController.php:460
#: app/Http/Controllers/SettingsController.php:938
msgid "Settings has been updated"
msgstr "ההגדרות עודכנו"

#: app/Http/Controllers/SettingsController.php:79
msgid "Settings Updated"
msgstr "ההגדרות עודכנו"

#: app/Hooks/Handlers/CountryNames.php:809
msgid "Seychelles"
msgstr "סיישל"

#: app/Http/Controllers/SettingsController.php:143
msgid "Show Message"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:813
msgid "Sierra Leone"
msgstr "סיירה לאון"

#: app/Services/AutoSubscribe.php:237
msgid "Sign me up for the newsletter!"
msgstr ""

#: app/Services/Helper.php:304
msgid "Simple Boxed"
msgstr "Boxed Simple"

#: app/Http/Controllers/FormsController.php:261
msgid "Simple Opt-in Form"
msgstr "טופס הצטרפות פשוט"

#: app/Hooks/Handlers/CountryNames.php:817
msgid "Singapore"
msgstr "סינגפור"

#: app/Models/CustomContactField.php:40
msgid "Single Line Text"
msgstr ""

#: app/Services/Helper.php:208
msgid "Site URL"
msgstr "כתובת אתר"

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:192
msgid "Skip if contact already exist in FluentCRM"
msgstr "דלג אם איש קשר כבר קיים ב- FluentCRM"

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:198
msgid "Skip name update if existing contact have old data (per primary field)"
msgstr ""

#: app/Services/Funnel/Actions/SendEmailAction.php:96
msgid "Skip sending email if date is overdued"
msgstr ""

#: app/Services/Funnel/FunnelHelper.php:35
msgid "Skip this automation if contact already exist"
msgstr "דלג על אוטומציה זו אם כבר קיים איש קשר"

#: app/Hooks/Handlers/CountryNames.php:821
msgid "Slovakia"
msgstr "סלובקיה"

#: app/Hooks/Handlers/CountryNames.php:825
msgid "Slovenia"
msgstr "סלובניה"

#: app/Services/Helper.php:539
msgid "Small"
msgstr ""

#: app/Hooks/Handlers/AdminMenu.php:210 app/Hooks/Handlers/AdminMenu.php:211
msgid "SMTP"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:829
msgid "Solomon Islands"
msgstr "איי שלמה"

#: app/Hooks/Handlers/CountryNames.php:833
msgid "Somalia"
msgstr "סומליה"

#: app/Http/Controllers/SettingsController.php:699
msgid "Something is wrong"
msgstr ""

#: app/Http/Controllers/SubscriberController.php:355
msgid "Sorry contact already exist"
msgstr ""

#: app/Hooks/Handlers/AdminBar.php:76
msgid "Sorry no contact found"
msgstr "מצטערים לא נמצא איש קשר"

#: app/Http/Controllers/MigratorController.php:38
#: app/Http/Controllers/MigratorController.php:67
#: app/Http/Controllers/MigratorController.php:93
#: app/Http/Controllers/MigratorController.php:125
msgid "Sorry no driver found for the selected CRM"
msgstr ""

#: app/Http/Controllers/ImporterController.php:54
#: app/Http/Controllers/ImporterController.php:78
msgid "Sorry no driver found for this import"
msgstr ""

#: app/Hooks/Handlers/ExternalPages.php:996
msgid "Sorry! No subscriber found in the database"
msgstr "מצטער! לא נמצא מנוי במסד הנתונים"

#: app/Http/Controllers/CampaignController.php:229
msgid "Sorry! No subscribers found based on your selection"
msgstr "מצטער! לא נמצאו מנויים על סמך בחירתך"

#: app/Hooks/Handlers/ExternalPages.php:392
#: app/Hooks/Handlers/ExternalPages.php:446
msgid "Sorry! We could not verify your email address"
msgstr ""

#: app/Http/Controllers/SetupController.php:65
#: app/Http/Controllers/SetupController.php:83
#: app/Http/Controllers/SetupController.php:106
msgid "Sorry! you do not have permission to install plugin"
msgstr "מצטער! אין לך הרשאה להתקין תוסף"

#: app/Hooks/Handlers/ExternalPages.php:663
msgid "Sorry! Your confirmation url is not valid"
msgstr ""

#: app/Hooks/Handlers/ExternalPages.php:508
msgid "Sorry, No email found based on your data"
msgstr "מצטערים, לא נמצא דוא\"ל על סמך הנתונים שלך"

#: app/Http/Controllers/CampaignController.php:353
#: app/Http/Controllers/CampaignController.php:381
msgid "Sorry, No subscribers found based on your filters"
msgstr "מצטערים, לא נמצאו מנויים על בסיס המסננים שלך"

#: app/Http/Controllers/SettingsController.php:882
msgid "Sorry, the provided provider does not exist"
msgstr ""

#: app/Http/Controllers/SettingsController.php:673
#: app/Http/Controllers/SettingsController.php:793
#: app/Http/Controllers/SettingsController.php:807
msgid "Sorry, the provided user does not have FluentCRM access"
msgstr ""

#: app/Http/Controllers/CompanyController.php:318
msgid "Sorry, we could not find the logo from website. Please upload manually"
msgstr ""

#: app/Http/Controllers/SettingsController.php:254
msgid "Sorry, You do not have admin permission to reset database"
msgstr "מצטערים, אין לך הרשאת מנהל לאיפוס מסד הנתונים"

#: app/Http/Controllers/SettingsController.php:799
msgid "Sorry, You do not have permission to create REST API"
msgstr ""

#: app/Http/Controllers/SettingsController.php:679
msgid "Sorry, You do not have permission to delete REST API"
msgstr ""

#: app/Hooks/Handlers/FunnelHandler.php:218
#: app/Hooks/Handlers/FunnelHandler.php:263
#: app/Hooks/Handlers/FunnelHandler.php:321
msgid "Sorry, You do not have permission to do this action"
msgstr "מצטערים, אין לך הרשאה לבצע פעולה זו"

#: app/Models/Subscriber.php:733 app/Services/Helper.php:970
msgid "Source"
msgstr "מָקוֹר"

#: app/Hooks/Handlers/CountryNames.php:837
msgid "South Africa"
msgstr "דרום אפריקה"

#: app/Hooks/Handlers/CountryNames.php:841
msgid "South Georgia/Sandwich Islands"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:845
msgid "South Korea"
msgstr "דרום קוריאה"

#: app/Hooks/Handlers/CountryNames.php:849
msgid "South Sudan"
msgstr "דרום סודן"

#: app/Hooks/Handlers/CountryNames.php:853
msgid "Spain"
msgstr "סְפָרַד"

#: app/Http/Controllers/SettingsController.php:356
msgid "SparkPost"
msgstr ""

#: app/Http/Controllers/SettingsController.php:359
msgid "SparkPost Bounce Handler Webhook URL"
msgstr ""

#: app/Services/Funnel/Actions/WaitTimeAction.php:132
msgid "Specify Date and Time"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:857
msgid "Sri Lanka"
msgstr "סרי לנקה"

#: app/Models/Company.php:64 app/Models/Subscriber.php:728
#: app/Services/Helper.php:170 app/Services/Helper.php:933
#: app/Hooks/Handlers/PrefFormHandler.php:51
#: app/Hooks/Handlers/PrefFormHandler.php:466
#: app/Services/CrmMigrator/BaseMigrator.php:40
#: app/Services/Funnel/FunnelHelper.php:168
msgid "State"
msgstr "מדינה"

#: app/Services/Helper.php:174 app/Services/Helper.php:1001
#: app/Http/Controllers/CampaignAnalyticsController.php:105
#: app/Http/Controllers/CampaignAnalyticsController.php:156
#: app/Http/Controllers/CampaignAnalyticsController.php:174
#: app/Hooks/CLI/Commands.php:162 app/Hooks/CLI/Commands.php:376
#: app/Hooks/CLI/Commands.php:584 app/Hooks/Handlers/PurchaseHistory.php:152
#: app/Hooks/Handlers/PurchaseHistory.php:388
#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:263
msgid "Status"
msgstr "סטָטוּס"

#: app/Services/Helper.php:1332
msgid "Status (Pro Required)"
msgstr ""

#: app/Http/Controllers/CompanyController.php:396
msgid "Status has been changed for the selected companies"
msgstr ""

#: app/Http/Controllers/FunnelController.php:501
msgid "Status has been changed for the selected funnels"
msgstr ""

#: app/Http/Controllers/SubscriberController.php:1102
msgid "Status has been changed for the selected subscribers"
msgstr ""

#: app/Http/Controllers/TemplateController.php:294
msgid "Status has been changed for the selected templates"
msgstr ""

#: app/Http/Controllers/FunnelController.php:695
#, php-format
msgid "Status has been updated to %s"
msgstr "הסטטוס עודכן ל-%s"

#: app/Http/Controllers/CampaignController.php:216
msgid "step saved"
msgstr ""

#: app/Services/AutoSubscribe.php:86
#: app/Hooks/Handlers/AutoSubscribeHandler.php:107
msgid "Subscribe to newsletter"
msgstr "הירשם לעדכונים"

#: app/Functions/helpers.php:498 app/Functions/helpers.php:545
msgid "Subscribed"
msgstr "מנויים"

#: app/Http/Controllers/FunnelController.php:646
msgid "Subscribed has been removed from this automation funnel"
msgstr "המנוי הוסר ממשפך האוטומציה הזה"

#: app/Hooks/Handlers/ExternalPages.php:696
msgid "Subscriber confirmed double opt-in from IP Address:"
msgstr ""

#: app/Hooks/Handlers/ExternalPages.php:695
msgid "Subscriber double opt-in confirmed"
msgstr ""

#: app/Http/Controllers/SubscriberController.php:107
msgid "Subscriber not found"
msgstr ""

#: app/Http/Controllers/SubscriberController.php:455
msgid "Subscriber successfully updated"
msgstr "המנוי עודכן בהצלחה"

#: app/Hooks/Handlers/ExternalPages.php:575
#, php-format
msgid "Subscriber unsubscribed from IP Address: %1s <br />Reason: %2s"
msgstr ""

#: app/Http/Controllers/SubscriberController.php:790
msgid "Subscriber's status need to be subscribed."
msgstr "על סטטוס המנוי להיות רשום כמנוי"

#: app/Http/Controllers/FunnelController.php:639
msgid "subscriber_ids parameter is required"
msgstr ""

#: app/Hooks/CLI/Commands.php:30
msgid "Subscribers"
msgstr "מנויים"

#: app/Http/Controllers/SubscriberController.php:924
msgid "Subscribers selection is required"
msgstr ""

#: app/Http/Controllers/SubscriberController.php:209
msgid "Subscribers successfully updated"
msgstr ""

#: app/Http/Controllers/FormsController.php:271
msgid "Subscription Form"
msgstr "טופס הרשמה"

#: app/Hooks/CLI/Commands.php:140
msgid "Subscription Payments"
msgstr ""

#: app/Services/Funnel/Triggers/FluentFormSubmissionTrigger.php:112
#: app/Services/Funnel/Triggers/UserRegistrationTrigger.php:46
msgid "Subscription Status"
msgstr "מצב המנוי"

#: app/Http/Controllers/FunnelController.php:671
msgid "Subscription status is required"
msgstr "סטטוס מנוי נדרש"

#: app/Hooks/Handlers/ExternalPages.php:115
#: app/Hooks/Handlers/ExternalPages.php:170
msgid "success"
msgstr ""

#: app/Http/Controllers/SubscriberController.php:335
msgid "Successfully added the subscriber."
msgstr "הוסיף בהצלחה את המנוי."

#: app/Http/Controllers/WebhookController.php:76
msgid "Successfully created the WebHook"
msgstr ""

#: app/Http/Controllers/WebhookController.php:96
msgid "Successfully deleted the webhook"
msgstr ""

#: app/Http/Controllers/ListsController.php:218
msgid "Successfully removed the list."
msgstr "הסיר את הרשימה בהצלחה."

#: app/Http/Controllers/TagsController.php:223
msgid "Successfully removed the tag."
msgstr "התגית הוסרה בהצלחה."

#: app/Http/Controllers/ListsController.php:98
#: app/Http/Controllers/ListsController.php:155
msgid "Successfully saved the list."
msgstr ""

#: app/Http/Controllers/TagsController.php:105
#: app/Http/Controllers/TagsController.php:159
msgid "Successfully saved the tag."
msgstr "שמור את התג בהצלחה."

#: app/Http/Controllers/TagsController.php:203
msgid "Successfully saved the tags."
msgstr "נשמר בהצלחה התגים."

#: app/Http/Controllers/SubscriberController.php:273
msgid "Successfully updated the "
msgstr ""

#: app/Http/Controllers/WebhookController.php:86
msgid "Successfully updated the webhook"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:861
msgid "Sudan"
msgstr "סודן"

#: fluent-crm.php:45
msgid "Support"
msgstr ""

#: app/Services/Helper.php:135
msgid "Support Tickets"
msgstr "כרטיסי תמיכה"

#: app/Hooks/Handlers/CountryNames.php:865
msgid "Suriname"
msgstr "סורינאם"

#: app/Hooks/Handlers/CountryNames.php:869
msgid "Svalbard and Jan Mayen"
msgstr "סוולבארד ויאן מאיין"

#: app/Hooks/Handlers/CountryNames.php:873
msgid "Swaziland"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:877
msgid "Sweden"
msgstr "שבדיה"

#: app/Hooks/Handlers/CountryNames.php:881
msgid "Switzerland"
msgstr "שוויץ"

#: app/Http/Controllers/FunnelController.php:854
msgid "Synced successfully"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:885
msgid "Syria"
msgstr "סוּריָה"

#: app/Services/Funnel/Benchmarks/TagAppliedBenchmark.php:23
#: app/Services/Funnel/Benchmarks/TagAppliedBenchmark.php:48
msgid "Tag Applied"
msgstr "תג הוחל"

#: app/Services/Funnel/Benchmarks/RemoveFromTagBenchmark.php:23
msgid "Tag Removed"
msgstr "התג הוסר"

#: app/Services/Funnel/Benchmarks/RemoveFromTagBenchmark.php:48
msgid "Tag Removed From Contact"
msgstr "התג הוסר מאיש קשר"

#: app/Services/Stats.php:39 app/Services/Helper.php:1019
#: app/Hooks/CLI/Commands.php:154 app/Hooks/CLI/Commands.php:368
#: app/Hooks/CLI/Commands.php:576 app/Hooks/Handlers/AdminMenu.php:106
#: app/Hooks/Handlers/AdminMenu.php:107 app/Hooks/Handlers/AdminMenu.php:330
#: app/Hooks/Handlers/EventTrackingHandler.php:269
msgid "Tags"
msgstr "תגיות"

#: app/Services/RoleBasedTagging.php:59
msgid "Tags to be added"
msgstr ""

#: app/Services/RoleBasedTagging.php:60
msgid "Tags to be removed"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:889
msgid "Taiwan"
msgstr "טייוואן"

#: app/Hooks/Handlers/CountryNames.php:893
msgid "Tajikistan"
msgstr "טג'יקיסטן"

#: app/Hooks/Handlers/CountryNames.php:897
msgid "Tanzania"
msgstr "טנזניה"

#: app/Services/RoleBasedTagging.php:58
msgid "Target User Role"
msgstr ""

#: app/Services/Funnel/Triggers/UserRegistrationTrigger.php:82
msgid "Targeted User Roles"
msgstr "תפקידי משתמש ממוקדים"

#: app/Http/Controllers/TemplateController.php:165
msgid "Template successfully created"
msgstr ""

#: app/Http/Controllers/TemplateController.php:202
msgid "Template successfully duplicated"
msgstr ""

#: app/Http/Controllers/TemplateController.php:263
msgid "Template successfully updated"
msgstr "התבנית עודכנה בהצלחה"

#: app/Http/Controllers/CampaignController.php:727
msgid "Test email successfully sent to "
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:901
msgid "Thailand"
msgstr "תאילנד"

#: app/Hooks/Handlers/AdminMenu.php:268
#, php-format
msgid "Thank you for using <a href=\"%s\">FluentCRM</a>."
msgstr ""

#: app/Services/Funnel/BaseTrigger.php:63
msgid ""
"The actions will run even the contact's status is not in subscribed status."
msgstr ""

#: app/Hooks/Handlers/CampaignGuard.php:45
msgid ""
"The campaign has been locked and not deletable due to it's current status"
msgstr ""

#: app/Hooks/Handlers/CampaignGuard.php:28
msgid ""
"The campaign has been locked and not modifiable due to it's current status"
msgstr ""

#: app/Hooks/Handlers/ExternalPages.php:489
msgid "The emails are inappropriate"
msgstr "המיילים אינם הולמים"

#: app/Hooks/Handlers/ExternalPages.php:490
msgid "The emails are spam"
msgstr "המיילים הם דואר זבל"

#: app/Http/Controllers/CsvController.php:44
msgid "The file must be a valid CSV."
msgstr "הקובץ חייב להיות CSV תקף."

#: app/Hooks/Handlers/ExternalPages.php:1029
msgid ""
"The new email has been used to another account. Please use a new email "
"address"
msgstr "האימייל החדש שימש לחשבון אחר. אנא השתמש בכתובת דוא\"ל חדשה"

#: app/Http/Controllers/SettingsController.php:519
msgid "The provided hook name is not valid"
msgstr "שם הוו שצוין אינו חוקי"

#: app/Http/Controllers/FunnelController.php:687
msgid "The status already completed state"
msgstr "המצב כבר הושלם"

#: app/Http/Controllers/TemplateController.php:320
msgid "The template has been deleted successfully."
msgstr "התבנית נמחקה בהצלחה."

#: app/Http/Controllers/DocsController.php:77
msgid ""
"The Ultimate SMTP and SES Plugin for WordPress. Connect with any SMTP, "
"SendGrid, Mailgun, SES, Sendinblue, PepiPost, Google, Microsoft and more."
msgstr ""

#: app/Http/Controllers/SubscriberController.php:945
#: app/Http/Controllers/SubscriberController.php:1107
msgid "This action requires FluentCRM Pro"
msgstr ""

#: app/Http/Controllers/FunnelController.php:835
#: app/Http/Controllers/FunnelController.php:843
msgid "This feature require latest version of FluentCRM Pro version"
msgstr ""

#: app/Services/Funnel/Triggers/FluentFormSubmissionTrigger.php:32
msgid ""
"This Funnel will be initiated when a new form submission has been submitted"
msgstr "משפך זה יופעל לאחר הגשת הגשת טופס חדש"

#: app/Services/Funnel/Triggers/FluentFormSubmissionTrigger.php:62
msgid ""
"This Funnel will be initiated when a new form submission has been submitted."
msgstr "משפך זה יופעל לאחר הגשת הגשת טופס חדש."

#: app/Services/Funnel/Triggers/UserRegistrationTrigger.php:24
#: app/Services/Funnel/Triggers/UserRegistrationTrigger.php:40
msgid ""
"This Funnel will be initiated when a new user has been registered in your "
"site"
msgstr "משפך זה יופעל כאשר משתמש חדש נרשם באתר שלך"

#: app/Http/Controllers/SettingsController.php:155
msgid "This message will be shown after a subscriber confirm subscription"
msgstr "הודעה זו תוצג לאחר אישור מנוי למנוי"

#: app/Services/Funnel/Benchmarks/ListAppliedBenchmark.php:24
#: app/Services/Funnel/Benchmarks/ListAppliedBenchmark.php:49
msgid "This will run when selected lists have been applied to a contact"
msgstr "פעולה זו תפעל כאשר רשימות נבחרות הוחלו על איש קשר"

#: app/Services/Funnel/Benchmarks/RemoveFromListBenchmark.php:24
#: app/Services/Funnel/Benchmarks/RemoveFromListBenchmark.php:49
msgid "This will run when selected lists have been removed from a contact"
msgstr "פעולה זו תפעל כאשר הרשימות שנבחרו הוסרו מאיש קשר"

#: app/Services/Funnel/Benchmarks/TagAppliedBenchmark.php:24
#: app/Services/Funnel/Benchmarks/TagAppliedBenchmark.php:49
msgid "This will run when selected Tags have been applied to a contact"
msgstr "פעולה זו תפעל כאשר התגים שנבחרו הוחלו על איש קשר"

#: app/Services/Funnel/Benchmarks/RemoveFromTagBenchmark.php:24
#: app/Services/Funnel/Benchmarks/RemoveFromTagBenchmark.php:49
msgid "This will run when selected Tags have been removed from a contact"
msgstr "פעולה זו תפעל כאשר התגים שנבחרו הוסרו מאיש קשר"

#: app/Hooks/Handlers/AdminMenu.php:810
msgid "Thumbnail"
msgstr ""

#: app/Models/Subscriber.php:724
msgid "Timezone"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:905
msgid "Timor-Leste"
msgstr "טימור-לסטה"

#: app/Services/Helper.php:1880
#: app/Http/Controllers/CampaignAnalyticsController.php:104
#: app/Hooks/Handlers/PrefFormHandler.php:44
msgid "Title"
msgstr "כותרת"

#: app/Hooks/Handlers/CountryNames.php:909
msgid "Togo"
msgstr "טוגו"

#: app/Hooks/Handlers/CountryNames.php:913
msgid "Tokelau"
msgstr "טוקלאו"

#: app/Hooks/Handlers/CountryNames.php:917
msgid "Tonga"
msgstr "טונגה"

#: app/Http/Controllers/CampaignAnalyticsController.php:107
#: app/Http/Controllers/CampaignAnalyticsController.php:158
#: app/Http/Controllers/CampaignAnalyticsController.php:176
#: app/Hooks/Handlers/PurchaseHistory.php:156
#: app/Hooks/Handlers/PurchaseHistory.php:393
msgid "Total"
msgstr ""

#: app/Services/Helper.php:1215 app/Services/Helper.php:1269
msgid "Total Order Count (Pro Required)"
msgstr ""

#: app/Services/Helper.php:1275
msgid "Total Order Value (Pro Required)"
msgstr ""

#: app/Services/Helper.php:1221
msgid "Total Order value (Pro Required)"
msgstr ""

#: app/Services/Helper.php:1326
msgid "Total Referrals (Pro Required)"
msgstr ""

#: app/Hooks/CLI/Commands.php:572
msgid "Total Students"
msgstr ""

#: app/Functions/helpers.php:622
msgid "Transaction"
msgstr ""

#: app/Functions/helpers.php:501 app/Functions/helpers.php:548
msgid "Transactional"
msgstr ""

#: app/Services/CrmMigrator/ActiveCampaignMigrator.php:14
msgid "Transfer your ActiveCampaign tags and contacts to FluentCRM"
msgstr ""

#: app/Services/CrmMigrator/DripMigrator.php:14
msgid "Transfer your Drip tags and contacts to FluentCRM"
msgstr ""

#: app/Services/CrmMigrator/MailChimpMigrator.php:18
msgid ""
"Transfer your mailchimp lists, tags and contacts from MailChimp to FluentCRM"
msgstr ""

#: app/Http/Controllers/FunnelController.php:186
msgid "Trigger name is same"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:921
msgid "Trinidad and Tobago"
msgstr "טרינידד וטובגו"

#: app/Hooks/Handlers/CountryNames.php:925
msgid "Tunisia"
msgstr "תוניסיה"

#: app/Hooks/Handlers/CountryNames.php:929
msgid "Turkey"
msgstr "טורקיה"

#: app/Hooks/Handlers/CountryNames.php:933
msgid "Turkmenistan"
msgstr "טורקמניסטן"

#: app/Hooks/Handlers/CountryNames.php:937
msgid "Turks and Caicos Islands"
msgstr "איי טורקס וקאיקוס"

#: app/Http/Controllers/ImporterController.php:252
msgid "TutorLMS"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:941
msgid "Tuvalu"
msgstr "טובאלו"

#: app/Functions/helpers.php:624
msgid "Tweet"
msgstr ""

#: app/Models/Company.php:69
msgid "Twitter URL"
msgstr ""

#: app/Models/Company.php:57 app/Services/Helper.php:1010
#: app/Services/Helper.php:1863 app/Hooks/Handlers/EventTrackingHandler.php:260
msgid "Type"
msgstr "סוּג"

#: app/Hooks/Handlers/AdminBar.php:73
msgid "Type and press enter"
msgstr "הקלד ולחץ על Enter"

#: app/Hooks/Handlers/AdminBar.php:74
msgid "Type to search contacts"
msgstr "הקלד לחיפוש אנשי קשר"

#: app/Hooks/Handlers/CountryNames.php:945
msgid "Uganda"
msgstr "אוגנדה"

#: app/Hooks/Handlers/CountryNames.php:949
msgid "Ukraine"
msgstr "אוקראינה"

#: app/Hooks/Handlers/CountryNames.php:953
msgid "United Arab Emirates"
msgstr "איחוד האמירויות הערביות"

#: app/Hooks/Handlers/CountryNames.php:957
msgid "United Kingdom (UK)"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:961
msgid "United States (US)"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:965
msgid "United States (US) Minor Outlying Islands"
msgstr ""

#: app/Services/Helper.php:1349
msgid "Unpaid Earnings (Pro Required)"
msgstr ""

#: app/Hooks/Handlers/ExternalPages.php:321
#: app/Hooks/Handlers/ExternalPages.php:325
#: app/views/external/manage_subscription_form.php:39
#: app/views/external/unsubscribe.php:19
#: app/Services/Libs/Parser/ShortcodeParser.php:226
msgid "Unsubscribe"
msgstr "בטל את הרישום"

#: app/Models/CampaignUrlMetric.php:150
msgid "Unsubscribe (%d)"
msgstr ""

#: app/Services/Helper.php:215
msgid "Unsubscribe Hyperlink HTML"
msgstr ""

#: app/Services/Helper.php:212
msgid "Unsubscribe URL"
msgstr "בטל את הרישום לכתובת האתר"

#: app/Functions/helpers.php:500 app/Functions/helpers.php:547
#: app/Hooks/Handlers/ExternalPages.php:574
msgid "Unsubscribed"
msgstr "לא מנויים"

#: app/Services/Funnel/FunnelHelper.php:31
msgid "Update if Exist"
msgstr "עדכן אם קיים"

#: app/Hooks/Handlers/PrefFormHandler.php:54
#: app/Hooks/Handlers/PrefFormHandler.php:124
msgid "Update info"
msgstr ""

#: app/views/external/manage_subscription_form.php:35
msgid "Update Profile"
msgstr "עדכן פרופיל"

#: app/views/external/manage_subscription.php:8
#: app/views/external/manage_subscription.php:27
msgid "Update your preferences"
msgstr "עדכן את ההעדפות שלך"

#: fluent-crm.php:50
msgid "Upgrade to Pro"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:969
msgid "Uruguay"
msgstr "אורוגוואי"

#: app/Services/Funnel/Actions/SendEmailAction.php:76
msgid "Use comma separated values for multiple"
msgstr "השתמש בערכים המופרדים בפסיקים למספר רב"

#: app/Services/Helper.php:166
msgid "User ID"
msgstr "תעודת זהות של משתמש"

#: app/Services/AutoSubscribe.php:28
msgid "User Signup Optin Settings"
msgstr "הגדרות הצטרפות להרשמת משתמשים"

#: app/Hooks/Handlers/CountryNames.php:973
msgid "Uzbekistan"
msgstr "אוזבקיסטן"

#: app/Http/Controllers/SettingsController.php:245
msgid "Valid"
msgstr ""

#: app/Hooks/Handlers/ExternalPages.php:792
msgid "Validation failed."
msgstr ""

#: app/Http/Controllers/CompanyController.php:294
#: app/Http/Controllers/CompanyController.php:298
#: app/Http/Controllers/SubscriberController.php:182
#: app/Http/Controllers/SubscriberController.php:186
msgid "Value is not valid"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:977
msgid "Vanuatu"
msgstr "ונואטו"

#: app/Hooks/Handlers/CountryNames.php:981
msgid "Vatican"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:985
msgid "Venezuela"
msgstr "ונצואלה"

#: app/Hooks/Handlers/ExternalPages.php:89
msgid "verify_key verification failed"
msgstr ""

#: app/Services/Stats.php:122
msgid "Video Tutorials (Free)"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:989
msgid "Vietnam"
msgstr "וייטנאם"

#: app/Http/Controllers/CampaignAnalyticsController.php:159
msgid "View"
msgstr "נוף"

#: app/Services/Stats.php:82
msgid "View Contacts"
msgstr "הצג אנשי קשר"

#: app/Hooks/Handlers/PurchaseHistory.php:373
msgid "View Customer Profile"
msgstr ""

#: fluent-crm.php:44
msgid "View FluentCRM Documentation"
msgstr ""

#: app/Services/Helper.php:214
msgid "View On Browser URL"
msgstr ""

#: app/Hooks/Handlers/PurchaseHistory.php:122
#: app/Hooks/Handlers/PurchaseHistory.php:307
#: app/Hooks/Handlers/PurchaseHistory.php:340
msgid "View Order"
msgstr ""

#: app/Http/Controllers/CampaignAnalyticsController.php:128
msgid "View Order Details"
msgstr "צפו בפרטי ההזמנה"

#: app/Hooks/Handlers/CountryNames.php:993
msgid "Virgin Islands (British)"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:997
msgid "Virgin Islands (US)"
msgstr ""

#: app/Services/Helper.php:346
msgid "Visual Builder"
msgstr ""

#: app/Services/Helper.php:526
msgid "Vivid cyan blue"
msgstr ""

#: app/Services/Helper.php:516
msgid "Vivid green cyan"
msgstr ""

#: app/Services/Helper.php:531
msgid "Vivid purple"
msgstr ""

#: app/Services/Funnel/Actions/WaitTimeAction.php:93
msgid "Wait by Custom Field"
msgstr ""

#: app/Services/Funnel/Actions/WaitTimeAction.php:81
msgid "Wait by period"
msgstr ""

#: app/Services/Funnel/Actions/WaitTimeAction.php:89
msgid "Wait by Weekday"
msgstr ""

#: app/Services/Funnel/Actions/WaitTimeAction.php:24
#: app/Services/Funnel/Actions/WaitTimeAction.php:73
msgid "Wait defined timespan before execute the next action"
msgstr "המתן פרק זמן מוגדר לפני ביצוע הפעולה הבאה"

#: app/Services/Funnel/Actions/WaitTimeAction.php:98
msgid "Wait Time"
msgstr "זמן המתנה"

#: app/Services/Funnel/Actions/WaitTimeAction.php:108
msgid "Wait Time Unit"
msgstr "יחידת זמן המתנה"

#: app/Services/Funnel/Actions/WaitTimeAction.php:85
msgid "Wait Until Date"
msgstr ""

#: app/Services/Funnel/Actions/WaitTimeAction.php:23
#: app/Services/Funnel/Actions/WaitTimeAction.php:72
msgid "Wait X Days/Hours"
msgstr "המתן X ימים / שעות"

#: app/Hooks/Handlers/CountryNames.php:1001
msgid "Wallis and Futuna"
msgstr "וואליס ופוטונה"

#: app/Hooks/Handlers/ExternalPages.php:322
msgid "We're sorry to see you go!"
msgstr ""

#: app/Hooks/Handlers/ExternalPages.php:476
msgid ""
"We've sent an email to your inbox that contains a link to email management "
"from. Please check your email address to get the link."
msgstr ""

#: app/Hooks/Handlers/ExternalPages.php:427
msgid ""
"We've sent an email to your inbox that contains a link to unsubscribe from "
"our mailing list. Please check your email address and unsubscribe."
msgstr ""

#: app/Hooks/Handlers/ExternalPages.php:748
msgid "Webhook must need to be as POST Method"
msgstr ""

#: app/Models/Company.php:70
msgid "Website URL"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:1005
msgid "Western Sahara"
msgstr "סהרה המערבית"

#: app/Services/Helper.php:491
msgid "White"
msgstr ""

#: app/Http/Controllers/ImporterController.php:270
msgid "Wishlist member"
msgstr ""

#: app/Services/Helper.php:453 app/Services/Helper.php:1210
msgid "WooCommerce"
msgstr "WooCommerce"

#: app/Services/AutoSubscribe.php:257
msgid "Woocommerce Checkout Subscription Field"
msgstr ""

#: app/Services/Helper.php:452
msgid "Woocommerce Purchase History"
msgstr "היסטוריית רכישות Woocommerce "

#: app/Http/Controllers/DocsController.php:86
msgid ""
"WordPress Helpdesk and Customer Support Ticket Plugin. Provide awesome "
"support and manage customer queries right from your WordPress dashboard."
msgstr ""

#: app/Services/Funnel/Triggers/UserRegistrationTrigger.php:22
msgid "WordPress Triggers"
msgstr ""

#: app/Http/Controllers/ImporterController.php:30
msgid "WordPress Users"
msgstr "משתמשי WordPress"

#. Author of the plugin
msgid "WP Email Newsletter Team - FluentCRM"
msgstr ""

#: app/Services/Helper.php:957
#, fuzzy
#| msgid "WP Users"
msgid "WP User ID"
msgstr "משתמשי WP"

#: app/Services/Helper.php:1035
msgid "WP User Role"
msgstr ""

#: app/Services/RoleBasedTagging.php:45
msgid "WP User Role Based Tag Mapping"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:1013
msgid "Yemen"
msgstr "תֵימָן"

#: app/Services/Helper.php:1313
msgid "Yes"
msgstr "כן"

#: app/Http/Controllers/FormsController.php:186
msgid "You are successfully subscribed to our email list"
msgstr "נרשמת בהצלחה לרשימת הדוא\"ל שלנו"

#: app/Hooks/Handlers/ExternalPages.php:288
#: app/Hooks/Handlers/ExternalPages.php:578
msgid "You are successfully unsubscribed from the email list"
msgstr "ביטלת את הרישום בהצלחה מרשימת הדוא\"ל"

#: app/Services/CrmMigrator/ActiveCampaignMigrator.php:41
msgid "You can find Account ID Settings -> Developer -> API Access"
msgstr ""

#: app/Services/CrmMigrator/DripMigrator.php:40
msgid "You can find Account ID Settings -> General Info -> Account ID"
msgstr ""

#: app/Services/CrmMigrator/ActiveCampaignMigrator.php:34
msgid "You can find your API key at ActiveCampaign Settings -> Developer"
msgstr ""

#: app/Services/CrmMigrator/ConvertKitMigrator.php:34
msgid "You can find your API key at ConvertKit "
msgstr ""

#: app/Services/CrmMigrator/DripMigrator.php:33
msgid "You can find your API key at Drip Profile -> User Info -> API Token"
msgstr ""

#: app/Services/CrmMigrator/MailChimpMigrator.php:36
msgid "You can find your API key at MailChimp Account -> Extras -> API keys"
msgstr ""

#: app/Services/CrmMigrator/MailerLiteMigrator.php:33
msgid "You can find your API key at MailerLite"
msgstr ""

#: app/Services/CrmMigrator/ConvertKitMigrator.php:41
msgid ""
"You can find your API Secret key at ConvertKit Account -> Settings -> "
"Advanced"
msgstr ""

#: app/Http/Controllers/CampaignController.php:1094
msgid ""
"You can only pause a campaign if it is on \"Working\" state, Please reload "
"this page"
msgstr "תוכל להשהות קמפיין רק אם הוא במצב \"עובד\". טען דף זה מחדש"

#: app/Http/Controllers/CampaignController.php:1121
msgid ""
"You can only resume a campaign if it is on \"paused\" state, Please reload "
"this page"
msgstr "תוכל לחדש קמפיין רק אם הוא במצב \"מושהה\". אנא טען דף זה מחדש"

#: app/Http/Controllers/CampaignController.php:1216
#: app/Http/Controllers/CampaignController.php:1222
msgid ""
"You can only un-schedule a campaign if it is on \"scheduled\" state, Please "
"reload this page"
msgstr ""

#: app/Http/Controllers/CampaignController.php:517
msgid "Your campaign email has been scheduled"
msgstr "דוא\"ל הקמפיין שלך נקבע"

#: app/Http/Controllers/SettingsController.php:116
msgid "Your double-optin email pre header"
msgstr ""

#: app/Http/Controllers/SettingsController.php:110
msgid "Your double-optin email subject"
msgstr "נושא הדואר האלקטרוני שלך עם opt-optin"

#: app/Hooks/Handlers/ExternalPages.php:323
#: app/views/external/manage_subscription_form.php:9
#: app/views/external/manage_subscription_request_form.php:38
#: app/views/external/unsubscribe_request_form.php:38
msgid "Your Email Address"
msgstr "כתובת המייל שלך"

#: app/Hooks/Handlers/ExternalPages.php:463
msgid "Your Email preferences URL"
msgstr ""

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:119
msgid "Your Feed Name"
msgstr "שם הפיד שלך"

#: app/Hooks/Handlers/PrefFormHandler.php:285
msgid "Your information has been updated"
msgstr ""

#: app/Services/Helper.php:1882
msgid "Your Note Title"
msgstr ""

#: app/Http/Controllers/MigratorController.php:55
msgid "Your provided API key is valid"
msgstr ""

#: app/Hooks/Handlers/ExternalPages.php:1074
msgid "Your provided information has been successfully updated"
msgstr "המידע שסיפקת עודכן בהצלחה"

#: app/Hooks/Handlers/CountryNames.php:1017
msgid "Zambia"
msgstr "זמביה"

#: app/Hooks/Handlers/CountryNames.php:1021
msgid "Zimbabwe"
msgstr "זימבבואה"

#: app/Hooks/Handlers/PrefFormHandler.php:52
msgid "ZIP Code"
msgstr ""

#: app/Hooks/Handlers/PrefFormHandler.php:477
msgid "Zip Code"
msgstr ""

#: app/Hooks/Handlers/CountryNames.php:29
#, fuzzy
#| msgid "Aland Islands"
msgid "Åland Islands"
msgstr "איי אלנד"
