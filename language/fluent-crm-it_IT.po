msgid ""
msgstr ""
"Project-Id-Version: FluentCRM Pro\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-19 13:57+0000\n"
"PO-Revision-Date: 2024-10-14 09:59+0000\n"
"Last-Translator: \n"
"Language-Team: Italiano\n"
"Language: it-IT\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Loco https://localise.biz/\n"
"X-Loco-Version: 2.6.11; wp-6.6.2\n"
"X-Domain: fluentcampaign-pro"

#: app/Services/TransStrings.php:1504
msgid " - Waiting for double opt-in confirmation"
msgstr " - In attesa della conferma del doppio opt-in"

#: app/Services/TransStrings.php:1503
msgid " - Waiting for double-optin confirmation"
msgstr " - In attesa della conferma del doppio opt-in"

#: app/Services/TransStrings.php:1505
msgid " - Waiting for next action"
msgstr " - In attesa della prossima azione"

#: app/Services/ExternalIntegrations/MailComplaince/Webhook.php:58
msgid " at "
msgstr " alle "

#: app/Services/CrmMigrator/ActiveCampaignMigrator.php:312
msgid " contacts have been imported so far."
msgstr " contatti sono stati importati finora."

#: app/Services/CrmMigrator/DripMigrator.php:153
#: app/Services/CrmMigrator/MailChimpMigrator.php:169
msgid " contacts will be imported"
msgstr " contatti saranno importati"

#: app/Services/CrmMigrator/MailerLiteMigrator.php:145
msgid " groups and associate contacts will be imported from MailerLite"
msgstr " gruppi e contatti associati saranno importati da MailerLite"

#: app/Services/CrmMigrator/ActiveCampaignMigrator.php:153
msgid " lists and associate contacts  will be imported"
msgstr " liste e contatti associati saranno importati"

#: app/Services/ExternalIntegrations/MailComplaince/Webhook.php:227
msgid " status was set from PostMark Webhook API. Reason: "
msgstr " lo stato è stato impostato dall'API Webhook di PostMark. Motivo: "

#: app/Services/ExternalIntegrations/MailComplaince/Webhook.php:92
#: app/Services/ExternalIntegrations/MailComplaince/Webhook.php:128
msgid " status was set from SendGrid Webhook API. Reason: "
msgstr " lo stato è stato impostato dall'API Webhook di SendGrid. Motivo: "

#: app/Services/ExternalIntegrations/MailComplaince/Webhook.php:181
msgid " status was set from Sparkpost Webhook API. Reason: "
msgstr " lo stato è stato impostato dall'API Webhook di Sparkpost. Motivo: "

#: app/Services/CrmMigrator/ConvertKitMigrator.php:148
msgid " tags and associate contacts will be imported from ConvertKit"
msgstr " tag e contatti associati saranno importati da ConvertKit"

#: app/Services/CrmMigrator/MailerLiteMigrator.php:271
#: app/Services/CrmMigrator/ConvertKitMigrator.php:257
msgid " tags have been imported so far"
msgstr " tag sono stati importati finora"

#: app/Services/ExternalIntegrations/MailComplaince/Webhook.php:58
msgid " was set by mailgun webhook api with event name: "
msgstr " è stato impostato dall'API webhook di Mailgun con nome evento: "

#: app/Http/Controllers/SettingsController.php:68
#: app/Http/Controllers/TemplateController.php:208
msgid ""
"##crm.manage_subscription_url## or ##crm.unsubscribe_url## string is "
"required for compliance. Please include unsubscription or manage "
"subscription link"
msgstr ""
"È necessario includere il collegamento ##crm.manage_subscription_url## o "
"##crm.unsubscribe_url## per la conformità. Si prega di includere il "
"collegamento per annullare l'iscrizione o gestire l'abbonamento."

#: app/Hooks/Handlers/PurchaseHistory.php:114
#, php-format
msgid "%1$s for %2$s item"
msgid_plural "%1$s for %2$s items"
msgstr[0] "%1$s per %2$s elemento"
msgstr[1] "%1$s per %2$s elementi"

#: app/Http/Controllers/SubscriberController.php:1149
msgid "%d subscribers has been attached to the selected automation funnel"
msgstr "%d iscritti sono stati collegati all'automazione selezionata"

#: app/Http/Controllers/SubscriberController.php:1028
msgid "%d subscribers has been attached to the selected company"
msgstr "%d iscritti sono stati collegati alla società selezionata"

#: app/Http/Controllers/SubscriberController.php:981
msgid "%d subscribers has been attached to the selected email sequence"
msgstr "%d iscritti sono stati collegati alla sequenza email selezionata"

#: app/Http/Controllers/SubscriberController.php:1075
msgid "%d subscribers has been detached from the selected company"
msgstr "%d iscritti sono stati scollegati dalla società selezionata"

#: app/Services/CrmMigrator/MailChimpMigrator.php:69
msgid "(Contacts count "
msgstr "(Conteggio contatti "

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:253
msgid ""
"(Optional) The selected tags will be removed from the contact (if exist)"
msgstr ""
"(Opzionale) I tag selezionati saranno rimossi dal contatto (se esistono)"

#: app/Services/TransStrings.php:1564
msgid ""
"(Please use the name and email as per your domain/SMTP settings. Email "
"mismatch settings may not deliver emails as expected)"
msgstr ""
"(Si prega di utilizzare nome e email in base alle impostazioni del "
"dominio/SMTP. Configurazioni non corrispondenti potrebbero impedire la "
"consegna dell'email come previsto)"

#: app/Services/TransStrings.php:13
msgid "(y)"
msgstr "(a)"

#: app/Services/TransStrings.php:14
msgid "+ Add"
msgstr "+ Aggiungi"

#: app/Services/TransStrings.php:15
msgid "+ Any SMTP Provider"
msgstr "+ Qualsiasi provider SMTP"

#: app/Services/TransStrings.php:16
msgid "+ New Option"
msgstr "+ Nuova Opzione"

#: app/Services/TransStrings.php:17
msgid "+ Photo"
msgstr "+ Foto"

#: app/Http/Controllers/CampaignController.php:727
msgid ", The dynamic tags may not replaced in test email"
msgstr ", I tag dinamici potrebbero non essere sostituiti nell'email di test"

#: app/Services/ExternalIntegrations/MailComplaince/Webhook.php:92
#: app/Services/ExternalIntegrations/MailComplaince/Webhook.php:128
#: app/Services/ExternalIntegrations/MailComplaince/Webhook.php:181
#: app/Services/ExternalIntegrations/MailComplaince/Webhook.php:227
msgid ". Recorded at: "
msgstr ". Registrato alle: "

#: app/Http/Controllers/FunnelController.php:532
msgid "[Copy] "
msgstr "[Copia]"

#: app/Http/Controllers/CampaignController.php:1171
#: app/Http/Controllers/TemplateController.php:180
msgid "[Duplicate] "
msgstr "[Duplicato]"

#: app/Services/Funnel/BaseBenchMark.php:78
msgid ""
"[Essential Point] Select IF this step is required for processing further "
"actions"
msgstr ""
"[Punto Essenziale] Seleziona SE questo passaggio è necessario per continuare "
"con ulteriori azioni"

#: app/Services/Funnel/BaseBenchMark.php:74
msgid "[Optional Point] This is an optional trigger point"
msgstr "[Punto Opzionale] Questo è un punto di innesco opzionale"

#: app/Services/TransStrings.php:18
#, fuzzy
#| msgid "-"
msgid "A"
msgstr "-"

#: app/Hooks/Handlers/ExternalPages.php:1069
#, php-format
msgid ""
"A confirmation email has been sent to %s. Please confirm your email address "
"to resubscribe"
msgstr ""
"Una email di conferma è stata inviata a %s. Si prega di confermare "
"l'indirizzo email per riscriversi"

#: app/Hooks/Handlers/ExternalPages.php:1048
#, php-format
msgid ""
"A confirmation email has been sent to %s. Please confirm your email address "
"to resubscribe with changed email address"
msgstr ""
"Una email di conferma è stata inviata a %s. Si prega di confermare "
"l'indirizzo email per riscriversi con l'indirizzo email modificato"

#: app/Services/TransStrings.php:768
msgid "A double opt-in email will be sent automatically"
msgstr "Una email di doppio opt-in sarà inviata automaticamente"

#: app/Services/TransStrings.php:19
msgid "A double opt-in email will be sent if the contact is new"
msgstr "Una email di doppio opt-in sarà inviata se il contatto è nuovo"

#: app/Services/TransStrings.php:20
msgid "A/B Testing Result"
msgstr "Risultato A/B Testing"

#: app/Services/TransStrings.php:1682
msgid "Abandon Carts - Reports"
msgstr "Carrelli abbandonati - Rapporti"

#: app/Services/TransStrings.php:1701
msgid "Abandoned Cart Settings"
msgstr "Impostazioni carrello abbandonato"

#: app/Services/TransStrings.php:27
msgid "About this company"
msgstr "Informazioni su questa azienda"

#: app/Services/CrmMigrator/MailerLiteMigrator.php:33
msgid "Account -> Integrations -> Developer API"
msgstr "Account -> Integrazioni -> API Sviluppatore"

#: app/Services/CrmMigrator/ConvertKitMigrator.php:34
msgid "Account -> Settings -> Advanced"
msgstr "Account -> Impostazioni -> Avanzate"

#: app/Services/CrmMigrator/DripMigrator.php:36
msgid "Account ID"
msgstr "ID Account"

#: app/Services/TransStrings.php:28
msgid "Action"
msgstr "Azione"

#: app/Services/TransStrings.php:1511
msgid "action"
msgstr "azione"

#: app/Services/TransStrings.php:29
msgid "Action Blocks"
msgstr "Blocchi azione"

#: app/Services/TransStrings.php:30
msgid "Action Type"
msgstr "Tipo di azione"

#: app/Services/TransStrings.php:31 app/Hooks/Handlers/PurchaseHistory.php:143
#: app/Hooks/Handlers/PurchaseHistory.php:359
msgid "Actions"
msgstr "Azioni"

#: app/Services/TransStrings.php:1512
msgid "actions"
msgstr "azioni"

#: app/Services/TransStrings.php:1491
msgid ""
"Actions blocks are tasks that you want to fire from your side for the target "
"contact"
msgstr ""
"I blocchi di azione sono compiti che vuoi eseguire per il contatto di "
"destinazione"

#: app/Services/TransStrings.php:437
msgid "Activate Fluent Forms Integration"
msgstr "Attiva integrazione con Fluent Forms"

#: app/Services/TransStrings.php:32 app/Services/Helper.php:1329
msgid "Active"
msgstr "Attivo"

#: app/Services/Stats.php:18
msgid "Active Contacts"
msgstr "Contatti attivi"

#: app/Http/Controllers/DocsController.php:94
msgid "Active Fluent Connect"
msgstr "Fluent Connect attivo"

#: app/Http/Controllers/DocsController.php:67
msgid "Active Fluent Forms"
msgstr "Fluent Forms attivo"

#: app/Http/Controllers/DocsController.php:76
msgid "Active Fluent SMTP"
msgstr "Fluent SMTP attivo"

#: app/Http/Controllers/DocsController.php:85
msgid "Active Fluent Support"
msgstr "Fluent Support attivo"

#: app/Services/CrmMigrator/ActiveCampaignMigrator.php:31
msgid "ActiveCampaign API Token"
msgstr "Token API ActiveCampaign"

#: app/Services/TransStrings.php:1001
msgid "Activities of this contact will be shown here"
msgstr "Le attività di questo contatto verranno mostrate qui"

#: app/Http/Controllers/SettingsController.php:573
msgid "Activity Logs"
msgstr "Log attività"

#: app/Services/TransStrings.php:33
msgid "Add"
msgstr "Aggiungi"

#: app/Services/TransStrings.php:74
msgid "Add a Sequence Email"
msgstr "Aggiungi una email di sequenza"

#: app/Services/AutoSubscribe.php:258
msgid "Add a subscription box to WooCommerce Checkout Form"
msgstr ""
"Aggiungi una casella di iscrizione nel modulo di checkout di WooCommerce"

#: app/Services/TransStrings.php:34
msgid "Add Action"
msgstr "Aggiungi azione"

#: app/Services/TransStrings.php:35
msgid "Add Action / Goal"
msgstr "Aggiungi azione / obiettivo"

#: app/Services/TransStrings.php:36
msgid "Add Action/Benchmark/Conditions"
msgstr "Aggiungi azione/benchmark/condizioni"

#: app/Services/TransStrings.php:37
msgid "Add Action/Goals/Conditions"
msgstr "Aggiungi azione/obiettivi/condizioni"

#: app/Services/TransStrings.php:38
msgid "Add Address Info"
msgstr "Aggiungi informazioni di indirizzo"

#: app/Services/TransStrings.php:1560
msgid ""
"Add and segment your EDD customers when someone purchase a product. You can "
"also send a welcome email to your first-time customers with an automated "
"thank you email.Segment your customers and run automatic funnels on product "
"sales and track the conversion rate of your marketing campaigns."
msgstr ""
"Aggiungi e segmenta i tuoi clienti EDD quando acquistano un prodotto. Puoi "
"anche inviare una email di benvenuto automatica ai tuoi nuovi clienti con "
"una email di ringraziamento automatizzata. Segmenta i tuoi clienti e crea "
"funnel automatici sulle vendite dei prodotti e traccia il tasso di "
"conversione delle tue campagne di marketing."

#: app/Services/TransStrings.php:39
msgid "Add Another Conditional Group"
msgstr "Aggiungi un altro gruppo condizionale"

#: app/Services/TransStrings.php:1416
msgid "Add another Sequence Email"
msgstr "Aggiungi un'altra email di sequenza"

#: app/Services/TransStrings.php:40
msgid "Add Automation"
msgstr "Aggiungi automazione"

#: app/Services/TransStrings.php:41
msgid "Add Campaign Title"
msgstr "Aggiungi titolo campagna"

#: app/Services/TransStrings.php:42
msgid "Add Company"
msgstr "Aggiungi azienda"

#: app/Services/TransStrings.php:43
msgid "Add Condition"
msgstr "Aggiungi condizione"

#: app/Services/TransStrings.php:693
msgid "Add Conditional Action, apply rules and split your automation funnel"
msgstr ""
"Aggiungi azione condizionale, applica regole e dividi il tuo funnel di "
"automazione"

#: app/Services/TransStrings.php:44
msgid "Add Contact"
msgstr "Aggiungi contatto"

#: app/Services/Funnel/Actions/ApplyCompanyAction.php:23
msgid "Add contact to the selected company"
msgstr "Aggiungi contatto all'azienda selezionata"

#: app/Services/Funnel/Actions/ApplyListAction.php:23
msgid "Add contact to the selected lists"
msgstr "Aggiungi contatto alle liste selezionate"

#: app/Services/TransStrings.php:45
msgid "Add Custom Data"
msgstr "Aggiungi dati personalizzati"

#: app/Services/TransStrings.php:46
msgid "Add Existing"
msgstr "Aggiungi esistente"

#: app/Services/TransStrings.php:75
msgid "Add existing company"
msgstr "Aggiungi azienda esistente"

#: app/Services/TransStrings.php:76
msgid "Add existing contacts"
msgstr "Aggiungi contatti esistenti"

#: app/Services/TransStrings.php:47
msgid "Add Field"
msgstr "Aggiungi campo"

#: app/Services/TransStrings.php:48
msgid "Add More"
msgstr "Aggiungi altro"

#: app/Services/TransStrings.php:1542
msgid ""
"Add More Users to your FluentCRM and give them selected permissions to "
"manage your marketing. For example, You can add user X to view only the "
"reports and User Y to manage only email campaigns."
msgstr ""
"Aggiungi più utenti a FluentCRM e concedi loro permessi selezionati per "
"gestire il tuo marketing. Ad esempio, puoi aggiungere l'utente X per "
"visualizzare solo i rapporti e l'utente Y per gestire solo le campagne email."

#: app/Services/TransStrings.php:49
msgid "Add New"
msgstr "Aggiungi nuovo"

#: app/Services/TransStrings.php:50
msgid "Add New Condition"
msgstr "Aggiungi nuova condizione"

#: app/Services/TransStrings.php:51
msgid "Add New Contact"
msgstr "Aggiungi nuovo contatto"

#: app/Services/TransStrings.php:52
msgid "Add New Custom Field"
msgstr "Aggiungi nuovo campo personalizzato"

#: app/Services/TransStrings.php:78 app/Services/TransStrings.php:655
msgid ""
"Add new filter to narrow down your contacts based on different properties"
msgstr ""
"Aggiungi nuovo filtro per restringere i contatti in base a diverse proprietà"

#: app/Services/TransStrings.php:53
msgid "Add New Key"
msgstr "Aggiungi nuova chiave"

#: app/Services/TransStrings.php:54
msgid "Add New Manager"
msgstr "Aggiungi nuovo manager"

#: app/Services/TransStrings.php:77
msgid "Add new Manager"
msgstr "Aggiungi nuovo manager"

#: app/Services/TransStrings.php:55
msgid "Add New REST API Key"
msgstr "Aggiungi nuova chiave REST API"

#: app/Services/TransStrings.php:56
msgid "Add New Smart Link"
msgstr "Aggiungi nuovo link smart"

#: app/Services/TransStrings.php:696
msgid "Add newly added steps to previously completed contacts."
msgstr ""
"Aggiungi i passaggi appena aggiunti ai contatti precedentemente completati."

#: app/Services/TransStrings.php:58
msgid "Add Options"
msgstr "Aggiungi opzioni"

#: app/Services/TransStrings.php:57
msgid "Add OR Condition"
msgstr "Aggiungi condizione OR"

#: app/Services/TransStrings.php:79
msgid "Add or Remove"
msgstr "Aggiungi o rimuovi"

#: app/Services/TransStrings.php:59
msgid "Add Sequence"
msgstr "Aggiungi sequenza"

#: app/Services/TransStrings.php:60
msgid "Add SmartCodes"
msgstr "Aggiungi SmartCodes"

#: app/Services/TransStrings.php:61
msgid "Add Social Media URLs"
msgstr "Aggiungi URL dei social media"

#: app/Services/TransStrings.php:62
msgid "Add Subscribers"
msgstr "Aggiungi iscritti"

#: app/Services/TransStrings.php:63
msgid "Add Tags"
msgstr "Aggiungi tag"

#: app/Services/TransStrings.php:64
msgid "Add Tags to Subscribers"
msgstr "Aggiungi tag agli iscritti"

#: app/Services/Funnel/Actions/ApplyTagAction.php:23
msgid "Add this contact to the selected Tags"
msgstr "Aggiungi questo contatto ai tag selezionati"

#: app/Services/TransStrings.php:65
msgid "Add To Automation"
msgstr "Aggiungi all'automazione"

#: app/Services/TransStrings.php:66
msgid "Add To Automation Funnel"
msgstr "Aggiungi al funnel di automazione"

#: app/Services/TransStrings.php:67
msgid "Add To Company"
msgstr "Aggiungi a Azienda"

#: app/Services/TransStrings.php:68
msgid "Add To Email Sequence"
msgstr "Aggiungi a Sequenza Email"

#: app/Services/TransStrings.php:81
msgid "Add to List"
msgstr "Aggiungi a Lista"

#: app/Services/TransStrings.php:69
msgid "Add To Lists"
msgstr "Aggiungi a Liste"

#: app/Services/TransStrings.php:70
msgid "Add To Sequence"
msgstr "Aggiungi a Sequenza"

#: app/Services/TransStrings.php:80 app/Services/TransStrings.php:82
msgid "Add to Sequence"
msgstr "Aggiungi a Sequenza"

#: app/Services/TransStrings.php:71
msgid "Add To Sequence: "
msgstr "Aggiungi a Sequenza:"

#: app/Services/TransStrings.php:72
msgid "Add To Tags"
msgstr "Aggiungi a Tag"

#: app/Services/TransStrings.php:83
msgid "Add to Tags"
msgstr "Aggiungi ai Tag"

#: app/Services/TransStrings.php:84
msgid "Add to this Sequence"
msgstr "Aggiungi a questa Sequenza"

#: app/Services/TransStrings.php:543
msgid "Add UTM Parameters For URLs"
msgstr "Aggiungi Parametri UTM per URL"

#: app/Services/TransStrings.php:73
msgid "Add Value"
msgstr "Aggiungi Valore"

#: app/Services/TransStrings.php:208
msgid "Add/Remove Tag of your Contacts based on email interaction"
msgstr ""
"Aggiungi/Rimuovi Tag ai tuoi Contatti in base all'interazione con l'email"

#: app/Services/TransStrings.php:89
msgid "Added"
msgstr "Aggiunto"

#: app/Services/TransStrings.php:90
msgid "Added @ "
msgstr "Aggiunto @"

#: app/Services/TransStrings.php:91
msgid "Additional Info"
msgstr "Informazioni Aggiuntive"

#: app/Hooks/Handlers/AdminMenu.php:197 app/Hooks/Handlers/AdminMenu.php:198
#: app/Hooks/Handlers/AdminMenu.php:1349 app/Hooks/Handlers/AdminMenu.php:1350
msgid "Addons"
msgstr "Componenti Aggiuntivi"

#: app/Services/TransStrings.php:94
msgid "Address"
msgstr "Indirizzo"

#: app/Services/TransStrings.php:95
msgid "Address Fields"
msgstr "Campi Indirizzo"

#: app/Services/TransStrings.php:96 app/Hooks/Handlers/PrefFormHandler.php:55
msgid "Address Information"
msgstr "Informazioni sull'Indirizzo"

#: app/Models/Company.php:60 app/Models/Subscriber.php:724
#: app/Services/TransStrings.php:97 app/Services/Helper.php:161
#: app/Services/Helper.php:912 app/Hooks/Handlers/PrefFormHandler.php:48
#: app/Hooks/Handlers/PrefFormHandler.php:433
#: app/Services/CrmMigrator/BaseMigrator.php:36
#: app/Services/Funnel/FunnelHelper.php:152
msgid "Address Line 1"
msgstr "Indirizzo, linea 1"

#: app/Models/Company.php:61 app/Models/Subscriber.php:725
#: app/Services/TransStrings.php:98 app/Services/Helper.php:162
#: app/Services/Helper.php:917 app/Hooks/Handlers/PrefFormHandler.php:49
#: app/Hooks/Handlers/PrefFormHandler.php:444
#: app/Services/CrmMigrator/BaseMigrator.php:37
#: app/Services/Funnel/FunnelHelper.php:156
msgid "Address Line 2"
msgstr "Indirizzo, linea 2"

#: app/Services/TransStrings.php:663
msgid "Adds the new FluentCRM navigation experience to the dashboard"
msgstr "Aggiunge la nuova esperienza di navigazione di FluentCRM al dashboard"

#: app/Services/Helper.php:201
msgid "Admin Email"
msgstr "Email Amministratore"

#: app/Services/TransStrings.php:99
msgid "Admin Email Address"
msgstr "Indirizzo Email Amministratore"

#: app/Services/TransStrings.php:100
msgid "Admin Email Addresses (Internal Use only)"
msgstr "Indirizzi Email Amministratore (Uso Interno)"

#: app/Services/TransStrings.php:103
msgid "Advanced Features Config"
msgstr "Configurazione Funzionalità Avanzate"

#: app/Services/TransStrings.php:104
msgid "Advanced Features Settings"
msgstr "Impostazioni Funzionalità Avanzate"

#: app/Services/TransStrings.php:105
msgid "Advanced Filter"
msgstr "Filtro Avanzato"

#: app/Services/TransStrings.php:106
msgid "Advanced Filter is a pro feature"
msgstr "Il filtro avanzato è una funzionalità pro"

#: app/Services/TransStrings.php:703
msgid ""
"Advanced Report is available on Premium Version of FluentCRM along wth lots "
"of integrations, advanced automation, sequence emails and more"
msgstr ""
"Il report avanzato è disponibile nella versione Premium di FluentCRM insieme "
"a molte integrazioni, automazione avanzata, email di sequenza e altro ancora."

#: app/Services/Helper.php:1314
msgid "Affiliate ID (Pro Required)"
msgstr "ID Affiliato (Richiede versione Pro)"

#: app/Hooks/Handlers/CountryNames.php:25
msgid "Afghanistan"
msgstr "Afghanistan"

#: app/Services/TransStrings.php:1513
msgid "after"
msgstr "dopo"

#: app/Services/TransStrings.php:107
msgid "After 1 Hour"
msgstr "Dopo 1 Ora"

#: app/Services/TransStrings.php:108
msgid "After 1 Week"
msgstr "Dopo 1 Settimana"

#: app/Services/TransStrings.php:109
msgid "After 2 Days"
msgstr "Dopo 2 Giorni"

#: app/Http/Controllers/SettingsController.php:133
msgid "After Confirmation Actions"
msgstr "Azioni Dopo Conferma"

#: app/Http/Controllers/SettingsController.php:153
#: app/Http/Controllers/SettingsController.php:154
msgid "After Confirmation Message"
msgstr "Messaggio Dopo Conferma"

#: app/Http/Controllers/SettingsController.php:219
msgid "After Confirmation Message is required"
msgstr "Il Messaggio Dopo Conferma è richiesto"

#: app/Http/Controllers/SettingsController.php:138
msgid "After Confirmation Type"
msgstr "Tipo di Conferma Successiva"

#: app/Hooks/Handlers/CountryNames.php:33
msgid "Albania"
msgstr "Albania"

#: app/Hooks/Handlers/CountryNames.php:37
msgid "Algeria"
msgstr "Algeria"

#: app/Services/TransStrings.php:110
#: app/Http/Controllers/ImporterController.php:153
msgid "All"
msgstr "Tutti"

#: app/Services/TransStrings.php:1514
msgid "All Administrators automatically will get full access of FluentCRM"
msgstr ""
"Tutti gli Amministratori avranno automaticamente pieno accesso a FluentCRM"

#: app/Services/TransStrings.php:118
msgid "All available contacts"
msgstr "Tutti i contatti disponibili"

#: app/Services/TransStrings.php:119
msgid "All available subscribers"
msgstr "Tutti gli iscritti disponibili"

#: app/Services/TransStrings.php:111 app/Hooks/Handlers/AdminMenu.php:354
msgid "All Campaigns"
msgstr "Tutte le Campagne"

#: app/Hooks/CLI/Commands.php:26 app/Hooks/Handlers/AdminMenu.php:302
msgid "All Contacts"
msgstr "Tutti i Contatti"

#: app/Services/TransStrings.php:120
msgid "All contacts from"
msgstr "Tutti i contatti da"

#: app/Http/Controllers/SubscriberController.php:907
msgid "All contacts has been processed"
msgstr "Tutti i contatti sono stati elaborati"

#: app/Services/TransStrings.php:1041
msgid "All contacts on Selected List segment"
msgstr "Tutti i contatti sul segmento della Lista Selezionata"

#: app/Services/TransStrings.php:112
msgid "All Done"
msgstr "Tutto Fatto"

#: app/Services/TransStrings.php:113
msgid "All Email Activities"
msgstr "Tutte le Attività Email"

#: app/Services/TransStrings.php:114 app/Hooks/CLI/Commands.php:42
#: app/Hooks/Handlers/AdminMenu.php:378
msgid "All Emails"
msgstr "Tutte le Email"

#: app/Http/Controllers/SettingsController.php:306
msgid "All FluentCRM Database Tables have been resetted"
msgstr "Tutte le Tabelle del Database FluentCRM sono state resettate"

#: app/Services/TransStrings.php:115
msgid "All Lists"
msgstr "Tutte le Liste"

#: app/Http/Controllers/SystemLogController.php:39
msgid "All logs has been deleted"
msgstr "Tutti i log sono stati eliminati"

#: app/Services/TransStrings.php:994
msgid "All Previous email history for this recurring campaign."
msgstr ""
"Tutta la cronologia delle email precedenti per questa campagna ricorrente."

#: app/Services/TransStrings.php:116
msgid "All Products"
msgstr "Tutti i Prodotti"

#: app/Services/TransStrings.php:1506
msgid ""
"All Selected List and subscribers has been added Successfully to this "
"sequence. Emails will be sent as per your configuration"
msgstr ""
"Tutte le Liste Selezionate e gli iscritti sono stati aggiunti con successo a "
"questa sequenza. Le email saranno inviate secondo la tua configurazione"

#: app/Services/TransStrings.php:1553
msgid "All selected templates will be deleted"
msgstr "Tutti i template selezionati saranno eliminati"

#: app/Services/TransStrings.php:117
msgid "All Sequences"
msgstr "Tutte le Sequenze"

#: app/Services/TransStrings.php:1549
msgid "All the associate data of the selected companies will be deleted"
msgstr "Tutti i dati associati delle aziende selezionate saranno eliminati"

#: app/Services/TransStrings.php:1550
msgid "All the associate data of the selected contacts will be deleted"
msgstr "Tutti i dati associati dei contatti selezionati saranno eliminati"

#: app/Services/TransStrings.php:1629
msgid ""
"All the associate data of the selected contacts will get a double optin mail"
msgstr ""
"Tutti i dati associati dei contatti selezionati riceveranno un'email di "
"doppio opt-in"

#: app/Services/TransStrings.php:1552
msgid "All the associate data of the selected funnels will be deleted"
msgstr "Tutti i dati associati dei funnel selezionati saranno eliminati"

#: app/Services/TransStrings.php:121
msgid "All the selected logs that are"
msgstr "Tutti i log selezionati che sono"

#: app/Services/TransStrings.php:124
msgid "All your email history from this recurring campaign wil be shown here."
msgstr ""
"Tutta la cronologia delle tue email per questa campagna ricorrente sarà "
"mostrata qui."

#: app/Services/TransStrings.php:1624
msgid ""
"All Your Fluent CRM Data (Contacts, Campaigns, Settings, Emails) will be "
"deleted"
msgstr ""
"Tutti i dati di Fluent CRM (Contatti, Campagne, Impostazioni, Email) saranno "
"eliminati"

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:218
msgid ""
"Allow FluentCRM integration conditionally based on your submission values"
msgstr "Consenti l'integrazione di FluentCRM in base ai valori inviati"

#: app/Services/TransStrings.php:1239
msgid ""
"Allow FluentCRM to collect non-sensitive diagnostic data and usage "
"information."
msgstr ""
"Consenti a FluentCRM di raccogliere dati diagnostici e informazioni di "
"utilizzo non sensibili."

#: app/Services/TransStrings.php:126
msgid "Almost Done!"
msgstr "Quasi Fatto!"

#: app/Http/Controllers/SettingsController.php:321
msgid "Amazon SES"
msgstr "Amazon SES"

#: app/Http/Controllers/SettingsController.php:324
msgid "Amazon SES Bounce Handler URL"
msgstr "URL Gestore di Bounce Amazon SES"

#: app/Hooks/Handlers/CountryNames.php:41
msgid "American Samoa"
msgstr "Samoa Americane"

#: app/Services/Funnel/Triggers/UserRegistrationTrigger.php:51
msgid "An Automated double-optin email will be sent for new subscribers"
msgstr ""
"Verrà inviata un'email di doppio opt-in automatica per i nuovi iscritti"

#: app/Services/TransStrings.php:1515
msgid "and manage subscription/unsubscribe url is mandatory"
msgstr ""
"il link di gestione sottoscrizione/annullamento iscrizione è obbligatorio"

#: app/Services/TransStrings.php:499
msgid ""
"and we will be happy to answer your questions and assist you with any "
"problems."
msgstr ""
"e saremo felici di rispondere alle tue domande e di assisterti per qualsiasi "
"problema."

#: app/Hooks/Handlers/CountryNames.php:45
msgid "Andorra"
msgstr "Andorra"

#: app/Hooks/Handlers/CountryNames.php:49
msgid "Angola"
msgstr "Angola"

#: app/Hooks/Handlers/CountryNames.php:53
msgid "Anguilla"
msgstr "Anguilla"

#: app/Services/TransStrings.php:130
msgid "Anonymize ip Address for associate contact data"
msgstr "Anonimizza l'indirizzo IP per i dati di contatto associati"

#: app/Hooks/Handlers/CountryNames.php:57
msgid "Antarctica"
msgstr "Antartide"

#: app/Hooks/Handlers/CountryNames.php:61
msgid "Antigua and Barbuda"
msgstr "Antigua e Barbuda"

#: app/Services/CrmMigrator/ActiveCampaignMigrator.php:37
#: app/Services/CrmMigrator/ActiveCampaignMigrator.php:38
msgid "API Access URL"
msgstr "URL di Accesso API"

#: app/Services/CrmMigrator/MailerLiteMigrator.php:29
#: app/Services/CrmMigrator/ConvertKitMigrator.php:30
#: app/Services/CrmMigrator/MailChimpMigrator.php:32
msgid "API Key"
msgstr "Chiave API"

#: app/Http/Controllers/SettingsController.php:845
msgid "API Key has been successfully created"
msgstr "La Chiave API è stata creata con successo"

#: app/Http/Controllers/SettingsController.php:704
msgid "API Key has been successfully deleted"
msgstr "La Chiave API è stata eliminata con successo"

#: app/Services/TransStrings.php:21
msgid "API Password:"
msgstr "Password API:"

#: app/Services/CrmMigrator/ConvertKitMigrator.php:37
msgid "API Secret"
msgstr "API Secret"

#: app/Services/CrmMigrator/ActiveCampaignMigrator.php:30
#: app/Services/CrmMigrator/DripMigrator.php:29
msgid "API Token"
msgstr "Token API"

#: app/Services/TransStrings.php:22
msgid "API Username:"
msgstr "Nome Utente API:"

#: app/Services/CrmMigrator/Api/ConvertKit.php:61
msgid "API_Error"
msgstr "Errore_API"

#: app/Services/TransStrings.php:23
msgid "APIs"
msgstr "API"

#: app/Services/TransStrings.php:133
msgid "Apply"
msgstr "Applica"

#: app/Services/Funnel/Actions/ApplyCompanyAction.php:22
msgid "Apply Company"
msgstr "Assegna Azienda"

#: app/Services/Funnel/Actions/ApplyCompanyAction.php:34
msgid "Apply Company to the contact"
msgstr "Assegna l'azienda al contatto"

#: app/Services/TransStrings.php:134
msgid "Apply Condition"
msgstr "Applica Condizione"

#: app/Services/Funnel/Actions/ApplyListAction.php:22
msgid "Apply List"
msgstr "Assegna Lista"

#: app/Services/Funnel/Actions/ApplyListAction.php:34
msgid "Apply List to the contact"
msgstr "Assegna la lista al contatto"

#: app/Services/TransStrings.php:135
msgid "Apply Lists"
msgstr "Assegna Liste"

#: app/Services/TransStrings.php:136
msgid "Apply Lists when clicked (optional)"
msgstr "Assegna le liste quando viene cliccato (opzionale)"

#: app/Services/Funnel/Actions/ApplyTagAction.php:22
msgid "Apply Tag"
msgstr "Assegna Tag"

#: app/Services/Funnel/Actions/ApplyTagAction.php:34
msgid "Apply Tag to the contact"
msgstr "Assegna il tag al contatto"

#: app/Services/TransStrings.php:137
msgid "Apply Tags"
msgstr "Assegna Tag"

#: app/Services/TransStrings.php:138
msgid "Apply Tags when clicked"
msgstr "Assegna Tag quando viene cliccato"

#: app/Services/TransStrings.php:139
msgid "April"
msgstr "Aprile"

#: app/Services/TransStrings.php:140
msgid "Archived"
msgstr "Archiviato"

#: app/Services/TransStrings.php:1554
msgid ""
"Are you sure about this action? If you confirm, Your old log data will be "
"deleted permanently!"
msgstr ""
"Sei sicuro di questa azione? Se confermi, i tuoi vecchi dati di log verranno "
"eliminati definitivamente!"

#: app/Services/TransStrings.php:141
msgid "Are you sure to delete this?"
msgstr "Sei sicuro di voler eliminare questo?"

#: app/Services/TransStrings.php:142
msgid "Are you sure to delete?"
msgstr "Sei sicuro di voler eliminare?"

#: app/Services/TransStrings.php:227 app/Services/TransStrings.php:1121
msgid "Are you sure to proceed?"
msgstr "Sei sicuro di voler procedere?"

#: app/Services/TransStrings.php:143
msgid "Are you sure to send double optin?"
msgstr "Sei sicuro di voler inviare il doppio opt-in?"

#: app/Services/TransStrings.php:85
msgid "Are you sure you want to add contacts to selected Automation Funnel?"
msgstr ""
"Sei sicuro di voler aggiungere i contatti al Funnel di Automazione "
"selezionato?"

#: app/Services/TransStrings.php:86
msgid "Are you sure you want to add contacts to selected company?"
msgstr "Sei sicuro di voler aggiungere i contatti all'azienda selezionata?"

#: app/Services/TransStrings.php:87
msgid "Are you sure you want to add contacts to selected email sequence?"
msgstr ""
"Sei sicuro di voler aggiungere i contatti alla sequenza email selezionata?"

#: app/Services/TransStrings.php:131
msgid "Are you sure you want to delete the api key?"
msgstr "Sei sicuro di voler eliminare la chiave API?"

#: app/Services/TransStrings.php:159
msgid "Are you sure you want to delete this automation?"
msgstr "Sei sicuro di voler eliminare questa automazione?"

#: app/Services/TransStrings.php:484
msgid "Are you sure you want to delete this block?"
msgstr "Sei sicuro di voler eliminare questo blocco?"

#: app/Services/TransStrings.php:360
msgid "Are you sure you want to delete this contact?"
msgstr "Sei sicuro di voler eliminare questo contatto?"

#: app/Services/TransStrings.php:1042
msgid "Are you sure you want to delete this Recurring Campaign?"
msgstr "Sei sicuro di voler eliminare questa Campagna Ricorrente?"

#: app/Services/TransStrings.php:144
msgid "Are you sure you want to delete this Sequence?"
msgstr "Sei sicuro di voler eliminare questa Sequenza?"

#: app/Services/TransStrings.php:485
msgid "Are you sure you want to delete this template?"
msgstr "Sei sicuro di voler eliminare questo template?"

#: app/Services/TransStrings.php:1074
msgid "Are you sure you want to remove contacts to selected company?"
msgstr "Sei sicuro di voler rimuovere i contatti dall'azienda selezionata?"

#: app/Services/TransStrings.php:145
msgid "Are you sure you want to remove this?"
msgstr "Sei sicuro di voler rimuovere questo?"

#: app/Services/TransStrings.php:1217
msgid "Are you sure, you want to remove these subscribers from this sequence?"
msgstr "Sei sicuro di voler rimuovere questi iscritti da questa sequenza?"

#: app/Services/TransStrings.php:1623
msgid "Are you sure, you want to reset the CRM Database?"
msgstr "Sei sicuro di voler resettare il database CRM?"

#: app/Services/TransStrings.php:1449
msgid "Are you sure? All of the Fluent CRM email will be sent from "
msgstr "Sei sicuro? Tutte le email di Fluent CRM saranno inviate da "

#: app/Hooks/Handlers/CountryNames.php:65
msgid "Argentina"
msgstr "Argentina"

#: app/Hooks/Handlers/CountryNames.php:69
msgid "Armenia"
msgstr "Armenia"

#: app/Hooks/Handlers/CountryNames.php:73
msgid "Aruba"
msgstr "Aruba"

#: app/Services/TransStrings.php:146
msgid "Assign Company"
msgstr "Assegna Azienda"

#: app/Services/AutoSubscribe.php:40 app/Services/AutoSubscribe.php:128
#: app/Services/AutoSubscribe.php:286
msgid "Assign List"
msgstr "Assegna Lista"

#: app/Services/RoleBasedTagging.php:46
msgid "Assign or Remove tags when a contact assign to a user role."
msgstr ""
"Assegna o Rimuovi tag quando un contatto viene assegnato a un ruolo utente."

#: app/Services/TransStrings.php:147
msgid "Assign selected companies"
msgstr "Assegna aziende selezionate"

#: app/Services/AutoSubscribe.php:54 app/Services/AutoSubscribe.php:141
#: app/Services/AutoSubscribe.php:299
msgid "Assign Tags"
msgstr "Assegna Tag"

#: app/Services/TransStrings.php:148
msgid "Assigned List in FluentCRM (optional)"
msgstr "Lista Assegnata in FluentCRM (opzionale)"

#: app/Services/TransStrings.php:1516
msgid "associate data with FluentCRM"
msgstr "associa dati con FluentCRM"

#: app/Services/TransStrings.php:149
msgid "Associate FluentCRM Manager (Non-Admin Only)"
msgstr "Associa Responsabile FluentCRM (Solo non-amministratori)"

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:135
msgid ""
"Associate your FluentCRM merge tags to the appropriate Fluent Form fields by "
"selecting the appropriate form field from the list."
msgstr ""
"Associa i tuoi tag di unione FluentCRM ai campi appropriati di Fluent Form "
"selezionando il campo modulo appropriato dall'elenco."

#: app/Services/TransStrings.php:1517
msgid "at"
msgstr "alle"

#: app/Services/TransStrings.php:150
msgid "Attach Selected Contacts"
msgstr "Allega contatti selezionati"

#: app/Services/TransStrings.php:151
msgid "Attached Tags and Lists"
msgstr "Tag e Liste Allegati"

#: app/Services/TransStrings.php:152
msgid "Attached Trigger could not be found"
msgstr "Trigger Allegato non trovato"

#: app/Services/TransStrings.php:1696
msgid "Attachments"
msgstr "Allegati"

#: app/Services/TransStrings.php:153
msgid "August"
msgstr "Agosto"

#: app/Hooks/Handlers/CountryNames.php:77
msgid "Australia"
msgstr "Australia"

#: app/Hooks/Handlers/CountryNames.php:81
msgid "Austria"
msgstr "Austria"

#: app/Services/TransStrings.php:154
msgid "Auto Create"
msgstr "Creazione Automatica"

#: app/Services/AutoSubscribe.php:211
msgid "Auto Sync User Data and Contact Data"
msgstr "Sincronizzazione Automatica Dati Utente e Contatti"

#: app/Services/AutoSubscribe.php:29
msgid "Automatically add your new user signups as subscriber in FluentCRM"
msgstr ""
"Aggiungi automaticamente i nuovi utenti registrati come iscritti in FluentCRM"

#: app/Services/AutoSubscribe.php:107
msgid "Automatically add your site commenter as subscriber in FluentCRM"
msgstr ""
"Aggiungi automaticamente i commentatori del sito come iscritti in FluentCRM"

#: app/Services/AutoSubscribe.php:265
msgid ""
"Automatically fill WooCommerce Checkout field value with current contact data"
msgstr ""
"Riempi automaticamente i campi del checkout di WooCommerce con i dati del "
"contatto attuale"

#: app/Services/AutoSubscribe.php:212
msgid "Automatically Sync your WP User Data and Fluent CRM Contact Data"
msgstr ""
"Sincronizza automaticamente i dati utente WP e i dati dei contatti di Fluent "
"CRM"

#: app/Services/Helper.php:1079
msgid "Automation Activity -"
msgstr "Attività Automazione -"

#: app/Services/PermissionManager.php:95
msgid "Automation Delete"
msgstr "Elimina Automazione"

#: app/Services/TransStrings.php:155
msgid "Automation Funnel"
msgstr "Funnel di Automazione"

#: app/Services/TransStrings.php:156
msgid "Automation Funnels"
msgstr "Funnel di Automazione"

#: app/Services/TransStrings.php:157
msgid "Automation Name"
msgstr "Nome Automazione"

#: app/Services/PermissionManager.php:83
msgid "Automation Read"
msgstr "Visualizza Automazione"

#: app/Services/TransStrings.php:158
msgid "Automation Trigger Title"
msgstr "Titolo Trigger Automazione"

#: app/Services/PermissionManager.php:88
msgid "Automation Write/Edit/Delete"
msgstr "Scrittura/Modifica/Eliminazione Automazione"

#: app/Services/Stats.php:100 app/Services/TransStrings.php:166
#: app/Hooks/CLI/Commands.php:38 app/Hooks/Handlers/AdminMenu.php:165
#: app/Hooks/Handlers/AdminMenu.php:166 app/Hooks/Handlers/AdminMenu.php:398
#: app/Hooks/Handlers/AdminMenu.php:1328 app/Hooks/Handlers/AdminMenu.php:1329
msgid "Automations"
msgstr "Automazioni"

#: app/Services/TransStrings.php:167
msgid "Average Order Value (AOV)"
msgstr "Valore Medio Ordine (AOV)"

#: app/Services/TransStrings.php:168
msgid "Average Order/Customer (AOC)"
msgstr "Ordine Medio/Cliente (AOC)"

#: app/Services/TransStrings.php:1380
msgid "Awesome! No one unsubscribed from this campaign"
msgstr "Fantastico! Nessuno si è disiscritto da questa campagna"

#: app/Hooks/Handlers/CountryNames.php:85
msgid "Azerbaijan"
msgstr "Azerbaigian"

#: app/Services/TransStrings.php:169
msgid "B"
msgstr "B"

#: app/Services/TransStrings.php:170
msgid "Back"
msgstr "Indietro"

#: app/Services/TransStrings.php:171
msgid "Back To Campaigns"
msgstr "Torna alle Campagne"

#: app/Services/TransStrings.php:172
msgid "Back to Sequence"
msgstr "Torna alla Sequenza"

#: app/Services/TransStrings.php:1689
msgid "Back To Templates"
msgstr "Torna ai Template"

#: app/Services/TransStrings.php:173
msgid "Background Color"
msgstr "Colore di Sfondo"

#: app/Hooks/Handlers/CountryNames.php:89
msgid "Bahamas"
msgstr "Bahamas"

#: app/Hooks/Handlers/CountryNames.php:93
msgid "Bahrain"
msgstr "Bahrain"

#: app/Hooks/Handlers/CountryNames.php:97
msgid "Bangladesh"
msgstr "Bangladesh"

#: app/Hooks/Handlers/CountryNames.php:101
msgid "Barbados"
msgstr "Barbados"

#: app/Services/CrmMigrator/ActiveCampaignMigrator.php:153
#: app/Services/CrmMigrator/DripMigrator.php:153
#: app/Services/CrmMigrator/MailChimpMigrator.php:169
msgid "Based on your selections "
msgstr "In base alle tue selezioni "

#: app/Services/CrmMigrator/MailerLiteMigrator.php:145
#: app/Services/CrmMigrator/ConvertKitMigrator.php:148
msgid "Based on your selections, "
msgstr "In base alle tue selezioni, "

#: app/Services/TransStrings.php:233
msgid ""
"Based on your time selection, the emails will be scheduled within that range "
"randomly"
msgstr ""
"In base alla tua selezione del tempo, le email verranno pianificate "
"casualmente all'interno di quel range"

#: app/Services/TransStrings.php:174
msgid "Basic Info"
msgstr "Informazioni di Base"

#: app/Services/TransStrings.php:175
msgid "Basic Information"
msgstr "Informazioni di Base"

#: app/Services/TransStrings.php:1518
#, fuzzy
#| msgid "Before"
msgid "before"
msgstr "prima"

#: app/Services/TransStrings.php:1519
msgid "before days"
msgstr "prima di giorni"

#: app/Hooks/Handlers/CountryNames.php:105
msgid "Belarus"
msgstr "Bielorussia"

#: app/Hooks/Handlers/CountryNames.php:113
msgid "Belau"
msgstr "Belau"

#: app/Hooks/Handlers/CountryNames.php:109
msgid "Belgium"
msgstr "Belgio"

#: app/Hooks/Handlers/CountryNames.php:117
msgid "Belize"
msgstr "Belize"

#: app/Services/TransStrings.php:1520
msgid "benchmark"
msgstr "benchmark"

#: app/Services/Funnel/BaseBenchMark.php:69
msgid "Benchmark type"
msgstr "Tipo di benchmark"

#: app/Services/TransStrings.php:177
msgid "BenchMark/Trigger Block"
msgstr "Blocco Benchmark/Trigger"

#: app/Services/TransStrings.php:178
msgid "Benchmarks"
msgstr "Benchmark"

#: app/Hooks/Handlers/CountryNames.php:121
msgid "Benin"
msgstr "Benin"

#: app/Hooks/Handlers/CountryNames.php:125
msgid "Bermuda"
msgstr "Bermuda"

#: app/Hooks/Handlers/CountryNames.php:129
msgid "Bhutan"
msgstr "Bhutan"

#: app/Services/Helper.php:475
msgid "Black"
msgstr "Nero"

#: app/Services/TransStrings.php:180
msgid "Blog Posts"
msgstr "Articoli del Blog"

#: app/Services/TransStrings.php:181
msgid "Body Background Color"
msgstr "Colore Sfondo Corpo"

#: app/Hooks/Handlers/CountryNames.php:133
msgid "Bolivia"
msgstr "Bolivia"

#: app/Hooks/Handlers/CountryNames.php:137
msgid "Bonaire, Saint Eustatius and Saba"
msgstr "Bonaire, Sant'Eustachio e Saba"

#: app/Services/TransStrings.php:182
msgid "Border Radius"
msgstr "Raggio Bordo"

#: app/Hooks/Handlers/CountryNames.php:141
msgid "Bosnia and Herzegovina"
msgstr "Bosnia ed Erzegovina"

#: app/Hooks/Handlers/CountryNames.php:145
msgid "Botswana"
msgstr "Botswana"

#: app/Services/TransStrings.php:183
msgid "Bounce Handler"
msgstr "Gestione Bounce"

#: app/Services/TransStrings.php:184
msgid "Bounce Handling Settings"
msgstr "Impostazioni Gestione Bounce"

#: app/Functions/helpers.php:502 app/Functions/helpers.php:549
#: app/Services/TransStrings.php:1522
msgid "Bounced"
msgstr "Rimbalzato"

#: app/Hooks/Handlers/CountryNames.php:149
msgid "Bouvet Island"
msgstr "Isola Bouvet"

#: app/Hooks/Handlers/CountryNames.php:153
msgid "Brazil"
msgstr "Brasile"

#: app/Hooks/Handlers/CountryNames.php:157
msgid "British Indian Ocean Territory"
msgstr "Territorio Britannico dell'Oceano Indiano"

#: app/Services/TransStrings.php:185
msgid "Broadcast"
msgstr "Trasmissione"

#: app/Services/TransStrings.php:209
msgid "Broadcast/Schedule This Email Campaign Now"
msgstr "Trasmetti/Pianifica Questa Campagna Email Ora"

#: app/Services/TransStrings.php:186
msgid "Broadcasts"
msgstr "Trasmissioni"

#: app/Hooks/Handlers/AdminMenu.php:304
msgid "Browse all your subscribers and customers"
msgstr "Sfoglia tutti i tuoi iscritti e clienti"

#: app/Hooks/Handlers/AdminMenu.php:316
msgid "Browse and Manage contact business/companies"
msgstr "Sfoglia e Gestisci attività/aziende dei contatti"

#: app/Hooks/Handlers/AdminMenu.php:324
msgid "Browse and Manage your lists associate with contact"
msgstr "Sfoglia e Gestisci le tue liste associate ai contatti"

#: app/Hooks/Handlers/AdminMenu.php:330
msgid "Browse and Manage your tags associate with contact"
msgstr "Sfoglia e Gestisci i tuoi tag associati ai contatti"

#: app/Hooks/Handlers/CountryNames.php:161
msgid "Brunei"
msgstr "Brunei"

#: app/Services/TransStrings.php:187
msgid "Build Email By Drag and Drop Visual Editor"
msgstr "Crea Email Con Editor Visuale Drag and Drop"

#: app/Hooks/Handlers/CountryNames.php:165
msgid "Bulgaria"
msgstr "Bulgaria"

#: app/Hooks/Handlers/CountryNames.php:169
msgid "Burkina Faso"
msgstr "Burkina Faso"

#: app/Hooks/Handlers/CountryNames.php:173
msgid "Burundi"
msgstr "Burundi"

#: app/Services/Helper.php:200
msgid "Business Address"
msgstr "Indirizzo Attività"

#: app/Services/TransStrings.php:188
msgid "Business Full Address"
msgstr "Indirizzo Completo Attività"

#: app/Services/TransStrings.php:189 app/Services/Helper.php:199
msgid "Business Name"
msgstr "Nome Attività"

#: app/Services/TransStrings.php:190
msgid "Business Settings"
msgstr "Impostazioni Attività"

#: app/Services/TransStrings.php:191
msgid "Button"
msgstr "Pulsante"

#: app/Services/TransStrings.php:192
msgid "Button Preview"
msgstr "Anteprima Pulsante"

#: app/Services/TransStrings.php:193
msgid "Button Text"
msgstr "Testo Pulsante"

#: app/Services/TransStrings.php:194
msgid "Button URL"
msgstr "URL Pulsante"

#: app/Services/TransStrings.php:195
msgid "By"
msgstr "Da"

#: app/Services/TransStrings.php:196
msgid "By Advanced Filter"
msgstr "Per Filtro Avanzato"

#: app/Services/TransStrings.php:197
msgid "By Date"
msgstr "Per Data"

#: app/Services/TransStrings.php:198
msgid "By Dynamic Segment"
msgstr "Per Segmento Dinamico"

#: app/Services/TransStrings.php:199
msgid "By List & Tag"
msgstr "Per Lista e Tag"

#: app/Functions/helpers.php:612
msgid "Call"
msgstr "Chiamata"

#: app/Hooks/Handlers/CountryNames.php:177
msgid "Cambodia"
msgstr "Cambogia"

#: app/Hooks/Handlers/CountryNames.php:181
msgid "Cameroon"
msgstr "Camerun"

#: app/Services/TransStrings.php:234
msgid "Campaign"
msgstr "Campagna"

#: app/Services/TransStrings.php:235
msgid "Campaign Actions"
msgstr "Azioni Campagna"

#: app/Services/TransStrings.php:236
msgid "Campaign Archive Settings"
msgstr "Impostazioni Archivio Campagna"

#: app/Services/TransStrings.php:255
msgid "Campaign Archive showcase is a pro features. Please upgrade to pro."
msgstr ""
"La visualizzazione dell'archivio campagna è una funzione pro. Effettua "
"l'upgrade per accedere."

#: app/Services/TransStrings.php:237
msgid "Campaign Archives"
msgstr "Archivi Campagna"

#: app/Services/TransStrings.php:238
msgid "Campaign Archives (Pro)"
msgstr "Archivi Campagna (Pro)"

#: app/Services/TransStrings.php:239
msgid "Campaign Archives on Frontend"
msgstr "Archivi Campagna nel Frontend"

#: app/Services/TransStrings.php:240
msgid "Campaign Content"
msgstr "Contenuto Campagna"

#: app/Services/TransStrings.php:253
msgid "Campaign deleted."
msgstr "Campagna eliminata."

#: app/Services/TransStrings.php:241
msgid "Campaign Details"
msgstr "Dettagli Campagna"

#: app/Services/Helper.php:1061
msgid "Campaign Email -"
msgstr "Email Campagna -"

#: app/Http/Controllers/CampaignController.php:1197
msgid "Campaign has been successfully duplicated"
msgstr "La campagna è stata duplicata con successo"

#: app/Http/Controllers/CampaignController.php:1110
msgid "Campaign has been successfully marked as paused"
msgstr "La campagna è stata segnata come in pausa"

#: app/Http/Controllers/CampaignController.php:1136
msgid "Campaign has been successfully resumed"
msgstr "La campagna è stata ripresa con successo"

#: app/Http/Controllers/CampaignController.php:1238
msgid "Campaign has been successfully un-scheduled"
msgstr "La campagna è stata rimossa dalla programmazione con successo"

#: app/Http/Controllers/CampaignController.php:1162
msgid "Campaign has been updated"
msgstr "La campagna è stata aggiornata"

#: app/Services/TransStrings.php:242
msgid "Campaign Link Clicks"
msgstr "Clic sui Link della Campagna"

#: app/Services/TransStrings.php:243
msgid "Campaign Medium (required)"
msgstr "Campagna Mezzo (obbligatorio)"

#: app/Services/TransStrings.php:244
msgid "Campaign Name (required)"
msgstr "Nome Campagna (obbligatorio)"

#: app/Services/TransStrings.php:245
msgid "Campaign Performance"
msgstr "Prestazioni Campagna"

#: app/Services/TransStrings.php:246
msgid "Campaign Recipients"
msgstr "Destinatari Campagna"

#: app/Services/TransStrings.php:247
msgid "Campaign Search Keyword"
msgstr "Parola Chiave Ricerca Campagna"

#: app/Services/TransStrings.php:248
msgid "Campaign Settings"
msgstr "Impostazioni Campagna"

#: app/Services/TransStrings.php:249
msgid "Campaign Source (required)"
msgstr "Fonte Campagna (obbligatorio)"

#: app/Services/TransStrings.php:254
#: app/Http/Controllers/CampaignController.php:446
msgid "Campaign status is not in draft status. Please reload the page"
msgstr "Lo stato della campagna non è in bozza. Ricarica la pagina"

#: app/Services/TransStrings.php:250
msgid "Campaign Status:"
msgstr "Stato Campagna:"

#: app/Services/TransStrings.php:251
msgid "Campaign Term"
msgstr "Termine Campagna"

#: app/Services/TransStrings.php:252
msgid "Campaign Title"
msgstr "Titolo Campagna"

#: app/Services/Stats.php:25 app/Services/TransStrings.php:257
#: app/Hooks/CLI/Commands.php:34 app/Hooks/Handlers/AdminMenu.php:116
#: app/Hooks/Handlers/AdminMenu.php:117
msgid "Campaigns"
msgstr "Campagne"

#: app/Services/TransStrings.php:258
msgid "Can a subscriber manage list subscriptions?"
msgstr "Un iscritto può gestire le iscrizioni alle liste?"

#: app/Hooks/Handlers/CountryNames.php:185
msgid "Canada"
msgstr "Canada"

#: app/Services/TransStrings.php:259
msgid "Cancel"
msgstr "Annulla"

#: app/Services/TransStrings.php:1526
msgid "cancel"
msgstr "annulla"

#: app/Services/TransStrings.php:260
msgid "Cancel Editing"
msgstr "Annulla Modifica"

#: app/Services/TransStrings.php:261
msgid "Cancel Schedule"
msgstr "Annulla Pianificazione"

#: app/Services/TransStrings.php:262
msgid "Cancel This email"
msgstr "Annulla Questa Email"

#: app/Services/TransStrings.php:263
msgid "Cancel this schedule"
msgstr "Annulla questa pianificazione"

#: app/Services/TransStrings.php:264
msgid "Cancelled"
msgstr "Annullato"

#: app/Services/TransStrings.php:1527
msgid "cancelled"
msgstr "annullato"

#: app/Hooks/Handlers/CountryNames.php:189
msgid "Cape Verde"
msgstr "Capo Verde"

#: app/Services/TransStrings.php:1702
msgid "Cart Abandoned Cut-off Time"
msgstr "Tempo Limite Carrello Abbandonato"

#: app/Services/TransStrings.php:265
msgid "Category"
msgstr "Categoria"

#: app/Hooks/Handlers/CountryNames.php:193
msgid "Cayman Islands"
msgstr "Isole Cayman"

#: app/Hooks/Handlers/CountryNames.php:197
msgid "Central African Republic"
msgstr "Repubblica Centrafricana"

#: app/Hooks/Handlers/CountryNames.php:201
msgid "Chad"
msgstr "Ciad"

#: app/Services/TransStrings.php:266
msgid "Change"
msgstr "Modifica"

#: app/Services/TransStrings.php:267
msgid "Change (%)"
msgstr "Variazione (%)"

#: app/Services/TransStrings.php:268
msgid "Change Automation Trigger"
msgstr "Cambia Trigger Automazione"

#: app/Services/TransStrings.php:269
msgid "Change Category"
msgstr "Cambia Categoria"

#: app/Services/TransStrings.php:270
msgid "Change Company Category"
msgstr "Cambia Categoria Azienda"

#: app/Services/TransStrings.php:271
msgid "Change Company Type"
msgstr "Cambia Tipo di Azienda"

#: app/Services/TransStrings.php:272
msgid "Change Contact Status"
msgstr "Cambia Stato del Contatto"

#: app/Services/TransStrings.php:273
msgid "Change Contact Type"
msgstr "Cambia Tipo di Contatto"

#: app/Services/TransStrings.php:274
msgid "Change Funnel Status"
msgstr "Cambia Stato del Funnel"

#: app/Services/TransStrings.php:278
msgid "Change preview contact"
msgstr "Modifica contatto in anteprima"

#: app/Services/TransStrings.php:275
msgid "Change Status"
msgstr "Cambia Stato"

#: app/Services/TransStrings.php:1002
msgid "Change Subscription Status"
msgstr "Cambia Stato dell'Iscrizione"

#: app/Services/TransStrings.php:276
msgid "Change Type"
msgstr "Cambia Tipo"

#: app/Services/TransStrings.php:277
msgid "Change User Role to "
msgstr "Cambia Ruolo Utente in"

#: app/Services/TransStrings.php:279
msgid "Chart Report"
msgstr "Rapporto Grafico"

#: app/Services/TransStrings.php:280
msgid "Check All"
msgstr "Seleziona Tutti"

#: app/Services/TransStrings.php:281
msgid "Check the documentation"
msgstr "Consulta la documentazione"

#: app/Services/AutoSubscribe.php:275 app/Services/AutoSubscribe.php:277
msgid "Checkbox Label for Checkout checkbox"
msgstr "Etichetta per il checkbox di Checkout"

#: app/Services/AutoSubscribe.php:117 app/Services/AutoSubscribe.php:119
msgid "Checkbox Label for Comment Form"
msgstr "Etichetta per il checkbox del Modulo Commenti"

#: app/Models/CustomContactField.php:70
msgid "Checkboxes"
msgstr "Checkbox"

#: app/Hooks/Handlers/CountryNames.php:205
msgid "Chile"
msgstr "Cile"

#: app/Hooks/Handlers/CountryNames.php:209
msgid "China"
msgstr "Cina"

#: app/Services/TransStrings.php:284
msgid "Choose a List"
msgstr "Scegli una Lista"

#: app/Services/TransStrings.php:285
msgid "Choose an option:"
msgstr "Scegli un'opzione:"

#: app/Services/TransStrings.php:282
msgid "Choose New"
msgstr "Scegli Nuovo"

#: app/Services/TransStrings.php:283
msgid "Choose Values"
msgstr "Scegli Valori"

#: app/Hooks/Handlers/CountryNames.php:213
msgid "Christmas Island"
msgstr "Isola di Natale"

#: app/Models/Company.php:63 app/Models/Subscriber.php:726
#: app/Services/TransStrings.php:286 app/Services/Helper.php:163
#: app/Services/Helper.php:922 app/Hooks/Handlers/PrefFormHandler.php:50
#: app/Hooks/Handlers/PrefFormHandler.php:455
#: app/Services/CrmMigrator/BaseMigrator.php:39
#: app/Services/Funnel/FunnelHelper.php:164
msgid "City"
msgstr "Città"

#: app/Services/Helper.php:319
msgid "Classic Editor"
msgstr "Editor Classico"

#: app/Services/TransStrings.php:287
msgid "Clear Filters"
msgstr "Cancella Filtri"

#: app/Services/TransStrings.php:288
msgid "Click"
msgstr "Clic"

#: app/Services/TransStrings.php:291
msgid "Click here"
msgstr "Clicca qui"

#: app/Services/TransStrings.php:1529
msgid "click here"
msgstr "clicca qui"

#: app/Services/TransStrings.php:289
msgid "Click Here to Renew your License"
msgstr "Clicca qui per rinnovare la tua licenza"

#: app/Services/TransStrings.php:290
msgid "Click Rate"
msgstr "Tasso di Clic"

#: app/Models/CampaignUrlMetric.php:130
msgid "Click Rate (%d)"
msgstr "Tasso di Clic (%d)"

#: app/Models/CampaignUrlMetric.php:141
msgid "Click To Open Rate"
msgstr "Tasso di Clic su Apertura"

#: app/Services/TransStrings.php:1530
msgid "click to upload"
msgstr "clicca per caricare"

#: app/Services/TransStrings.php:292
msgid "Clicked"
msgstr "Cliccato"

#: app/Services/TransStrings.php:293
msgid "Clicks"
msgstr "Clic"

#: app/Services/TransStrings.php:294
msgid "Clone"
msgstr "Clona"

#: app/Services/TransStrings.php:295
msgid "Close"
msgstr "Chiudi"

#: app/Services/TransStrings.php:296
msgid "Close Log"
msgstr "Chiudi Registro"

#: app/Hooks/Handlers/CountryNames.php:217
msgid "Cocos (Keeling) Islands"
msgstr "Isole Cocos (Keeling)"

#: app/Services/TransStrings.php:770
msgid ""
"Collect and segment email contacts from wooCommerce checkout and send emails."
" Run funnels, segment your customers into FluentCRM"
msgstr ""
"Raccogli e segmenta i contatti email dal checkout di WooCommerce e invia "
"email. Esegui funnel e segmenta i tuoi clienti in FluentCRM"

#: app/Http/Controllers/DocsController.php:68
msgid ""
"Collect leads and build any type of forms, accept payments, connect with "
"your CRM with the Fastest Contact Form Builder Plugin for WordPress"
msgstr ""
"Raccogli lead e crea qualsiasi tipo di modulo, accetta pagamenti, collegati "
"al tuo CRM con il plugin più veloce per la creazione di moduli di contatto "
"per WordPress"

#: app/Hooks/Handlers/CountryNames.php:221
msgid "Colombia"
msgstr "Colombia"

#: app/Services/TransStrings.php:297
msgid "Color"
msgstr "Colore"

#: app/Http/Controllers/CompanyController.php:288
#: app/Http/Controllers/SubscriberController.php:176
msgid "Column is not valid"
msgstr "La colonna non è valida"

#: app/Services/TransStrings.php:298
msgid "Columns"
msgstr "Colonne"

#: app/Services/TransStrings.php:299
msgid "Comma Separated (,)"
msgstr "Separato da Virgole (,)"

#: app/Services/AutoSubscribe.php:106
msgid "Comment Form Subscription Settings"
msgstr "Impostazioni Iscrizione Modulo Commenti"

#: app/Services/TransStrings.php:300
msgid "Commerce Fields"
msgstr "Campi di Commercio"

#: app/Hooks/Handlers/CountryNames.php:225
msgid "Comoros"
msgstr "Comore"

#: app/Services/TransStrings.php:301 app/Hooks/Handlers/AdminMenu.php:85
#: app/Hooks/Handlers/AdminMenu.php:86 app/Hooks/Handlers/AdminMenu.php:314
msgid "Companies"
msgstr "Aziende"

#: app/Services/TransStrings.php:302
msgid "Companies imported successfully"
msgstr "Aziende importate con successo"

#: app/Http/Controllers/CompanyController.php:358
msgid "Companies selection is required"
msgstr "È necessaria la selezione delle aziende"

#: app/Services/TransStrings.php:303 app/Services/Helper.php:1119
msgid "Company"
msgstr "Azienda"

#: app/Services/Helper.php:1129
msgid "Company - Industry"
msgstr "Azienda - Settore"

#: app/Services/Helper.php:1139
msgid "Company - Type"
msgstr "Azienda - Tipo"

#: app/Services/TransStrings.php:304
msgid "Company / Business"
msgstr "Azienda / Attività"

#: app/Services/Helper.php:176
msgid "Company Address"
msgstr "Indirizzo Azienda"

#: app/Services/TransStrings.php:317
msgid "Company association removed"
msgstr "Associazione aziendale rimossa"

#: app/Http/Controllers/CompanyController.php:436
msgid "Company Category has been updated for the selected companies"
msgstr "La categoria aziendale è stata aggiornata per le aziende selezionate"

#: app/Models/Company.php:55
msgid "Company Description"
msgstr "Descrizione Azienda"

#: app/Models/Company.php:58 app/Services/TransStrings.php:305
msgid "Company Email"
msgstr "Email Azienda"

#: app/Services/TransStrings.php:306
msgid "Company Fields"
msgstr "Campi Azienda"

#: app/Http/Controllers/CompanyController.php:232
msgid "Company has been created successfully"
msgstr "Azienda creata con successo"

#: app/Http/Controllers/CompanyController.php:345
msgid "Company has been deleted successfully"
msgstr "Azienda eliminata con successo"

#: app/Http/Controllers/CompanyController.php:162
msgid "Company has been successfully detached"
msgstr "Azienda scollegata con successo"

#: app/Http/Controllers/CompanyController.php:260
msgid "Company has been updated"
msgstr "Azienda aggiornata"

#: app/Services/TransStrings.php:307
msgid "Company ID:"
msgstr "ID Azienda:"

#: app/Services/TransStrings.php:308 app/Services/Helper.php:175
msgid "Company Industry"
msgstr "Settore Azienda"

#: app/Services/TransStrings.php:309
msgid "Company Logo"
msgstr "Logo Azienda"

#: app/Models/Company.php:56
msgid "Company Logo URL"
msgstr "URL Logo Azienda"

#: app/Services/TransStrings.php:318
msgid "Company marked as primary"
msgstr "Azienda segnata come primaria"

#: app/Services/TransStrings.php:310
msgid "Company Module"
msgstr "Modulo Azienda"

#: app/Services/TransStrings.php:311
msgid "Company Module Settings"
msgstr "Impostazioni Modulo Azienda"

#: app/Services/TransStrings.php:312 app/Services/Helper.php:174
msgid "Company Name"
msgstr "Nome Azienda"

#: app/Services/TransStrings.php:313
msgid "Company Name (required)"
msgstr "Nome Azienda (obbligatorio)"

#: app/Models/Company.php:51
msgid "Company Name *"
msgstr "Nome Azienda *"

#: app/Services/TransStrings.php:314
msgid "Company Owner"
msgstr "Proprietario Azienda"

#: app/Models/Company.php:59
msgid "Company Phone"
msgstr "Telefono Azienda"

#: app/Services/TransStrings.php:315
msgid "Company Phone Number"
msgstr "Numero di Telefono Azienda"

#: app/Http/Controllers/CompanyController.php:333
msgid "Company successfully updated"
msgstr "Azienda aggiornata con successo"

#: app/Services/TransStrings.php:316
msgid "Company Type"
msgstr "Tipo Azienda"

#: app/Http/Controllers/CompanyController.php:416
msgid "Company Type has been updated for the selected companies"
msgstr "Il tipo aziendale è stato aggiornato per le aziende selezionate"

#: app/Services/TransStrings.php:323
msgid "Compare Date"
msgstr "Confronta Data"

#: app/Services/TransStrings.php:1532
msgid "compare to"
msgstr "confronta con"

#: app/Functions/helpers.php:503 app/Functions/helpers.php:550
#: app/Services/TransStrings.php:1533
msgid "Complained"
msgstr "Reclamato"

#: app/Services/TransStrings.php:324
msgid "Complete"
msgstr "Completa"

#: app/Services/TransStrings.php:325
msgid "Complete Import"
msgstr "Completa Importazione"

#: app/Services/TransStrings.php:326
msgid "Complete Installation"
msgstr "Completa Installazione"

#: app/Services/TransStrings.php:327 app/Hooks/CLI/Commands.php:132
msgid "Completed"
msgstr "Completato"

#: app/Services/TransStrings.php:1534
msgid "completed"
msgstr "completato"

#: app/Services/TransStrings.php:844
msgid "Completed. You can close this modal now"
msgstr "Completato. Puoi chiudere questo modal ora"

#: app/Services/TransStrings.php:328
msgid "Compliance"
msgstr "Conformità"

#: app/Services/TransStrings.php:329
msgid "Compliance Settings"
msgstr "Impostazioni Conformità"

#: app/Services/TransStrings.php:330
msgid "Compose"
msgstr "Componi"

#: app/Services/TransStrings.php:331
msgid "Compose Your Email Body"
msgstr "Componi il Corpo della tua Email"

#: app/Services/TransStrings.php:1535
msgid "condition"
msgstr "condizione"

#: app/Services/TransStrings.php:334
msgid "Condition block is not available"
msgstr "Blocco Condizione non disponibile"

#: app/Services/TransStrings.php:332
msgid "Condition Blocks"
msgstr "Blocchi Condizione"

#: app/Services/TransStrings.php:200
msgid "CONDITION TYPE"
msgstr "TIPO CONDIZIONE"

#: app/Services/TransStrings.php:333
msgid "Condition Value"
msgstr "Valore Condizione"

#: app/Services/TransStrings.php:335
msgid "Condition:"
msgstr "Condizione:"

#: app/Services/TransStrings.php:1536
msgid "conditional"
msgstr "condizionale"

#: app/Services/TransStrings.php:336
msgid "Conditional Action"
msgstr "Azione Condizionale"

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:217
msgid "Conditional Logics"
msgstr "Logiche Condizionali"

#: app/Services/TransStrings.php:337
msgid "Conditionals"
msgstr "Condizionali"

#: app/Services/TransStrings.php:338
msgid "Conditions"
msgstr "Condizioni"

#: app/Services/TransStrings.php:339
msgid "Configuration"
msgstr "Configurazione"

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:52
msgid "Configuration required!"
msgstr "Configurazione richiesta!"

#: app/Services/TransStrings.php:340
msgid "Configure FluentSMTP"
msgstr "Configura FluentSMTP"

#: app/Services/TransStrings.php:341
msgid "Confirm"
msgstr "Conferma"

#: app/Services/TransStrings.php:1537
msgid "confirm"
msgstr "conferma"

#: app/Services/TransStrings.php:342
msgid "Confirm Import"
msgstr "Conferma Importazione"

#: app/Services/Libs/Parser/ShortcodeParser.php:252
msgid "Confirm Subscription"
msgstr "Conferma Iscrizione"

#: app/Hooks/Handlers/ExternalPages.php:414
msgid "Confirm your unsubscribe Request"
msgstr "Conferma la tua richiesta di annullamento iscrizione"

#: app/Hooks/Handlers/CountryNames.php:229
msgid "Congo (Brazzaville)"
msgstr "Congo (Brazzaville)"

#: app/Hooks/Handlers/CountryNames.php:233
msgid "Congo (Kinshasa)"
msgstr "Congo (Kinshasa)"

#: app/Services/TransStrings.php:343
msgid "Congratulations"
msgstr "Congratulazioni"

#: app/Services/TransStrings.php:344
msgid "Connect"
msgstr "Connetti"

#: app/Http/Controllers/DocsController.php:95
msgid ""
"Connect FluentCRM with ThriveCart and create, segment contact and run "
"automation on ThriveCart purchase events."
msgstr ""
"Connetti FluentCRM con ThriveCart per creare, segmentare i contatti ed "
"eseguire automazioni sugli eventi di acquisto di ThriveCart."

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:36
msgid ""
"Connect FluentCRM with WP Fluent Forms and subscribe a contact when a form "
"is submitted."
msgstr ""
"Connetti FluentCRM con WP Fluent Forms e iscrivi un contatto quando un "
"modulo viene inviato."

#: app/Services/TransStrings.php:345
msgid "Connect with"
msgstr "Connetti con"

#: app/Services/TransStrings.php:346
msgid "Connect with your CRM"
msgstr "Connetti con il tuo CRM"

#: app/Services/TransStrings.php:347 app/Services/Helper.php:152
#: app/Services/Helper.php:890
msgid "Contact"
msgstr "Contatto"

#: app/Services/Helper.php:1041
msgid "Contact Activities"
msgstr "Attività del contatto"

#: app/Services/Funnel/Benchmarks/ListAppliedBenchmark.php:69
msgid "contact added in all of the selected lists"
msgstr "contatto aggiunto a tutte le liste selezionate"

#: app/Services/Funnel/Benchmarks/TagAppliedBenchmark.php:69
msgid "contact added in all of the selected Tags"
msgstr "contatto aggiunto a tutti i tag selezionati"

#: app/Services/Funnel/Benchmarks/ListAppliedBenchmark.php:65
msgid "contact added in any of the selected Lists"
msgstr "contatto aggiunto a una delle liste selezionate"

#: app/Services/Funnel/Benchmarks/TagAppliedBenchmark.php:65
msgid "contact added in any of the selected Tags"
msgstr "contatto aggiunto a uno dei tag selezionati"

#: app/Http/Controllers/SubscriberController.php:763
msgid "Contact Already Subscribed"
msgstr "Contatto Già Iscritto"

#: app/Services/TransStrings.php:357
msgid "Contact can see all lists and manage subscriptions"
msgstr "Il contatto può vedere tutte le liste e gestire le iscrizioni"

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:376
msgid ""
"Contact creation has been skipped because contact already exist in the "
"database"
msgstr ""
"La creazione del contatto è stata saltata perché il contatto esiste già nel "
"database"

#: app/Services/Helper.php:158
msgid "Contact Email"
msgstr "Email del contatto"

#: app/Services/TransStrings.php:348
msgid "Contact Export Limit"
msgstr "Limite Esportazione Contatti"

#: app/Services/TransStrings.php:349
msgid "Contact Export Offset"
msgstr "Intervallo Esportazione Contatti"

#: app/Services/TransStrings.php:350
msgid "Contact Field"
msgstr "Campo Contatto"

#: app/Services/Funnel/Triggers/FluentFormSubmissionTrigger.php:85
msgid "Contact Field (CRM)"
msgstr "Campo Contatto (CRM)"

#: app/Services/TransStrings.php:1003
msgid "Contact Files & Attachments"
msgstr "File e Allegati del Contatto"

#: app/Services/TransStrings.php:1015
msgid "Contact has been added manually to this automation funnel"
msgstr ""
"Il contatto è stato aggiunto manualmente a questo funnel di automazione"

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:460
msgid "Contact has been created in FluentCRM. Contact ID: "
msgstr "Il contatto è stato creato in FluentCRM. ID Contatto: "

#: app/Http/Controllers/SubscriberController.php:348
msgid "contact has been successfully updated."
msgstr "Il contatto è stato aggiornato con successo."

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:511
msgid "Contact has been updated in FluentCRM. Contact ID: "
msgstr "Il contatto è stato aggiornato in FluentCRM. ID Contatto: "

#: app/Services/Helper.php:159
msgid "Contact ID"
msgstr "ID Contatto"

#: app/Services/TransStrings.php:351
msgid "Contact ID:"
msgstr "ID Contatto:"

#: app/Services/TransStrings.php:358
msgid "Contact is not associated with any companies"
msgstr "Il contatto non è associato ad alcuna azienda"

#: app/Services/TransStrings.php:1004
msgid "Contact Notes & Activities"
msgstr "Note e Attività del Contatto"

#: app/Services/TransStrings.php:359
msgid "Contact only see and manage the following list subscriptions"
msgstr ""
"Il contatto può vedere e gestire solo le seguenti iscrizioni alle liste"

#: app/Services/Funnel/Benchmarks/RemoveFromListBenchmark.php:69
msgid "contact removed from all of the selected lists"
msgstr "contatto rimosso da tutte le liste selezionate"

#: app/Services/Funnel/Benchmarks/RemoveFromListBenchmark.php:65
msgid "contact removed from any of the selected Lists"
msgstr "contatto rimosso da una delle liste selezionate"

#: app/Services/Helper.php:991 app/Hooks/Handlers/EventTrackingHandler.php:256
msgid "Contact Segment"
msgstr "Segmento del contatto"

#: app/Services/TransStrings.php:352
msgid "Contact Segment Lists"
msgstr "Liste Segmento Contatto"

#: app/Services/Stats.php:80
msgid "Contact Segments"
msgstr "Segmenti del Contatto"

#: app/Services/TransStrings.php:353
msgid "Contact Selections"
msgstr "Selezioni del Contatto"

#: app/Services/TransStrings.php:354
msgid "Contact Source"
msgstr "Fonte del Contatto"

#: app/Services/TransStrings.php:355
#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:174
msgid "Contact Tags"
msgstr "Tag del Contatto"

#: app/Services/PermissionManager.php:42
msgid "Contact Tags/List/Companies/Segment Create or Update"
msgstr "Creazione o Aggiornamento di Tag/Lista/Azienda/Segmento Contatto"

#: app/Services/PermissionManager.php:49
msgid "Contact Tags/List/Companies/Segment Delete"
msgstr "Eliminazione di Tag/Lista/Azienda/Segmento Contatto"

#: app/Services/TransStrings.php:356
msgid "Contact Type"
msgstr "Tipo di Contatto"

#: app/Http/Controllers/SubscriberController.php:1172
msgid "Contact Type has been updated for the selected subscribers"
msgstr "Il Tipo di Contatto è stato aggiornato per gli iscritti selezionati"

#: app/Services/Funnel/Actions/WaitTimeAction.php:68
msgid "Contact's Next Date of Birth"
msgstr "Prossima Data di Nascita del Contatto"

#: app/Services/TransStrings.php:362 app/Services/Helper.php:1811
#: app/Hooks/Handlers/AdminMenu.php:74 app/Hooks/Handlers/AdminMenu.php:75
#: app/Hooks/Handlers/AdminMenu.php:296 app/Hooks/Handlers/AdminMenu.php:1281
#: app/Hooks/Handlers/AdminMenu.php:1282
msgid "Contacts"
msgstr "Contatti"

#: app/Services/PermissionManager.php:21
msgid "Contacts Add/Update/Import"
msgstr "Aggiungi/Aggiorna/Importa Contatti"

#: app/Services/TransStrings.php:127
msgid ""
"Contacts already in the Automation will be skipped & only subscribed "
"contacts will be attached"
msgstr ""
"I contatti già presenti nell'automazione saranno ignorati e solo i contatti "
"iscritti saranno aggiunti"

#: app/Services/TransStrings.php:128
msgid "Contacts already in the company will be skipped"
msgstr "I contatti già associati all'azienda saranno ignorati"

#: app/Services/TransStrings.php:129
msgid "Contacts already in the email sequence will be skipped"
msgstr "I contatti già presenti nella sequenza email saranno ignorati"

#: app/Services/Funnel/BaseBenchMark.php:89
msgid ""
"Contacts can enter directly to this sequence point. If you enable this then "
"any contact meet with goal will enter in this goal point."
msgstr ""
"I contatti possono accedere direttamente a questo punto della sequenza. Se "
"abilitato, qualsiasi contatto che soddisfa l'obiettivo entrerà in questo "
"punto obiettivo."

#: app/Services/PermissionManager.php:28
msgid "Contacts Delete"
msgstr "Elimina Contatti"

#: app/Services/TransStrings.php:361
msgid "Contacts doesn't exist in the company will be skipped"
msgstr "I contatti non presenti nell'azienda saranno ignorati"

#: app/Services/PermissionManager.php:35
msgid "Contacts Export"
msgstr "Esporta Contatti"

#: app/Services/TransStrings.php:1052
msgid "contacts found based on your selection"
msgstr "contatti trovati in base alla tua selezione"

#: app/Services/TransStrings.php:363
msgid "Contacts has been attached with this campaign"
msgstr "I contatti sono stati associati a questa campagna"

#: app/Services/TransStrings.php:232
msgid "contacts has been processed"
msgstr "contatti elaborati"

#: app/Services/TransStrings.php:210
msgid "Contacts processed so far..."
msgstr "Contatti elaborati finora..."

#: app/Services/PermissionManager.php:16
msgid "Contacts Read"
msgstr "Leggi Contatti"

#: app/Services/TransStrings.php:845
msgid "Contacts that are duplicate"
msgstr "Contatti duplicati"

#: app/Services/TransStrings.php:364
msgid "Contacts that are invalid"
msgstr "Contatti non validi"

#: app/Services/TransStrings.php:365
msgid "Contains"
msgstr "Contiene"

#: app/Services/TransStrings.php:366
msgid "Content Background Color"
msgstr "Colore di Sfondo del Contenuto"

#: app/Services/TransStrings.php:367
msgid "Content Font Family"
msgstr "Famiglia di Font del Contenuto"

#: app/Services/TransStrings.php:368
msgid "Content Max Width (PX)"
msgstr "Larghezza Massima Contenuto (PX)"

#: app/Services/TransStrings.php:369
msgid "Continue"
msgstr "Continua"

#: app/Services/TransStrings.php:370
msgid "Continue [Map Data]"
msgstr "Continua [Mappa Dati]"

#: app/Services/TransStrings.php:371
msgid "Continue [Review and Import]"
msgstr "Continua [Revisione e Importazione]"

#: app/Services/TransStrings.php:211
msgid "Continue [Subject & Settings]"
msgstr "Continua [Oggetto e Impostazioni]"

#: app/Services/TransStrings.php:372
msgid "Continue to next step [conditions]"
msgstr "Continua al prossimo passaggio [condizioni]"

#: app/Services/TransStrings.php:212
msgid "Continue To Next Step [Recipients]"
msgstr "Continua al Prossimo Passaggio [Destinatari]"

#: app/Services/TransStrings.php:1044
msgid "Continue To Next Step [Review and Send]"
msgstr "Continua al Prossimo Passaggio [Revisione e Invio]"

#: app/Services/CrmMigrator/ConvertKitMigrator.php:31
msgid "ConvertKit API Key"
msgstr "Chiave API ConvertKit"

#: app/Services/CrmMigrator/ConvertKitMigrator.php:38
msgid "ConvertKit API Secret"
msgstr "Segreto API ConvertKit"

#: app/Hooks/Handlers/CountryNames.php:237
msgid "Cook Islands"
msgstr "Isole Cook"

#: app/Services/TransStrings.php:1698
msgid "Cool-Off Period"
msgstr "Periodo di Raffreddamento"

#: app/Services/TransStrings.php:373
msgid "Copied to your clipboard"
msgstr "Copiato negli appunti"

#: app/Services/TransStrings.php:1538
msgid ""
"Copy the keys in the right column and paste it into the app you want to use "
"for sending the POST request, so it will send the data into the contact "
"field you want."
msgstr ""
"Copia le chiavi nella colonna di destra e incollale nell'app che desideri "
"utilizzare per inviare la richiesta POST, così invierà i dati al campo di "
"contatto desiderato."

#: app/Services/TransStrings.php:1450
msgid "Copy the webhook URL you want to send your POST request to."
msgstr "Copia l'URL del webhook a cui vuoi inviare la tua richiesta POST."

#: app/Hooks/Handlers/CountryNames.php:241
msgid "Costa Rica"
msgstr "Costa Rica"

#: app/Models/Company.php:65 app/Models/Subscriber.php:729
#: app/Services/TransStrings.php:374 app/Services/Helper.php:166
#: app/Services/Helper.php:937 app/Hooks/Handlers/PrefFormHandler.php:53
#: app/Services/CrmMigrator/BaseMigrator.php:41
msgid "Country"
msgstr "Paese"

#: app/Services/Funnel/FunnelHelper.php:172
msgid "country"
msgstr "paese"

#: app/Services/TransStrings.php:375
msgid "Counts"
msgstr "Conteggi"

#: app/Services/TransStrings.php:377
msgid "Create"
msgstr "Crea"

#: app/Services/TransStrings.php:378
msgid "Create & Add Another"
msgstr "Crea e Aggiungi un Altro"

#: app/Services/Stats.php:166
msgid "Create a Campaign"
msgstr "Crea una Campagna"

#: app/Services/Stats.php:180 app/Services/TransStrings.php:397
msgid "Create a Form"
msgstr "Crea un Modulo"

#: app/Services/TransStrings.php:398
msgid "Create a New Automation"
msgstr "Crea una Nuova Automazione"

#: app/Services/TransStrings.php:399
msgid "Create a New Form"
msgstr "Crea un Nuovo Modulo"

#: app/Services/TransStrings.php:400
msgid "Create a new Smart link"
msgstr "Crea un nuovo link Smart"

#: app/Services/TransStrings.php:401
msgid "Create a note"
msgstr "Crea una nota"

#: app/Services/TransStrings.php:402
msgid "Create a recurring email broadcast"
msgstr "Crea una trasmissione email ricorrente"

#: app/Services/Stats.php:152
msgid "Create a Tag"
msgstr "Crea un Tag"

#: app/Services/Stats.php:173
msgid "Create an Automation"
msgstr "Crea un'Automazione"

#: app/Services/TransStrings.php:1496
msgid "Create an Automation Funnel"
msgstr "Crea un Funnel di Automazione"

#: app/Services/TransStrings.php:379
msgid "Create Campaign"
msgstr "Crea Campagna"

#: app/Services/TransStrings.php:380
msgid "Create Company"
msgstr "Crea Azienda"

#: app/Services/TransStrings.php:403
msgid "Create company"
msgstr "Crea azienda"

#: app/Services/TransStrings.php:404
msgid "Create company & Assign"
msgstr "Crea azienda e Assegna"

#: app/Services/TransStrings.php:381
msgid "Create Contact"
msgstr "Crea Contatto"

#: app/Services/TransStrings.php:405
msgid "Create contact & Assign"
msgstr "Crea contatto e Assegna"

#: app/Services/TransStrings.php:382
msgid "Create Custom Segment"
msgstr "Crea Segmento Personalizzato"

#: app/Services/TransStrings.php:122
msgid ""
"Create dynamic Segments of contacts by using dynamic contact properties and "
"filter your target audience"
msgstr ""
"Crea segmenti dinamici di contatti utilizzando proprietà dinamiche e filtra "
"il tuo pubblico target"

#: app/Services/TransStrings.php:1540
msgid ""
"Create dynamic Segments of contacts by using dynamic contact properties and "
"filter your target audience. Available in pro version of Fluent CRM"
msgstr ""
"Crea segmenti dinamici di contatti utilizzando proprietà dinamiche e filtra "
"il tuo pubblico target. Disponibile nella versione Pro di Fluent CRM"

#: app/Services/TransStrings.php:383
msgid "Create Email Template"
msgstr "Crea Modello Email"

#: app/Hooks/Handlers/AdminMenu.php:374
msgid "Create email templates to use as a starting point in your emails"
msgstr "Crea modelli email da usare come punto di partenza per le tue email"

#: app/Services/TransStrings.php:384
msgid "Create Form"
msgstr "Crea Modulo"

#: app/Hooks/Handlers/AdminMenu.php:368
msgid "Create Multiple Emails and Send in order as a Drip Email Campaign"
msgstr "Crea Email Multiple e Invia in ordine come una Campagna Email Drip"

#: app/Services/TransStrings.php:385
msgid "Create New"
msgstr "Crea Nuovo"

#: app/Services/TransStrings.php:386
msgid "Create New Campaign"
msgstr "Crea Nuova Campagna"

#: app/Services/TransStrings.php:406
msgid "Create new email sequence"
msgstr "Crea una nuova sequenza email"

#: app/Services/TransStrings.php:387
msgid "Create New Incoming Webhook"
msgstr "Crea Nuovo Webhook in Entrata"

#: app/Services/TransStrings.php:388
msgid "Create New Recurring Campaign"
msgstr "Crea Nuova Campagna Ricorrente"

#: app/Services/TransStrings.php:389
msgid "Create New Sequence"
msgstr "Crea Nuova Sequenza"

#: app/Services/TransStrings.php:390
msgid "Create New Template"
msgstr "Crea Nuovo Modello"

#: app/Services/TransStrings.php:407
msgid "Create or Update contacts from incoming Webhooks"
msgstr "Crea o Aggiorna contatti dai Webhook in Entrata"

#: app/Services/TransStrings.php:408
msgid "Create owner as contact if not exist"
msgstr "Crea il proprietario come contatto se non esiste"

#: app/Services/TransStrings.php:391
msgid "Create Recurring Campaign"
msgstr "Crea Campagna Ricorrente"

#: app/Services/TransStrings.php:1625
msgid ""
"Create REST API Key for specific CRM Manager to access and manage data over "
"API"
msgstr ""
"Crea una Chiave API REST per un Manager CRM specifico per accedere e gestire "
"i dati tramite API"

#: app/Services/TransStrings.php:392
msgid "Create Segment"
msgstr "Crea Segmento"

#: app/Services/TransStrings.php:393
msgid "Create Smart Link"
msgstr "Crea Link Smart"

#: app/Services/TransStrings.php:394
msgid "Create Template"
msgstr "Crea Modello"

#: app/Services/TransStrings.php:395
msgid "Create Webhook"
msgstr "Crea Webhook"

#: app/Services/TransStrings.php:213
msgid "Create Your First Email Campaign"
msgstr "Crea la tua Prima Campagna Email"

#: app/Services/TransStrings.php:123
msgid "Create Your First Email Sequence"
msgstr "Crea la tua Prima Sequenza Email"

#: app/Services/TransStrings.php:396
msgid "Create Your First Form"
msgstr "Crea il tuo Primo Modulo"

#: app/Services/TransStrings.php:414
msgid "Created"
msgstr "Creato"

#: app/Services/TransStrings.php:415 app/Services/Helper.php:978
msgid "Created At"
msgstr "Creato il"

#: app/Services/TransStrings.php:416
msgid "Created at"
msgstr "Creato il"

#: app/Services/TransStrings.php:201
#: app/Services/Funnel/Triggers/FluentFormSubmissionTrigger.php:30
#: app/Services/Funnel/Actions/ApplyCompanyAction.php:21
#: app/Services/Funnel/Actions/DetachTagAction.php:21
#: app/Services/Funnel/Actions/DetachListAction.php:21
#: app/Services/Funnel/Actions/ApplyTagAction.php:21
#: app/Services/Funnel/Actions/ApplyListAction.php:21
#: app/Services/Funnel/Actions/WaitTimeAction.php:22
#: app/Services/Funnel/Actions/DetachCompanyAction.php:21
msgid "CRM"
msgstr "CRM"

#. Description of the plugin
msgid "CRM and Email Newsletter Plugin for WordPress"
msgstr "Plugin CRM e Newsletter Email per WordPress"

#: app/Services/PermissionManager.php:11
msgid "CRM Dashboard"
msgstr "Dashboard CRM"

#: app/Services/TransStrings.php:202
msgid "CRM Managers"
msgstr "Manager CRM"

#: app/Services/TransStrings.php:203
msgid "CRM Managers - Roles and Permissions"
msgstr "Manager CRM - Ruoli e Permessi"

#: app/Hooks/Handlers/CountryNames.php:245
msgid "Croatia"
msgstr "Croazia"

#: app/Services/TransStrings.php:204
msgid "CRON Job Status"
msgstr "Stato del Job CRON"

#: app/Services/TransStrings.php:205
msgid "CSV"
msgstr "CSV"

#: app/Services/TransStrings.php:206
#: app/Http/Controllers/ImporterController.php:25
msgid "CSV File"
msgstr "File CSV"

#: app/Services/TransStrings.php:207
msgid "CSV Headers"
msgstr "Intestazioni CSV"

#: app/Hooks/Handlers/CountryNames.php:249
msgid "Cuba"
msgstr "Cuba"

#: app/Services/TransStrings.php:417
msgid "Cumulative"
msgstr "Cumulativo"

#: app/Hooks/Handlers/CountryNames.php:253
msgid "Cura&ccedil;ao"
msgstr "Curaçao"

#: app/Services/TransStrings.php:418
msgid "Current"
msgstr "Corrente"

#: app/Services/TransStrings.php:419
msgid "Current Date & Time (server):"
msgstr "Data e Ora Corrente (server):"

#: app/Services/TransStrings.php:214
msgid "Current Server Time (Based on your Site Settings)"
msgstr "Ora Corrente del Server (in base alle impostazioni del tuo sito)"

#: app/Services/TransStrings.php:420
msgid "Current Status"
msgstr "Stato Corrente"

#: app/Services/TransStrings.php:421
msgid "Custom"
msgstr "Personalizzato"

#: app/Models/CustomCompanyField.php:29
msgid "Custom Company Data"
msgstr "Dati Personalizzati dell'Azienda"

#: app/Services/TransStrings.php:422
msgid "Custom Contact Field"
msgstr "Campo Personalizzato del Contatto"

#: app/Services/TransStrings.php:423
msgid "Custom Contact Fields"
msgstr "Campi Personalizzati del Contatto"

#: app/Services/TransStrings.php:424
msgid "Custom Contact Properties"
msgstr "Proprietà Personalizzate del Contatto"

#: app/Services/Helper.php:204
msgid "Custom Date Format (Any PHP Date Format)"
msgstr "Formato Data Personalizzato (Qualsiasi Formato Data PHP)"

#: app/Models/CustomEmailCampaign.php:26
msgid "Custom Email"
msgstr "Email Personalizzata"

#: app/Services/Funnel/Actions/SendEmailAction.php:75
msgid "Custom Email Addresses"
msgstr "Indirizzi Email Personalizzati"

#: app/Services/TransStrings.php:425
msgid "Custom Email Footer Text"
msgstr "Testo Personalizzato del Piè di Pagina dell'Email"

#: app/Http/Controllers/SubscriberController.php:813
msgid "Custom Email has been successfully sent"
msgstr "Email Personalizzata inviata con successo"

#: app/Http/Controllers/SubscriberController.php:777
msgid "Custom Email to Contact"
msgstr "Email Personalizzata al Contatto"

#: app/Services/TransStrings.php:426
msgid "Custom Field Label"
msgstr "Etichetta Campo Personalizzato"

#: app/Services/TransStrings.php:427
msgid "Custom Field Slug"
msgstr "Slug Campo Personalizzato"

#: app/Services/TransStrings.php:428 app/Services/Helper.php:190
#: app/Services/Helper.php:1194
msgid "Custom Fields"
msgstr "Campi Personalizzati"

#: app/Services/TransStrings.php:429
msgid "Custom Fields Config"
msgstr "Configurazione Campi Personalizzati"

#: app/Models/CustomContactField.php:191 app/Services/TransStrings.php:430
msgid "Custom Profile Data"
msgstr "Dati Personalizzati del Profilo"

#: app/Functions/helpers.php:577
#: app/Http/Controllers/CampaignAnalyticsController.php:155
#: app/Http/Controllers/CampaignAnalyticsController.php:173
msgid "Customer"
msgstr "Cliente"

#: app/Services/TransStrings.php:1546
msgid "customer"
msgstr "cliente"

#: app/Hooks/CLI/Commands.php:144
msgid "Customer Counts"
msgstr "Conteggi Clienti"

#: app/Services/TransStrings.php:432
msgid "Customer Since"
msgstr "Cliente dal"

#: app/Services/TransStrings.php:433 app/Hooks/Handlers/PurchaseHistory.php:43
#: app/Hooks/Handlers/PurchaseHistory.php:76
#: app/Hooks/Handlers/PurchaseHistory.php:437
msgid "Customer Summary"
msgstr "Riepilogo Cliente"

#: app/Services/Helper.php:480
msgid "Cyan bluish gray"
msgstr "Ciano grigio bluastro"

#: app/Hooks/Handlers/CountryNames.php:257
msgid "Cyprus"
msgstr "Cipro"

#: app/Hooks/Handlers/CountryNames.php:261
msgid "Czechia (Czech Republic)"
msgstr "Repubblica Ceca (Cechia)"

#: app/Services/TransStrings.php:435
msgid "Daily"
msgstr "Giornaliero"

#: app/Services/TransStrings.php:436
msgid "Danger Zone"
msgstr "Zona di Pericolo"

#: app/Services/TransStrings.php:439 app/Hooks/Handlers/AdminMenu.php:65
#: app/Hooks/Handlers/AdminMenu.php:66 app/Hooks/Handlers/AdminMenu.php:288
#: app/Hooks/Handlers/AdminMenu.php:1274 app/Hooks/Handlers/AdminMenu.php:1275
msgid "Dashboard"
msgstr "Dashboard"

#: app/Services/TransStrings.php:440
msgid "Data Cleanup"
msgstr "Pulizia dei Dati"

#: app/Services/TransStrings.php:441
msgid "Data Sync is required for"
msgstr "È necessaria la Sincronizzazione dei Dati per"

#: app/Services/TransStrings.php:766
msgid "Data syncing has been completed. Please reload this page now"
msgstr "Sincronizzazione dati completata. Si prega di ricaricare la pagina ora"

#: app/Models/CustomContactField.php:75 app/Services/TransStrings.php:442
#: app/Http/Controllers/CampaignAnalyticsController.php:106
#: app/Http/Controllers/CampaignAnalyticsController.php:157
#: app/Http/Controllers/CampaignAnalyticsController.php:175
#: app/Hooks/Handlers/PurchaseHistory.php:132
#: app/Hooks/Handlers/PurchaseHistory.php:348
msgid "Date"
msgstr "Data"

#: app/Services/TransStrings.php:1547
msgid "date"
msgstr "data"

#: app/Services/TransStrings.php:443
msgid "Date & Time Format"
msgstr "Formato Data e Ora"

#: app/Services/TransStrings.php:444
msgid "Date Added"
msgstr "Data Aggiunta"

#: app/Models/CustomContactField.php:80
msgid "Date and Time"
msgstr "Data e Ora"

#: app/Services/TransStrings.php:446 app/Services/Helper.php:169
#: app/Services/Helper.php:968 app/Services/Helper.php:983
#: app/Hooks/Handlers/PrefFormHandler.php:47
#: app/Hooks/Handlers/PrefFormHandler.php:396
msgid "Date of Birth"
msgstr "Data di nascita"

#: app/Models/Subscriber.php:733
msgid "Date of Birth (Y-m-d Format only)"
msgstr "Data di Nascita (Solo Formato Y-m-d)"

#: app/Services/Helper.php:1866
msgid "Date Time"
msgstr "Data Ora"

#: app/Services/TransStrings.php:445
msgid "Date Time difference (EG: 2 hours ago)"
msgstr "Differenza di Data e Ora (Es: 2 ore fa)"

#: app/Services/TransStrings.php:1690
msgid "Date-Time"
msgstr "Data-Ora"

#: app/Services/TransStrings.php:448
msgid "Dates"
msgstr "Date"

#: app/Services/TransStrings.php:449
msgid "Day"
msgstr "Giorno"

#: app/Services/TransStrings.php:450
#: app/Services/Funnel/Actions/WaitTimeAction.php:114
msgid "Days"
msgstr "Giorni"

#: app/Services/TransStrings.php:1548
msgid "days"
msgstr "giorni"

#: app/Services/TransStrings.php:451
msgid "Days Ago"
msgstr "Giorni Fa"

#: app/Services/TransStrings.php:452
msgid "December"
msgstr "Dicembre"

#: app/Services/TransStrings.php:453
msgid "Dedicated API and SMTP connections"
msgstr "Connessioni API e SMTP dedicate"

#: app/Services/TransStrings.php:454
msgid "Default Companies"
msgstr "Aziende Predefinite"

#: app/Services/TransStrings.php:465
msgid "Default contact status (for new contacts)"
msgstr "Stato contatto predefinito (per nuovi contatti)"

#: app/Services/TransStrings.php:455
msgid "Default Content Color"
msgstr "Colore Contenuto Predefinito"

#: app/Services/TransStrings.php:466
msgid "Default footer has been disabled. Please include"
msgstr ""
"Il piè di pagina predefinito è stato disabilitato. Si prega di includere"

#: app/Services/TransStrings.php:456
msgid "Default From Settings"
msgstr "Impostazioni Predefinite Da"

#: app/Services/TransStrings.php:457
msgid "Default Headings Color"
msgstr "Colore Intestazioni Predefinito"

#: app/Services/TransStrings.php:458
msgid "Default Link Color"
msgstr "Colore Link Predefinito"

#: app/Services/TransStrings.php:459
msgid "Default List"
msgstr "Lista Predefinita"

#: app/Services/TransStrings.php:460
msgid "Default List to Contact (Optional)"
msgstr "Lista Predefinita per il Contatto (Opzionale)"

#: app/Services/TransStrings.php:461
msgid "Default Reply to Name (Optional)"
msgstr "Nome Predefinito per Rispondi a (Opzionale)"

#: app/Services/TransStrings.php:463
msgid "Default Tag for Contact (Optional)"
msgstr "Tag Predefinito per il Contatto (Opzionale)"

#: app/Services/TransStrings.php:462
msgid "Default Tag ID (optional)"
msgstr "ID Tag Predefinito (opzionale)"

#: app/Services/TransStrings.php:464
msgid "Default Tags"
msgstr "Tag Predefiniti"

#: app/Services/TransStrings.php:467
msgid "Delay"
msgstr "Ritardo"

#: app/Services/TransStrings.php:468
msgid "Delete"
msgstr "Elimina"

#: app/Services/TransStrings.php:469
msgid "Delete API Key"
msgstr "Elimina Chiave API"

#: app/Services/TransStrings.php:470
msgid "Delete Automation"
msgstr "Elimina Automazione"

#: app/Services/TransStrings.php:471
msgid "Delete Campaign"
msgstr "Elimina Campagna"

#: app/Services/TransStrings.php:472
msgid "Delete Companies"
msgstr "Elimina Aziende"

#: app/Services/TransStrings.php:481
msgid "Delete connected contact when a user get deleted"
msgstr "Elimina il contatto collegato quando un utente viene eliminato"

#: app/Services/TransStrings.php:473
msgid "Delete Contact"
msgstr "Elimina Contatto"

#: app/Services/TransStrings.php:474
msgid "Delete Contacts"
msgstr "Elimina Contatti"

#: app/Services/AutoSubscribe.php:226
msgid "Delete FluentCRM contact on WP User delete"
msgstr "Elimina contatto di FluentCRM quando viene eliminato un utente WP"

#: app/Services/TransStrings.php:475
msgid "Delete Funnels"
msgstr "Elimina Funnel"

#: app/Services/TransStrings.php:482
msgid "Delete older data more than?"
msgstr "Elimina i dati più vecchi di?"

#: app/Services/TransStrings.php:476
msgid "Delete Selected"
msgstr "Elimina Selezionati"

#: app/Services/TransStrings.php:477
msgid "Delete Selected Contacts"
msgstr "Elimina Contatti Selezionati"

#: app/Services/TransStrings.php:478
msgid "Delete Sequence"
msgstr "Elimina Sequenza"

#: app/Services/TransStrings.php:479
msgid "Delete Template"
msgstr "Elimina Modello"

#: app/Services/TransStrings.php:480
msgid "Delete Templates"
msgstr "Elimina Modelli"

#: app/Services/TransStrings.php:483
msgid "Delete this group"
msgstr "Elimina questo gruppo"

#: app/Services/TransStrings.php:486
msgid "Delivery Date"
msgstr "Data di Consegna"

#: app/Services/TransStrings.php:487
msgid "Delivery Date & time"
msgstr "Data e ora di consegna"

#: app/Services/TransStrings.php:488
msgid "Delivery time"
msgstr "Ora di consegna"

#: app/Hooks/Handlers/CountryNames.php:265
msgid "Denmark"
msgstr "Danimarca"

#: app/Services/TransStrings.php:489 app/Services/Helper.php:1881
msgid "Description"
msgstr "Descrizione"

#: app/Http/Controllers/SettingsController.php:127
msgid "Design Template"
msgstr "Modello di Design"

#: app/Services/TransStrings.php:490
msgid "Design Your Button"
msgstr "Personalizza il tuo Pulsante"

#: app/Services/TransStrings.php:491
msgid "Detach Tags and Lists"
msgstr "Scollega Tag e Liste"

#: fluent-crm.php:46
msgid "Developer Docs"
msgstr "Documentazione per Sviluppatori"

#: app/Http/Controllers/SettingsController.php:260
msgid ""
"Development mode is not activated. So you can not use this feature. You can "
"define \"FLUENTCRM_IS_DEV_FEATURES\" in your wp-config to enable this feature"
msgstr ""
"La modalità sviluppo non è attivata. Pertanto non è possibile utilizzare "
"questa funzione. È possibile definire \"FLUENTCRM_IS_DEV_FEATURES\" nel wp-"
"config per abilitare questa funzione."

#: app/Services/TransStrings.php:492
msgid "Disable Default Email Footer"
msgstr "Disabilita il piè di pagina predefinito dell'email"

#: app/Services/TransStrings.php:493
msgid "Disabled"
msgstr "Disabilitato"

#: app/Services/TransStrings.php:494
msgid "Divi Bloom Integration"
msgstr "Integrazione Divi Bloom"

#: app/Services/TransStrings.php:495
msgid "Divi Themes"
msgstr "Temi Divi"

#: app/Hooks/Handlers/CountryNames.php:269
msgid "Djibouti"
msgstr "Gibuti"

#: app/Services/TransStrings.php:497
msgid "Do another Action"
msgstr "Esegui un'altra azione"

#: app/Services/TransStrings.php:910
msgid "Do not import segment from untrusted sources."
msgstr "Non importare segmenti da fonti non attendibili."

#: app/Services/TransStrings.php:911
msgid "Do not import templates from untrusted sources."
msgstr "Non importare modelli da fonti non attendibili."

#: app/Services/AutoSubscribe.php:325
msgid "Do not show the checkbox if current user already in subscribed state"
msgstr ""
"Non mostrare la casella di controllo se l'utente corrente è già iscritto"

#: app/Services/AutoSubscribe.php:167
msgid "Do not show the checkbox if current user already subscribed state"
msgstr ""
"Non mostrare la casella di controllo se l'utente corrente è già iscritto"

#: app/Services/TransStrings.php:496
msgid "Do Not Trigger Automations (Tag & List related Events)"
msgstr "Non attivare automazioni (eventi relativi a Tag e Liste)"

#: app/Services/TransStrings.php:1376
msgid "Do you really want to leave? you have unsaved changes!"
msgstr "Sei sicuro di voler uscire? Ci sono modifiche non salvate!"

#: app/Services/TransStrings.php:695
msgid "Do you want to skip the data change?"
msgstr "Vuoi ignorare la modifica dei dati?"

#: app/Services/TransStrings.php:498
msgid "Do you want to update the companies data"
msgstr "Vuoi aggiornare i dati delle aziende?"

#: app/Services/TransStrings.php:1666
msgid "Do you want to update the subscribers data if it's already exist?"
msgstr "Vuoi aggiornare i dati degli iscritti se esistono già?"

#: fluent-crm.php:44
msgid "Docs & FAQs"
msgstr "Documentazione e FAQ"

#: app/Services/TransStrings.php:500
msgid "Documentation"
msgstr "Documentazione"

#: app/Services/TransStrings.php:1555
msgid "documentation"
msgstr "documentazione"

#: app/Services/Stats.php:110
msgid "Documentations"
msgstr "Documentazioni"

#: app/Services/TransStrings.php:1556
msgid "does not equal"
msgstr "non è uguale"

#: app/Services/TransStrings.php:501
msgid "Does not include (in any)"
msgstr "Non include (in nessuno)"

#: app/Services/TransStrings.php:1557
msgid "does not includes"
msgstr "non include"

#: app/Hooks/Handlers/CountryNames.php:273
msgid "Dominica"
msgstr "Dominica"

#: app/Hooks/Handlers/CountryNames.php:277
msgid "Dominican Republic"
msgstr "Repubblica Dominicana"

#: app/Services/TransStrings.php:1558
msgid "Don't have a license key?"
msgstr "Non hai una chiave di licenza?"

#: app/Services/AutoSubscribe.php:68 app/Services/AutoSubscribe.php:178
#: app/Services/AutoSubscribe.php:336 app/Services/TransStrings.php:502
msgid "Double Opt-In"
msgstr "Doppio Opt-In"

#: app/Services/TransStrings.php:503
msgid "Double Opt-in Email Settings Details"
msgstr "Dettagli impostazioni email doppio opt-in"

#: app/Services/TransStrings.php:504
msgid "Double Opt-in Settings"
msgstr "Impostazioni Doppio Opt-In"

#: app/Http/Controllers/SettingsController.php:237
msgid "Double Opt-in settings has been updated"
msgstr "Le impostazioni del doppio opt-in sono state aggiornate"

#: app/Http/Controllers/SubscriberController.php:770
msgid "Double OptIn email has been sent"
msgstr "L'email di doppio opt-in è stata inviata"

#: app/Http/Controllers/SubscriberController.php:938
msgid "Double optin sent to selected contacts"
msgstr "Doppio opt-in inviato ai contatti selezionati"

#: app/Http/Controllers/SettingsController.php:120
msgid "Double-Optin Email Body"
msgstr "Corpo dell'email di doppio opt-in"

#: app/Services/TransStrings.php:505
msgid "Download sample file"
msgstr "Scarica file di esempio"

#: app/Services/TransStrings.php:506
msgid "Draft"
msgstr "Bozza"

#: app/Services/TransStrings.php:1559
msgid "draft"
msgstr "bozza"

#: app/Services/TransStrings.php:507
msgid ""
"Draft emails was automatically created from your email configuration. You "
"can now review, edit and\n"
"                    schedule and send the campaign."
msgstr ""
"Le email in bozza sono state create automaticamente dalle tue impostazioni "
"email. Puoi ora rivedere, modificare, programmare e inviare la campagna."

#: app/Services/TransStrings.php:509
msgid "Drafts"
msgstr "Bozze"

#: app/Services/CrmMigrator/DripMigrator.php:37
msgid "Drip Account ID"
msgstr "ID Account Drip"

#: app/Services/CrmMigrator/DripMigrator.php:30
msgid "Drip API Token"
msgstr "Token API Drip"

#: app/Services/TransStrings.php:510
msgid "Drop"
msgstr "Rilascia"

#: app/Services/TransStrings.php:512
msgid "Drop file here or"
msgstr "Rilascia qui il file o"

#: app/Services/TransStrings.php:511
msgid "Drop JSON file here or"
msgstr "Rilascia qui il file JSON o"

#: app/Services/TransStrings.php:513
msgid "Duplicate"
msgstr "Duplica"

#: app/Services/Helper.php:203
msgid "Dynamic Date (ex: +2 days from now)"
msgstr "Data Dinamica (es: +2 giorni da oggi)"

#: app/Services/TransStrings.php:514
msgid "Dynamic Segments"
msgstr "Segmenti Dinamici"

#: app/Services/Helper.php:1337
msgid "Earnings (Pro Required)"
msgstr "Guadagni (Pro Richiesto)"

#: app/Services/Helper.php:454
msgid "Easy Digital Downloads"
msgstr "Easy Digital Downloads"

#: app/Services/TransStrings.php:516
msgid "ECOMMERCE INTEGRATION:"
msgstr "INTEGRAZIONE ECOMMERCE:"

#: app/Hooks/Handlers/CountryNames.php:281
msgid "Ecuador"
msgstr "Ecuador"

#: app/Services/Helper.php:1258
msgid "EDD"
msgstr "EDD"

#: app/Services/TransStrings.php:517
msgid "EDD Integration:"
msgstr "Integrazione EDD:"

#: app/Services/Helper.php:453
msgid "EDD Purchase History"
msgstr "Storico Acquisti EDD"

#: app/Services/TransStrings.php:521
msgid "Edit"
msgstr "Modifica"

#: app/Services/TransStrings.php:1561
msgid "edit"
msgstr "modifica"

#: app/Services/TransStrings.php:522
msgid "Edit Campaign"
msgstr "Modifica Campagna"

#: app/Services/TransStrings.php:523
msgid "Edit Configuration"
msgstr "Modifica Configurazione"

#: app/Services/TransStrings.php:524
msgid "Edit Connected Automation"
msgstr "Modifica Automazione Connessa"

#: app/Services/TransStrings.php:525
msgid "Edit Connection"
msgstr "Modifica Connessione"

#: app/Services/TransStrings.php:526
msgid "Edit Email Body"
msgstr "Modifica Corpo dell'Email"

#: app/Services/TransStrings.php:527
msgid "Edit Emails"
msgstr "Modifica Email"

#: app/Services/TransStrings.php:528
msgid "Edit Form"
msgstr "Modifica Modulo"

#: app/Services/TransStrings.php:529
msgid "Edit Funnel"
msgstr "Modifica Funnel"

#: app/Services/TransStrings.php:530
msgid "Edit Integration Settings"
msgstr "Modifica Impostazioni Integrazione"

#: app/Services/TransStrings.php:531
msgid "Edit Manager"
msgstr "Modifica Manager"

#: app/Services/TransStrings.php:532
msgid "Edit Note"
msgstr "Modifica Nota"

#: app/Services/TransStrings.php:533
msgid "Edit Primary Automation Trigger"
msgstr "Modifica Trigger Automazione Primaria"

#: app/Services/TransStrings.php:534
msgid "Edit Recipients"
msgstr "Modifica Destinatari"

#: app/Services/TransStrings.php:535
msgid "Edit Sequence and Settings"
msgstr "Modifica Sequenza e Impostazioni"

#: app/Services/TransStrings.php:536
msgid "Edit Smart link"
msgstr "Modifica Smart Link"

#: app/Services/TransStrings.php:537
msgid "Edit Subject"
msgstr "Modifica Oggetto"

#: app/Services/TransStrings.php:538
msgid "Edit Template"
msgstr "Modifica Modello"

#: app/Services/TransStrings.php:539
msgid "Edit The Form"
msgstr "Modifica il Modulo"

#: app/Services/TransStrings.php:540
msgid "Editor is loading. Please wait"
msgstr "L'editor è in fase di caricamento. Attendere prego"

#: app/Services/TransStrings.php:1562
msgid "eg: Active Contacts"
msgstr "es: Contatti Attivi"

#: app/Services/TransStrings.php:518
msgid "EG: Tag"
msgstr "ES: Tag"

#: app/Services/TransStrings.php:519
msgid "EG: User Type"
msgstr "ES: Tipo di Utente"

#: app/Services/TransStrings.php:1563
msgid "eg: Weekly Post Updated"
msgstr "es: Aggiornamento Settimanale Post"

#: app/Hooks/Handlers/CountryNames.php:285
msgid "Egypt"
msgstr "Egitto"

#: app/Hooks/Handlers/CountryNames.php:289
msgid "El Salvador"
msgstr "El Salvador"

#: app/Http/Controllers/SettingsController.php:363
msgid "Elastic Email"
msgstr "Elastic Email"

#: app/Http/Controllers/SettingsController.php:366
msgid "Elastic Email Bounce Handler Webhook URL"
msgstr "URL Webhook per Gestione dei Rimbalzi Elastic Email"

#: app/Services/TransStrings.php:541
msgid "Elementor Page Builder"
msgstr "Page Builder di Elementor"

#: app/Services/TransStrings.php:542
msgid "Elementor Pro Form Integration"
msgstr "Integrazione Form di Elementor Pro"

#: app/Models/Subscriber.php:722 app/Functions/helpers.php:613
#: app/Services/TransStrings.php:546 app/Services/Helper.php:908
#: app/Hooks/Handlers/PrefFormHandler.php:45
#: app/Hooks/Handlers/PrefFormHandler.php:362
#: app/Services/Funnel/FunnelHelper.php:138
#: app/Services/Funnel/Actions/SendEmailAction.php:27
msgid "Email"
msgstr "Email"

#: app/Services/TransStrings.php:547
#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:142
msgid "Email Address"
msgstr "Indirizzo Email"

#: app/Services/CrmMigrator/MailerLiteMigrator.php:22
#: app/Services/CrmMigrator/ConvertKitMigrator.php:22
msgid "Email Address and First name will be mapped automatically"
msgstr "Indirizzo email e nome verranno mappati automaticamente"

#: app/Services/TransStrings.php:548
msgid "Email Address for bi-monthly newsletter"
msgstr "Indirizzo email per la newsletter bimestrale"

#: app/Services/CrmMigrator/ActiveCampaignMigrator.php:27
#: app/Services/CrmMigrator/DripMigrator.php:26
msgid "Email and main contact fields will be mapped automatically"
msgstr ""
"L'email e i campi principali del contatto verranno mappati automaticamente"

#: app/Services/TransStrings.php:1670
msgid ""
"email as per your domain/SMTP settings. Email mismatch settings may not "
"deliver emails as expected"
msgstr ""
"email in base alle impostazioni del tuo dominio/SMTP. Impostazioni di "
"mismatch potrebbero non consentire la consegna delle email come previsto."

#: app/Services/TransStrings.php:549
#: app/Http/Controllers/SettingsController.php:121
msgid "Email Body"
msgstr "Corpo dell'Email"

#: app/Http/Controllers/SettingsController.php:218
msgid "Email Body is required"
msgstr "Il corpo dell'email è obbligatorio"

#: app/Http/Controllers/SettingsController.php:230
msgid "Email Body need to contains activation link"
msgstr "Il corpo dell'email deve contenere il link di attivazione"

#: app/Services/TransStrings.php:215
msgid "Email body successfully updated"
msgstr "Corpo dell'email aggiornato con successo"

#: app/Services/TransStrings.php:550
msgid "Email Campaign"
msgstr "Campagna Email"

#: app/Services/Stats.php:85 app/Services/TransStrings.php:551
msgid "Email Campaigns"
msgstr "Campagne Email"

#: app/Services/TransStrings.php:552
msgid "Email Click Logs"
msgstr "Log dei Click dell'Email"

#: app/Services/TransStrings.php:553
msgid "Email Click tracking is disabled via PHP Hook"
msgstr "Il monitoraggio dei click sull'email è disabilitato tramite PHP Hook"

#: app/Services/TransStrings.php:554
msgid "Email Clicks"
msgstr "Click Email"

#: app/Http/Controllers/SettingsController.php:555
#: app/Http/Controllers/SettingsController.php:564
msgid "Email clicks"
msgstr "Click sulle email"

#: app/Services/TransStrings.php:555
msgid "Email Configuration"
msgstr "Configurazione Email"

#: app/views/external/confirmation.php:8
msgid "Email Confirmation"
msgstr "Conferma Email"

#: app/Http/Controllers/SettingsController.php:128
msgid "Email Design Template for this double-optin email"
msgstr "Modello di Design Email per questa email di doppio opt-in"

#: app/Services/TransStrings.php:584
msgid "Email field is required"
msgstr "Il campo email è obbligatorio"

#: app/Services/TransStrings.php:556
msgid "Email Footer Settings"
msgstr "Impostazioni Piè di Pagina Email"

#: app/Services/TransStrings.php:557
msgid "Email Footer Text"
msgstr "Testo Piè di Pagina Email"

#: app/Services/TransStrings.php:558
msgid "Email Footer Type"
msgstr "Tipo di Piè di Pagina Email"

#: app/Services/TransStrings.php:559
msgid "Email From Name"
msgstr "Nome Mittente Email"

#: app/Services/TransStrings.php:1695
msgid "Email Headers"
msgstr "Intestazioni Email"

#: app/Services/TransStrings.php:560
msgid "Email History"
msgstr "Storico Email"

#: app/Services/TransStrings.php:561
#: app/Http/Controllers/SettingsController.php:546
msgid "Email History Logs"
msgstr "Log Storico Email"

#: app/Hooks/Handlers/ExternalPages.php:1021
msgid "Email is not valid. Please provide a valid email"
msgstr "Email non valida. Si prega di fornire un'email valida"

#: app/Services/TransStrings.php:562
msgid "Email Link Click Stats"
msgstr "Statistiche Click Link Email"

#: app/Services/TransStrings.php:563
msgid "Email Logging for better visibility"
msgstr "Log Email per una migliore visibilità"

#: app/views/external/manage_subscription_request_form.php:43
#: app/views/external/unsubscribe_request_form.php:43
msgid "Email me the link"
msgstr "Inviami il link via email"

#: app/Services/TransStrings.php:564
msgid "Email Open Logs"
msgstr "Log Aperture Email"

#: app/Services/TransStrings.php:565
msgid "Email Open Stats"
msgstr "Statistiche Aperture Email"

#: app/Services/TransStrings.php:566
msgid "Email Open tracking is disabled via PHP Hook"
msgstr "Il monitoraggio delle aperture email è disabilitato tramite PHP Hook"

#: app/Services/TransStrings.php:585
msgid "Email opened"
msgstr "Email aperta"

#: app/Http/Controllers/SettingsController.php:115
msgid "Email Pre Header"
msgstr "Pre-intestazione Email"

#: app/Services/TransStrings.php:567
msgid "Email Pre-Header"
msgstr "Pre-intestazione email"

#: app/Services/Libs/Parser/ShortcodeParser.php:239
msgid "Email Preference"
msgstr "Preferenza email"

#: app/Services/TransStrings.php:568
msgid "Email Preference Settings"
msgstr "Impostazioni preferenza email"

#: app/Services/TransStrings.php:569
msgid "Email Preview"
msgstr "Anteprima email"

#: app/Services/TransStrings.php:570
msgid "Email Preview (for better view, please send test email)"
msgstr "Anteprima email (per una visione migliore, invia una email di prova)"

#: app/Services/TransStrings.php:571
msgid "Email Routing based on the sender email address"
msgstr "Instradamento email basato sull'indirizzo email del mittente"

#: app/Services/TransStrings.php:572
msgid "Email Sending Stats"
msgstr "Statistiche invio email"

#: app/Services/TransStrings.php:573
#: app/Http/Controllers/CampaignController.php:522
msgid "Email Sending will be started soon"
msgstr "L'invio dell'email inizierà presto"

#: app/Services/TransStrings.php:574
msgid "Email Sent"
msgstr "Email inviata"

#: app/Services/TransStrings.php:575
msgid "Email Sent %"
msgstr "Percentuale email inviata"

#: app/Models/FunnelCampaign.php:91
msgid "Email Sent From Funnel"
msgstr "Email inviata dal Funnel"

#: app/Services/Funnel/Actions/SendEmailAction.php:238
msgid "Email Sent From Funnel: "
msgstr "Email inviata dal Funnel: "

#: app/Services/Helper.php:1097
msgid "Email Sequence Activity -"
msgstr "Attività sequenza email -"

#: app/Services/Stats.php:90 app/Services/TransStrings.php:576
#: app/Hooks/Handlers/AdminMenu.php:134 app/Hooks/Handlers/AdminMenu.php:135
#: app/Hooks/Handlers/AdminMenu.php:366
msgid "Email Sequences"
msgstr "Sequenze email"

#: app/Services/TransStrings.php:577
msgid "Email Service Provider Settings"
msgstr "Impostazioni del provider di servizi email"

#: app/Services/TransStrings.php:578
msgid "Email Settings"
msgstr "Impostazioni email"

#: app/Services/TransStrings.php:579
msgid "Email Styling Settings"
msgstr "Impostazioni di stile email"

#: app/Services/TransStrings.php:580
msgid "Email Styling Settings & Footer Settings"
msgstr "Impostazioni di stile email e impostazioni del piè di pagina"

#: app/Services/TransStrings.php:581
#: app/Http/Controllers/SettingsController.php:109
msgid "Email Subject"
msgstr "Oggetto email"

#: app/Services/TransStrings.php:582
msgid "Email Subject & Details"
msgstr "Oggetto email e dettagli"

#: app/Http/Controllers/SettingsController.php:217
msgid "Email Subject is required"
msgstr "L'oggetto dell'email è obbligatorio"

#: app/Services/Stats.php:46 app/Services/TransStrings.php:583
#: app/Hooks/Handlers/AdminMenu.php:143 app/Hooks/Handlers/AdminMenu.php:144
#: app/Hooks/Handlers/AdminMenu.php:372
msgid "Email Templates"
msgstr "Modelli email"

#: app/Services/PermissionManager.php:68
msgid "Email Templates Manage"
msgstr "Gestisci modelli email"

#: app/Services/TransStrings.php:595 app/Services/Helper.php:104
#: app/Hooks/Handlers/AdminMenu.php:346 app/Hooks/Handlers/AdminMenu.php:1288
#: app/Hooks/Handlers/AdminMenu.php:1289
msgid "Emails"
msgstr "Email"

#: app/Services/TransStrings.php:596
msgid "Emails Analytics"
msgstr "Analisi email"

#: app/Services/TransStrings.php:593
msgid ""
"Emails are being scheduled and sending in the background at the same time"
msgstr ""
"Le email sono in fase di programmazione e invio in background "
"contemporaneamente"

#: app/Services/TransStrings.php:598
msgid "Emails are currently on processing"
msgstr "Le email sono attualmente in fase di elaborazione"

#: app/Services/TransStrings.php:1417
msgid "Emails are sending at this moment"
msgstr "Le email vengono inviate in questo momento"

#: app/Services/PermissionManager.php:73
msgid "Emails Delete"
msgstr "Elimina email"

#: app/Services/TransStrings.php:1005
msgid "Emails from different campaigns and automations"
msgstr "Email provenienti da diverse campagne e automazioni"

#: app/Services/PermissionManager.php:56
msgid "Emails Read"
msgstr "Leggi email"

#: app/Services/Stats.php:32
msgid "Emails Sent"
msgstr "Email inviate"

#: app/Services/TransStrings.php:597
msgid "Emails Stats"
msgstr "Statistiche email"

#: app/Services/TransStrings.php:1115
msgid "Emails will be sent at scheduled time."
msgstr "Le email saranno inviate all'orario programmato."

#: app/Services/TransStrings.php:600
msgid ""
"Emails will be sent automatically regardless of any conditions. If you want "
"to send conditionally"
msgstr ""
"Le email saranno inviate automaticamente indipendentemente dalle condizioni. "
"Se desideri inviare condizionatamente"

#: app/Services/TransStrings.php:599
msgid "Emails will be sent now."
msgstr "Le email saranno inviate ora."

#: app/Services/PermissionManager.php:61
msgid "Emails Write/Send"
msgstr "Scrivi/Invia email"

#: app/Models/Company.php:66
msgid "Employees Number"
msgstr "Numero di dipendenti"

#: app/Services/TransStrings.php:601
msgid "Empty"
msgstr "Vuoto"

#: app/Services/TransStrings.php:544
msgid "Enable A/B testing for email subjects"
msgstr "Abilita il test A/B per gli oggetti email"

#: app/Services/TransStrings.php:1700
msgid "Enable Abandoned Cart Tracking for WooCommerce"
msgstr "Abilita il tracciamento dei carrelli abbandonati per WooCommerce"

#: app/Services/AutoSubscribe.php:313
msgid "Enable auto checked status on checkout page checkbox"
msgstr ""
"Abilita lo stato selezionato automaticamente nella casella di controllo "
"della pagina di checkout"

#: app/Services/AutoSubscribe.php:155
msgid "Enable auto checked status on Comment Form subscription"
msgstr ""
"Abilita lo stato selezionato automaticamente per la sottoscrizione nel "
"modulo di commento"

#: app/Services/TransStrings.php:602
msgid "Enable Campaign Archive Frontend Feature"
msgstr "Abilita la funzionalità dell'archivio campagne nel frontend"

#: app/Services/TransStrings.php:603
msgid "Enable Company Module for Contacts"
msgstr "Abilita il modulo aziendale per i contatti"

#: app/Services/AutoSubscribe.php:114
msgid ""
"Enable Create new contacts in FluentCRM when a visitor add a comment in your "
"comment form"
msgstr ""
"Abilita la creazione di nuovi contatti in FluentCRM quando un visitatore "
"aggiunge un commento nel modulo di commento"

#: app/Services/AutoSubscribe.php:34
msgid ""
"Enable Create new contacts in FluentCRM when users register in WordPress"
msgstr ""
"Abilita la creazione di nuovi contatti in FluentCRM quando gli utenti si "
"registrano in WordPress"

#: app/Services/TransStrings.php:1497
msgid "Enable Double Opt-in Confirmation"
msgstr "Abilita la conferma con doppio opt-in"

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:204
msgid "Enable Double opt-in for new contacts"
msgstr "Abilita il doppio opt-in per i nuovi contatti"

#: app/Services/AutoSubscribe.php:69 app/Services/AutoSubscribe.php:179
#: app/Services/AutoSubscribe.php:337 app/Services/TransStrings.php:604
msgid "Enable Double-Optin Email Confirmation"
msgstr "Abilita la conferma email con doppio opt-in"

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:183
msgid "Enable Dynamic Tag Selection"
msgstr "Abilita la selezione dinamica dei tag"

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:210
msgid ""
"Enable Force Subscribe if contact is not in subscribed status (Existing "
"contact only)"
msgstr ""
"Abilita la sottoscrizione forzata se il contatto non è in stato di "
"sottoscrizione (solo contatti esistenti)"

#: app/Services/TransStrings.php:605
msgid "Enable Preference Form Shortcode"
msgstr "Abilita lo shortcode del modulo preferenze"

#: app/Services/TransStrings.php:606
msgid "Enable Quick Contact Navigation"
msgstr "Abilita la navigazione rapida tra i contatti"

#: app/Services/RoleBasedTagging.php:51
msgid "Enable Role Based Tag Mapping"
msgstr "Abilita la mappatura dei tag basata sul ruolo"

#: app/Services/TransStrings.php:607
msgid "Enable Specific Days Only"
msgstr "Abilita solo giorni specifici"

#: app/Services/AutoSubscribe.php:272
msgid "Enable Subscription Checkbox to WooCommerce Checkout Page"
msgstr ""
"Abilita la casella di sottoscrizione alla pagina di checkout di WooCommerce"

#: app/Services/AutoSubscribe.php:219
msgid "Enable Sync between WP User Data and Fluent CRM Contact Data"
msgstr ""
"Abilita la sincronizzazione tra dati utente WP e dati contatto di Fluent CRM"

#: app/Services/TransStrings.php:1681
msgid "Enable System Log for debugging"
msgstr "Abilita il log di sistema per il debug"

#: app/Http/Controllers/SettingsController.php:175
msgid "Enable Tag based double optin redirect"
msgstr "Abilita il reindirizzamento basato sui tag con doppio opt-in"

#: app/Services/TransStrings.php:1030
msgid ""
"Enable this feature if you want Quick navigation bar for navigating next or "
"previous contacts easily from single contact screen"
msgstr ""
"Abilita questa funzione se desideri una barra di navigazione rapida per "
"spostarti facilmente tra i contatti dalla schermata di un singolo contatto"

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:265
msgid "Enable This feed"
msgstr "Abilita questo feed"

#: app/Services/TransStrings.php:608
msgid "Enabled"
msgstr "Abilitato"

#: app/Services/TransStrings.php:610
msgid "End date"
msgstr "Data di fine"

#: app/Services/TransStrings.php:609
msgid "End Range"
msgstr "Intervallo di fine"

#: app/Services/TransStrings.php:611
msgid "Ends With"
msgstr "Termina con"

#: app/Services/TransStrings.php:1566
msgid "ends with"
msgstr "termina con"

#: app/Services/Helper.php:1400 app/Services/Helper.php:1456
msgid "Enrollment Categories (Pro Required)"
msgstr "Categorie di iscrizione (Richiesto Pro)"

#: app/Services/Helper.php:1382 app/Services/Helper.php:1439
msgid "Enrollment Courses (Pro Required)"
msgstr "Corsi di iscrizione (Richiesto Pro)"

#: app/Services/Helper.php:1390
msgid "Enrollment Groups (Pro Required)"
msgstr "Gruppi di iscrizione (Richiesto Pro)"

#: app/Services/Helper.php:1447
msgid "Enrollment Memberships (Pro Required)"
msgstr "Abbonamenti di iscrizione (Richiesto Pro)"

#: app/Services/Helper.php:1409 app/Services/Helper.php:1465
msgid "Enrollment Tags (Pro Required)"
msgstr "Tag di iscrizione (Richiesto Pro)"

#: app/Services/Reporting.php:138
msgid "Entrance"
msgstr "Ingresso"

#: app/Services/TransStrings.php:612
msgid "Equal"
msgstr "Uguale"

#: app/Services/TransStrings.php:1567
msgid "equal"
msgstr "uguale"

#: app/Hooks/Handlers/CountryNames.php:293
msgid "Equatorial Guinea"
msgstr "Guinea Equatoriale"

#: app/Hooks/Handlers/CountryNames.php:297
msgid "Eritrea"
msgstr "Eritrea"

#: app/Services/TransStrings.php:613
msgid "Error"
msgstr "Errore"

#: app/Services/TransStrings.php:614
msgid "Error Details"
msgstr "Dettagli errore"

#: app/Services/TransStrings.php:615
msgid "Errors"
msgstr "Errori"

#: app/Services/TransStrings.php:616
msgid "Errors Found"
msgstr "Errori trovati"

#: app/Services/TransStrings.php:1502
msgid "Estimated Contacts based on your selections:"
msgstr "Contatti stimati in base alle tue selezioni:"

#: app/Services/TransStrings.php:617
msgid "Estimated Contacts:"
msgstr "Contatti stimati:"

#: app/Hooks/Handlers/CountryNames.php:301
msgid "Estonia"
msgstr "Estonia"

#: app/Hooks/Handlers/CountryNames.php:305
msgid "Ethiopia"
msgstr "Etiopia"

#: app/Http/Controllers/SubscriberController.php:1423
msgid "Event has been tracked"
msgstr "L'evento è stato tracciato"

#: app/Hooks/Handlers/EventTrackingHandler.php:293
msgid "Event Key"
msgstr "Chiave evento"

#: app/Services/TransStrings.php:618
msgid "Event Name"
msgstr "Nome evento"

#: app/Hooks/Handlers/EventTrackingHandler.php:308
msgid "Event Occurrence Count"
msgstr "Numero di occorrenze evento"

#: app/Hooks/Handlers/EventTrackingHandler.php:360
msgid "Event Title"
msgstr "Titolo evento"

#: app/Http/Controllers/SubscriberController.php:1388
msgid "Event Tracker is not enabled"
msgstr "Il tracciamento eventi non è abilitato"

#: app/Hooks/Handlers/EventTrackingHandler.php:30
#: app/Hooks/Handlers/EventTrackingHandler.php:217
#: app/Hooks/Handlers/EventTrackingHandler.php:235
#: app/Hooks/Handlers/EventTrackingHandler.php:251
#, fuzzy
#| msgid "Add Event Tracking"
msgid "Event Tracking"
msgstr "Tracciamento eventi"

#: app/Services/TransStrings.php:619
msgid ""
"Event Tracking is a flexible feature that helps you collect data on a wide "
"variety of contact behavior. You can create an event for any activity "
"programmatically or from different automations. Then you can use those "
"events to filter contacts or use on automation conditional logics."
msgstr ""
"Il tracciamento eventi è una funzione flessibile che ti aiuta a raccogliere "
"dati su una vasta gamma di comportamenti dei contatti. Puoi creare un evento "
"per qualsiasi attività programmaticamente o tramite diverse automazioni. "
"Successivamente, puoi utilizzare questi eventi per filtrare i contatti o "
"nelle logiche condizionali delle automazioni."

#: app/Hooks/Handlers/EventTrackingHandler.php:333
msgid "Event Value"
msgstr "Valore evento"

#: app/Services/TransStrings.php:620
msgid "Every Friday"
msgstr "Ogni venerdì"

#: app/Services/TransStrings.php:621
msgid "Every Monday"
msgstr "Ogni lunedì"

#: app/Services/TransStrings.php:622
msgid "Every Saturday"
msgstr "Ogni sabato"

#: app/Services/TransStrings.php:623
msgid "Every Sunday"
msgstr "Ogni domenica"

#: app/Services/TransStrings.php:624
msgid "Every Thursday"
msgstr "Ogni giovedì"

#: app/Services/TransStrings.php:625
msgid "Every Tuesday"
msgstr "Ogni martedì"

#: app/Services/TransStrings.php:626
msgid "Every Wednesday"
msgstr "Ogni mercoledì"

#: app/Services/TransStrings.php:627
msgid "Everything is ready."
msgstr "Tutto è pronto."

#: app/Services/TransStrings.php:628
msgid "Example:"
msgstr "Esempio:"

#: app/Services/TransStrings.php:629
msgid "Excluded Contacts"
msgstr "Contatti esclusi"

#: app/Services/TransStrings.php:632
msgid "Experimental Features"
msgstr "Funzionalità sperimentali"

#: app/Services/TransStrings.php:633
msgid "Experimental Features Settings"
msgstr "Impostazioni funzionalità sperimentali"

#: app/Services/TransStrings.php:634
msgid "Experimental Settings"
msgstr "Impostazioni sperimentali"

#: app/Services/TransStrings.php:635
msgid "Export"
msgstr "Esporta"

#: app/Services/TransStrings.php:636
msgid "Export Contacts"
msgstr "Esporta contatti"

#: app/Services/TransStrings.php:637
msgid "Export Subscriber"
msgstr "Esporta iscritto"

#: app/Services/TransStrings.php:638
msgid "Export Template"
msgstr "Esporta modello"

#: app/Services/TransStrings.php:639
msgid "Exporting... Please wait"
msgstr "Esportazione in corso... Attendere prego"

#: app/Services/TransStrings.php:92
msgid ""
"Extend FluentCRM functionalities and supercharge your email marketing and "
"automation"
msgstr ""
"Estendi le funzionalità di FluentCRM e potenzia il tuo email marketing e le "
"automazioni"

#: app/Services/TransStrings.php:640
msgid "Facebook Company Page URL"
msgstr "URL pagina aziendale di Facebook"

#: app/Services/TransStrings.php:641
msgid "Facebook Page Url"
msgstr "URL pagina Facebook"

#: app/Functions/helpers.php:625
msgid "Facebook Post"
msgstr "Post di Facebook"

#: app/Models/Company.php:68
msgid "Facebook URL"
msgstr "URL Facebook"

#: app/Services/TransStrings.php:1568 app/Hooks/Handlers/ExternalPages.php:99
msgid "failed"
msgstr "fallito"

#: app/Services/TransStrings.php:1495
msgid "failed to send to recipients. Try Resending"
msgstr "invio ai destinatari fallito. Riprova a inviare"

#: app/Hooks/Handlers/CountryNames.php:309
msgid "Falkland Islands"
msgstr "Isole Falkland"

#: app/Hooks/Handlers/CountryNames.php:313
msgid "Faroe Islands"
msgstr "Isole Faroe"

#: app/Services/TransStrings.php:642
msgid "Features of Fluent SMTP Plugin"
msgstr "Caratteristiche del plugin Fluent SMTP"

#: app/Services/TransStrings.php:644
msgid "February"
msgstr "Febbraio"

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:117
msgid "Feed Name"
msgstr "Nome feed"

#: app/Functions/helpers.php:623
msgid "Feedback"
msgstr "Feedback"

#: app/Services/TransStrings.php:645
msgid "Feel free to add a note regarding this URL"
msgstr "Aggiungi una nota su questo URL, se lo desideri"

#: app/Services/TransStrings.php:646
msgid "Fetching License Information Please wait"
msgstr "Recupero delle informazioni sulla licenza in corso, attendere prego"

#: app/Services/TransStrings.php:647
msgid "Field"
msgstr "Campo"

#: app/Services/TransStrings.php:648
msgid "Field Type"
msgstr "Tipo di campo"

#: app/Services/TransStrings.php:649
msgid "Field Value Options"
msgstr "Opzioni valore campo"

#: app/Http/Controllers/CustomContactFieldsController.php:35
#: app/Http/Controllers/CompanyController.php:730
msgid "Fields saved successfully!"
msgstr "Campi salvati con successo!"

#: app/Hooks/Handlers/CountryNames.php:317
msgid "Fiji"
msgstr "Figi"

#: app/Services/TransStrings.php:650
msgid "Filter"
msgstr "Filtro"

#: app/Services/TransStrings.php:651
msgid "Filter By Property and find appropriate Contacts"
msgstr "Filtra per proprietà e trova i contatti appropriati"

#: app/Services/TransStrings.php:653
msgid "Filter by status"
msgstr "Filtra per stato"

#: app/Services/TransStrings.php:652
msgid "Filter Subscribers"
msgstr "Filtra iscritti"

#: app/Services/TransStrings.php:654
msgid "Filtered by"
msgstr "Filtrato per"

#: app/Hooks/Handlers/AdminMenu.php:380
msgid "Find all the emails that are being sent or scheduled by FluentCRM"
msgstr "Trova tutte le email inviate o programmate da FluentCRM"

#: app/Hooks/Handlers/CountryNames.php:321
msgid "Finland"
msgstr "Finlandia"

#: app/Services/Helper.php:1376 app/Services/Helper.php:1433
msgid "First Enrollment Date (Pro Required)"
msgstr "Data di prima iscrizione (Richiesto Pro)"

#: app/Models/Subscriber.php:719 app/Services/TransStrings.php:656
#: app/Services/Helper.php:156 app/Services/Helper.php:898
#: app/Hooks/Handlers/PrefFormHandler.php:42
#: app/Hooks/Handlers/PrefFormHandler.php:321
#: app/views/external/manage_subscription_form.php:14
#: app/views/external/manage_subscription_form.php:16
#: app/Services/CrmMigrator/BaseMigrator.php:25
#: app/Services/Funnel/FunnelHelper.php:130
#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:148
msgid "First Name"
msgstr "Nome"

#: app/Services/Helper.php:1227 app/Services/Helper.php:1281
msgid "First Order Date (Pro Required)"
msgstr "Data del primo ordine (Richiesto Pro)"

#: app/Services/TransStrings.php:1639
msgid "first. If you still can't find the answer"
msgstr "prima. Se non riesci ancora a trovare la risposta"

#: app/Http/Controllers/SetupController.php:89
#: app/Http/Controllers/DocsController.php:89
msgid "Fluent Connect"
msgstr "Fluent Connect"

#: config/app.php:6
#, fuzzy
#| msgid "Fluent CRM"
msgid "Fluent Crm"
msgstr "Fluent CRM"

#: app/Hooks/Handlers/Cleanup.php:192
msgid "Fluent CRM Data"
msgstr "Dati di Fluent CRM"

#: app/Http/Controllers/SetupController.php:159
#: app/Http/Controllers/DocsController.php:62
#: app/Hooks/Handlers/FormSubmissions.php:23
msgid "Fluent Forms"
msgstr "Fluent Forms"

#: app/Http/Controllers/SetupController.php:57
msgid "Fluent Forms has been installed and activated"
msgstr "Fluent Forms è stato installato e attivato"

#: app/Services/TransStrings.php:657
#, fuzzy
#| msgid "FluentCRM Integration"
msgid "Fluent Forms Integration"
msgstr "Integrazione Fluent Forms"

#: app/Services/TransStrings.php:1620
msgid ""
"Fluent Forms is a free form plugin for WordPress that is integrated with "
"FluentCRM"
msgstr ""
"Fluent Forms è un plugin di moduli gratuito per WordPress integrato con "
"FluentCRM"

#: app/Services/TransStrings.php:1571
msgid ""
"Fluent Forms is fast and very light-weight and works perfectly with "
"FluentCRM."
msgstr ""
"Fluent Forms è rapido, molto leggero e funziona perfettamente con FluentCRM."

#: app/Services/TransStrings.php:672 app/Services/TransStrings.php:680
msgid "Fluent Forms that are connected with your CRM"
msgstr "Fluent Forms collegati al tuo CRM"

#: app/Http/Controllers/DocsController.php:71
msgid "Fluent SMTP"
msgstr "Fluent SMTP"

#: app/Http/Controllers/SetupController.php:112
#: app/Http/Controllers/DocsController.php:80
msgid "Fluent Support"
msgstr "Fluent Support"

#: app/Http/Controllers/SetupController.php:120
msgid "Fluent Support plugin has been installed and activated successfully"
msgstr "Il plugin Fluent Support è stato installato e attivato con successo"

#: app/Http/Controllers/SetupController.php:97
msgid "FluentConnect plugin has been installed and activated successfully"
msgstr "Il plugin FluentConnect è stato installato e attivato con successo"

#: app/Hooks/Handlers/AdminMenu.php:54 app/Hooks/Handlers/AdminMenu.php:55
#: app/Services/ExternalIntegrations/BricksBuilderIntegration.php:21
#: app/views/emails/plain/Template.php:95
#: app/views/emails/classic/Template.php:93
#: app/views/emails/raw_classic/Template.php:37
#: app/views/emails/simple/Template.php:92
#: app/views/emails/web_preview/Template.php:14
#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:27
msgid "FluentCRM"
msgstr "FluentCRM"

#. Name of the plugin
msgid "FluentCRM - Marketing Automation For WordPress"
msgstr "FluentCRM - Automazione del marketing per WordPress"

#: app/views/admin/setup_wizard.php:6
msgid "FluentCRM - Setup Wizard"
msgstr "FluentCRM - Procedura guidata di configurazione"

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:354
msgid "FluentCRM API called skipped because no valid email available"
msgstr ""
"Chiamata all'API di FluentCRM saltata perché non è disponibile un'email "
"valida"

#: app/Hooks/Handlers/Cleanup.php:168
msgid "FluentCRM Data"
msgstr "Dati FluentCRM"

#: app/Services/TransStrings.php:658
#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:137
#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:167
msgid "FluentCRM Field"
msgstr "Campo FluentCRM"

#: app/Services/TransStrings.php:1570
msgid ""
"FluentCRM has integrations with all of your favorite plugins. This settings "
"will be available based on your plugin installed. These integrations are "
"only available on the"
msgstr ""
"FluentCRM è integrato con tutti i tuoi plugin preferiti. Queste impostazioni "
"saranno disponibili in base ai plugin installati. Queste integrazioni sono "
"disponibili solo su"

#: app/Http/Controllers/FormsController.php:196
msgid "FluentCRM Integration Feed"
msgstr "Feed di integrazione FluentCRM"

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:54
msgid ""
"FluentCRM is not configured yet! Please configure your FluentCRM api first"
msgstr "FluentCRM non è ancora configurato! Configura prima l'API di FluentCRM"

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:124
msgid "FluentCRM List"
msgstr "Lista FluentCRM"

#: app/Services/ExternalIntegrations/BricksBuilderIntegration.php:67
msgid "FluentCRM Lists"
msgstr "Liste FluentCRM"

#: app/Services/ExternalIntegrations/FluentForm/FluentFormInit.php:53
msgid "FluentCRM Profile"
msgstr "Profilo FluentCRM"

#: app/Services/ExternalIntegrations/BricksBuilderIntegration.php:47
msgid "FluentCRM Tags"
msgstr "Tag FluentCRM"

#: app/Services/Stats.php:124 app/Services/TransStrings.php:661
#: app/Http/Controllers/SetupController.php:170
msgid "FluentSMTP"
msgstr "FluentSMTP"

#: app/Services/TransStrings.php:1269
msgid ""
"FluentSMTP - The ultimate SMTP & Email Service Connection Plugin for "
"WordPress"
msgstr ""
"FluentSMTP - Il plugin definitivo di connessione SMTP e email per WordPress"

#: app/Http/Controllers/SetupController.php:74
msgid "FluentSMTP plugin has been installed and activated successfully"
msgstr "Il plugin FluentSMTP è stato installato e attivato con successo"

#: app/Services/TransStrings.php:662
msgid "FluentSMTP plugin has successfully installed"
msgstr "Il plugin FluentSMTP è stato installato correttamente"

#: app/Services/TransStrings.php:1569
msgid ""
"FluentSMTP requires to configure properly. Please configure FluentSMTP to "
"make your email delivery works."
msgstr ""
"FluentSMTP richiede una corretta configurazione. Configura FluentSMTP per "
"garantire il funzionamento dell'invio email."

#: app/Services/TransStrings.php:1573
msgid "follow this tutorial"
msgstr "segui questo tutorial"

#: app/Services/TransStrings.php:667
msgid "Font Family"
msgstr "Famiglia di caratteri"

#: app/Services/TransStrings.php:668
msgid "Font Size"
msgstr "Dimensione carattere"

#: app/Services/TransStrings.php:669
msgid "Font Style"
msgstr "Stile del carattere"

#: app/Services/TransStrings.php:670
msgid "Footer Text Color"
msgstr "Colore del testo del piè di pagina"

#: app/Services/TransStrings.php:1594
msgid ""
"For FluentCRM users, we built a well-optimized SMTP/Amazon SES plugin. It "
"will help you manage all your WordPress website emails, including FluentCRM "
"emails."
msgstr ""
"Per gli utenti di FluentCRM, abbiamo sviluppato un plugin SMTP/Amazon SES "
"ben ottimizzato. Aiuterà a gestire tutte le email del sito WordPress, "
"incluse quelle di FluentCRM."

#: app/Services/TransStrings.php:1574
msgid "for migrating from"
msgstr "per la migrazione da"

#: app/Services/TransStrings.php:1575
msgid "for sending your WordPress emails. This section is for you"
msgstr "per l'invio delle email di WordPress. Questa sezione è per te"

#: app/Services/TransStrings.php:671
msgid "For Step by Step instruction please"
msgstr "Per istruzioni dettagliate passo a passo, per favore"

#: app/Services/TransStrings.php:1576
msgid ""
"Force update contact status. This will update all imported contact status "
"regardless their previous status"
msgstr ""
"Forza l'aggiornamento dello stato del contatto. Questo aggiornerà tutti gli "
"stati dei contatti importati indipendentemente dallo stato precedente"

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:138
#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:168
#: app/Services/Funnel/Triggers/FluentFormSubmissionTrigger.php:86
msgid "Form Field"
msgstr "Campo del modulo"

#: app/Http/Controllers/FormsController.php:236
msgid "Form has been created"
msgstr "Modulo creato con successo"

#: app/Services/TransStrings.php:676
msgid "Form INTEGRATION:"
msgstr "INTEGRAZIONE Modulo:"

#: app/Services/TransStrings.php:1006
msgid ""
"Form Submission from Fluent Forms will be shown here. Currently, Fluent "
"Forms is not installed"
msgstr ""
"Qui verranno mostrati gli invii dei moduli da Fluent Forms. Attualmente, "
"Fluent Forms non è installato"

#: app/Services/Helper.php:120
msgid "Form Submissions"
msgstr "Invii dei moduli"

#: app/Hooks/Handlers/FormSubmissions.php:22
msgid "Form Submissions (Fluent Forms)"
msgstr "Invii dei moduli (Fluent Forms)"

#: app/Services/TransStrings.php:677
msgid "Form Submissions from"
msgstr "Invii dei moduli da"

#: app/Services/TransStrings.php:678
msgid "Form Title"
msgstr "Titolo del modulo"

#: app/Services/Stats.php:95 app/Services/TransStrings.php:679
#: app/Hooks/Handlers/AdminMenu.php:154 app/Hooks/Handlers/AdminMenu.php:155
#: app/Hooks/Handlers/AdminMenu.php:390 app/Hooks/Handlers/AdminMenu.php:1321
#: app/Hooks/Handlers/AdminMenu.php:1322
msgid "Forms"
msgstr "Moduli"

#: app/Hooks/Handlers/CountryNames.php:325
msgid "France"
msgstr "Francia"

#: app/Hooks/Handlers/CountryNames.php:329
msgid "French Guiana"
msgstr "Guyana Francese"

#: app/Hooks/Handlers/CountryNames.php:333
msgid "French Polynesia"
msgstr "Polinesia Francese"

#: app/Hooks/Handlers/CountryNames.php:337
msgid "French Southern Territories"
msgstr "Territori Francesi del Sud"

#: app/Services/TransStrings.php:682
msgid "Friday"
msgstr "Venerdì"

#: app/Services/TransStrings.php:683
msgid "Friendly Name for identification"
msgstr "Nome descrittivo per identificazione"

#: app/Services/TransStrings.php:1691
msgid "From"
msgstr "Da"

#: app/Services/TransStrings.php:684
msgid "From Email"
msgstr "Email del mittente"

#: app/Services/TransStrings.php:685
msgid "From Email Address"
msgstr "Indirizzo email del mittente"

#: app/Services/TransStrings.php:686
#, fuzzy
#| msgid "Group Name"
msgid "From Name"
msgstr "Nome del mittente"

#: app/Models/Subscriber.php:721 app/Services/Helper.php:154
#: app/Services/CrmMigrator/BaseMigrator.php:27
#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:156
msgid "Full Name"
msgstr "Nome completo"

#: app/Http/Controllers/CampaignAnalyticsController.php:75
#, php-format
msgctxt "full name"
msgid "%1$s %2$s"
msgstr "%1$s %2$s"

#: app/Hooks/Handlers/AdminMenu.php:854
msgid "Full Size"
msgstr "Dimensione completa"

#: app/Services/TransStrings.php:689
msgid "Funnel Builder will be available soon"
msgstr "Il costruttore di Funnel sarà presto disponibile"

#: app/Models/FunnelCampaign.php:30
msgid "Funnel Campaign Holder"
msgstr "Titolare della campagna di Funnel"

#: app/Services/TransStrings.php:694
msgid "Funnel data changed"
msgstr "Dati del Funnel modificati"

#: app/Http/Controllers/FunnelController.php:963
msgid "Funnel has been created from template"
msgstr "Il Funnel è stato creato dal modello"

#: app/Http/Controllers/FunnelController.php:140
msgid "Funnel has been created. Please configure now"
msgstr "Il Funnel è stato creato. Configuralo ora"

#: app/Http/Controllers/FunnelController.php:157
msgid "Funnel has been deleted"
msgstr "Il Funnel è stato eliminato"

#: app/Http/Controllers/FunnelController.php:609
msgid "Funnel has been successfully cloned"
msgstr "Il Funnel è stato clonato con successo"

#: app/Http/Controllers/FunnelController.php:622
#, fuzzy
#| msgid "Templates has been successfully imported"
msgid "Funnel has been successfully imported"
msgstr "Il Funnel è stato importato con successo"

#: app/Services/TransStrings.php:690
msgid "Funnel Items"
msgstr "Elementi del Funnel"

#: app/Services/TransStrings.php:691
msgid "Funnel Name"
msgstr "Nome del Funnel"

#: app/Services/TransStrings.php:692
msgid "Funnel Report"
msgstr "Report del Funnel"

#: app/Http/Controllers/FunnelController.php:824
msgid "Funnel status need to be published"
msgstr "Lo stato del Funnel deve essere pubblicato"

#: app/Http/Controllers/FunnelController.php:195
msgid "Funnel Trigger has been successfully updated"
msgstr "Il trigger del Funnel è stato aggiornato con successo"

#: app/Hooks/Handlers/CountryNames.php:341
msgid "Gabon"
msgstr "Gabon"

#: app/Hooks/Handlers/CountryNames.php:345
msgid "Gambia"
msgstr "Gambia"

#: app/Services/Helper.php:197
msgid "General"
msgstr "Generale"

#: app/Services/Helper.php:894
msgid "General Properties"
msgstr "Proprietà generali"

#: app/Services/TransStrings.php:697
msgid "General Settings"
msgstr "Impostazioni generali"

#: app/Hooks/Handlers/CountryNames.php:349
msgid "Georgia"
msgstr "Georgia"

#: app/Hooks/Handlers/CountryNames.php:353
msgid "Germany"
msgstr "Germania"

#: app/Services/TransStrings.php:704
msgid "Get Advanced Report of your contacts, emails, clicks and revenue"
msgstr "Ottieni un report avanzato sui tuoi contatti, email, clic e entrate"

#: app/views/external/manage_subscription_request_form.php:32
msgid "Get Email Subscription Management Link"
msgstr "Ottieni il link per la gestione dell'abbonamento email"

#: app/Services/TransStrings.php:698
msgid "Get FluentCRM Email Pro"
msgstr "Ottieni FluentCRM Email Pro"

#: app/Services/TransStrings.php:699
#, fuzzy
#| msgid "FluentCRM Pro"
msgid "Get FluentCRM Pro"
msgstr "Ottieni FluentCRM Pro"

#: app/Services/TransStrings.php:700
msgid "Get FluentCRM Pro Now"
msgstr "Ottieni subito FluentCRM Pro"

#: app/Services/TransStrings.php:705
msgid ""
"Get improved features and faster fixes by sharing non-sensitive data via "
"usage tracking that shows us\n"
"                how FluentCRM is used. No personal data is tracked or stored."
msgstr ""
"Ottieni funzionalità migliorate e correzioni più rapide condividendo dati "
"non sensibili tramite il monitoraggio dell'uso, che ci mostra come viene "
"utilizzato FluentCRM. Nessun dato personale viene monitorato o archiviato."

#: app/Services/TransStrings.php:665
msgid ""
"Get more related contact info with FluentCRM Pro. You can get more info "
"about the contact like their purchase history, membership, course info and "
"more."
msgstr ""
"Ottieni ulteriori informazioni sui contatti con FluentCRM Pro. Puoi ottenere "
"informazioni aggiuntive come la cronologia acquisti, l'appartenenza a gruppi,"
" le informazioni sui corsi e altro."

#: app/Services/TransStrings.php:702
msgid "Get more related contact info with Pro"
msgstr "Ottieni più informazioni sui contatti con Pro"

#: app/Services/TransStrings.php:701
msgid "Get Pre-Defined Contact Lists"
msgstr "Ottieni liste di contatti predefinite"

#: app/Hooks/Handlers/AdminMenu.php:423
msgid "Get Pro"
msgstr "Ottieni Pro"

#: app/Services/TransStrings.php:1418
msgid "Get started with adding an email to this sequence"
msgstr "Inizia aggiungendo un'email a questa sequenza"

#: fluent-crm.php:45
msgid "Get Support"
msgstr "Ottieni supporto"

#: app/views/external/unsubscribe_request_form.php:32
msgid "Get Unsubscribe Link"
msgstr "Ottieni il link di disiscrizione"

#: app/Services/TransStrings.php:707
msgid "Getting Started"
msgstr "Iniziare"

#: app/Services/TransStrings.php:708
msgid "Getting Started With Audience"
msgstr "Iniziare con il pubblico"

#: app/Hooks/Handlers/CountryNames.php:357
msgid "Ghana"
msgstr "Ghana"

#: app/Hooks/Handlers/CountryNames.php:361
msgid "Gibraltar"
msgstr "Gibilterra"

#: app/Services/TransStrings.php:709
msgid "Global Email Settings"
msgstr "Impostazioni email globali"

#: app/Services/TransStrings.php:710
msgid "Go Back"
msgstr "Torna indietro"

#: app/Services/TransStrings.php:712
msgid "Go to company record"
msgstr "Vai alla scheda aziendale"

#: app/Services/TransStrings.php:711
msgid "Go to CRM Dashboard"
msgstr "Vai alla dashboard CRM"

#: app/Services/TransStrings.php:713
msgid "Goals"
msgstr "Obiettivi"

#: app/Services/TransStrings.php:714
msgid "Goals / Benchmark"
msgstr "Obiettivi / Riferimento"

#: app/Services/TransStrings.php:715
msgid "Goto FluentSMTP Settings"
msgstr "Vai alle impostazioni di FluentSMTP"

#: app/Services/TransStrings.php:716
msgid "Great!"
msgstr "Ottimo!"

#: app/Services/TransStrings.php:717
msgid "Greater Than"
msgstr "Maggiore di"

#: app/Hooks/Handlers/CountryNames.php:365
msgid "Greece"
msgstr "Grecia"

#: app/Hooks/Handlers/CountryNames.php:369
msgid "Greenland"
msgstr "Groenlandia"

#: app/Hooks/Handlers/CountryNames.php:373
msgid "Grenada"
msgstr "Grenada"

#: app/Services/TransStrings.php:718
msgid "Grow Your Audience"
msgstr "Fai crescere il tuo pubblico"

#: app/Services/TransStrings.php:673
msgid "Grow Your Audience by Opt-in Forms"
msgstr "Fai crescere il tuo pubblico con moduli di opt-in"

#: app/Hooks/Handlers/CountryNames.php:377
msgid "Guadeloupe"
msgstr "Guadalupa"

#: app/Hooks/Handlers/CountryNames.php:381
msgid "Guam"
msgstr "Guam"

#: app/Hooks/Handlers/CountryNames.php:385
msgid "Guatemala"
msgstr "Guatemala"

#: app/Hooks/Handlers/CountryNames.php:389
msgid "Guernsey"
msgstr "Guernsey"

#: app/Hooks/Handlers/CountryNames.php:393
msgid "Guinea"
msgstr "Guinea"

#: app/Hooks/Handlers/CountryNames.php:397
msgid "Guinea-Bissau"
msgstr "Guinea-Bissau"

#: app/Services/TransStrings.php:720
msgid "Gutenberg / WordPress Editor Conditional Blocks"
msgstr "Blocchi condizionali di Gutenberg / Editor di WordPress"

#: app/Hooks/Handlers/CountryNames.php:401
msgid "Guyana"
msgstr "Guyana"

#: app/Hooks/Handlers/CountryNames.php:405
msgid "Haiti"
msgstr "Haiti"

#: app/Http/Controllers/SubscriberController.php:838
msgid "Handled could not be found."
msgstr "Non è stato possibile trovare l'elemento gestito."

#: app/Services/TransStrings.php:1577
msgid "has been completed."
msgstr "è stato completato."

#: app/Services/TransStrings.php:721
msgid "Have a new license Key?"
msgstr "Hai una nuova chiave di licenza?"

#: app/Services/TransStrings.php:722
msgid "Headings Font Family"
msgstr "Famiglia di caratteri per i titoli"

#: app/Hooks/Handlers/CountryNames.php:409
msgid "Heard Island and McDonald Islands"
msgstr "Isola Heard e Isole McDonald"

#: app/Hooks/Handlers/AdminMenu.php:219 app/Hooks/Handlers/AdminMenu.php:220
#: app/Hooks/Handlers/AdminMenu.php:1356 app/Hooks/Handlers/AdminMenu.php:1357
msgid "Help"
msgstr "Aiuto"

#: app/Services/TransStrings.php:723
msgid "Help us to make FluentCRM better"
msgstr "Aiutaci a migliorare FluentCRM"

#: app/Hooks/Handlers/CountryNames.php:413
msgid "Honduras"
msgstr "Honduras"

#: app/Hooks/Handlers/CountryNames.php:417
msgid "Hong Kong"
msgstr "Hong Kong"

#: app/Services/TransStrings.php:725
#: app/Services/Funnel/Actions/WaitTimeAction.php:118
msgid "Hours"
msgstr "Ore"

#: app/Services/TransStrings.php:726
msgid "How can we help you?"
msgstr "Come possiamo aiutarti?"

#: app/Services/TransStrings.php:1531
msgid "How many contacts clicked at least one of the links in the email"
msgstr "Quanti contatti hanno cliccato almeno uno dei link nell'email"

#: app/Services/TransStrings.php:727
msgid "How often you want to send this email?"
msgstr "Quanto spesso vuoi inviare questa email?"

#. URI of the plugin
#. Author URI of the plugin
msgid "https://fluentcrm.com"
msgstr "https://fluentcrm.com"

#: app/Hooks/Handlers/CountryNames.php:421
msgid "Hungary"
msgstr "Ungheria"

#: app/Hooks/Handlers/ExternalPages.php:488
msgid "I never signed up for this email list"
msgstr "Non mi sono mai iscritto a questa lista di email"

#: app/Hooks/Handlers/ExternalPages.php:487
msgid "I no longer want to receive these emails"
msgstr "Non voglio più ricevere queste email"

#: app/Hooks/Handlers/CountryNames.php:425
msgid "Iceland"
msgstr "Islanda"

#: app/Services/TransStrings.php:728
#: app/Http/Controllers/CampaignAnalyticsController.php:103
#: app/Http/Controllers/SubscriberController.php:735
msgid "ID"
msgstr "ID"

#: app/Services/TransStrings.php:730
msgid "Identifiers"
msgstr "Identificatori"

#: app/Services/TransStrings.php:731
msgid "Identify the paid keywords"
msgstr "Identifica le parole chiave a pagamento"

#: app/Services/Funnel/Triggers/UserRegistrationTrigger.php:75
msgid "If Contact Already Exist?"
msgstr "Se il contatto esiste già?"

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:157
msgid ""
"If First Name & Last Name is not available full name will be used to get "
"first name and last name"
msgstr ""
"Se Nome e Cognome non sono disponibili, verrà utilizzato il nome completo "
"per ricavare il nome e il cognome"

#: app/Services/TransStrings.php:1578
msgid "if it's already exist?"
msgstr "se esiste già?"

#: app/Services/Funnel/Actions/WaitTimeAction.php:201
msgid ""
"If no value is found in the contact's custom field or past date then it will "
"wait only 1 minute by default"
msgstr ""
"Se non viene trovato un valore nel campo personalizzato del contatto o una "
"data passata, attenderà solo 1 minuto per impostazione predefinita"

#: app/Services/Funnel/Actions/SendEmailAction.php:108
msgid ""
"If schedule date is past in the runtime then email will be sent immediately"
msgstr ""
"Se la data programmata è già passata, l'email verrà inviata immediatamente"

#: app/Services/TransStrings.php:160
msgid ""
"If you add any new step to your automation after some contacts completed the "
"automation, you can resume the newly added steps to the existing completed "
"contacts."
msgstr ""
"Se aggiungi un nuovo passaggio all'automazione dopo che alcuni contatti "
"l'hanno completata, puoi far riprendere i passaggi appena aggiunti ai "
"contatti che hanno già completato l'automazione."

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:244
msgid ""
"If you check any of the events then this feed will only run to the selected "
"events"
msgstr ""
"Se selezioni uno degli eventi, questo feed verrà eseguito solo per gli "
"eventi selezionati"

#: app/Services/TransStrings.php:1565
msgid ""
"If you check this email will be sent without footer contents including "
"unsubscribe link. Please use smartcode to include unsubscribe link"
msgstr ""
"Se selezioni questa opzione, l'email verrà inviata senza i contenuti del piè "
"di pagina, incluso il link per annullare l'iscrizione. Usa lo smartcode per "
"includere il link di annullamento iscrizione"

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:212
msgid ""
"If you enable this then contact will forcefully subscribed no matter in "
"which status that contact had"
msgstr ""
"Se abiliti questa opzione, il contatto sarà iscritto forzatamente "
"indipendentemente dallo stato precedente"

#: app/Services/Funnel/Triggers/FluentFormSubmissionTrigger.php:181
msgid ""
"If you enable this then this will run only once per customer otherwise, It "
"will delete the existing automation flow and start new"
msgstr ""
"Se abiliti questa opzione, verrà eseguito solo una volta per cliente; "
"altrimenti eliminerà il flusso di automazione esistente e ne avvierà uno "
"nuovo"

#: app/Services/TransStrings.php:320
msgid ""
"If you enable this, FluentCRM will try to fetch the company logo "
"automatically from the given website url."
msgstr ""
"Se abiliti questa opzione, FluentCRM proverà a recuperare automaticamente il "
"logo dell'azienda dall'URL del sito web fornito."

#: app/Services/TransStrings.php:322
msgid ""
"If you enable this, then company module will be enabled and then you can add "
"companies and assign contacts to companies."
msgstr ""
"Se abiliti questa opzione, il modulo aziendale verrà abilitato e potrai "
"aggiungere aziende e assegnare contatti alle aziende."

#: app/Services/TransStrings.php:664
msgid ""
"If you enable this, then FluentCRM will hide the default WP Admin Sidebar "
"and Replace with FluentCRM's own navigation for better focus."
msgstr ""
"Se abiliti questa opzione, FluentCRM nasconderà la barra laterale "
"predefinita di WP Admin e la sostituirà con la propria navigazione per una "
"migliore concentrazione."

#: app/Services/TransStrings.php:681
msgid ""
"If you need to create and connect more advanced forms please use Fluent "
"Forms."
msgstr ""
"Se hai bisogno di creare e collegare moduli più avanzati, usa Fluent Forms."

#: app/Services/Funnel/BaseBenchMark.php:81
msgid ""
"If you select [Optional Point] it will work as an Optional Trigger otherwise,"
" it will wait for full-fill this action"
msgstr ""
"Se selezioni [Punto opzionale], funzionerà come trigger opzionale; "
"altrimenti, attenderà il completamento di questa azione"

#: app/Services/TransStrings.php:520
msgid ""
"If you select a time range then FluentCRM schedule the email to that time "
"range"
msgstr ""
"Se selezioni un intervallo di tempo, FluentCRM programmerà l'email in "
"quell'intervallo"

#: app/Services/TransStrings.php:216
msgid ""
"If you think everything is alright. You can broadcast/Schedule the emails "
"now."
msgstr ""
"Se pensi che sia tutto a posto, puoi ora trasmettere/programmare le email."

#: app/Services/TransStrings.php:732
msgid "If you use"
msgstr "Se usi"

#: app/Services/TransStrings.php:1668
msgid ""
"If you use GridPane, Please follow the following tutorial or contact with "
"your hosting provider"
msgstr ""
"Se utilizzi GridPane, segui il seguente tutorial o contatta il tuo provider "
"di hosting"

#: app/Services/TransStrings.php:733
msgid "If you want to disable auto-syncing"
msgstr "Se desideri disabilitare la sincronizzazione automatica"

#: app/Services/TransStrings.php:591
msgid ""
"If you want to showcase your email campaigns in the frontend page with a "
"shortcode then you may enable this features"
msgstr ""
"Se desideri mostrare le tue campagne email in una pagina frontend tramite "
"shortcode, puoi abilitare questa funzionalità"

#: app/Services/TransStrings.php:734
msgid "Immediately"
msgstr "Immediatamente"

#: app/Services/TransStrings.php:735
msgid "Import"
msgstr "Importa"

#: app/Http/Controllers/ImporterController.php:300
#, php-format
msgid ""
"Import %s members by member groups and member types then segment by "
"associate tags. This is a pro feature. Please upgrade to activate this "
"feature"
msgstr ""
"Importa %s membri per gruppi e tipi di membri e segmenta con i tag associati."
" Questa è una funzione pro. Aggiorna per attivarla"

#: app/Services/TransStrings.php:736
msgid "Import Automation Funnel"
msgstr "Importa Funnel di Automazione"

#: app/Services/TransStrings.php:738
msgid "Import Companies"
msgstr "Importa aziende"

#: app/Services/Stats.php:159 app/Services/TransStrings.php:739
msgid "Import Contacts"
msgstr "Importa contatti"

#: app/Services/TransStrings.php:737
#, fuzzy
#| msgid "Import by"
msgid "Import CSV"
msgstr "Importa CSV"

#: app/Services/TransStrings.php:740
msgid "Import From Other Providers"
msgstr "Importa da altri provider"

#: app/Services/TransStrings.php:741
msgid "Import Funnel From JSON File"
msgstr "Importa Funnel da file JSON"

#: app/Services/TransStrings.php:745
msgid "Import has been completed, You may close this modal now"
msgstr "L'importazione è stata completata, puoi chiudere questa finestra ora"

#: app/Http/Controllers/ImporterController.php:246
msgid ""
"Import LearnDash students by course and groups then segment by associate "
"tags. This is a pro feature. Please upgrade to activate this feature"
msgstr ""
"Importa studenti di LearnDash per corso e gruppi, poi segmentali con i tag "
"associati. Questa è una funzione pro. Aggiorna per attivarla"

#: app/Http/Controllers/ImporterController.php:309
msgid ""
"Import LearnPress students by course then segment by associate tags. This is "
"a pro feature. Please upgrade to activate this feature"
msgstr ""
"Importa studenti di LearnPress per corso e segmentali con i tag associati. "
"Questa è una funzione pro. Aggiorna per attivarla"

#: app/Http/Controllers/ImporterController.php:237
msgid ""
"Import LifterLMS students by course and groups then segment by associate "
"tags. This is a pro feature. Please upgrade to activate this feature"
msgstr ""
"Importa studenti di LifterLMS per corso e gruppi, poi segmentali con i tag "
"associati. Questa è una funzione pro. Aggiorna per attivarla"

#: app/Services/TransStrings.php:746
msgid "Import only active subscribers from"
msgstr "Importa solo iscritti attivi da"

#: app/Http/Controllers/ImporterController.php:264
msgid ""
"Import Paid Membership Pro members by membership levels then segment by "
"associate tags. This is a pro feature. Please upgrade to activate this "
"feature"
msgstr ""
"Importa membri di Paid Membership Pro per livelli di iscrizione e segmentali "
"con i tag associati. Questa è una funzione pro. Aggiorna per attivarla"

#: app/Http/Controllers/ImporterController.php:282
msgid ""
"Import Restrict Content Pro members by membership levels then segment by "
"associate tags. This is a pro feature. Please upgrade to activate this "
"feature"
msgstr ""
"Importa membri di Restrict Content Pro per livelli di iscrizione e "
"segmentali con i tag associati. Questa è una funzione pro. Aggiorna per "
"attivarla"

#: app/Services/TransStrings.php:742
msgid "Import Subscribers"
msgstr "Importa iscritti"

#: app/Services/TransStrings.php:743
msgid "Import Template"
msgstr "Importa modello"

#: app/Http/Controllers/ImporterController.php:255
msgid ""
"Import TutorLMS students by course then segment by associate tags. This is a "
"pro feature. Please upgrade to activate this feature"
msgstr ""
"Importa studenti di TutorLMS per corso e segmentali con i tag associati. "
"Questa è una funzione pro. Aggiorna per attivarla"

#: app/Services/TransStrings.php:744
#: app/Http/Controllers/ImporterController.php:158
msgid "Import Users Now"
msgstr "Importa utenti ora"

#: app/Http/Controllers/ImporterController.php:273
msgid ""
"Import Wishlist members by membership levels then segment by associate tags. "
"This is a pro feature. Please upgrade to activate this feature"
msgstr ""
"Importa membri di Wishlist per livelli di iscrizione e segmentali con i tag "
"associati. Questa è una funzione pro. Aggiorna per attivarla"

#: app/Services/TransStrings.php:747
msgid ""
"Import your exported dynamic segment JSON file here. Please upload your JSON "
"file."
msgstr ""
"Importa qui il tuo file JSON del segmento dinamico esportato. Carica il file "
"JSON."

#: app/Services/TransStrings.php:748
msgid ""
"Import your exported email template JSON file here. Please upload your JSON "
"file."
msgstr ""
"Importa qui il tuo file JSON del modello email esportato. Carica il file "
"JSON."

#: app/Services/CrmMigrator/MailerLiteMigrator.php:271
#: app/Services/CrmMigrator/ConvertKitMigrator.php:257
msgid "Importer is running now. "
msgstr "L'importazione è in corso."

#: app/Services/TransStrings.php:846
msgid "Importing Contacts from your CSV. Please Do not close this modal"
msgstr ""
"Importazione dei contatti dal tuo CSV in corso. Non chiudere questa finestra."

#: app/Services/TransStrings.php:1580
msgid ""
"Importing Funnel from JSON file is a pro feature. Please download and active "
"FluentCRM Pro First"
msgstr ""
"L'importazione di Funnel da file JSON è una funzione pro. Scarica e attiva "
"FluentCRM Pro."

#: app/Services/TransStrings.php:749
msgid "Importing now..."
msgstr "Importazione in corso..."

#: app/Services/TransStrings.php:751
msgid "In"
msgstr "In"

#: app/Services/TransStrings.php:752
msgid "In Details Reporting"
msgstr "Report dettagliato"

#: app/Services/TransStrings.php:1581
msgid "in the date"
msgstr "nella data"

#: app/Services/TransStrings.php:1582
msgid "in your"
msgstr "nel tuo"

#: app/Services/TransStrings.php:1583
msgid "in your email body for compliance"
msgstr "nel corpo dell'email per conformità"

#: app/Services/Helper.php:1330
msgid "Inactive"
msgstr "Inattivo"

#: app/Services/TransStrings.php:753
msgid "Include Contact Info in Personal Data export by WP"
msgstr ""
"Includi informazioni di contatto nell'esportazione dei dati personali di WP"

#: app/Services/TransStrings.php:754
msgid "Included Contacts"
msgstr "Contatti inclusi"

#: app/Services/TransStrings.php:1584
msgid "includes"
msgstr "include"

#: app/Services/TransStrings.php:1585
msgid "includes all of"
msgstr "include tutto di"

#: app/Services/TransStrings.php:1586
#: app/Services/ExternalIntegrations/BricksBuilderIntegration.php:52
#: app/Services/ExternalIntegrations/BricksBuilderIntegration.php:72
msgid "includes in"
msgstr "include in"

#: app/Services/TransStrings.php:1587
msgid "includes in any of"
msgstr "include in uno di"

#: app/Services/TransStrings.php:1588
msgid "includes none of"
msgstr "non include nessuno di"

#: app/Services/TransStrings.php:755
msgid "Includes none of (match all)"
msgstr "Non include nessuno di (corrispondenza totale)"

#: app/Services/TransStrings.php:756
msgid "Incoming Webhook Settings"
msgstr "Impostazioni webhook in entrata"

#: app/Services/TransStrings.php:757
msgid "Incoming Webhooks"
msgstr "Webhooks in entrata"

#: app/Services/TransStrings.php:758
msgid "Incomplete"
msgstr "Incompleto"

#: app/Hooks/Handlers/CountryNames.php:429
msgid "India"
msgstr "India"

#: app/Services/TransStrings.php:759
msgid "Individual Reporting"
msgstr "Report individuale"

#: app/Hooks/Handlers/CountryNames.php:433
msgid "Indonesia"
msgstr "Indonesia"

#: app/Models/Company.php:54 app/Services/TransStrings.php:760
msgid "Industry"
msgstr "Settore"

#: app/Services/TransStrings.php:761
msgid "Info"
msgstr "Informazioni"

#: app/Http/Controllers/FormsController.php:251
msgid "Inline Opt-in Form"
msgstr "Modulo di opt-in inline"

#: app/Services/TransStrings.php:762
msgid "Insert"
msgstr "Inserisci"

#: app/Services/TransStrings.php:763
msgid "Insert a valid url that this link will direct to"
msgstr "Inserisci un URL valido a cui questo link reindirizzerà"

#: app/Http/Controllers/DocsController.php:94
msgid "Install Fluent Connect"
msgstr "Installa Fluent Connect"

#: app/Http/Controllers/DocsController.php:67
msgid "Install Fluent Forms"
msgstr "Installa Fluent Forms"

#: app/Services/TransStrings.php:1240
msgid "Install Fluent Forms Plugin for Lead collections Forms."
msgstr "Installa il plugin Fluent Forms per i moduli di raccolta lead."

#: app/Http/Controllers/DocsController.php:76
msgid "Install Fluent SMTP"
msgstr "Installa Fluent SMTP"

#: app/Http/Controllers/DocsController.php:85
msgid "Install Fluent Support"
msgstr "Installa Fluent Support"

#: app/Services/TransStrings.php:1589
msgid "Install FluentSMTP Plugin (It's free)"
msgstr "Installa il plugin FluentSMTP (gratuito)"

#: app/Http/Controllers/SetupController.php:44
msgid "Installation has been completed"
msgstr "L'installazione è stata completata"

#: app/Services/TransStrings.php:764
msgid "Installed"
msgstr "Installato"

#: app/Services/TransStrings.php:765
msgid "Integration Settings"
msgstr "Impostazioni di integrazione"

#: app/Services/TransStrings.php:771
msgid "Integrations"
msgstr "Integrazioni"

#: app/Services/TransStrings.php:772
msgid "Internal Campaign Title"
msgstr "Titolo campagna interno"

#: app/Services/TransStrings.php:773
msgid "Internal Description"
msgstr "Descrizione interna"

#: app/Services/TransStrings.php:774
msgid "Internal Label"
msgstr "Etichetta interna"

#: app/Services/TransStrings.php:775
msgid "Internal Subtitle"
msgstr "Sottotitolo interno"

#: app/Services/TransStrings.php:674
msgid "Internal Subtitle (Optional)"
msgstr "Sottotitolo interno (opzionale)"

#: app/Services/TransStrings.php:776
msgid "Internal Title"
msgstr "Titolo interno"

#: app/Http/Controllers/SubscriberController.php:864
msgid "Invalid Advanced Filters"
msgstr "Filtri avanzati non validi"

#: app/Http/Controllers/SubscriberController.php:1113
msgid "Invalid Automation Funnel ID"
msgstr "ID Funnel di automazione non valido"

#: app/Http/Controllers/FunnelController.php:523
#: app/Http/Controllers/TemplateController.php:290
msgid "invalid bulk action"
msgstr "azione collettiva non valida"

#: app/Http/Controllers/SubscriberController.php:989
#: app/Http/Controllers/SubscriberController.php:1036
msgid "Invalid Company ID"
msgstr "ID azienda non valido"

#: app/Hooks/Handlers/FunnelHandler.php:332
msgid "Invalid Data"
msgstr "Dati non validi"

#: app/Http/Controllers/WebhookBounceController.php:67
msgid "Invalid Data or Security Code"
msgstr "Dati o codice di sicurezza non validi"

#: app/Http/Controllers/SubscriberController.php:951
msgid "Invalid Email Sequence ID"
msgstr "ID sequenza email non valido"

#: app/Services/TransStrings.php:777
msgid "Invalid Emails:"
msgstr "Email non valide:"

#: app/Services/TransStrings.php:780
msgid "Invalid links detected. Check the following button / links"
msgstr "Link non validi rilevati. Controlla i seguenti pulsanti/link"

#: app/Services/TransStrings.php:778
#: app/Http/Controllers/CampaignController.php:501
msgid "Invalid schedule date"
msgstr "Data di programmazione non valida"

#: app/Services/TransStrings.php:779
msgid "Invalid schedule date range"
msgstr "Intervallo di date di programmazione non valido"

#: app/Services/TransStrings.php:1056
msgid "Invalid selection of lists and tags in contacts included."
msgstr "Selezione non valida di liste e tag nei contatti inclusi."

#: app/Services/TransStrings.php:1507
msgid "Invalid selection of lists and tags in subscribers included."
msgstr "Selezione non valida di liste e tag negli iscritti inclusi."

#: app/Hooks/Handlers/ExternalPages.php:770
msgid "Invalid Webhook Hash"
msgstr "Hash webhook non valido"

#: app/Hooks/Handlers/ExternalPages.php:761
msgid "Invalid Webhook URL"
msgstr "URL webhook non valido"

#: app/Functions/helpers.php:620
msgid "Invoice: Paid"
msgstr "Fattura: Pagata"

#: app/Functions/helpers.php:619
msgid "Invoice: Part Paid"
msgstr "Fattura: Parzialmente pagata"

#: app/Functions/helpers.php:621
msgid "Invoice: Refunded"
msgstr "Fattura: Rimborsata"

#: app/Functions/helpers.php:618
msgid "Invoice: Sent"
msgstr "Fattura: Inviata"

#: app/Models/Subscriber.php:730 app/Services/TransStrings.php:729
msgid "IP Address"
msgstr "Indirizzo IP"

#: app/Hooks/Handlers/CountryNames.php:437
msgid "Iran"
msgstr "Iran"

#: app/Hooks/Handlers/CountryNames.php:441
msgid "Iraq"
msgstr "Iraq"

#: app/Hooks/Handlers/CountryNames.php:445
msgid "Ireland"
msgstr "Irlanda"

#: app/Services/ExternalIntegrations/BricksBuilderIntegration.php:55
#: app/Services/ExternalIntegrations/BricksBuilderIntegration.php:75
msgid "is"
msgstr "è"

#: app/Services/Helper.php:1304
msgid "Is Affiliate (Pro Required)"
msgstr "È affiliato (Richiesto Pro)"

#: app/Hooks/Handlers/CountryNames.php:449
msgid "Isle of Man"
msgstr "Isola di Man"

#: app/Hooks/Handlers/CountryNames.php:453
msgid "Israel"
msgstr "Israele"

#: app/Services/TransStrings.php:1622
msgid ""
"It is recommended to keep the texts as default aligned. Your provided email "
"design template will align the texts"
msgstr ""
"Si consiglia di mantenere i testi allineati come predefinito. Il modello di "
"design email fornito allineerà i testi"

#: app/Hooks/Handlers/CountryNames.php:457
msgid "Italy"
msgstr "Italia"

#: app/Services/TransStrings.php:660
msgid "It’s completely optional and shouldn’t take longer than two minutes."
msgstr ""
"È completamente facoltativo e non dovrebbe richiedere più di due minuti."

#: app/Hooks/Handlers/CountryNames.php:461
msgid "Ivory Coast"
msgstr "Costa d'Avorio"

#: app/Hooks/Handlers/CountryNames.php:465
msgid "Jamaica"
msgstr "Giamaica"

#: app/Services/TransStrings.php:781
msgid "January"
msgstr "Gennaio"

#: app/Hooks/Handlers/CountryNames.php:469
msgid "Japan"
msgstr "Giappone"

#: app/Hooks/Handlers/CountryNames.php:473
msgid "Jersey"
msgstr "Jersey"

#: app/Hooks/Handlers/CountryNames.php:477
msgid "Jordan"
msgstr "Giordania"

#: app/Services/TransStrings.php:782
msgid "July"
msgstr "Luglio"

#: app/Services/TransStrings.php:783
msgid "June"
msgstr "Giugno"

#: app/Hooks/Handlers/CountryNames.php:481
msgid "Kazakhstan"
msgstr "Kazakistan"

#: app/Services/TransStrings.php:1592 app/Services/Helper.php:1869
msgid "keep blank for current time"
msgstr "lascia vuoto per l'orario corrente"

#: app/Hooks/Handlers/CountryNames.php:485
msgid "Kenya"
msgstr "Kenya"

#: app/Services/TransStrings.php:784
msgid "Key"
msgstr "Chiave"

#: app/Hooks/Handlers/CountryNames.php:489
msgid "Kiribati"
msgstr "Kiribati"

#: app/Hooks/Handlers/CountryNames.php:497
msgid "Kosovo"
msgstr "Kosovo"

#: app/Hooks/Handlers/CountryNames.php:493
msgid "Kuwait"
msgstr "Kuwait"

#: app/Hooks/Handlers/CountryNames.php:501
msgid "Kyrgyzstan"
msgstr "Kirghizistan"

#: app/Services/TransStrings.php:786
msgid "Label"
msgstr "Etichetta"

#: app/Hooks/Handlers/CountryNames.php:505
msgid "Laos"
msgstr "Laos"

#: app/Services/Helper.php:545 app/Hooks/Handlers/AdminMenu.php:853
msgid "Large"
msgstr "Grande"

#: app/Services/Helper.php:551
msgid "Larger"
msgstr "Più grande"

#: app/Services/TransStrings.php:787
msgid "Last 3 months"
msgstr "Ultimi 3 mesi"

#: app/Services/TransStrings.php:788 app/Services/Helper.php:973
msgid "Last Activity"
msgstr "Ultima attività"

#: app/Services/TransStrings.php:789
msgid "Last Change Date"
msgstr "Data dell'ultima modifica"

#: app/Services/TransStrings.php:790
msgid "Last Changed"
msgstr "Ultima modifica"

#: app/Services/Helper.php:1056
msgid "Last Email Clicked"
msgstr "Ultima email cliccata"

#: app/Services/Helper.php:1050
msgid "Last Email Open"
msgstr "Ultima email aperta"

#: app/Services/Helper.php:1045
msgid "Last Email Sent"
msgstr "Ultima email inviata"

#: app/Services/Helper.php:1370 app/Services/Helper.php:1427
msgid "Last Enrollment Date (Pro Required)"
msgstr "Data dell'ultima iscrizione (Richiesto Pro)"

#: app/Services/TransStrings.php:791
msgid "Last Executed At"
msgstr "Ultima esecuzione alle"

#: app/Services/TransStrings.php:795
msgid "Last month"
msgstr "Ultimo mese"

#: app/Models/Subscriber.php:720 app/Services/TransStrings.php:792
#: app/Services/Helper.php:157 app/Services/Helper.php:903
#: app/Hooks/Handlers/PrefFormHandler.php:43
#: app/Hooks/Handlers/PrefFormHandler.php:336
#: app/views/external/manage_subscription_form.php:20
#: app/views/external/manage_subscription_form.php:21
#: app/Services/CrmMigrator/BaseMigrator.php:26
#: app/Services/Funnel/FunnelHelper.php:134
#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:152
msgid "Last Name"
msgstr "Cognome"

#: app/Services/TransStrings.php:796
#, fuzzy
#| msgid "Last Order"
msgid "Last order"
msgstr "Ultimo ordine"

#: app/Services/TransStrings.php:793
msgid "Last Order Date"
msgstr "Data dell'ultimo ordine"

#: app/Services/Helper.php:1221 app/Services/Helper.php:1275
msgid "Last Order Date (Pro Required)"
msgstr "Data dell'ultimo ordine (Richiesto Pro)"

#: app/Services/Helper.php:1355
msgid "Last Payout Date (Pro Required)"
msgstr "Data dell'ultimo pagamento (Richiesto Pro)"

#: app/Services/TransStrings.php:794
msgid "Last Purchase Date"
msgstr "Data dell'ultimo acquisto"

#: app/Services/TransStrings.php:797
msgid "Last quarter"
msgstr "Ultimo trimestre"

#: app/Services/TransStrings.php:798
msgid "Last week"
msgstr "Ultima settimana"

#: app/Services/TransStrings.php:799
msgid "Latest Action"
msgstr "Azione più recente"

#: app/Services/Helper.php:205
msgid "Latest Post Title (Published)"
msgstr "Titolo dell'ultimo post (pubblicato)"

#: app/Hooks/Handlers/CountryNames.php:509
msgid "Latvia"
msgstr "Lettonia"

#: app/Services/TransStrings.php:800
msgid "Launch Visual Editor"
msgstr "Avvia editor visivo"

#: app/Functions/helpers.php:576
msgid "Lead"
msgstr "Lead"

#: app/Services/TransStrings.php:1593
msgid "lead"
msgstr "lead"

#: app/Services/TransStrings.php:801
msgid "Learn More"
msgstr "Scopri di più"

#: app/Services/TransStrings.php:802
msgid "Learn more about FluentSMTP Plugin."
msgstr "Scopri di più sul plugin FluentSMTP."

#: app/Services/Helper.php:1365 app/Http/Controllers/ImporterController.php:243
msgid "LearnDash"
msgstr "LearnDash"

#: app/Services/TransStrings.php:803
msgid "LearnDash Integration:"
msgstr "Integrazione con LearnDash:"

#: app/Http/Controllers/ImporterController.php:306
msgid "LearnPress"
msgstr "LearnPress"

#: app/Services/TransStrings.php:820
msgid "Leave blank if you do not want to filter campaigns"
msgstr "Lascia vuoto se non desideri filtrare le campagne"

#: app/Services/Funnel/Triggers/UserRegistrationTrigger.php:86
msgid "Leave blank to run for all user roles"
msgstr "Lascia vuoto per eseguire su tutti i ruoli utente"

#: app/Services/TransStrings.php:630
msgid "Leave these blank for no limit or offset"
msgstr "Lascia vuoti questi campi per nessun limite o offset"

#: app/Hooks/Handlers/CountryNames.php:513
msgid "Lebanon"
msgstr "Libano"

#: app/Hooks/Handlers/CountryNames.php:517
msgid "Lesotho"
msgstr "Lesotho"

#: app/Services/TransStrings.php:804
msgid "Less Than"
msgstr "Meno di"

#: app/Services/TransStrings.php:805
msgid "Let's Go"
msgstr "Andiamo"

#: app/Hooks/Handlers/CountryNames.php:521
msgid "Liberia"
msgstr "Liberia"

#: app/Hooks/Handlers/CountryNames.php:525
msgid "Libya"
msgstr "Libia"

#: app/Services/TransStrings.php:806
msgid "License Management"
msgstr "Gestione licenza"

#: app/Hooks/Handlers/CountryNames.php:529
msgid "Liechtenstein"
msgstr "Liechtenstein"

#: app/Functions/helpers.php:1030 app/Services/TransStrings.php:807
msgid "Lifetime Value"
msgstr "Valore a vita"

#: app/Services/Helper.php:1422 app/Http/Controllers/ImporterController.php:234
msgid "LifterLMS"
msgstr "LifterLMS"

#: app/Services/TransStrings.php:808
msgid "LifterLMS:"
msgstr "LifterLMS:"

#: app/Services/Helper.php:505
msgid "Light green cyan"
msgstr "Verde chiaro ciano"

#: app/Services/TransStrings.php:811
msgid "Line"
msgstr "Linea"

#: app/Services/TransStrings.php:812
msgid "Line Height"
msgstr "Altezza della linea"

#: app/Services/TransStrings.php:815
msgid "Link activity"
msgstr "Attività dei link"

#: app/Services/TransStrings.php:813
msgid "Link Metrics"
msgstr "Metriche dei link"

#: app/Services/TransStrings.php:814
msgid "Link Title"
msgstr "Titolo del link"

#: app/Services/TransStrings.php:816
msgid "LinkedIn Company Page URL"
msgstr "URL della pagina aziendale LinkedIn"

#: app/Models/Company.php:67
msgid "LinkedIn URL"
msgstr "URL LinkedIn"

#: app/Services/TransStrings.php:817
msgid "Linkedin Url"
msgstr "URL Linkedin"

#: app/Services/TransStrings.php:819
msgid "List"
msgstr "Lista"

#: app/Services/TransStrings.php:1597
msgid "list"
msgstr "lista"

#: app/Services/Funnel/Benchmarks/ListAppliedBenchmark.php:23
#: app/Services/Funnel/Benchmarks/ListAppliedBenchmark.php:48
msgid "List Applied"
msgstr "Lista applicata"

#: app/Services/TransStrings.php:818
msgid ""
"List are categories of your contacts. You can add lists and assign contacts "
"to your list for better segmentation"
msgstr ""
"Le liste sono categorie dei tuoi contatti. Puoi aggiungere liste e assegnare "
"contatti per una migliore segmentazione"

#: app/Services/Funnel/Benchmarks/RemoveFromListBenchmark.php:23
msgid "List Removed"
msgstr "Lista rimossa"

#: app/Services/Funnel/Benchmarks/RemoveFromListBenchmark.php:48
msgid "List Removed From Contact"
msgstr "Lista rimossa dal contatto"

#: app/Services/TransStrings.php:821
msgid "List the campaigns if the title match the provided keyword"
msgstr "Elenca le campagne se il titolo corrisponde alla parola chiave fornita"

#: app/Services/TransStrings.php:822 app/Services/Helper.php:1021
#: app/Hooks/CLI/Commands.php:158 app/Hooks/CLI/Commands.php:372
#: app/Hooks/CLI/Commands.php:580 app/Hooks/Handlers/AdminMenu.php:95
#: app/Hooks/Handlers/AdminMenu.php:96 app/Hooks/Handlers/AdminMenu.php:322
#: app/Hooks/Handlers/EventTrackingHandler.php:277
msgid "Lists"
msgstr "Liste"

#: app/Services/TransStrings.php:1598
msgid "lists"
msgstr "liste"

#: app/Hooks/Handlers/CountryNames.php:533
msgid "Lithuania"
msgstr "Lituania"

#: app/Services/TransStrings.php:785
msgid "LMS INTEGRATION:"
msgstr "INTEGRAZIONE LMS:"

#: app/Hooks/Handlers/AdminBar.php:77
msgid "Load More"
msgstr "Carica altro"

#: app/Services/TransStrings.php:823
msgid "Loading"
msgstr "Caricamento in corso"

#: app/Services/TransStrings.php:824
msgid "Loading Campaign Data..."
msgstr "Caricamento dati campagna..."

#: app/Services/TransStrings.php:825
msgid "Loading Editor..."
msgstr "Caricamento editor..."

#: app/Services/TransStrings.php:826
msgid "Loading Preview. Please wait..."
msgstr "Caricamento anteprima. Attendere prego..."

#: app/Services/TransStrings.php:827
msgid "Loading Settings..."
msgstr "Caricamento impostazioni..."

#: app/Services/TransStrings.php:828
msgid "Loading..."
msgstr "Caricamento..."

#: app/Services/TransStrings.php:829
msgid "Loading....."
msgstr "Caricamento....."

#: app/Services/TransStrings.php:830
msgid "Logo"
msgstr "Logo"

#: app/Http/Controllers/SettingsController.php:655
msgid "Logs older than %d days have been deleted successfully"
msgstr "I log più vecchi di %d giorni sono stati eliminati con successo"

#: app/Services/TransStrings.php:163
msgid ""
"Looks like all the steps are synced with your Automation contacts. No action "
"is required."
msgstr ""
"Sembra che tutti i passaggi siano sincronizzati con i tuoi contatti di "
"automazione. Nessuna azione richiesta."

#: app/Services/TransStrings.php:1521
msgid ""
"Looks like settings is missing for this block. Maybe the block does not exist"
msgstr ""
"Sembra che manchino impostazioni per questo blocco. Forse il blocco non "
"esiste"

#: app/Services/TransStrings.php:831
msgid "Looks like there had a problem to sync the data"
msgstr "Sembra che ci sia stato un problema con la sincronizzazione dei dati"

#: app/views/external/unsubscribe_request_form.php:33
msgid ""
"Looks like we could not determine your info. Please fill up the form and get "
"unsubscribe link via email."
msgstr ""
"Sembra che non siamo riusciti a determinare le tue informazioni. Compila il "
"modulo per ottenere il link di disiscrizione via email."

#: app/views/external/manage_subscription_request_form.php:33
msgid ""
"Looks like we could not determine your info. Please fill up the form and get "
"your email subscription form link via email."
msgstr ""
"Sembra che non siamo riusciti a determinare le tue informazioni. Compila il "
"modulo per ottenere il link al modulo di iscrizione via email."

#: app/Hooks/Handlers/ExternalPages.php:398
msgid "Looks like you are already unsubscribed"
msgstr "Sembra che tu sia già disiscritto"

#: app/Services/TransStrings.php:1669
msgid ""
"Looks like you are using Old version of WordPress. Use at-least version 5.4 "
"to use Smart Email Editor powered by Block Editor"
msgstr ""
"Sembra che tu stia utilizzando una versione obsoleta di WordPress. Utilizza "
"almeno la versione 5.4 per usare lo Smart Email Editor basato su Block Editor"

#: app/Services/TransStrings.php:217
msgid "Looks like you did not broadcast any email campaign yet"
msgstr "Sembra che non hai ancora trasmesso nessuna campagna email"

#: app/Services/TransStrings.php:675
msgid "Looks Like you did not create any forms yet!"
msgstr "Sembra che non hai ancora creato nessun modulo!"

#: app/Services/TransStrings.php:1089
msgid ""
"Looks like you did not have any additional Manager yet. Please create a "
"manager (non administrator role) first for FluentCRM"
msgstr ""
"Sembra che non hai ancora un Manager aggiuntivo. Crea prima un manager "
"(ruolo non amministratore) per FluentCRM"

#: app/Services/TransStrings.php:1572
msgid ""
"Looks like you did not install FluentSMTP yet. FluentSMTP is the the "
"ultimate SMTP & Email Service Connection Plugin for WordPress."
msgstr ""
"Sembra che non hai ancora installato FluentSMTP. FluentSMTP è il plugin SMTP "
"e di connessione ai servizi email definitivo per WordPress."

#: app/Services/TransStrings.php:125
msgid "Looks like you did not set any sequence emails yet"
msgstr "Sembra che non hai ancora impostato nessuna sequenza di email"

#: app/Services/TransStrings.php:1043
msgid "Looks like you do not have any recurring Email Campaigns yet."
msgstr "Sembra che non hai ancora nessuna campagna email ricorrente."

#: app/Services/TransStrings.php:1614
msgid "Looks like you do not have permission to view the requested resource."
msgstr "Sembra che non hai il permesso di visualizzare la risorsa richiesta."

#: app/Http/Controllers/CsvController.php:69
msgid ""
"Looks like your csv has same name header multiple times. Please fix your csv "
"first and remove any duplicate header column"
msgstr ""
"Sembra che il tuo CSV abbia più volte lo stesso nome di intestazione. "
"Correggi il CSV rimuovendo eventuali intestazioni duplicate."

#: app/Services/TransStrings.php:1267
msgid "Looks like your FluentSMTP plugin is not configured yet"
msgstr "Sembra che il tuo plugin FluentSMTP non sia ancora configurato"

#: app/Services/TransStrings.php:832
msgid "Looks like your license has been expired"
msgstr "Sembra che la tua licenza sia scaduta"

#: app/Services/TransStrings.php:1631
msgid ""
"Looks like your server does not support PUT and DELETE REST api Requests. "
"Maybe It's blocking from your server firewall"
msgstr ""
"Sembra che il tuo server non supporti le richieste API REST PUT e DELETE. "
"Forse è bloccato dal firewall del server"

#: app/Services/TransStrings.php:1685
msgid "Lost Orders"
msgstr "Ordini persi"

#: app/Services/Helper.php:500
msgid "Luminous vivid amber"
msgstr "Ambra vivida luminosa"

#: app/Services/Helper.php:495
msgid "Luminous vivid orange"
msgstr "Arancione vivida luminosa"

#: app/Hooks/Handlers/CountryNames.php:537
msgid "Luxembourg"
msgstr "Lussemburgo"

#: app/Hooks/Handlers/CountryNames.php:541
msgid "Macao"
msgstr "Macao"

#: app/Hooks/Handlers/CountryNames.php:549
msgid "Madagascar"
msgstr "Madagascar"

#: app/Services/CrmMigrator/MailChimpMigrator.php:33
msgid "MailChimp API Key"
msgstr "Chiave API MailChimp"

#: app/Services/TransStrings.php:1693
msgid "Mailer"
msgstr "Mailer"

#: app/Services/CrmMigrator/MailerLiteMigrator.php:30
msgid "MailerLite API Key"
msgstr "Chiave API MailerLite"

#: app/Http/Controllers/SettingsController.php:328
msgid "Mailgun"
msgstr "Mailgun"

#: app/Http/Controllers/SettingsController.php:331
msgid "Mailgun Bounce Handler Webhook URL"
msgstr "URL Webhook di gestione rimbalzi Mailgun"

#: app/Hooks/Handlers/PrefFormHandler.php:56
#: app/views/external/manage_subscription_form.php:26
msgid "Mailing List Groups"
msgstr "Gruppi di liste di distribuzione"

#: app/Services/TransStrings.php:834
msgid "Main Contact Properties"
msgstr "Proprietà principali del contatto"

#: app/Hooks/Handlers/CountryNames.php:553
msgid "Malawi"
msgstr "Malawi"

#: app/Hooks/Handlers/CountryNames.php:557
msgid "Malaysia"
msgstr "Malesia"

#: app/Hooks/Handlers/CountryNames.php:561
msgid "Maldives"
msgstr "Maldive"

#: app/Hooks/Handlers/CountryNames.php:565
msgid "Mali"
msgstr "Mali"

#: app/Hooks/Handlers/CountryNames.php:569
msgid "Malta"
msgstr "Malta"

#: app/Services/TransStrings.php:835
msgid "Manage APIs"
msgstr "Gestisci API"

#: app/Services/PermissionManager.php:102
msgid "Manage CRM Settings"
msgstr "Gestisci impostazioni CRM"

#: app/Services/PermissionManager.php:78
msgid "Manage Forms"
msgstr "Gestisci moduli"

#: app/Services/Helper.php:210
msgid "Manage Subscription Hyperlink HTML"
msgstr "Gestisci il collegamento HTML per l'iscrizione"

#: app/Services/Helper.php:207
msgid "Manage Subscription URL"
msgstr "Gestisci URL di iscrizione"

#: app/Hooks/Handlers/AdminMenu.php:336
msgid "Manage your dynamic contact segments"
msgstr "Gestisci i tuoi segmenti di contatti dinamici"

#: app/Services/TransStrings.php:836
msgid "Managers"
msgstr "Manager"

#: app/Services/TransStrings.php:838
msgid "Map Contact Fields"
msgstr "Mappa i campi del contatto"

#: app/Services/TransStrings.php:850
msgid "Map CSV Fields with Contact Property"
msgstr "Mappa i campi CSV con le proprietà del contatto"

#: app/Services/TransStrings.php:839
msgid "Map Data"
msgstr "Mappa dati"

#: app/Services/TransStrings.php:840
msgid "Map List"
msgstr "Mappa lista"

#: app/Services/Funnel/Triggers/FluentFormSubmissionTrigger.php:90
msgid "Map Other Data"
msgstr "Mappa altri dati"

#: app/Services/Funnel/Triggers/FluentFormSubmissionTrigger.php:82
msgid "Map Primary Data"
msgstr "Mappa i dati principali"

#: app/Services/RoleBasedTagging.php:57
msgid "Map Role and associate tags"
msgstr "Mappa il ruolo e associa i tag"

#: app/Services/TransStrings.php:841
msgid "Map Tags"
msgstr "Mappa tag"

#: app/Services/TransStrings.php:842
msgid "Map the data"
msgstr "Mappa i dati"

#: app/Services/TransStrings.php:843
msgid "Map your Data"
msgstr "Mappa i tuoi dati"

#: app/Services/TransStrings.php:852
msgid "March"
msgstr "Marzo"

#: app/Services/TransStrings.php:1699
msgid "Mark as Lost after"
msgstr "Segna come perso dopo"

#: app/Services/TransStrings.php:853
msgid "Mark as Primary"
msgstr "Segna come principale"

#: app/Services/TransStrings.php:1703
msgid "Mark Cart as Recovered when WooCommerce Order Status Changes to:"
msgstr ""
"Segna il carrello come recuperato quando lo stato dell'ordine WooCommerce "
"cambia in:"

#: app/Services/TransStrings.php:854
msgid "Marketing medium: (e.g. cpc, banner, email)"
msgstr "Canale di marketing: (es. cpc, banner, email)"

#: app/Hooks/Handlers/CountryNames.php:573
msgid "Marshall Islands"
msgstr "Isole Marshall"

#: app/Hooks/Handlers/CountryNames.php:577
msgid "Martinique"
msgstr "Martinica"

#: app/Services/TransStrings.php:855
msgid "Match Type"
msgstr "Tipo di corrispondenza"

#: app/Services/TransStrings.php:856
msgid "Matching "
msgstr "Corrispondenza"

#: app/Hooks/Handlers/CountryNames.php:581
msgid "Mauritania"
msgstr "Mauritania"

#: app/Hooks/Handlers/CountryNames.php:585
msgid "Mauritius"
msgstr "Mauritius"

#: app/Services/TransStrings.php:857
msgid "Max Campaigns to list (max 50)"
msgstr "Numero massimo di campagne da elencare (max 50)"

#: app/Hooks/CLI/Commands.php:54
msgid "Max Rune Time"
msgstr "Tempo massimo di esecuzione"

#: app/Services/TransStrings.php:858
msgid "Maximum Email Limit Per Second"
msgstr "Limite massimo di email per secondo"

#: app/Services/TransStrings.php:859
msgid "May"
msgstr "Maggio"

#: app/Services/TransStrings.php:1599
msgid "May be remove"
msgstr "Potrebbe essere rimosso"

#: app/Hooks/Handlers/CountryNames.php:589
msgid "Mayotte"
msgstr "Mayotte"

#: app/Services/Helper.php:539 app/Hooks/Handlers/AdminMenu.php:852
msgid "Medium"
msgstr "Medio"

#: app/Functions/helpers.php:614
msgid "Meeting"
msgstr "Riunione"

#: app/Services/TransStrings.php:860
#, fuzzy
#| msgid "MemberPress"
msgid "MemberPress."
msgstr "MemberPress."

#: app/Services/TransStrings.php:833
msgid "MEMBERSHIP INTEGRATION:"
msgstr "INTEGRAZIONE ABBONAMENTO:"

#: app/Hooks/Handlers/CountryNames.php:593
msgid "Mexico"
msgstr "Messico"

#: app/Hooks/Handlers/CountryNames.php:597
msgid "Micronesia"
msgstr "Micronesia"

#: app/Services/CrmMigrator/ConvertKitMigrator.php:14
msgid "Migrate your ConvertKit contacts and associate to FluentCRM"
msgstr "Migra i tuoi contatti ConvertKit e associati a FluentCRM"

#: app/Services/CrmMigrator/MailerLiteMigrator.php:14
msgid "Migrate your MailerLite contacts and associate to FluentCRM"
msgstr "Migra i tuoi contatti MailerLite e associati a FluentCRM"

#: app/Services/TransStrings.php:861
msgid "Migrating"
msgstr "Migrazione in corso"

#: app/Services/TransStrings.php:862
#: app/Services/Funnel/Actions/WaitTimeAction.php:122
msgid "Minutes"
msgstr "Minuti"

#: app/Hooks/Handlers/CountryNames.php:601
msgid "Moldova"
msgstr "Moldavia"

#: app/Hooks/Handlers/CountryNames.php:605
msgid "Monaco"
msgstr "Monaco"

#: app/Services/TransStrings.php:863
msgid "Monday"
msgstr "Lunedì"

#: app/Hooks/Handlers/CountryNames.php:609
msgid "Mongolia"
msgstr "Mongolia"

#: app/Hooks/Handlers/CountryNames.php:613
msgid "Montenegro"
msgstr "Montenegro"

#: app/Services/TransStrings.php:864
msgid "Month"
msgstr "Mese"

#: app/Services/TransStrings.php:865
msgid "Monthly"
msgstr "Mensile"

#: app/Services/TransStrings.php:866
msgid "Months"
msgstr "Mesi"

#: app/Hooks/Handlers/CountryNames.php:617
msgid "Montserrat"
msgstr "Montserrat"

#: app/Hooks/Handlers/CountryNames.php:621
msgid "Morocco"
msgstr "Marocco"

#: app/Hooks/Handlers/CountryNames.php:625
msgid "Mozambique"
msgstr "Mozambico"

#: app/Models/CustomContactField.php:45
msgid "Multi Line Text"
msgstr "Testo a più righe"

#: app/Models/CustomContactField.php:60
msgid "Multiple Select choice"
msgstr "Scelta selezione multipla"

#: app/Hooks/Handlers/CountryNames.php:629
msgid "Myanmar"
msgstr "Myanmar"

#: app/Services/TransStrings.php:867
msgid "MyAwesomeBusiness"
msgstr "LaMiaFantasticaAzienda"

#: app/Services/TransStrings.php:868
msgid "MyAwesomeBusiness Inc."
msgstr "LaMiaFantasticaAzienda Inc."

#: app/Services/TransStrings.php:1600
msgid "n/a"
msgstr "n/d"

#: app/Services/TransStrings.php:869
msgid "Name"
msgstr "Nome"

#: app/Services/TransStrings.php:871
msgid "Name of this key"
msgstr "Nome di questa chiave"

#: app/Models/Subscriber.php:718 app/Services/TransStrings.php:870
#: app/Services/Helper.php:155 app/Services/CrmMigrator/BaseMigrator.php:24
#: app/Services/Funnel/FunnelHelper.php:148
msgid "Name Prefix"
msgstr "Prefisso nome"

#: app/Services/Helper.php:956
msgid "Name Prefix (Title)"
msgstr "Prefisso nome (Titolo)"

#: app/Services/TransStrings.php:872
msgid "Name that will be used to send emails"
msgstr "Nome che verrà utilizzato per inviare email"

#: app/Services/TransStrings.php:873
msgid "Name this Custom Segment"
msgstr "Dai un nome a questo segmento personalizzato"

#: app/Hooks/Handlers/CountryNames.php:633
msgid "Namibia"
msgstr "Namibia"

#: app/Hooks/Handlers/CountryNames.php:637
msgid "Nauru"
msgstr "Nauru"

#: app/Services/TransStrings.php:874
msgid "Navigation"
msgstr "Navigazione"

#: app/Services/Funnel/Benchmarks/RemoveFromTagBenchmark.php:69
msgid "Need all selected tags removed from the contact"
msgstr "È necessario rimuovere tutti i tag selezionati dal contatto"

#: app/Hooks/Handlers/CountryNames.php:641
msgid "Nepal"
msgstr "Nepal"

#: app/Hooks/Handlers/CountryNames.php:645
msgid "Netherlands"
msgstr "Paesi Bassi"

#: app/Services/TransStrings.php:875
msgid "Never-mind, I changed my mind"
msgstr "Non importa, ho cambiato idea"

#: app/Hooks/Handlers/CountryNames.php:649
msgid "New Caledonia"
msgstr "Nuova Caledonia"

#: app/Services/TransStrings.php:876
msgid "New Dynamic Segment"
msgstr "Nuovo segmento dinamico"

#: app/Services/Funnel/Triggers/FluentFormSubmissionTrigger.php:73
msgid "New Fluent Forms Submission Funnel"
msgstr "Nuovo funnel per invii di Fluent Forms"

#: app/Services/Funnel/Triggers/FluentFormSubmissionTrigger.php:31
msgid "New Form Submission (Fluent Forms)"
msgstr "Nuovo invio modulo (Fluent Forms)"

#: app/Services/TransStrings.php:165
msgid "New steps are synced for your completed contacts"
msgstr "Nuovi passaggi sincronizzati per i tuoi contatti completati"

#: app/Services/TransStrings.php:877
msgid "New Subscriber Status"
msgstr "Stato nuovo iscritto"

#: app/Services/Funnel/Triggers/UserRegistrationTrigger.php:23
msgid "New User Sign Up"
msgstr "Nuova iscrizione utente"

#: app/Services/Funnel/Triggers/UserRegistrationTrigger.php:39
msgid "New User Sign Up Funnel"
msgstr "Nuovo funnel di iscrizione utente"

#: app/Hooks/Handlers/CountryNames.php:653
msgid "New Zealand"
msgstr "Nuova Zelanda"

#: app/Services/TransStrings.php:878
msgid "Next"
msgstr "Successivo"

#: app/Services/TransStrings.php:882
msgid "Next [Map Columns]"
msgstr "Successivo [Mappa colonne]"

#: app/Services/TransStrings.php:883
#: app/Http/Controllers/ImporterController.php:157
msgid "Next [Review Data]"
msgstr "Successivo [Rivedi dati]"

#: app/Services/TransStrings.php:879
msgid "Next Email"
msgstr "Email successiva"

#: app/Services/TransStrings.php:880
msgid "Next Run"
msgstr "Prossima esecuzione"

#: app/Services/TransStrings.php:881
msgid "Next Step"
msgstr "Prossimo passaggio"

#: app/Services/TransStrings.php:884
msgid "Next step"
msgstr "Passaggio successivo"

#: app/Hooks/Handlers/CountryNames.php:657
msgid "Nicaragua"
msgstr "Nicaragua"

#: app/Hooks/Handlers/CountryNames.php:661
msgid "Niger"
msgstr "Niger"

#: app/Hooks/Handlers/CountryNames.php:665
msgid "Nigeria"
msgstr "Nigeria"

#: app/Hooks/Handlers/CountryNames.php:669
msgid "Niue"
msgstr "Niue"

#: app/Services/TransStrings.php:885 app/Services/Helper.php:1308
msgid "No"
msgstr "No"

#: app/Hooks/Handlers/ExternalPages.php:1163
msgid "No Action found"
msgstr "Nessuna azione trovata"

#: app/Services/TransStrings.php:1706
msgid "No active Abandoned Cart automation."
msgstr "Nessuna automazione carrello abbandonato attiva."

#: app/Services/TransStrings.php:904
msgid ""
"No additional information available for this contact. Related data like "
"purchase history, membership, course info will be shown."
msgstr ""
"Nessuna informazione aggiuntiva disponibile per questo contatto. Verranno "
"mostrate informazioni correlate come cronologia acquisti, abbonamenti e "
"dettagli sui corsi."

#: app/Services/TransStrings.php:894
msgid "No changes found"
msgstr "Nessuna modifica trovata"

#: app/Services/TransStrings.php:886
msgid "No Companies Found"
msgstr "Nessuna azienda trovata"

#: app/Services/TransStrings.php:895
msgid "No companies found based on your search"
msgstr "Nessuna azienda trovata in base alla tua ricerca"

#: app/Services/TransStrings.php:887
msgid "No Comparison"
msgstr "Nessun confronto"

#: app/Services/TransStrings.php:888
msgid "No Contacts Found"
msgstr "Nessun contatto trovato"

#: app/Services/TransStrings.php:896
msgid "No contacts found based on your search"
msgstr "Nessun contatto trovato in base alla tua ricerca"

#: app/Http/Controllers/FunnelController.php:676
msgid "No Corresponding report found"
msgstr "Nessun report corrispondente trovato"

#: app/Services/TransStrings.php:889
msgid "No Data Available"
msgstr "Nessun dato disponibile"

#: app/Services/TransStrings.php:890
msgid "No Data Found"
msgstr "Nessun dato trovato"

#: app/Services/TransStrings.php:891
msgid "No Form Found"
msgstr "Nessun modulo trovato"

#: app/Services/TransStrings.php:1602
msgid ""
"No HTML will be supported for this email. Additionally email tracking and "
"link analytics will not be available"
msgstr ""
"Non sarà supportato HTML per questa email. Inoltre, il tracciamento delle "
"email e le analisi dei link non saranno disponibili"

#: app/Services/TransStrings.php:897
msgid "No items found"
msgstr "Nessun elemento trovato"

#: app/Services/TransStrings.php:898
msgid "No link activity recorded yet"
msgstr "Nessuna attività dei link registrata"

#: app/Services/TransStrings.php:899
msgid "No lists found"
msgstr "Nessuna lista trovata"

#: app/Services/TransStrings.php:900
msgid "No mapping found."
msgstr "Nessuna mappatura trovata."

#: app/Services/TransStrings.php:1007
msgid "No Note found. Please add the first note"
msgstr "Nessuna nota trovata. Aggiungi la prima nota"

#: app/Services/TransStrings.php:1704
msgid "No Shipping Address found"
msgstr "Nessun indirizzo di spedizione trovato"

#: app/Services/TransStrings.php:892
msgid "No Subscriber Found"
msgstr "Nessun iscritto trovato"

#: app/Services/TransStrings.php:901
msgid ""
"No subscriber found to send test. Please add at least one contact as "
"subscribed status"
msgstr ""
"Nessun iscritto trovato per inviare il test. Aggiungi almeno un contatto con "
"stato iscritto"

#: app/Http/Controllers/CampaignController.php:664
msgid ""
"No subscriber found to send test. Please add atleast one contact as "
"subscribed status"
msgstr ""
"Nessun iscritto trovato per inviare il test. Aggiungi almeno un contatto con "
"stato iscritto"

#: app/Services/TransStrings.php:902
msgid "No tags found"
msgstr "Nessun tag trovato"

#: app/Services/TransStrings.php:893
msgid "No Thanks"
msgstr "No, grazie"

#: app/Services/TransStrings.php:1672
msgid ""
"No time right now? If you don’t want to go through the wizard, you can skip "
"and return to the WordPress dashboard. Come back anytime if you change your "
"mind!"
msgstr ""
"Non hai tempo ora? Se non vuoi completare il wizard, puoi saltarlo e tornare "
"alla bacheca di WordPress. Puoi sempre tornare se cambi idea!"

#: app/Http/Controllers/SubscriberController.php:969
#: app/Http/Controllers/SubscriberController.php:1007
#: app/Http/Controllers/SubscriberController.php:1054
#: app/Http/Controllers/SubscriberController.php:1131
msgid "No valid active subscribers found for this chunk"
msgstr "Nessun iscritto attivo valido trovato per questo gruppo"

#: app/Http/Controllers/SubscriberController.php:1012
#: app/Http/Controllers/SubscriberController.php:1059
msgid "No valid active subscribers found for this company"
msgstr "Nessun iscritto attivo valido trovato per questa azienda"

#: app/Http/Controllers/SubscriberController.php:1136
msgid "No valid active subscribers found for this funnel"
msgstr "Nessun iscritto attivo valido trovato per questo funnel"

#: app/Http/Controllers/SubscriberController.php:973
msgid "No valid active subscribers found for this sequence"
msgstr "Nessun iscritto attivo valido trovato per questa sequenza"

#: app/Services/TransStrings.php:903
msgid "No, Contact can not manage list subscriptions"
msgstr "No, il contatto non può gestire le iscrizioni alla lista"

#: app/Hooks/Handlers/CountryNames.php:673
msgid "Norfolk Island"
msgstr "Isola Norfolk"

#: app/Hooks/Handlers/CountryNames.php:681
msgid "North Korea"
msgstr "Corea del Nord"

#: app/Hooks/Handlers/CountryNames.php:545
msgid "North Macedonia"
msgstr "Macedonia del Nord"

#: app/Hooks/Handlers/CountryNames.php:677
msgid "Northern Mariana Islands"
msgstr "Isole Marianne Settentrionali"

#: app/Hooks/Handlers/CountryNames.php:685
msgid "Norway"
msgstr "Norvegia"

#: app/Services/TransStrings.php:905
msgid "Not Contains"
msgstr "Non contiene"

#: app/Services/TransStrings.php:906
msgid "Not Empty"
msgstr "Non vuoto"

#: app/Services/TransStrings.php:907
msgid "Not Equal"
msgstr "Non uguale"

#: app/Services/ExternalIntegrations/BricksBuilderIntegration.php:53
#: app/Services/ExternalIntegrations/BricksBuilderIntegration.php:73
msgid "not includes"
msgstr "non include"

#: app/Services/TransStrings.php:1604
msgid "not includes in"
msgstr "non include in"

#: app/Services/TransStrings.php:1605
msgid "not includes in any"
msgstr "non include in nessuno"

#: app/Services/TransStrings.php:908
msgid "Not Right Now"
msgstr "Non adesso"

#: app/Services/TransStrings.php:909
msgid "Not Scheduled"
msgstr "Non programmato"

#: app/Services/TransStrings.php:1237
msgid "Not Working. Please make sure your server has this request type enabled"
msgstr ""
"Non funziona. Assicurati che il tuo server supporti questo tipo di richiesta"

#: app/Functions/helpers.php:611 app/Services/TransStrings.php:912
msgid "Note"
msgstr "Nota"

#: app/Services/TransStrings.php:913
msgid "Note (Optional)"
msgstr "Nota (opzionale)"

#: app/Http/Controllers/CompanyController.php:651
msgid "Note has been successfully added"
msgstr "Nota aggiunta con successo"

#: app/Http/Controllers/SubscriberController.php:642
msgid "Note successfully added"
msgstr "Nota aggiunta con successo"

#: app/Http/Controllers/CompanyController.php:709
#: app/Http/Controllers/SubscriberController.php:702
msgid "Note successfully deleted"
msgstr "Nota eliminata con successo"

#: app/Http/Controllers/CompanyController.php:690
#: app/Http/Controllers/SubscriberController.php:683
msgid "Note successfully updated"
msgstr "Nota aggiornata con successo"

#: app/Services/TransStrings.php:132
msgid "Note: Once deleted, the API key cannot be recovered."
msgstr "Nota: Una volta eliminata, la chiave API non può essere recuperata."

#: app/Services/TransStrings.php:228
msgid ""
"Note: The associated contacts in this campaign will receive emails shortly."
msgstr ""
"Nota: I contatti associati a questa campagna riceveranno le email a breve."

#: app/Services/TransStrings.php:914
msgid "Notes"
msgstr "Note"

#: app/Services/TransStrings.php:915 app/Services/Helper.php:136
#: app/Services/Helper.php:1816
msgid "Notes & Activities"
msgstr "Note e attività"

#: app/Services/TransStrings.php:916
msgid "Notes export feature is only available on pro version"
msgstr ""
"La funzione di esportazione delle note è disponibile solo nella versione pro"

#: app/Services/TransStrings.php:917
msgid "November"
msgstr "Novembre"

#: app/Services/TransStrings.php:918
msgid "Number of Employees"
msgstr "Numero di dipendenti"

#: app/Models/CustomContactField.php:50
msgid "Numeric Field"
msgstr "Campo numerico"

#: app/Services/TransStrings.php:920
msgid "October"
msgstr "Ottobre"

#: app/Services/TransStrings.php:1606
msgid "older than"
msgstr "più vecchio di"

#: app/Hooks/Handlers/CountryNames.php:689
msgid "Oman"
msgstr "Oman"

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:234
msgid "On Payment Refund"
msgstr "Al rimborso del pagamento"

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:232
msgid "On Subscription Active"
msgstr "Su abbonamento attivo"

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:233
msgid "On Subscription Cancel"
msgstr "Su abbonamento annullato"

#: app/Services/TransStrings.php:102
msgid ""
"Only for internal use. This email will not be shown to your customers. Use "
"comma separated value to add multiple emails."
msgstr ""
"Solo per uso interno. Questa email non verrà mostrata ai tuoi clienti. Usa "
"valori separati da virgola per aggiungere più email."

#: app/Services/CrmMigrator/MailerLiteMigrator.php:23
msgid "Only Selected Groups will be imported from MailerLite"
msgstr "Verranno importati solo i gruppi selezionati da MailerLite"

#: app/Services/CrmMigrator/ConvertKitMigrator.php:23
msgid "Only Selected tags will be imported from ConvertKit"
msgstr "Verranno importati solo i tag selezionati da ConvertKit"

#: app/Services/TransStrings.php:921
msgid "Oops!"
msgstr "Ops!"

#: app/Hooks/Handlers/WpQueryLogger.php:45
msgid "Oops! You are not able to see query logs."
msgstr "Ops! Non puoi visualizzare i log delle query."

#: app/Services/TransStrings.php:922
msgid "Open & Clicks"
msgstr "Aperture e clic"

#: app/Services/TransStrings.php:1607
msgid "open a support ticket"
msgstr "apri un ticket di supporto"

#: app/Services/TransStrings.php:923
msgid "Open Count"
msgstr "Conteggio aperture"

#: app/Services/TransStrings.php:924
msgid "Open Rate"
msgstr "Percentuale di apertura"

#: app/Models/CampaignUrlMetric.php:120
msgid "Open Rate (%d)"
msgstr "Percentuale di apertura (%d)"

#: app/Services/TransStrings.php:925 app/Services/TransStrings.php:1608
msgid ""
"Open rate is estimated based on pixel loads. it may not show the correct "
"analytics as many email clients load or block tracking regardless of the "
"actual email open"
msgstr ""
"La percentuale di apertura è stimata in base al caricamento dei pixel. "
"Potrebbe non fornire analisi accurate poiché molti client email caricano o "
"bloccano il tracciamento a prescindere dall'effettiva apertura dell'email"

#: app/Services/TransStrings.php:926
msgid "Opened"
msgstr "Aperto"

#: app/Services/TransStrings.php:927
msgid "Opens"
msgstr "Aperture"

#: app/Services/TransStrings.php:928
#, fuzzy
#| msgid "Moderator"
msgid "Operator"
msgstr "Operatore"

#: app/Services/TransStrings.php:929
msgid "Optimized API connection with Mail Service Providers"
msgstr "Connessione API ottimizzata con i fornitori di servizi di posta"

#: app/Http/Controllers/SettingsController.php:114
msgid "Optin Email Pre Header"
msgstr "Preheader email di iscrizione"

#: app/Http/Controllers/SettingsController.php:108
msgid "Optin Email Subject"
msgstr "Oggetto email di iscrizione"

#: app/Services/TransStrings.php:919
msgid "OR"
msgstr "O"

#: app/Services/TransStrings.php:1609
#: app/views/external/manage_subscription_form.php:39
msgid "or"
msgstr "o"

#: app/Hooks/Handlers/PurchaseHistory.php:128
#: app/Hooks/Handlers/PurchaseHistory.php:344
#, fuzzy
#| msgid "Other"
msgid "Order"
msgstr "Ordine"

#: app/Services/TransStrings.php:1610
msgid "order"
msgstr "ordine"

#: app/Services/TransStrings.php:930
msgid "Order Count"
msgstr "Conteggio ordini"

#: app/Hooks/Handlers/ExternalPages.php:491
msgid "Other (fill in reason below)"
msgstr "Altro (inserisci il motivo di seguito)"

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:164
msgid "Other Fields"
msgstr "Altri campi"

#: app/Services/TransStrings.php:931
msgid "Overall Conversion Rate"
msgstr "Tasso di conversione complessivo"

#: app/Services/Helper.php:99
msgid "Overview"
msgstr "Panoramica"

#: app/Models/Company.php:52
msgid "Owner Email"
msgstr "Email del proprietario"

#: app/Models/Company.php:53
msgid "Owner Name"
msgstr "Nome del proprietario"

#: app/Services/TransStrings.php:932
msgid "Oxygen Builder Integration"
msgstr "Integrazione con Oxygen Builder"

#: app/Services/TransStrings.php:933
msgid "Page"
msgstr "Pagina"

#: app/Services/TransStrings.php:934
msgid "Page Builder INTEGRATIONS:"
msgstr "INTEGRAZIONI Page Builder:"

#: app/Http/Controllers/ImporterController.php:261
msgid "Paid Membership Pro"
msgstr "Abbonamento a pagamento Pro"

#: app/Services/TransStrings.php:935
msgid "Paid Membership Pro integration."
msgstr "Integrazione con Paid Membership Pro."

#: app/Hooks/Handlers/CountryNames.php:693
msgid "Pakistan"
msgstr "Pakistan"

#: app/Services/Helper.php:515
msgid "Pale cyan blue"
msgstr "Ciano chiaro"

#: app/Services/Helper.php:490
msgid "Pale pink"
msgstr "Rosa pallido"

#: app/Hooks/Handlers/CountryNames.php:697
msgid "Palestinian Territory"
msgstr "Territori Palestinesi"

#: app/Hooks/Handlers/CountryNames.php:701
msgid "Panama"
msgstr "Panama"

#: app/Hooks/Handlers/CountryNames.php:705
msgid "Papua New Guinea"
msgstr "Papua Nuova Guinea"

#: app/Hooks/Handlers/CountryNames.php:709
msgid "Paraguay"
msgstr "Paraguay"

#: app/Services/TransStrings.php:409
msgid ""
"Paste the following shortcode to any page or post to start growing your "
"audience"
msgstr ""
"Incolla il seguente shortcode in qualsiasi pagina o post per iniziare a far "
"crescere il tuo pubblico"

#: app/Services/TransStrings.php:936
msgid "Path: "
msgstr "Percorso:"

#: app/Services/TransStrings.php:937
msgid "Pause Sending"
msgstr "Metti in pausa l'invio"

#: app/Services/Helper.php:461
msgid "Paymattic"
msgstr "Paymattic"

#: app/Services/Helper.php:460
msgid "Paymattic Purchase History"
msgstr "Storico acquisti Paymattic"

#: app/Functions/helpers.php:499 app/Functions/helpers.php:546
#: app/Services/TransStrings.php:938 app/Services/TransStrings.php:1611
#: app/Services/Helper.php:1331
msgid "Pending"
msgstr "In sospeso"

#: app/Services/Stats.php:58
msgid "Pending Emails"
msgstr "Email in sospeso"

#: app/Http/Controllers/SettingsController.php:335
msgid "PepiPost"
msgstr "PepiPost"

#: app/Http/Controllers/SettingsController.php:338
msgid "PepiPost Bounce Handler Webhook URL"
msgstr "URL webhook per gestione dei bounce con PepiPost"

#: app/Services/TransStrings.php:1612
msgid "permanently"
msgstr "permanentemente"

#: app/Services/TransStrings.php:939
msgid "Permissions"
msgstr "Permessi"

#: app/Hooks/Handlers/CountryNames.php:713
msgid "Peru"
msgstr "Perù"

#: app/Hooks/Handlers/CountryNames.php:717
msgid "Philippines"
msgstr "Filippine"

#: app/Models/Subscriber.php:731 app/Services/TransStrings.php:940
#: app/Services/Helper.php:946 app/Hooks/Handlers/PrefFormHandler.php:377
#: app/Services/CrmMigrator/BaseMigrator.php:28
#: app/Services/Funnel/FunnelHelper.php:176
msgid "Phone"
msgstr "Telefono"

#: app/Services/TransStrings.php:941 app/Services/Helper.php:167
msgid "Phone Number"
msgstr "Numero di telefono"

#: app/Services/TransStrings.php:942 app/Hooks/Handlers/PrefFormHandler.php:46
msgid "Phone/Mobile"
msgstr "Telefono/Cellulare"

#: app/Services/TransStrings.php:943
msgid "Pick a date"
msgstr "Scegli una data"

#: app/Services/TransStrings.php:944
msgid "Pick a date and time"
msgstr "Scegli data e ora"

#: app/Hooks/Handlers/CountryNames.php:721
msgid "Pitcairn"
msgstr "Pitcairn"

#: app/Services/Helper.php:305
msgid "Plain Centered"
msgstr "Semplice centrato"

#: app/Services/Helper.php:312
msgid "Plain Left"
msgstr "Semplice a sinistra"

#: app/Services/TransStrings.php:945
msgid "Please"
msgstr "Per favore"

#: app/Services/TransStrings.php:955
msgid "Please add at least one list"
msgstr "Per favore aggiungi almeno una lista"

#: app/Services/TransStrings.php:954
msgid "Please add at least one Tag"
msgstr "Per favore aggiungi almeno un tag"

#: app/Http/Controllers/ImporterController.php:148
msgid "Please check the user roles that you want to import as contact"
msgstr "Seleziona i ruoli utente che desideri importare come contatti"

#: app/Http/Controllers/FormsController.php:184
msgid "Please check your inbox to confirm your subscription"
msgstr "Controlla la tua casella di posta per confermare l'iscrizione"

#: app/Services/TransStrings.php:956
msgid "Please configure"
msgstr "Per favore configura"

#: app/Services/TransStrings.php:1613
msgid "Please contact your site administrator to request the permission"
msgstr "Contatta l'amministratore del sito per richiedere i permessi"

#: app/Services/TransStrings.php:1405
msgid "Please do not close this modal."
msgstr "Per favore, non chiudere questa finestra."

#: app/Services/TransStrings.php:767
msgid "Please do not close this window or navigate to other page"
msgstr "Non chiudere questa finestra né navigare ad un'altra pagina"

#: app/Services/TransStrings.php:1045
msgid "Please do not close this window."
msgstr "Non chiudere questa finestra."

#: app/Hooks/Handlers/WpQueryLogger.php:37
msgid ""
"Please enable query logging by calling enableQueryLog() before queries ran."
msgstr ""
"Abilita il logging delle query chiamando enableQueryLog() prima di eseguire "
"le query."

#: app/Services/TransStrings.php:957
msgid "Please enter a keyword"
msgstr "Inserisci una parola chiave"

#: app/Services/TransStrings.php:946
msgid "Please Field Option Values"
msgstr "Per favore inserisci i valori delle opzioni del campo"

#: app/Hooks/Handlers/PrefFormHandler.php:218
msgid "Please fill up all required fields"
msgstr "Riempi tutti i campi obbligatori"

#: app/Services/TransStrings.php:1498
msgid "Please fill up all the fields"
msgstr "Compila tutti i campi"

#: app/Services/Funnel/Actions/WaitTimeAction.php:135
msgid ""
"Please input date and time and this step will be executed after that time "
"(TimeZone will be as per your WordPress Date Time Zone)"
msgstr ""
"Inserisci data e ora, e questo passaggio sarà eseguito dopo tale orario "
"(fuso orario secondo la configurazione di WordPress)"

#: app/Hooks/Handlers/ExternalPages.php:324
msgid "Please let us know a reason"
msgstr "Per favore, facci sapere un motivo"

#: app/Services/TransStrings.php:1419
msgid "Please make sure this email is supported by your SMTP/SES"
msgstr "Assicurati che questa email sia supportata dal tuo SMTP/SES"

#: app/Services/TransStrings.php:1543
msgid ""
"Please make sure your csv file has unique headers. Otherwise, it may fail to "
"import"
msgstr ""
"Assicurati che il tuo file CSV abbia intestazioni uniche, altrimenti "
"potrebbe non riuscire a importarlo"

#: app/Services/TransStrings.php:851
msgid "Please map the csv headers with the respective subscriber fields."
msgstr "Abbina le intestazioni del CSV ai rispettivi campi dell’iscritto."

#: app/Services/TransStrings.php:1508
msgid ""
"Please note that, sequences emails will be scheduled to the contacts as per "
"the current state"
msgstr ""
"Nota: le email della sequenza saranno programmate ai contatti in base allo "
"stato attuale"

#: app/Services/TransStrings.php:1271
msgid ""
"Please note, some smart codes like footer links may not work on test email"
msgstr ""
"Nota: alcuni codici intelligenti come i link del footer potrebbero non "
"funzionare nelle email di prova"

#: app/Http/Controllers/SettingsController.php:367
msgid ""
"Please paste this URL into your Elastic Email's Webhook settings to enable "
"Bounce Handling with FluentCRM"
msgstr ""
"Incolla questo URL nelle impostazioni Webhook di Elastic Email per abilitare "
"la gestione dei bounce con FluentCRM"

#: app/Http/Controllers/SettingsController.php:332
msgid ""
"Please paste this URL into your Mailgun's Webhook settings to enable Bounce "
"Handling with FluentCRM"
msgstr ""
"Incolla questo URL nelle impostazioni Webhook di Mailgun per abilitare la "
"gestione dei bounce con FluentCRM"

#: app/Http/Controllers/SettingsController.php:339
msgid ""
"Please paste this URL into your PepiPost's Webhook settings to enable Bounce "
"Handling with FluentCRM"
msgstr ""
"Incolla questo URL nelle impostazioni Webhook di PepiPost per abilitare la "
"gestione dei bounce con FluentCRM"

#: app/Http/Controllers/SettingsController.php:374
msgid ""
"Please paste this URL into your Postal Server's Webhook settings to enable "
"Bounce Handling with FluentCRM. Please select only MessageBounced & "
"MessageDeliveryFailed event"
msgstr ""
"Incolla questo URL nelle impostazioni Webhook del tuo server Postal per "
"abilitare la gestione dei bounce con FluentCRM. Seleziona solo gli eventi "
"MessageBounced e MessageDeliveryFailed"

#: app/Http/Controllers/SettingsController.php:346
msgid ""
"Please paste this URL into your PostMark's Webhook settings to enable Bounce "
"Handling with FluentCRM"
msgstr ""
"Incolla questo URL nelle impostazioni Webhook di PostMark per abilitare la "
"gestione dei bounce con FluentCRM"

#: app/Http/Controllers/SettingsController.php:353
msgid ""
"Please paste this URL into your SendGrid's Webhook settings to enable Bounce "
"Handling with FluentCRM"
msgstr ""
"Incolla questo URL nelle impostazioni Webhook di SendGrid per abilitare la "
"gestione dei bounce con FluentCRM"

#: app/Http/Controllers/SettingsController.php:360
msgid ""
"Please paste this URL into your SparkPost's Webhook settings to enable "
"Bounce Handling with FluentCRM"
msgstr ""
"Incolla questo URL nelle impostazioni Webhook di SparkPost per abilitare la "
"gestione dei bounce con FluentCRM"

#: app/Services/TransStrings.php:958
msgid "Please provide a day"
msgstr "Indica un giorno"

#: app/Services/TransStrings.php:948
msgid "Please Provide a Form Title"
msgstr "Indica un titolo per il modulo"

#: app/Services/TransStrings.php:959
msgid "Please provide a license key"
msgstr "Fornisci una chiave di licenza"

#: app/Services/TransStrings.php:659
msgid "Please Provide a license key of FluentCRM - Email Marketing Addon"
msgstr "Fornisci una chiave di licenza per FluentCRM - Email Marketing Addon"

#: app/Services/TransStrings.php:1545
msgid "Please provide a name so you can identify easily"
msgstr "Fornisci un nome per identificare facilmente"

#: app/Services/TransStrings.php:960
msgid "Please provide a template name"
msgstr "Fornisci un nome per il modello"

#: app/Services/TransStrings.php:961
msgid "Please provide a time"
msgstr "Fornisci un orario"

#: app/Hooks/Handlers/ExternalPages.php:384
#: app/Hooks/Handlers/ExternalPages.php:438
msgid "Please provide a valid email address"
msgstr "Fornisci un indirizzo email valido"

#: app/Services/TransStrings.php:962
msgid "Please provide an unique title"
msgstr "Fornisci un titolo univoco"

#: app/Http/Controllers/SubscriberController.php:1207
msgid "Please provide bulk options"
msgstr "Fornisci opzioni in blocco"

#: app/Http/Controllers/CampaignController.php:868
msgid "Please provide campaign IDs"
msgstr "Fornisci ID delle campagne"

#: app/Services/TransStrings.php:963
msgid "Please provide condition value"
msgstr "Fornisci un valore di condizione"

#: app/Http/Controllers/SettingsController.php:134
msgid "Please provide details after a contact confirm double option from email"
msgstr ""
"Fornisci i dettagli dopo che un contatto conferma la doppia opzione tramite "
"email"

#: app/Services/TransStrings.php:837
msgid "Please Provide Email address of your existing system user"
msgstr "Fornisci l'indirizzo email del tuo utente di sistema esistente"

#: app/Services/TransStrings.php:219
msgid "Please provide email Body."
msgstr "Fornisci il corpo dell'email."

#: app/Services/TransStrings.php:221
msgid "Please provide email body."
msgstr "Fornisci il corpo dell'email."

#: app/Services/Funnel/Actions/SendEmailAction.php:54
msgid "Please provide email details that you want to send"
msgstr "Fornisci i dettagli dell'email che desideri inviare"

#: app/Services/TransStrings.php:220
msgid "Please provide email Subject."
msgstr "Fornisci l'oggetto dell'email."

#: app/Http/Controllers/FunnelController.php:471
msgid "Please provide funnel IDs"
msgstr "Fornisci ID dei funnel"

#: app/Http/Controllers/FunnelController.php:416
msgid "Please provide funnel subscriber IDs"
msgstr "Fornisci gli ID degli iscritti al funnel"

#: app/Services/TransStrings.php:1034
msgid "Please Provide HTML of your Email"
msgstr "Fornisci l'HTML della tua email"

#: app/Services/TransStrings.php:949
msgid "Please Provide label"
msgstr "Fornisci un'etichetta"

#: app/Services/TransStrings.php:376
msgid "Please provide Name of this Segment"
msgstr "Fornisci il nome di questo segmento"

#: app/Http/Controllers/SettingsController.php:166
msgid "Please provide redirect URL after confirmation"
msgstr "Fornisci l'URL di reindirizzamento dopo la conferma"

#: app/Services/TransStrings.php:218
msgid "Please provide Subject Lines for A/B Test."
msgstr "Fornisci le righe dell'oggetto per il test A/B."

#: app/Services/TransStrings.php:947
msgid "Please Provide Text only (HTML will not support)"
msgstr "Fornisci solo testo (HTML non supportato)"

#: app/Services/TransStrings.php:950
msgid "Please Provide your business information"
msgstr "Fornisci le informazioni della tua attività"

#: app/Services/TransStrings.php:964
msgid "Please purchase Pro"
msgstr "Acquista la versione Pro"

#: app/Services/TransStrings.php:965
msgid "Please review before you delete your old logs"
msgstr "Verifica prima di eliminare i vecchi log"

#: app/Services/TransStrings.php:966
msgid "Please save the details as you can not retrieve again"
msgstr "Salva i dettagli poiché non potrai recuperarli nuovamente"

#: app/Services/TransStrings.php:967
msgid "Please select a date and time"
msgstr "Seleziona una data e un'ora"

#: app/Services/TransStrings.php:968
msgid "Please select allowed days to send emails"
msgstr "Seleziona i giorni consentiti per l'invio delle email"

#: app/Services/TransStrings.php:1008
msgid "Please select an option first"
msgstr "Seleziona prima un'opzione"

#: app/Services/TransStrings.php:969
msgid "Please select at least one tag"
msgstr "Seleziona almeno un tag"

#: app/Services/TransStrings.php:951
msgid "Please Select Clicked URLS"
msgstr "Seleziona gli URL cliccati"

#: app/Services/TransStrings.php:631
msgid "Please select columns that you want to export"
msgstr "Seleziona le colonne che desideri esportare"

#: app/Services/TransStrings.php:970
msgid "Please select companies first"
msgstr "Seleziona prima le aziende"

#: app/Http/Controllers/FunnelController.php:479
#: app/Http/Controllers/CompanyController.php:380
#: app/Http/Controllers/TemplateController.php:260
#: app/Http/Controllers/SubscriberController.php:1082
msgid "Please select status"
msgstr "Seleziona lo stato"

#: app/Services/TransStrings.php:971
msgid "Please select subscribers first"
msgstr "Seleziona prima gli iscritti"

#: app/Services/TransStrings.php:952
msgid "Please Select Tags first"
msgstr "Seleziona prima i tag"

#: app/Services/TransStrings.php:972
msgid "Please select the filters"
msgstr "Seleziona i filtri"

#: app/Services/TransStrings.php:973
msgid "Please select the segment"
msgstr "Seleziona il segmento"

#: app/Services/TransStrings.php:1046
msgid ""
"Please select to which dynamic segment you want to send emails for this "
"campaign"
msgstr ""
"Seleziona a quale segmento dinamico vuoi inviare le email per questa campagna"

#: app/Http/Controllers/SettingsController.php:139
msgid "Please select what will happen once a contact confirm double-optin "
msgstr ""
"Seleziona cosa succederà una volta che un contatto conferma il double-optin"

#: app/Services/TransStrings.php:974
msgid "Please set date and time"
msgstr "Imposta data e ora"

#: app/Services/TransStrings.php:1233
msgid "Please set date and time to send this campaign"
msgstr "Imposta data e ora per inviare questa campagna"

#: app/views/external/unsubscribe.php:64
msgid "Please specify"
msgstr "Specifica"

#: app/Services/TransStrings.php:587
msgid ""
"Please specify if you want to let your subscribers manage the associate "
"lists or not"
msgstr ""
"Specifica se desideri consentire agli iscritti di gestire le liste associate"

#: app/Services/Funnel/Triggers/UserRegistrationTrigger.php:76
msgid ""
"Please specify what will happen if the subscriber already exist in the "
"database"
msgstr "Specifica cosa accadrà se l'iscritto è già presente nel database"

#: app/Services/TransStrings.php:975
msgid "Please update FluentCRM Pro first"
msgstr "Aggiorna prima FluentCRM Pro"

#: app/Hooks/actions.php:177
#, fuzzy
#| msgid "Please update FluentCRM to latest version"
msgid "Please update FluentCRM Pro to latest version"
msgstr "Aggiorna FluentCRM Pro all'ultima versione"

#: app/Services/TransStrings.php:1494
msgid "Please upgrade to pro to use this feature"
msgstr "Aggiorna alla versione Pro per utilizzare questa funzione"

#: app/Services/TransStrings.php:976
msgid "Please upgrade."
msgstr "Effettua l'upgrade."

#: app/Services/TransStrings.php:953
msgid "Please Upload a CSV first"
msgstr "Carica prima un file CSV"

#: app/Services/TransStrings.php:1499
msgid ""
"Please use the following smart url and when a user click a link in an email, "
"on your site or anywhere then your selected tags and lists will be applied."
msgstr ""
"Utilizza il seguente URL smart: quando un utente clicca su un link in "
"un'email, sul tuo sito o altrove, i tag e le liste selezionate saranno "
"applicati."

#: app/Services/TransStrings.php:666
msgid ""
"Please use the shortcode [fluentcrm_pref] to show the form for your "
"subscribers"
msgstr ""
"Utilizza lo shortcode [fluentcrm_pref] per mostrare il modulo ai tuoi "
"iscritti"

#: app/Http/Controllers/SettingsController.php:325
msgid "Please use this bounce handler url in your Amazon SES + SNS settings"
msgstr ""
"Usa questo URL per la gestione dei bounce nelle impostazioni di Amazon SES + "
"SNS"

#: app/Services/TransStrings.php:977
msgid "Please view the"
msgstr "Guarda il"

#: app/Hooks/Handlers/CountryNames.php:725
msgid "Poland"
msgstr "Polonia"

#: app/Services/TransStrings.php:1688
msgid "Popular pre-built funnel templates"
msgstr "Modelli di funnel predefiniti popolari"

#: app/Hooks/Handlers/CountryNames.php:729
msgid "Portugal"
msgstr "Portogallo"

#: app/Models/Company.php:62 app/Models/Subscriber.php:728
#: app/Services/TransStrings.php:979 app/Services/Helper.php:165
#: app/Services/Helper.php:932 app/Services/CrmMigrator/BaseMigrator.php:38
#: app/Services/Funnel/FunnelHelper.php:160
msgid "Postal Code"
msgstr "CAP"

#: app/Http/Controllers/SettingsController.php:370
msgid "Postal Server"
msgstr "Server Postal"

#: app/Http/Controllers/SettingsController.php:373
msgid "Postal Server Bounce Handler Webhook URL"
msgstr "URL webhook per la gestione dei bounce con Server Postal"

#: app/Http/Controllers/SettingsController.php:342
msgid "PostMark"
msgstr "PostMark"

#: app/Http/Controllers/SettingsController.php:345
msgid "PostMark Bounce Handler Webhook URL"
msgstr "URL webhook per la gestione dei bounce con PostMark"

#: app/views/emails/plain/Template.php:95
#: app/views/emails/classic/Template.php:93
#: app/views/emails/raw_classic/Template.php:37
#: app/views/emails/simple/Template.php:92
#: app/views/emails/web_preview/Template.php:14
msgid "Powered By"
msgstr "Powered By"

#: app/Services/TransStrings.php:980
msgid "Prefix"
msgstr "Prefisso"

#: app/Services/TransStrings.php:981
msgid "Prev step"
msgstr "Passaggio precedente"

#: app/Services/TransStrings.php:982
msgid "Preview"
msgstr "Anteprima"

#: app/Services/TransStrings.php:983
msgid "Preview Email"
msgstr "Anteprima Email"

#: app/Services/TransStrings.php:1615
msgid ""
"Preview Email may differ from actual email that was sent. Specially the "
"dynamic data is shown as it's current state. You may check in FluentSMTP's "
"log to see the actual email"
msgstr ""
"L'email di anteprima potrebbe differire dall'email effettivamente inviata. "
"In particolare, i dati dinamici vengono mostrati nel loro stato attuale. "
"Puoi controllare nei log di FluentSMTP per vedere l'email effettiva"

#: app/Services/TransStrings.php:984
msgid "Preview Form"
msgstr "Anteprima Modulo"

#: app/Services/TransStrings.php:985
msgid "Preview Log Summary"
msgstr "Anteprima riepilogo log"

#: app/Services/TransStrings.php:986
msgid "Preview Text:"
msgstr "Anteprima Testo:"

#: app/Services/TransStrings.php:987
msgid "Preview The Form"
msgstr "Anteprima del Modulo"

#: app/Services/TransStrings.php:988
msgid "Previous"
msgstr "Precedente"

#: app/Services/TransStrings.php:989
msgid "Previous Emails"
msgstr "Email precedenti"

#: app/Services/TransStrings.php:990
msgid "Previous Month"
msgstr "Mese precedente"

#: app/Services/TransStrings.php:991
msgid "Previous Period"
msgstr "Periodo precedente"

#: app/Services/TransStrings.php:992
msgid "Previous Quarter"
msgstr "Trimestre precedente"

#: app/Services/TransStrings.php:993
msgid "Previous Year"
msgstr "Anno precedente"

#: app/Services/TransStrings.php:995
msgid "Primary"
msgstr "Primario"

#: app/Models/Subscriber.php:737 app/Services/TransStrings.php:996
msgid "Primary Company"
msgstr "Azienda principale"

#: app/Services/TransStrings.php:997
#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:134
msgid "Primary Fields"
msgstr "Campi principali"

#: app/Services/TransStrings.php:998
msgid "Primary Fields that can be editable"
msgstr "Campi principali modificabili"

#: app/Services/TransStrings.php:999
msgid "Priority (%)"
msgstr "Priorità (%)"

#: app/Services/TransStrings.php:1000 app/views/admin/menu_page.php:15
msgid "Pro"
msgstr "Pro"

#: app/Services/TransStrings.php:1616
msgid "pro version of FluentCRM"
msgstr "versione pro di FluentCRM"

#: app/Services/TransStrings.php:1010
#: app/Http/Controllers/UsersController.php:79 app/Hooks/CLI/Commands.php:136
msgid "Processing"
msgstr "In elaborazione"

#: app/Services/TransStrings.php:1617
msgid "processing"
msgstr "elaborazione"

#: app/Services/TransStrings.php:1047
msgid ""
"Processing Error happened. Maybe it's timeout error. Resume or StartOver"
msgstr ""
"Si è verificato un errore di elaborazione. Potrebbe trattarsi di un errore "
"di timeout. Riprendi o Ricomincia"

#: app/Services/TransStrings.php:1011
msgid "Processing now..."
msgstr "In elaborazione ora..."

#: app/Services/TransStrings.php:1012
msgid "Processing now.Please wait a bit..."
msgstr "In elaborazione. Attendi un momento..."

#: app/Services/TransStrings.php:1013
msgid "Product, promo code, or slogan (e.g. spring_sale)"
msgstr "Prodotto, codice promozionale o slogan (es. sconto_primavera)"

#: app/Services/TransStrings.php:1014
msgid "Profile"
msgstr "Profilo"

#: app/Services/TransStrings.php:1018
msgid "Provide campaign details"
msgstr "Fornisci i dettagli della campagna"

#: app/Http/Controllers/SettingsController.php:122
msgid "Provide Email Body for the double-optin"
msgstr "Fornisci il corpo dell'email per la doppia conferma"

#: app/Services/TransStrings.php:1016
msgid "Provide Name"
msgstr "Fornisci il nome"

#: app/Services/TransStrings.php:176
msgid "Provide the basic information of your recurring email campaign."
msgstr "Fornisci le informazioni di base della tua campagna email ricorrente."

#: app/Services/TransStrings.php:588
msgid "Provide Valid Email Address that will be used in reply to (Optional)"
msgstr "Fornisci un indirizzo email valido per le risposte (Opzionale)"

#: app/Services/TransStrings.php:1017
msgid "Provide Valid Email Address that will be used to send emails"
msgstr "Fornisci un indirizzo email valido che verrà usato per inviare email"

#: app/Http/Controllers/SubscriberController.php:292
#: app/Http/Controllers/SubscriberController.php:374
msgid "Provided email already assigned to another subscriber."
msgstr "L'email fornita è già assegnata a un altro iscritto."

#: app/Http/Controllers/ListsController.php:199
msgid "Provided Lists have been successfully created"
msgstr "Le liste fornite sono state create con successo"

#: app/Services/TransStrings.php:1019
msgid "Public User Clicks"
msgstr "Clic degli utenti pubblici"

#: app/Services/TransStrings.php:1020
msgid "Publish"
msgstr "Pubblica"

#: app/Services/TransStrings.php:1618
msgid "published"
msgstr "pubblicato"

#: app/Services/TransStrings.php:1619
msgid "published within"
msgstr "pubblicato entro"

#: app/Hooks/Handlers/CountryNames.php:733
msgid "Puerto Rico"
msgstr "Porto Rico"

#: app/Services/TransStrings.php:1021
msgid "Purchase Count"
msgstr "Conteggio acquisti"

#: app/Services/Helper.php:112
msgid "Purchase History"
msgstr "Storico degli acquisti"

#: app/Services/TransStrings.php:1022
msgid "Purchase History from"
msgstr "Storico degli acquisti da"

#: app/Services/TransStrings.php:1009
msgid ""
"Purchase history from EDD/WooCommerce will be shown here. Currently no E-"
"Commerce Plugin found in your installation"
msgstr ""
"Lo storico degli acquisti da EDD/WooCommerce verrà mostrato qui. Attualmente,"
" non è stato trovato alcun plugin e-commerce nella tua installazione"

#: app/Services/TransStrings.php:1023
msgid "Purchase one here"
msgstr "Acquistane uno qui"

#: app/Hooks/Handlers/PurchaseHistory.php:448
msgid "Purchased Products"
msgstr "Prodotti acquistati"

#: app/Services/Helper.php:1233 app/Services/Helper.php:1287
msgid "Purchased Products (Pro Required)"
msgstr "Prodotti acquistati (Richiesta versione Pro)"

#: app/Services/TransStrings.php:1025
msgid "Purged"
msgstr "Eliminato"

#: app/Hooks/Handlers/CountryNames.php:737
msgid "Qatar"
msgstr "Qatar"

#: app/Services/TransStrings.php:1026
msgid "Quick Contact Navigation"
msgstr "Navigazione rapida dei contatti"

#: app/Services/TransStrings.php:1027 app/Hooks/Handlers/AdminBar.php:75
msgid "Quick Links"
msgstr "Collegamenti rapidi"

#: app/Services/TransStrings.php:1028
msgid "Quick Overview"
msgstr "Panoramica rapida"

#: app/Services/TransStrings.php:1029
msgid "Quick Stats"
msgstr "Statistiche rapide"

#: app/Functions/helpers.php:616
msgid "Quote: Accepted"
msgstr "Preventivo: Accettato"

#: app/Functions/helpers.php:617
msgid "Quote: Refused"
msgstr "Preventivo: Rifiutato"

#: app/Functions/helpers.php:615
msgid "Quote: Sent"
msgstr "Preventivo: Inviato"

#: app/Models/CustomContactField.php:65
msgid "Radio Choice"
msgstr "Scelta radio"

#: app/Services/Helper.php:328
msgid "Raw HTML"
msgstr "HTML puro"

#: app/Services/TransStrings.php:1036
msgid "Re-apply New Steps"
msgstr "Riappllica nuovi passaggi"

#: app/Services/TransStrings.php:1035
msgid "Re-Sync"
msgstr "Risinc."

#: app/Services/TransStrings.php:1037
msgid "Read CLI Documentation"
msgstr "Leggi la documentazione CLI"

#: app/Services/TransStrings.php:1621
msgid "read the doc for advanced usage"
msgstr "leggi la documentazione per l'uso avanzato"

#: app/Services/TransStrings.php:1038
msgid "Read the documentation"
msgstr "Leggi la documentazione"

#: app/Services/TransStrings.php:1039
msgid "Real-Time Email Delivery"
msgstr "Consegna email in tempo reale"

#: app/Services/TransStrings.php:1040
msgid "Reason:"
msgstr "Motivo:"

#: app/Services/TransStrings.php:1053
msgid "Recipient Sections"
msgstr "Sezioni dei destinatari"

#: app/Services/TransStrings.php:1054
#: app/Http/Controllers/CampaignController.php:275
msgid "Recipient settings has been updated"
msgstr "Le impostazioni del destinatario sono state aggiornate"

#: app/Services/TransStrings.php:1055
msgid "Recipients"
msgstr "Destinatari"

#: app/Services/TransStrings.php:1057
msgid "Recommended Plugins and Addons"
msgstr "Plugin e Addon consigliati"

#: app/Services/TransStrings.php:1684
msgid "Recovered Orders"
msgstr "Ordini recuperati"

#: app/Services/TransStrings.php:1058 app/Hooks/Handlers/AdminMenu.php:125
#: app/Hooks/Handlers/AdminMenu.php:126 app/Hooks/Handlers/AdminMenu.php:360
msgid "Recurring Campaigns"
msgstr "Campagne ricorrenti"

#: app/Services/TransStrings.php:1059
msgid "Recurring Email Campaigns"
msgstr "Campagne email ricorrenti"

#: app/Http/Controllers/SettingsController.php:147
msgid "Redirect to an URL"
msgstr "Reindirizza a un URL"

#: app/Http/Controllers/SettingsController.php:164
#: app/Http/Controllers/SettingsController.php:165
#, fuzzy
#| msgid "Redirect To"
msgid "Redirect URL"
msgstr "Reindirizza a"

#: app/Services/TransStrings.php:590
msgid "Redirect URL after unsubscribe (leave blank for inline message)"
msgstr ""
"URL di reindirizzamento dopo la cancellazione (lascia vuoto per messaggio in "
"linea)"

#: app/Services/TransStrings.php:1060
msgid "Refresh Email Preview"
msgstr "Aggiorna anteprima email"

#: app/Services/Helper.php:1349
msgid "Registration Date (Pro Required)"
msgstr "Data di registrazione (Richiesta versione Pro)"

#: app/Services/TransStrings.php:1061
msgid "Relationship Type"
msgstr "Tipo di relazione"

#: app/Services/TransStrings.php:1062
msgid "Reload Page"
msgstr "Ricarica pagina"

#: app/Services/TransStrings.php:1063
msgid "Remove"
msgstr "Rimuovi"

#: app/Services/TransStrings.php:1064
msgid "Remove Association"
msgstr "Rimuovi associazione"

#: app/Services/Funnel/Actions/DetachCompanyAction.php:34
msgid "Remove Contact from the Selected Company"
msgstr "Rimuovi contatto dall'azienda selezionata"

#: app/Services/Funnel/Actions/DetachListAction.php:34
msgid "Remove Contact from the Selected Lists"
msgstr "Rimuovi contatto dalle liste selezionate"

#: app/Services/Funnel/Actions/DetachTagAction.php:34
msgid "Remove Contact from the Selected Tags"
msgstr "Rimuovi contatto dai tag selezionati"

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:251
msgid "Remove Contact Tags"
msgstr "Rimuovi tag contatto"

#: app/Services/TransStrings.php:1065
#: app/Services/Funnel/Actions/DetachCompanyAction.php:22
msgid "Remove From Company"
msgstr "Rimuovi dall'azienda"

#: app/Services/Funnel/Actions/DetachListAction.php:22
msgid "Remove From List"
msgstr "Rimuovi dalla lista"

#: app/Services/TransStrings.php:1066
msgid "Remove From Lists"
msgstr "Rimuovi dalle liste"

#: app/Services/TransStrings.php:1067
msgid "Remove From Sequence"
msgstr "Rimuovi dalla sequenza"

#: app/Services/Funnel/Actions/DetachTagAction.php:22
msgid "Remove From Tag"
msgstr "Rimuovi dal tag"

#: app/Services/TransStrings.php:1068
msgid "Remove From Tags"
msgstr "Rimuovi dai tag"

#: app/Services/TransStrings.php:1069
msgid "Remove Lists when clicked (optional)"
msgstr "Rimuovi liste al clic (opzionale)"

#: app/Services/TransStrings.php:1070
msgid "Remove Tags"
msgstr "Rimuovi tag"

#: app/Services/TransStrings.php:222
msgid "Remove Tags From Subscribers"
msgstr "Rimuovi tag dagli iscritti"

#: app/Services/TransStrings.php:1071
msgid "Remove Tags when clicked (optional)"
msgstr "Rimuovi tag al clic (opzionale)"

#: app/Services/Funnel/Actions/DetachCompanyAction.php:23
msgid "Remove this contact from the selected company"
msgstr "Rimuovi questo contatto dall'azienda selezionata"

#: app/Services/Funnel/Actions/DetachListAction.php:23
msgid "Remove this contact from the selected lists"
msgstr "Rimuovi questo contatto dalle liste selezionate"

#: app/Services/Funnel/Actions/DetachTagAction.php:23
msgid "Remove this contact from the selected Tags"
msgstr "Rimuovi questo contatto dai tag selezionati"

#: app/Services/TransStrings.php:1072
#, fuzzy
#| msgid "Remove WP User Role"
msgid "Remove User Role"
msgstr "Rimuovi ruolo utente"

#: app/Services/TransStrings.php:1073
msgid "Remove User Role: "
msgstr "Rimuovi ruolo utente:"

#: app/Services/TransStrings.php:1075
msgid "Replace Options"
msgstr "Sostituisci opzioni"

#: app/Services/TransStrings.php:1076
msgid "Replace Value"
msgstr "Sostituisci valore"

#: app/Services/TransStrings.php:1077
msgid "Reply To Email"
msgstr "Rispondi a Email"

#: app/Services/TransStrings.php:1079
msgid "Reply to Email (Optional)"
msgstr "Rispondi a Email (Opzionale)"

#: app/Services/TransStrings.php:1078
msgid "Reply To Name"
msgstr "Rispondi a Nome"

#: app/Services/TransStrings.php:1080
msgid "Reply to Name"
msgstr "Rispondi a Nome"

#: app/Services/TransStrings.php:1081
msgid "Reply to Name (Optional)"
msgstr "Rispondi a Nome (Opzionale)"

#: app/Services/TransStrings.php:1082
msgid "Report Type:"
msgstr "Tipo di report:"

#: app/Services/TransStrings.php:1083 app/Hooks/Handlers/AdminMenu.php:188
#: app/Hooks/Handlers/AdminMenu.php:189 app/Hooks/Handlers/AdminMenu.php:409
#: app/Hooks/Handlers/AdminMenu.php:1335 app/Hooks/Handlers/AdminMenu.php:1336
msgid "Reports"
msgstr "Report"

#: app/views/external/manage_subscription_request_form.php:13
msgid "Request Manage Subscription"
msgstr "Richiedi la gestione dell'iscrizione"

#: app/views/external/unsubscribe_request_form.php:13
msgid "Request Unsubscribe"
msgstr "Richiedi disiscrizione"

#: app/Services/TransStrings.php:1084
msgid "Require FluentCRM Pro"
msgstr "Richiede FluentCRM Pro"

#: app/Services/TransStrings.php:1085
msgid "Resend"
msgstr "Reinvia"

#: app/Services/TransStrings.php:1086
msgid "Resend Any Emails"
msgstr "Reinvia tutte le email"

#: app/Services/TransStrings.php:1692
msgid "Resent Count"
msgstr "Conteggio reinvii"

#: app/Services/TransStrings.php:1087
msgid "Reset FluentCRM Database tables"
msgstr "Reimposta le tabelle del database di FluentCRM"

#: app/Services/TransStrings.php:1031
msgid "REST API"
msgstr "REST API"

#: app/Services/TransStrings.php:1032
msgid "REST API Access Management"
msgstr "Gestione Accesso REST API"

#: app/Services/TransStrings.php:1033
msgid "REST API key has been created to selected user"
msgstr "La chiave REST API è stata creata per l'utente selezionato"

#: app/Services/TransStrings.php:1088
msgid "Rest API Status"
msgstr "Stato API REST"

#: app/Http/Controllers/ImporterController.php:279
msgid "Restrict Content Pro"
msgstr "Restrict Content Pro"

#: app/Services/TransStrings.php:1091
msgid "Restrict Content Pro Integration"
msgstr "Integrazione Restrict Content Pro"

#: app/Services/TransStrings.php:1092
msgid "Resume"
msgstr "Riprendi"

#: app/Services/TransStrings.php:1093
msgid "Resume Sending"
msgstr "Riprendi l'invio"

#: app/Services/TransStrings.php:1094
msgid "Retry Sending"
msgstr "Ritenta l'invio"

#: app/Hooks/Handlers/CountryNames.php:741
msgid "Reunion"
msgstr "Reunion"

#: app/Models/Campaign.php:658 app/Models/CampaignUrlMetric.php:164
#: app/Services/TransStrings.php:1095
msgid "Revenue"
msgstr "Entrate"

#: app/Services/TransStrings.php:1096
msgid "Revenue From Sequence Emails"
msgstr "Entrate dalle email in sequenza"

#: app/Services/TransStrings.php:1098
msgid "Revenue from this email campaign"
msgstr "Entrate da questa campagna email"

#: app/Services/TransStrings.php:1097
msgid "Revenue Report"
msgstr "Rapporto Entrate"

#: app/Services/TransStrings.php:1099
msgid "Review"
msgstr "Rivedi"

#: app/Services/TransStrings.php:1100
msgid "Review & Import"
msgstr "Rivedi e Importa"

#: app/Services/TransStrings.php:1101
msgid "Review & Send"
msgstr "Rivedi e Invia"

#: app/Hooks/Handlers/CountryNames.php:745
msgid "Romania"
msgstr "Romania"

#: app/Services/TransStrings.php:1102
msgid "Run Action Task to do particular task on the contact"
msgstr "Esegui un'attività specifica per il contatto"

#: app/Services/TransStrings.php:1595
msgid ""
"Run funnels and marketing automation for different LMS actions like course "
"enrollment, course completed, lesson completed, Group Membership Enrollment, "
"etc."
msgstr ""
"Esegui funnel e automazione di marketing per diverse azioni LMS come "
"iscrizione ai corsi, corso completato, lezione completata, iscrizione a "
"gruppi, ecc."

#: app/Services/TransStrings.php:1596
msgid ""
"Run funnels and marketing automations for different LMS actions like course "
"enrollment, course completed, lesson completed etc."
msgstr ""
"Esegui funnel e automazione di marketing per azioni LMS come iscrizione al "
"corso, completamento corso, completamento lezione, ecc."

#: app/Services/TransStrings.php:1661
msgid ""
"Run funnels and marketing automations for different TutorLMS actions like "
"course enrollment, course completed etc."
msgstr ""
"Esegui funnel e automazioni di marketing per diverse azioni TutorLMS come "
"iscrizione ai corsi, corso completato, ecc."

#: app/Services/Funnel/Benchmarks/RemoveFromTagBenchmark.php:65
msgid "Run if any selected tag removed from a contact"
msgstr ""
"Esegui se uno qualsiasi dei tag selezionati viene rimosso da un contatto"

#: app/Services/TransStrings.php:1103
msgid "Run Manually"
msgstr "Esegui manualmente"

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:241
msgid "Run only on events"
msgstr "Esegui solo sugli eventi"

#: app/Services/Funnel/BaseTrigger.php:56
msgid ""
"Run the automation actions even contact status is not in subscribed status"
msgstr ""
"Esegui le azioni di automazione anche se lo stato del contatto non è su "
"iscritto"

#: app/Services/Funnel/Triggers/FluentFormSubmissionTrigger.php:180
msgid ""
"Run this automation only once per contact. If unchecked then it will over-"
"write existing flow"
msgstr ""
"Esegui questa automazione solo una volta per contatto. Se non selezionata, "
"sovrascriverà il flusso esistente"

#: app/Services/Funnel/Benchmarks/RemoveFromTagBenchmark.php:60
#: app/Services/Funnel/Benchmarks/RemoveFromListBenchmark.php:60
#: app/Services/Funnel/Benchmarks/ListAppliedBenchmark.php:60
#: app/Services/Funnel/Benchmarks/TagAppliedBenchmark.php:60
msgid "Run When"
msgstr "Esegui Quando"

#: app/Hooks/Handlers/CountryNames.php:749
msgid "Russia"
msgstr "Russia"

#: app/Hooks/Handlers/CountryNames.php:753
msgid "Rwanda"
msgstr "Ruanda"

#: app/Hooks/Handlers/CountryNames.php:793
msgid "S&atilde;o Tom&eacute; and Pr&iacute;ncipe"
msgstr "São Tomé e Príncipe"

#: app/Hooks/Handlers/CountryNames.php:757
msgid "Saint Barth&eacute;lemy"
msgstr "Saint Barthélemy"

#: app/Hooks/Handlers/CountryNames.php:761
msgid "Saint Helena"
msgstr "Sant'Elena"

#: app/Hooks/Handlers/CountryNames.php:765
msgid "Saint Kitts and Nevis"
msgstr "Saint Kitts e Nevis"

#: app/Hooks/Handlers/CountryNames.php:769
msgid "Saint Lucia"
msgstr "Santa Lucia"

#: app/Hooks/Handlers/CountryNames.php:777
msgid "Saint Martin (Dutch part)"
msgstr "Saint Martin (parte olandese)"

#: app/Hooks/Handlers/CountryNames.php:773
msgid "Saint Martin (French part)"
msgstr "Saint Martin (parte francese)"

#: app/Hooks/Handlers/CountryNames.php:781
msgid "Saint Pierre and Miquelon"
msgstr "Saint Pierre e Miquelon"

#: app/Hooks/Handlers/CountryNames.php:785
msgid "Saint Vincent and the Grenadines"
msgstr "Saint Vincent e Grenadine"

#: app/Services/TransStrings.php:1105
msgid "Sales"
msgstr "Vendite"

#: app/Hooks/Handlers/CountryNames.php:1009
msgid "Samoa"
msgstr "Samoa"

#: app/Hooks/Handlers/CountryNames.php:789
msgid "San Marino"
msgstr "San Marino"

#: app/Services/TransStrings.php:1106
msgid "Saturday"
msgstr "Sabato"

#: app/Hooks/Handlers/CountryNames.php:797
msgid "Saudi Arabia"
msgstr "Arabia Saudita"

#: app/Services/TransStrings.php:1107
msgid "Save"
msgstr "Salva"

#: app/Services/TransStrings.php:1108
msgid "Save & close"
msgstr "Salva e chiudi"

#: app/Services/TransStrings.php:1112
msgid "Save as dynamic Segment"
msgstr "Salva come segmento dinamico"

#: app/Services/TransStrings.php:1113
msgid "Save as template"
msgstr "Salva come modello"

#: app/Services/TransStrings.php:1109
msgid "Save Note"
msgstr "Salva nota"

#: app/Services/TransStrings.php:1110
msgid "Save Settings"
msgstr "Salva impostazioni"

#: app/Services/TransStrings.php:1111
msgid "Save Template"
msgstr "Salva modello"

#: app/Services/TransStrings.php:1114
msgid "Saved"
msgstr "Salvato"

#: app/Services/TransStrings.php:1116
msgid "Schedule"
msgstr "Programma"

#: app/Services/TransStrings.php:1117
msgid "Schedule Campaign"
msgstr "Programma campagna"

#: app/Services/Funnel/Actions/SendEmailAction.php:104
msgid "Schedule Date and Time"
msgstr "Programma data e ora"

#: app/Services/TransStrings.php:1122
msgid "Schedule emails within a specified date-time range"
msgstr "Programma email entro un intervallo di data e ora specifico"

#: app/Services/TransStrings.php:1119
msgid "Schedule the emails"
msgstr "Programma le email"

#: app/Services/TransStrings.php:1120
msgid "Schedule this campaign"
msgstr "Programma questa campagna"

#: app/Services/Funnel/Actions/SendEmailAction.php:90
msgid "Schedule this email to a specific date"
msgstr "Programma questa email a una data specifica"

#: app/Services/TransStrings.php:1118
msgid "Schedule Time"
msgstr "Orario di programmazione"

#: app/Services/TransStrings.php:1123
msgid "Scheduled"
msgstr "Programmato"

#: app/Services/TransStrings.php:1626
msgid "scheduled"
msgstr "programmato"

#: app/Services/TransStrings.php:1124
msgid "Scheduled (pending)"
msgstr "Programmato (in attesa)"

#: app/Services/TransStrings.php:1125
msgid "Scheduled At"
msgstr "Programmata a"

#: app/Services/TransStrings.php:1126
msgid "Scheduled at:"
msgstr "Programmata a:"

#: app/Http/Controllers/SettingsController.php:491
#: app/Http/Controllers/SettingsController.php:513
msgid "Scheduled Automation Tasks"
msgstr "Attività di automazione programmate"

#: app/Http/Controllers/SettingsController.php:482
#: app/Http/Controllers/SettingsController.php:514
msgid "Scheduled Email Processing"
msgstr "Elaborazione email programmata"

#: app/Http/Controllers/SettingsController.php:512
msgid "Scheduled Email Sending"
msgstr "Invio email programmato"

#: app/Http/Controllers/SettingsController.php:473
msgid "Scheduled Email Sending Tasks"
msgstr "Attività di invio email programmate"

#: app/Hooks/CLI/Commands.php:50
msgid "Scheduled Emails"
msgstr "Email programmate"

#: app/Services/TransStrings.php:1127
msgid "Scheduled on"
msgstr "Programmato il"

#: app/Services/TransStrings.php:1128
msgid "Scheduling Settings"
msgstr "Impostazioni di programmazione"

#: app/Services/TransStrings.php:1129
msgid "Search"
msgstr "Cerca"

#: app/Services/TransStrings.php:1683
msgid "Search by Email"
msgstr "Cerca per Email"

#: app/Services/TransStrings.php:1133
msgid "Search by title..."
msgstr "Cerca per titolo..."

#: app/Services/TransStrings.php:1130
msgid "Search Companies"
msgstr "Cerca aziende"

#: app/Services/TransStrings.php:1134
msgid "Search contact"
msgstr "Cerca contatto"

#: app/Hooks/Handlers/AdminBar.php:72 app/Hooks/Handlers/AdminBar.php:84
msgid "Search Contacts"
msgstr "Cerca contatti"

#: app/Services/TransStrings.php:1131
msgid "Search Results for:"
msgstr "Risultati di ricerca per:"

#: app/Services/TransStrings.php:1132
msgid "Search Type and Enter..."
msgstr "Digita e premi Invio per cercare..."

#: app/Services/TransStrings.php:1135
msgid "Search..."
msgstr "Cerca..."

#: app/Services/TransStrings.php:1136
msgid "Segment Name"
msgstr "Nome del segmento"

#: app/Services/TransStrings.php:1627
msgid ""
"Segment your contacts by advanced data points like contact's properties, "
"custom fields, tags, purchase history etc."
msgstr ""
"Segmenta i tuoi contatti utilizzando dati avanzati come proprietà, campi "
"personalizzati, tag, cronologia degli acquisti, ecc."

#: app/Hooks/Handlers/AdminMenu.php:334
msgid "Segments"
msgstr "Segmenti"

#: app/Services/TransStrings.php:1137
#, fuzzy
#| msgid "selected"
msgid "Select"
msgstr "Seleziona"

#: app/Services/TransStrings.php:1138
msgid "Select A List"
msgstr "Seleziona una lista"

#: app/Services/TransStrings.php:1173
msgid "Select a List"
msgstr "Seleziona una lista"

#: app/Services/TransStrings.php:1175
msgid "Select a starter design to build your email"
msgstr "Seleziona un design di partenza per creare la tua email"

#: app/Services/Funnel/Benchmarks/RemoveFromTagBenchmark.php:56
msgid "Select a Tag"
msgstr "Seleziona un tag"

#: app/Services/TransStrings.php:1176
msgid "Select a template"
msgstr "Seleziona un modello"

#: app/Services/TransStrings.php:1174
msgid "Select a Trigger"
msgstr "Seleziona un trigger"

#: app/Services/TransStrings.php:1139
msgid "Select Action"
msgstr "Seleziona azione"

#: app/Services/TransStrings.php:1140
msgid "Select Activity Type"
msgstr "Seleziona il tipo di attività"

#: app/Services/AutoSubscribe.php:44 app/Services/AutoSubscribe.php:131
#: app/Services/AutoSubscribe.php:289
msgid "Select Assign List"
msgstr "Seleziona la lista assegnata"

#: app/Services/AutoSubscribe.php:58 app/Services/AutoSubscribe.php:144
#: app/Services/AutoSubscribe.php:302
msgid "Select Assign Tag"
msgstr "Seleziona il tag assegnato"

#: app/Services/TransStrings.php:1141
#, fuzzy
#| msgid "Select Automations"
msgid "Select Automation"
msgstr "Seleziona automazione"

#: app/Services/TransStrings.php:1177
msgid "Select automation conditions"
msgstr "Seleziona le condizioni di automazione"

#: app/Services/TransStrings.php:1142
msgid "Select Automation Funnel"
msgstr "Seleziona il funnel di automazione"

#: app/Services/TransStrings.php:1143
msgid "Select Bulk Action"
msgstr "Seleziona azione in blocco"

#: app/Services/TransStrings.php:1178
msgid "Select by Roles"
msgstr "Seleziona per ruoli"

#: app/Models/CustomContactField.php:55
msgid "Select choice"
msgstr "Seleziona scelta"

#: app/Services/TransStrings.php:1144
msgid "Select Companies"
msgstr "Seleziona aziende"

#: app/Services/Funnel/Actions/ApplyCompanyAction.php:40
#: app/Services/Funnel/Actions/ApplyCompanyAction.php:41
#: app/Services/Funnel/Actions/DetachCompanyAction.php:40
#: app/Services/Funnel/Actions/DetachCompanyAction.php:41
msgid "Select Company"
msgstr "Seleziona azienda"

#: app/Services/Funnel/Actions/DetachCompanyAction.php:35
msgid "Select Company that you want to remove from targeted Contact"
msgstr "Seleziona l'azienda che vuoi rimuovere dal contatto mirato"

#: app/Services/TransStrings.php:1145
#, fuzzy
#| msgid "Check Condition"
msgid "Select Condition"
msgstr "Seleziona condizione"

#: app/Services/TransStrings.php:1146
msgid "Select Contact"
msgstr "Seleziona contatto"

#: app/Services/Funnel/Triggers/FluentFormSubmissionTrigger.php:93
#: app/Services/Funnel/Triggers/FluentFormSubmissionTrigger.php:95
msgid "Select Contact Property"
msgstr "Seleziona la proprietà del contatto"

#: app/Services/Funnel/Actions/WaitTimeAction.php:199
msgid "Select Contact's Custom Field"
msgstr "Seleziona il campo personalizzato del contatto"

#: app/Hooks/Handlers/PrefFormHandler.php:486
msgid "Select Country"
msgstr "Seleziona il paese"

#: app/Services/TransStrings.php:1179
msgid "Select country"
msgstr "Seleziona paese"

#: app/Services/TransStrings.php:1147
msgid "Select Current CRM"
msgstr "Seleziona CRM corrente"

#: app/Services/TransStrings.php:1180
msgid "Select custom contacts by advanced filters"
msgstr "Seleziona contatti personalizzati con filtri avanzati"

#: app/Services/TransStrings.php:1181
msgid "Select data source and operator first"
msgstr "Seleziona prima la fonte dati e l'operatore"

#: app/Services/Funnel/Actions/WaitTimeAction.php:134
msgid "Select Date & Time"
msgstr "Seleziona data e ora"

#: app/Services/TransStrings.php:1190
msgid "Select Date & Time Range that you want to send the emails"
msgstr "Seleziona l'intervallo di data e ora in cui vuoi inviare le email"

#: app/Services/Funnel/Actions/SendEmailAction.php:107
msgid "Select Date and Time"
msgstr "Seleziona data e ora"

#: app/Services/TransStrings.php:1182
msgid "Select date and time"
msgstr "Seleziona data e ora"

#: app/Services/TransStrings.php:1148
msgid "Select Day of the month"
msgstr "Seleziona giorno del mese"

#: app/Services/TransStrings.php:1149
msgid "Select Day of the week"
msgstr "Seleziona giorno della settimana"

#: app/Services/TransStrings.php:1150
msgid "Select Dynamic Segment"
msgstr "Seleziona segmento dinamico"

#: app/Services/TransStrings.php:1151
msgid "Select Email Recipients"
msgstr "Seleziona destinatari dell'email"

#: app/Services/TransStrings.php:1152
msgid "Select Field Type"
msgstr "Seleziona tipo di campo"

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:125
#, fuzzy
#| msgid "Select FluentCRM Tag"
msgid "Select FluentCRM List"
msgstr "Seleziona lista FluentCRM"

#: app/Services/Funnel/Triggers/FluentFormSubmissionTrigger.php:94
msgid "Select Form Field"
msgstr "Seleziona campo modulo"

#: app/Services/Funnel/Triggers/FluentFormSubmissionTrigger.php:96
msgid "Select Form Property"
msgstr "Seleziona proprietà del modulo"

#: app/Services/TransStrings.php:1153
msgid "Select Image for Your Email Body"
msgstr "Seleziona immagine per il corpo dell'email"

#: app/Services/TransStrings.php:1154
#: app/Services/Funnel/Benchmarks/RemoveFromListBenchmark.php:56
#: app/Services/Funnel/Benchmarks/ListAppliedBenchmark.php:56
#: app/Services/Funnel/Actions/DetachListAction.php:42
#: app/Services/Funnel/Actions/ApplyListAction.php:42
msgid "Select List"
msgstr "Seleziona lista"

#: app/Services/TransStrings.php:1155
msgid "Select List First"
msgstr "Seleziona prima la lista"

#: app/Services/TransStrings.php:1050
msgid "Select List or all subscribers first and then select tag"
msgstr "Seleziona prima la lista o tutti gli iscritti e poi il tag"

#: app/Services/ExternalIntegrations/BricksBuilderIntegration.php:81
#: app/Services/Funnel/Benchmarks/RemoveFromListBenchmark.php:55
#: app/Services/Funnel/Benchmarks/ListAppliedBenchmark.php:55
#: app/Services/Funnel/Actions/DetachListAction.php:41
#: app/Services/Funnel/Actions/ApplyListAction.php:41
msgid "Select Lists"
msgstr "Seleziona liste"

#: app/Services/TransStrings.php:1183
msgid "Select lists"
msgstr "Seleziona liste"

#: app/Services/TransStrings.php:1048
msgid ""
"Select Lists and Tags that you want to exclude from this campaign. Excluded "
"contacts will be subtracted from your included selection"
msgstr ""
"Seleziona liste e tag che vuoi escludere da questa campagna. I contatti "
"esclusi verranno sottratti dalla tua selezione inclusa"

#: app/Services/TransStrings.php:1049
msgid ""
"Select Lists and Tags that you want to send emails for this campaign. You "
"can create multiple row to send to all of them"
msgstr ""
"Seleziona liste e tag ai quali vuoi inviare email per questa campagna. Puoi "
"creare più righe per inviarle a tutti"

#: app/Services/Funnel/Actions/DetachListAction.php:35
msgid "Select Lists that you want to remove from targeted Contact"
msgstr "Seleziona le liste che vuoi rimuovere dal contatto mirato"

#: app/Services/TransStrings.php:1156
msgid "Select Lists that you want to show for contacts"
msgstr "Seleziona le liste che vuoi mostrare per i contatti"

#: app/Services/TransStrings.php:1157
msgid "Select Logs you want to delete"
msgstr "Seleziona i log che vuoi eliminare"

#: app/Services/TransStrings.php:1158
msgid "Select New Automation Trigger"
msgstr "Seleziona nuovo trigger di automazione"

#: app/Services/TransStrings.php:1159
msgid "Select Operator"
msgstr "Seleziona operatore"

#: app/Services/TransStrings.php:1160
msgid "Select Option"
msgstr "Seleziona opzione"

#: app/Services/Funnel/Triggers/UserRegistrationTrigger.php:84
msgid "Select Roles"
msgstr "Seleziona ruoli"

#: app/Services/TransStrings.php:1161
msgid "Select Schedule time"
msgstr "Seleziona orario di programmazione"

#: app/Services/TransStrings.php:1162
#, fuzzy
#| msgid "Select Sequences"
msgid "Select Sequence"
msgstr "Seleziona sequenza"

#: app/Services/TransStrings.php:1283
msgid "Select Source from where you want to import your contacts"
msgstr "Seleziona la fonte da cui vuoi importare i contatti"

#: app/Services/TransStrings.php:1163
#: app/Services/Funnel/Triggers/FluentFormSubmissionTrigger.php:113
#: app/Services/Funnel/Triggers/UserRegistrationTrigger.php:47
msgid "Select Status"
msgstr "Seleziona stato"

#: app/Services/TransStrings.php:1184
msgid "Select status"
msgstr "Seleziona stato"

#: app/Services/TransStrings.php:1164
msgid "Select Subscribers"
msgstr "Seleziona iscritti"

#: app/Services/TransStrings.php:223
msgid "Select Subscribers who click selected links"
msgstr "Seleziona iscritti che hanno cliccato i link selezionati"

#: app/Services/TransStrings.php:224
msgid "Select Subscribers who did not open email"
msgstr "Seleziona iscritti che non hanno aperto l'email"

#: app/Services/TransStrings.php:225
msgid "Select Subscribers who open the emails"
msgstr "Seleziona iscritti che hanno aperto le email"

#: app/Services/TransStrings.php:1165
#: app/Services/Funnel/Actions/DetachTagAction.php:42
#: app/Services/Funnel/Actions/ApplyTagAction.php:43
msgid "Select Tag"
msgstr "Seleziona tag"

#: app/Services/TransStrings.php:1166
#: app/Services/ExternalIntegrations/BricksBuilderIntegration.php:61
#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:175
#: app/Services/Funnel/Benchmarks/RemoveFromTagBenchmark.php:55
#: app/Services/Funnel/Benchmarks/TagAppliedBenchmark.php:56
#: app/Services/Funnel/Benchmarks/TagAppliedBenchmark.php:57
#: app/Services/Funnel/Actions/DetachTagAction.php:41
#: app/Services/Funnel/Actions/ApplyTagAction.php:42
msgid "Select Tags"
msgstr "Seleziona tag"

#: app/Services/TransStrings.php:1185
msgid "Select tags"
msgstr "Seleziona i tag"

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:252
msgid "Select Tags (remove from contact)"
msgstr "Seleziona tag (rimuovi dal contatto)"

#: app/Services/Funnel/Actions/DetachTagAction.php:35
msgid "Select Tags that you want to remove from targeted Contact"
msgstr "Seleziona i tag che desideri rimuovere dal contatto target"

#: app/Services/TransStrings.php:1167
msgid "Select Targeted Tags"
msgstr "Seleziona tag mirati"

#: app/Services/TransStrings.php:1168
msgid "Select Template"
msgstr "Seleziona modello"

#: app/Services/TransStrings.php:1186
msgid "Select the fields that user can manage"
msgstr "Seleziona i campi che l'utente può gestire"

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:126
msgid "Select the FluentCRM List you would like to add your contacts to."
msgstr "Seleziona la lista FluentCRM a cui aggiungere i contatti."

#: app/Services/AutoSubscribe.php:132
msgid ""
"Select the list that will be assigned for comment will be made in comment "
"forms"
msgstr "Seleziona la lista da assegnare ai commenti nei moduli di commento"

#: app/Services/AutoSubscribe.php:45
msgid ""
"Select the list that will be assigned for new user registration in your site"
msgstr ""
"Seleziona la lista da assegnare per le nuove registrazioni sul tuo sito"

#: app/Services/AutoSubscribe.php:290
msgid "Select the list that will be assigned when checkbox checked"
msgstr ""
"Seleziona la lista da assegnare quando è selezionata la casella di controllo"

#: app/Services/AutoSubscribe.php:145
msgid ""
"Select the tags that will be assigned for new comment will be made in "
"comment forms"
msgstr ""
"Seleziona i tag da assegnare per i nuovi commenti nei moduli di commento"

#: app/Services/AutoSubscribe.php:59
msgid ""
"Select the tags that will be assigned for new user registration in your site"
msgstr "Seleziona i tag da assegnare per le nuove registrazioni sul tuo sito"

#: app/Services/AutoSubscribe.php:303
msgid "Select the tags that will be assigned when checkbox checked"
msgstr ""
"Seleziona i tag da assegnare quando è selezionata la casella di controllo"

#: app/Services/TransStrings.php:1187
msgid "Select the trigger for this automation"
msgstr "Seleziona il trigger per questa automazione"

#: app/Services/TransStrings.php:226
msgid "Select URLS that where clicked (Will Match for any Selected URLs)"
msgstr "Seleziona gli URL cliccati (Corrisponderà a qualsiasi URL selezionato)"

#: app/Services/TransStrings.php:1169
#: app/Http/Controllers/ImporterController.php:147
msgid "Select User Roles"
msgstr "Seleziona ruoli utente"

#: app/Services/TransStrings.php:1170
msgid "Select Value"
msgstr "Seleziona valore"

#: app/Services/TransStrings.php:1188
msgid "Select which day of the month to send email"
msgstr "Seleziona quale giorno del mese inviare l'email"

#: app/Services/TransStrings.php:1189
msgid "Select which day you want to send email"
msgstr "Seleziona il giorno in cui inviare l'email"

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:165
msgid ""
"Select which Fluent Form fields pair with their<br /> respective FlunentCRM "
"fields."
msgstr ""
"Abbina i campi del modulo Fluent Form ai rispettivi campi di FluentCRM."

#: app/Services/Funnel/Actions/ApplyCompanyAction.php:35
#: app/Services/Funnel/Actions/ApplyListAction.php:35
msgid "Select which list will be added to the contact"
msgstr "Seleziona la lista da aggiungere al contatto"

#: app/Services/Funnel/Triggers/UserRegistrationTrigger.php:83
msgid "Select which roles registration will run this automation Funnel"
msgstr ""
"Seleziona quali ruoli d'iscrizione eseguiranno questo funnel di automazione"

#: app/Services/Funnel/Actions/ApplyTagAction.php:35
msgid "Select which tag will be added to the contact"
msgstr "Seleziona il tag da aggiungere al contatto"

#: app/Services/TransStrings.php:1171
msgid "Select Your CSV Delimiter"
msgstr "Seleziona il delimitatore CSV"

#: app/Services/TransStrings.php:1628
msgid "Select Your Current CRM / Email Marketing software"
msgstr "Seleziona il tuo CRM / software di email marketing attuale"

#: app/Services/TransStrings.php:1172
msgid "Select Your Email Service Provider"
msgstr "Seleziona il tuo fornitore di servizi email"

#: app/Services/Funnel/Triggers/FluentFormSubmissionTrigger.php:78
msgid "Select your form"
msgstr "Seleziona il tuo modulo"

#: app/Http/Controllers/SubscriberController.php:1186
msgid "Selected Action is not valid"
msgstr "L'azione selezionata non è valida"

#: app/Http/Controllers/CompanyController.php:441
#: app/Http/Controllers/SubscriberController.php:1222
msgid "Selected bulk action has been successfully completed"
msgstr "L'azione in blocco selezionata è stata completata con successo"

#: app/Http/Controllers/CampaignController.php:881
msgid "Selected Campaigns has been deleted permanently"
msgstr "Le campagne selezionate sono state eliminate definitivamente"

#: app/Http/Controllers/CompanyController.php:147
msgid "Selected Companies has been attached successfully"
msgstr "Le aziende selezionate sono state collegate con successo"

#: app/Http/Controllers/CompanyController.php:374
msgid "Selected Companies has been deleted permanently"
msgstr "Le aziende selezionate sono state eliminate definitivamente"

#: app/Http/Controllers/SubscriberController.php:931
msgid "Selected Contacts has been deleted permanently"
msgstr "I contatti selezionati sono stati eliminati definitivamente"

#: app/Http/Controllers/SettingsController.php:526
msgid "Selected CRON Event successfully ran"
msgstr "L'evento CRON selezionato è stato eseguito con successo"

#: app/Services/TransStrings.php:1191
#: app/Http/Controllers/CampaignController.php:434
msgid "Selected emails are deleted"
msgstr "Le email selezionate sono state eliminate"

#: app/Http/Controllers/ReportingController.php:90
#: app/Http/Controllers/SubscriberController.php:577
msgid "Selected emails has been deleted"
msgstr "Le email selezionate sono state eliminate"

#: app/Http/Controllers/FunnelController.php:517
msgid "Selected Funnels has been deleted permanently"
msgstr "I funnel selezionati sono stati eliminati definitivamente"

#: app/Http/Controllers/ListsController.php:234
msgid "Selected Lists has been removed permanently"
msgstr "Le liste selezionate sono state rimosse definitivamente"

#: app/Http/Controllers/SubscriberController.php:220
msgid "Selected Subscriber has been deleted successfully"
msgstr "L'iscritto selezionato è stato eliminato con successo"

#: app/Http/Controllers/SubscriberController.php:236
msgid "Selected Subscribers has been deleted"
msgstr "Gli iscritti selezionati sono stati eliminati"

#: app/Http/Controllers/FunnelController.php:431
msgid "Selected subscribers has been removed from this automation funnels"
msgstr ""
"Gli iscritti selezionati sono stati rimossi da questi funnel di automazione"

#: app/Http/Controllers/TagsController.php:244
msgid "Selected Tags has been removed permanently"
msgstr "I tag selezionati sono stati rimossi definitivamente"

#: app/Http/Controllers/TemplateController.php:285
msgid "Selected Templates has been deleted permanently"
msgstr "I modelli selezionati sono stati eliminati definitivamente"

#: app/Services/TransStrings.php:1192
msgid "Semicolon Separated (;)"
msgstr "Separato da punto e virgola (;)"

#: app/Services/TransStrings.php:1193
msgid "Send"
msgstr "Invia"

#: app/Services/Funnel/Actions/SendEmailAction.php:29
msgid "Send a custom Email to your subscriber or custom email address"
msgstr ""
"Invia un'email personalizzata ai tuoi iscritti o a un indirizzo email "
"personalizzato"

#: app/Services/TransStrings.php:1202
msgid "Send a test email"
msgstr "Invia un'email di prova"

#: app/Hooks/Handlers/AdminMenu.php:362
msgid ""
"Send automated daily or weekly emails of your dynamic data like new blog "
"posts"
msgstr ""
"Invia automaticamente email giornaliere o settimanali con i tuoi dati "
"dinamici, come nuovi post del blog"

#: app/Services/TransStrings.php:1194
#: app/Services/Funnel/Actions/SendEmailAction.php:28
#: app/Services/Funnel/Actions/SendEmailAction.php:53
msgid "Send Custom Email"
msgstr "Invia email personalizzata"

#: app/Services/TransStrings.php:1195
msgid "Send Double Optin"
msgstr "Invia Double Opt-in"

#: app/Services/TransStrings.php:1196
msgid "Send Double Optin Email"
msgstr "Invia email Double Opt-in"

#: app/Services/TransStrings.php:1197
msgid "Send Double Optin Email for new contacts"
msgstr "Invia email Double Opt-in per nuovi contatti"

#: app/Services/TransStrings.php:1198
msgid "Send Double Optin To Pending Contacts"
msgstr "Invia Double Opt-in ai contatti in sospeso"

#: app/Services/TransStrings.php:1199
msgid "Send Email"
msgstr "Invia email"

#: app/Hooks/Handlers/AdminMenu.php:356
msgid ""
"Send Email Broadcast to your selected subscribers by tags, lists or segment"
msgstr ""
"Invia una trasmissione email agli iscritti selezionati per tag, liste o "
"segmenti"

#: app/Services/Funnel/Actions/SendEmailAction.php:59
msgid "Send Email to"
msgstr "Invia email a"

#: app/Hooks/CLI/Commands.php:46
msgid "Send Emails"
msgstr "Invia email"

#: app/Services/TransStrings.php:1206
msgid ""
"Send Emails automatically to the selected day and time (if you disable this, "
"emails will be on draft state and you can trigger emails after review)"
msgstr ""
"Invia email automaticamente nel giorno e orario selezionati (se disabilitato,"
" le email saranno in stato di bozza e potrai attivarle dopo la revisione)"

#: app/Services/TransStrings.php:1207
msgid ""
"Send emails daily / weekly / monthly automatically based in your published "
"content as newsletter"
msgstr ""
"Invia automaticamente email giornaliere / settimanali / mensili basate sui "
"contenuti pubblicati come newsletter"

#: app/Services/TransStrings.php:1203
msgid "Send emails if"
msgstr "Invia email se"

#: app/Services/TransStrings.php:1200
msgid "Send Emails Now"
msgstr "Invia email adesso"

#: app/Services/TransStrings.php:26
msgid "Send Emails with Multiple Subject Line (A/B) test"
msgstr "Invia email con test A/B per soggetti multipli"

#: app/Services/TransStrings.php:1201
msgid "Send HTTP "
msgstr "Invia HTTP"

#: app/Services/TransStrings.php:229
msgid "Send or schedule campaign emails"
msgstr "Invia o programma email di campagna"

#: app/Services/TransStrings.php:586
msgid ""
"Send Sequence/Drip emails to your subscribers with Email Sequence Module"
msgstr ""
"Invia email a goccia ai tuoi iscritti con il modulo di sequenza di email"

#: app/Services/TransStrings.php:1204
msgid "Send the emails right now"
msgstr "Invia le email adesso"

#: app/Services/Funnel/Actions/SendEmailAction.php:67
msgid "Send to Custom Email Address"
msgstr "Invia a indirizzo email personalizzato"

#: app/Services/TransStrings.php:1205
msgid "Send to Draft"
msgstr "Invia in bozza"

#: app/Services/Funnel/Actions/SendEmailAction.php:74
msgid "Send To Email Addresses (If Custom)"
msgstr "Invia a indirizzi email (se personalizzati)"

#: app/Services/Funnel/Actions/SendEmailAction.php:63
msgid "Send To the contact"
msgstr "Invia al contatto"

#: app/Http/Controllers/SettingsController.php:349
msgid "SendGrid"
msgstr "SendGrid"

#: app/Http/Controllers/SettingsController.php:352
msgid "SendGrid Bounce Handler Webhook URL"
msgstr "URL webhook per la gestione dei rimbalzi di SendGrid"

#: app/Services/TransStrings.php:1208
msgid "Sending"
msgstr "Invio"

#: app/Services/TransStrings.php:1209
msgid "Sending Conditions"
msgstr "Condizioni di invio"

#: app/Services/TransStrings.php:1210
msgid "Sending Time Range"
msgstr "Intervallo di tempo per l'invio"

#: app/Services/TransStrings.php:1211
msgid "Sending To Contacts"
msgstr "Invio ai contatti"

#: app/Hooks/Handlers/CountryNames.php:801
msgid "Senegal"
msgstr "Senegal"

#: app/Services/TransStrings.php:1212
msgid "Sent"
msgstr "Inviato"

#: app/Services/TransStrings.php:1630
msgid "sent"
msgstr "inviato"

#: app/Services/TransStrings.php:1213
msgid "September"
msgstr "Settembre"

#: app/Services/TransStrings.php:1214
msgid "Sequence"
msgstr "Sequenza"

#: app/Services/TransStrings.php:1215
msgid "Sequence Subscribers"
msgstr "Iscritti alla sequenza"

#: app/Http/Controllers/FunnelController.php:273
#: app/Hooks/Handlers/FunnelHandler.php:231
msgid "Sequence successfully updated"
msgstr "Sequenza aggiornata con successo"

#: app/Services/TransStrings.php:1216
msgid "Sequence Title"
msgstr "Titolo della sequenza"

#: app/Hooks/Handlers/CountryNames.php:805
msgid "Serbia"
msgstr "Serbia"

#: app/Services/TransStrings.php:1674
msgid ""
"Server environment details (php, mysql, server, WordPress versions), crm "
"usage, Site language, Number of active and inactive plugins, Site name and "
"url, Your name and email address. No sensitive data is tracked."
msgstr ""
"Dettagli dell'ambiente server (php, mysql, versioni del server e di "
"WordPress), uso del CRM, lingua del sito, numero di plugin attivi e inattivi,"
" nome del sito e URL, nome ed email dell'utente. Nessun dato sensibile viene "
"tracciato."

#: app/Services/TransStrings.php:1218
msgid "Server Issue detected"
msgstr "Rilevato problema al server"

#: app/Services/TransStrings.php:1694
msgid "Server Response"
msgstr "Risposta del server"

#: app/Hooks/Handlers/AdminMenu.php:263
#, php-format
msgid "Server-Side Cron Job is not enabled %1sView Documentation%2s."
msgstr ""
"Il Cron Job lato server non è abilitato %1sVisualizza la documentazione%2s."

#: app/Services/TransStrings.php:1231
msgid "Set after how many"
msgstr "Imposta dopo quanti"

#: app/Services/TransStrings.php:1219
msgid "Set Campaign"
msgstr "Imposta campagna"

#: app/Services/TransStrings.php:1220
msgid "Set Companies"
msgstr "Imposta aziende"

#: app/Services/TransStrings.php:1221
msgid "Set Condition"
msgstr "Imposta condizione"

#: app/Services/TransStrings.php:1420
#: app/Services/Funnel/Actions/SendEmailAction.php:117
msgid "Set Custom From Name and Email"
msgstr "Imposta nome e email personalizzati del mittente"

#: app/Services/TransStrings.php:1222
#, fuzzy
#| msgid "Select Email Sequence"
msgid "Set Email Sequence"
msgstr "Imposta sequenza email"

#: app/Services/TransStrings.php:1223
msgid "Set Email Subject & Body"
msgstr "Imposta oggetto e corpo dell'email"

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:55
msgid "Set FluentCRM"
msgstr "Imposta FluentCRM"

#: app/Services/TransStrings.php:1224
msgid "Set Lists"
msgstr "Imposta liste"

#: app/Services/TransStrings.php:1632
msgid ""
"Set maximum emails will be sent per second. Set this number based on your "
"email service provider"
msgstr ""
"Imposta il numero massimo di email inviate per secondo. Imposta questo "
"valore in base al tuo fornitore di servizi email"

#: app/Services/TransStrings.php:1225
msgid "Set Note Title"
msgstr "Imposta il titolo della nota"

#: app/Services/TransStrings.php:1226
msgid "Set Property"
msgstr "Imposta proprietà"

#: app/Services/TransStrings.php:1227
#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:185
msgid "Set Tag"
msgstr "Imposta tag"

#: app/Services/TransStrings.php:1228
msgid "Set Tags"
msgstr "Imposta tag"

#: app/Services/TransStrings.php:1705
msgid "Set up an automation"
msgstr "Imposta un'automazione"

#: app/Services/TransStrings.php:1232
msgid "Set up your schedule"
msgstr "Configura il tuo programma"

#: app/Services/TransStrings.php:1229
msgid "Set User Role"
msgstr "Imposta ruolo utente"

#: app/Services/TransStrings.php:1230
msgid "Set Webhook URL"
msgstr "Imposta URL webhook"

#: app/Services/Stats.php:105 app/Services/TransStrings.php:1235
#: app/Hooks/Handlers/AdminMenu.php:179 app/Hooks/Handlers/AdminMenu.php:180
#: app/Hooks/Handlers/AdminMenu.php:415 app/Hooks/Handlers/AdminMenu.php:1342
#: app/Hooks/Handlers/AdminMenu.php:1343
msgid "Settings"
msgstr "Impostazioni"

#: app/Http/Controllers/SettingsController.php:460
#: app/Http/Controllers/SettingsController.php:938
msgid "Settings has been updated"
msgstr "Le impostazioni sono state aggiornate"

#: app/Http/Controllers/SettingsController.php:79
msgid "Settings Updated"
msgstr "Impostazioni aggiornate"

#: app/Services/TransStrings.php:1236
msgid "Settings Updated."
msgstr "Impostazioni aggiornate."

#: app/Services/TransStrings.php:1238
msgid "Setup"
msgstr "Configurazione"

#: app/Hooks/Handlers/CountryNames.php:809
msgid "Seychelles"
msgstr "Seychelles"

#: app/Services/TransStrings.php:1243
msgid "Share Essentials"
msgstr "Condividi elementi essenziali"

#: app/Services/TransStrings.php:1686
msgid "Shipping"
msgstr "Spedizione"

#: app/Services/TransStrings.php:1244
msgid "Shortcode"
msgstr "Shortcode"

#: app/Services/TransStrings.php:1245
msgid "Show Errors"
msgstr "Mostra errori"

#: app/Services/TransStrings.php:1246
msgid "Show IF in Selected Tag"
msgstr "Mostra SE nel tag selezionato"

#: app/Services/TransStrings.php:1254
msgid "Show if in tags:"
msgstr "Mostra se nei tag:"

#: app/Services/TransStrings.php:1247
msgid "Show IF not in selected tag"
msgstr "Mostra SE non nel tag selezionato"

#: app/Services/TransStrings.php:1255
msgid "Show if not in tags:"
msgstr "Mostra se non nei tag:"

#: app/Services/TransStrings.php:1248
msgid "Show Individual Emails"
msgstr "Mostra email individuali"

#: app/Services/TransStrings.php:1249
msgid "Show Individual Recipients"
msgstr "Mostra destinatari individuali"

#: app/Services/TransStrings.php:1250
msgid "Show Invalid Contacts"
msgstr "Mostra contatti non validi"

#: app/Http/Controllers/SettingsController.php:143
msgid "Show Message"
msgstr "Mostra messaggio"

#: app/Services/TransStrings.php:1251
msgid "Show Report"
msgstr "Mostra report"

#: app/Services/TransStrings.php:1252
msgid "Show Skipped Contacts"
msgstr "Mostra contatti saltati"

#: app/Services/TransStrings.php:1253
msgid "Show Subscribers"
msgstr "Mostra iscritti"

#: app/Services/TransStrings.php:1256
msgid "Showing preview for current contact"
msgstr "Visualizzazione dell'anteprima per il contatto corrente"

#: app/Services/TransStrings.php:1257
msgid "Showing stats from"
msgstr "Mostra statistiche da"

#: app/Hooks/Handlers/CountryNames.php:813
msgid "Sierra Leone"
msgstr "Sierra Leone"

#: app/Services/AutoSubscribe.php:237
msgid "Sign me up for the newsletter!"
msgstr "Iscrivimi alla newsletter!"

#: app/Services/Helper.php:298
msgid "Simple Boxed"
msgstr "Semplice con bordo"

#: app/Services/TransStrings.php:1634
msgid ""
"Simple enough to be quick to use, but powerful enough to really filter down "
"your contacts into target segments"
msgstr ""
"Abbastanza semplice da usare rapidamente, ma potente per filtrare i tuoi "
"contatti in segmenti mirati"

#: app/Http/Controllers/FormsController.php:261
msgid "Simple Opt-in Form"
msgstr "Modulo di iscrizione semplice"

#: app/Hooks/Handlers/CountryNames.php:817
msgid "Singapore"
msgstr "Singapore"

#: app/Models/CustomContactField.php:40
msgid "Single Line Text"
msgstr "Testo a linea singola"

#: app/Services/TransStrings.php:1707 app/Services/Helper.php:202
msgid "Site URL"
msgstr "URL del sito"

#: app/Services/TransStrings.php:1258
msgid "Skip"
msgstr "Salta"

#: app/Services/TransStrings.php:1259
msgid "Skip if already in DB"
msgstr "Salta se già presente nel database"

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:192
msgid "Skip if contact already exist in FluentCRM"
msgstr "Salta se il contatto è già presente in FluentCRM"

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:198
msgid "Skip name update if existing contact have old data (per primary field)"
msgstr ""
"Salta l'aggiornamento del nome se il contatto esistente ha vecchi dati (per "
"campo primario)"

#: app/Services/Funnel/Actions/SendEmailAction.php:96
msgid "Skip sending email if date is overdued"
msgstr "Salta l'invio dell'email se la data è scaduta"

#: app/Services/Funnel/FunnelHelper.php:35
msgid "Skip this automation if contact already exist"
msgstr "Salta questa automazione se il contatto esiste già"

#: app/Hooks/Handlers/CountryNames.php:821
msgid "Slovakia"
msgstr "Slovacchia"

#: app/Hooks/Handlers/CountryNames.php:825
msgid "Slovenia"
msgstr "Slovenia"

#: app/Services/TransStrings.php:1260
msgid "Slug"
msgstr "Slug"

#: app/Services/TransStrings.php:1261
msgid "Slug (Optional)"
msgstr "Slug (Opzionale)"

#: app/Services/Helper.php:533
msgid "Small"
msgstr "Piccolo"

#: app/Services/TransStrings.php:1262
msgid "Smart Links"
msgstr "Smart Links"

#: app/Services/TransStrings.php:1263
msgid "Smart URL"
msgstr "Smart URL"

#: app/Services/TransStrings.php:1265
msgid "Smartcode has been copied to your clipboard"
msgstr "Il codice smart è stato copiato negli appunti"

#: app/Services/TransStrings.php:1266
msgid "Smartcode:"
msgstr "Codice smart:"

#: app/Services/TransStrings.php:1264
msgid "SmartLinks"
msgstr "SmartLinks"

#: app/Services/TransStrings.php:809
msgid ""
"SmartLinks allow you to tag and segment your subscribers when they click a "
"link in an email, on your site or anywhere!"
msgstr ""
"Gli SmartLinks ti permettono di taggare e segmentare i tuoi iscritti quando "
"cliccano su un link in un'email, sul tuo sito o altrove!"

#: app/Hooks/Handlers/AdminMenu.php:208 app/Hooks/Handlers/AdminMenu.php:209
msgid "SMTP"
msgstr "SMTP"

#: app/Services/TransStrings.php:1104
msgid "SMTP/Email Sending Service Settings"
msgstr "Impostazioni del servizio di invio email/SMTP"

#: app/Services/TransStrings.php:1234
msgid "SMTP/Email Service Settings"
msgstr "Impostazioni del servizio email/SMTP"

#: app/Services/TransStrings.php:1270
msgid "Social Links"
msgstr "Link Social"

#: app/Hooks/Handlers/CountryNames.php:829
msgid "Solomon Islands"
msgstr "Isole Salomone"

#: app/Hooks/Handlers/CountryNames.php:833
msgid "Somalia"
msgstr "Somalia"

#: app/Services/TransStrings.php:1406
msgid "Some of the users matching your criteria"
msgstr "Alcuni utenti corrispondenti ai tuoi criteri"

#: app/Http/Controllers/SettingsController.php:699
msgid "Something is wrong"
msgstr "Qualcosa non va"

#: app/Services/TransStrings.php:750
msgid "Something is wrong when importing. Please check the error message below"
msgstr ""
"Si è verificato un problema durante l'importazione. Verifica il messaggio di "
"errore riportato qui sotto"

#: app/Services/TransStrings.php:1272
msgid "Something is wrong!"
msgstr "Qualcosa è andato storto!"

#: app/Services/TransStrings.php:1273
msgid "Something went wrong!"
msgstr "Si è verificato un errore!"

#: app/Services/TransStrings.php:1274
msgid "Something went wrong, when syncing data"
msgstr "Si è verificato un errore durante la sincronizzazione dei dati"

#: app/Http/Controllers/SubscriberController.php:355
msgid "Sorry contact already exist"
msgstr "Spiacente, il contatto esiste già"

#: app/Hooks/Handlers/AdminBar.php:76
msgid "Sorry no contact found"
msgstr "Spiacente, nessun contatto trovato"

#: app/Http/Controllers/MigratorController.php:38
#: app/Http/Controllers/MigratorController.php:67
#: app/Http/Controllers/MigratorController.php:93
#: app/Http/Controllers/MigratorController.php:125
msgid "Sorry no driver found for the selected CRM"
msgstr "Spiacente, nessun driver trovato per il CRM selezionato"

#: app/Services/TransStrings.php:1275
#: app/Http/Controllers/ImporterController.php:54
#: app/Http/Controllers/ImporterController.php:78
msgid "Sorry no driver found for this import"
msgstr "Spiacente, nessun driver trovato per questo import"

#: app/Services/TransStrings.php:230
msgid "Sorry no links found that contacts clicked"
msgstr "Spiacente, nessun link trovato cliccato dai contatti"

#: app/Services/TransStrings.php:1276
#, fuzzy
#| msgid "No Subscribers found based on your selection"
msgid "Sorry no subscribers found based on your selection"
msgstr "Spiacente, nessun iscritto trovato in base alla tua selezione"

#: app/Services/TransStrings.php:592
msgid ""
"Sorry you can not insert the selected template type in this email campaign"
msgstr ""
"Spiacente, non puoi inserire il tipo di modello selezionato in questa "
"campagna email"

#: app/Services/TransStrings.php:1277
#, fuzzy
#| msgid "Sorry! No emails found"
msgid "Sorry! No docs found"
msgstr "Spiacente! Nessuna email trovata"

#: app/Hooks/Handlers/ExternalPages.php:996
msgid "Sorry! No subscriber found in the database"
msgstr "Spiacente! Nessun iscritto trovato nel database"

#: app/Services/TransStrings.php:1278
#: app/Http/Controllers/CampaignController.php:229
msgid "Sorry! No subscribers found based on your selection"
msgstr "Spiacente! Nessun iscritto trovato in base alla tua selezione"

#: app/Services/TransStrings.php:978
msgid "Sorry! The plugin could not be installed. Please install manually"
msgstr ""
"Spiacente! Il plugin non può essere installato. Effettua l'installazione "
"manualmente"

#: app/Hooks/Handlers/ExternalPages.php:392
#: app/Hooks/Handlers/ExternalPages.php:446
msgid "Sorry! We could not verify your email address"
msgstr "Spiacente! Non possiamo verificare il tuo indirizzo email"

#: app/Http/Controllers/SetupController.php:65
#: app/Http/Controllers/SetupController.php:83
#: app/Http/Controllers/SetupController.php:106
msgid "Sorry! you do not have permission to install plugin"
msgstr "Spiacente! Non hai il permesso di installare il plugin"

#: app/Hooks/Handlers/ExternalPages.php:663
msgid "Sorry! Your confirmation url is not valid"
msgstr "Spiacente! Il tuo URL di conferma non è valido"

#: app/Hooks/Handlers/ExternalPages.php:508
msgid "Sorry, No email found based on your data"
msgstr "Spiacente, nessuna email trovata in base ai tuoi dati"

#: app/Services/TransStrings.php:1279
msgid "Sorry, No emails found in this automation"
msgstr "Spiacente, nessuna email trovata in questa automazione"

#: app/Services/TransStrings.php:1280
#: app/Http/Controllers/CampaignController.php:353
#: app/Http/Controllers/CampaignController.php:381
msgid "Sorry, No subscribers found based on your filters"
msgstr "Spiacente, nessun iscritto trovato in base ai tuoi filtri"

#: app/Http/Controllers/SettingsController.php:882
msgid "Sorry, the provided provider does not exist"
msgstr "Spiacente, il provider fornito non esiste"

#: app/Http/Controllers/SettingsController.php:673
#: app/Http/Controllers/SettingsController.php:793
#: app/Http/Controllers/SettingsController.php:807
msgid "Sorry, the provided user does not have FluentCRM access"
msgstr "Spiacente, l'utente fornito non ha accesso a FluentCRM"

#: app/Services/TransStrings.php:1281
msgid "Sorry, the selected plugins could not be installed"
msgstr "Spiacente, i plugin selezionati non possono essere installati"

#: app/Http/Controllers/CompanyController.php:318
msgid "Sorry, we could not find the logo from website. Please upload manually"
msgstr ""
"Spiacente, non siamo riusciti a trovare il logo dal sito web. Si prega di "
"caricarlo manualmente"

#: app/Http/Controllers/SettingsController.php:254
msgid "Sorry, You do not have admin permission to reset database"
msgstr ""
"Spiacente, non hai il permesso di amministratore per resettare il database"

#: app/Http/Controllers/SettingsController.php:799
msgid "Sorry, You do not have permission to create REST API"
msgstr "Spiacente, non hai il permesso di creare un'API REST"

#: app/Http/Controllers/SettingsController.php:679
msgid "Sorry, You do not have permission to delete REST API"
msgstr "Spiacente, non hai il permesso di eliminare un'API REST"

#: app/Hooks/Handlers/FunnelHandler.php:218
#: app/Hooks/Handlers/FunnelHandler.php:263
#: app/Hooks/Handlers/FunnelHandler.php:321
msgid "Sorry, You do not have permission to do this action"
msgstr "Spiacente, non hai il permesso di eseguire questa azione"

#: app/Models/Subscriber.php:732 app/Services/TransStrings.php:1282
#: app/Services/Helper.php:964
msgid "Source"
msgstr "Fonte"

#: app/Hooks/Handlers/CountryNames.php:837
msgid "South Africa"
msgstr "Sudafrica"

#: app/Hooks/Handlers/CountryNames.php:841
msgid "South Georgia/Sandwich Islands"
msgstr "Georgia del Sud e Isole Sandwich"

#: app/Hooks/Handlers/CountryNames.php:845
msgid "South Korea"
msgstr "Corea del Sud"

#: app/Hooks/Handlers/CountryNames.php:849
msgid "South Sudan"
msgstr "Sudan del Sud"

#: app/Hooks/Handlers/CountryNames.php:853
msgid "Spain"
msgstr "Spagna"

#: app/Http/Controllers/SettingsController.php:356
msgid "SparkPost"
msgstr "SparkPost"

#: app/Http/Controllers/SettingsController.php:359
msgid "SparkPost Bounce Handler Webhook URL"
msgstr "URL Webhook per gestione rimbalzi SparkPost"

#: app/Services/Funnel/Actions/WaitTimeAction.php:132
msgid "Specify Date and Time"
msgstr "Specifica data e ora"

#: app/Hooks/Handlers/CountryNames.php:857
msgid "Sri Lanka"
msgstr "Sri Lanka"

#: app/Services/TransStrings.php:1288
#, fuzzy
#| msgid "Cart Data"
msgid "Start date"
msgstr "Data di inizio"

#: app/Services/TransStrings.php:1284
msgid "Start Range"
msgstr "Intervallo di inizio"

#: app/Services/TransStrings.php:1285
msgid "Start Using SmartLinks"
msgstr "Inizia a usare SmartLinks"

#: app/Services/TransStrings.php:1286
msgid "Start Writing Email Here"
msgstr "Inizia a scrivere l'email qui"

#: app/Services/TransStrings.php:1287
msgid "Start Writing Here"
msgstr "Inizia a scrivere qui"

#: app/Services/TransStrings.php:1290
msgid "Started At"
msgstr "Iniziato a"

#: app/Services/TransStrings.php:1289
msgid "StartOver"
msgstr "Ricomincia"

#: app/Services/TransStrings.php:1291
msgid "Starts With"
msgstr "Inizia con"

#: app/Services/TransStrings.php:1635
msgid "starts with"
msgstr "inizia con"

#: app/Models/Company.php:64 app/Models/Subscriber.php:727
#: app/Services/TransStrings.php:1292 app/Services/Helper.php:164
#: app/Services/Helper.php:927 app/Hooks/Handlers/PrefFormHandler.php:51
#: app/Hooks/Handlers/PrefFormHandler.php:466
#: app/Services/CrmMigrator/BaseMigrator.php:40
#: app/Services/Funnel/FunnelHelper.php:168
msgid "State"
msgstr "Stato"

#: app/Services/TransStrings.php:1293
msgid "Stats"
msgstr "Statistiche"

#: app/Services/TransStrings.php:1294 app/Services/Helper.php:168
#: app/Services/Helper.php:995
#: app/Http/Controllers/CampaignAnalyticsController.php:105
#: app/Http/Controllers/CampaignAnalyticsController.php:156
#: app/Http/Controllers/CampaignAnalyticsController.php:174
#: app/Hooks/CLI/Commands.php:162 app/Hooks/CLI/Commands.php:376
#: app/Hooks/CLI/Commands.php:584 app/Hooks/Handlers/PurchaseHistory.php:135
#: app/Hooks/Handlers/PurchaseHistory.php:351
#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:263
msgid "Status"
msgstr "Stato"

#: app/Services/TransStrings.php:1636
msgid "status"
msgstr "stato"

#: app/Services/Helper.php:1326
msgid "Status (Pro Required)"
msgstr "Stato (Pro richiesto)"

#: app/Services/TransStrings.php:1697
msgid "Status for New Contacts"
msgstr "Stato per nuovi contatti"

#: app/Services/TransStrings.php:847
msgid "Status for the new subscribers"
msgstr "Stato per i nuovi iscritti"

#: app/Http/Controllers/CompanyController.php:396
msgid "Status has been changed for the selected companies"
msgstr "Lo stato è stato modificato per le aziende selezionate"

#: app/Http/Controllers/FunnelController.php:496
msgid "Status has been changed for the selected funnels"
msgstr "Lo stato è stato modificato per i funnel selezionati"

#: app/Http/Controllers/SubscriberController.php:1100
msgid "Status has been changed for the selected subscribers"
msgstr "Lo stato è stato modificato per gli iscritti selezionati"

#: app/Http/Controllers/TemplateController.php:275
msgid "Status has been changed for the selected templates"
msgstr "Lo stato è stato modificato per i modelli selezionati"

#: app/Http/Controllers/FunnelController.php:690
#, php-format
msgid "Status has been updated to %s"
msgstr "Lo stato è stato aggiornato a %s"

#: app/Services/TransStrings.php:1637
msgid "statuses"
msgstr "stati"

#: app/Services/TransStrings.php:1295
msgid "Step 1"
msgstr "Passo 1"

#: app/Services/TransStrings.php:1296
msgid "Step 2"
msgstr "Passo 2"

#: app/Services/TransStrings.php:1297
msgid "Step Report"
msgstr "Rapporto del passo"

#: app/Services/TransStrings.php:1638
#: app/Http/Controllers/CampaignController.php:216
msgid "step saved"
msgstr "passo salvato"

#: app/Services/TransStrings.php:1640
msgid "street, state, zip, country"
msgstr "via, stato, CAP, paese"

#: app/Services/TransStrings.php:1298
msgid "Subject"
msgstr "Oggetto"

#: app/Services/TransStrings.php:1299
msgid "Subject & Settings"
msgstr "Oggetto e impostazioni"

#: app/Services/TransStrings.php:1300
msgid "Subject Analytics"
msgstr "Analisi dell'oggetto"

#: app/Services/TransStrings.php:1301
msgid "Subject Test"
msgstr "Test dell'oggetto"

#: app/Services/AutoSubscribe.php:86
#: app/Hooks/Handlers/AutoSubscribeHandler.php:107
msgid "Subscribe to newsletter"
msgstr "Iscriviti alla newsletter"

#: app/Functions/helpers.php:498 app/Functions/helpers.php:545
#: app/Services/TransStrings.php:1302 app/Services/TransStrings.php:1641
msgid "Subscribed"
msgstr "Iscritto"

#: app/Services/TransStrings.php:1642
msgid "subscribed contacts"
msgstr "contatti iscritti"

#: app/Http/Controllers/FunnelController.php:641
msgid "Subscribed has been removed from this automation funnel"
msgstr "L'iscrizione è stata rimossa da questo funnel di automazione"

#: app/Services/TransStrings.php:1303
msgid "Subscriber"
msgstr "Iscritto"

#: app/Services/TransStrings.php:1304
msgid "Subscriber Clicks"
msgstr "Clic degli iscritti"

#: app/Hooks/Handlers/ExternalPages.php:696
msgid "Subscriber confirmed double opt-in from IP Address:"
msgstr "L'iscritto ha confermato il doppio opt-in dall'indirizzo IP:"

#: app/Hooks/Handlers/ExternalPages.php:695
msgid "Subscriber double opt-in confirmed"
msgstr "Doppio opt-in dell'iscritto confermato"

#: app/Services/TransStrings.php:1305
msgid "Subscriber Fields"
msgstr "Campi dell'iscritto"

#: app/Http/Controllers/SubscriberController.php:107
msgid "Subscriber not found"
msgstr "Iscritto non trovato"

#: app/Http/Controllers/SubscriberController.php:453
msgid "Subscriber successfully updated"
msgstr "Iscritto aggiornato con successo"

#: app/Hooks/Handlers/ExternalPages.php:575
#, php-format
msgid "Subscriber unsubscribed from IP Address: %1s <br />Reason: %2s"
msgstr "Iscritto disiscritto dall'indirizzo IP: %1s <br />Motivo: %2s"

#: app/Http/Controllers/SubscriberController.php:788
msgid "Subscriber's status need to be subscribed."
msgstr "Lo stato dell'iscritto deve essere iscritto."

#: app/Http/Controllers/FunnelController.php:634
msgid "subscriber_ids parameter is required"
msgstr "Parametro subscriber_ids richiesto"

#: app/Services/TransStrings.php:1306 app/Hooks/CLI/Commands.php:30
msgid "Subscribers"
msgstr "Iscritti"

#: app/Services/TransStrings.php:1307
msgid "Subscribers Growth"
msgstr "Crescita degli iscritti"

#: app/Services/TransStrings.php:1510
msgid "Subscribers has been added to this email sequence"
msgstr "Gli iscritti sono stati aggiunti a questa sequenza di email"

#: app/Services/TransStrings.php:1509
msgid "Subscribers have been added successfully to this sequence."
msgstr "Gli iscritti sono stati aggiunti con successo a questa sequenza."

#: app/Services/TransStrings.php:848
msgid "Subscribers imported successfully."
msgstr "Iscritti importati con successo."

#: app/Http/Controllers/SubscriberController.php:922
msgid "Subscribers selection is required"
msgstr "È richiesta la selezione degli iscritti"

#: app/Http/Controllers/SubscriberController.php:209
msgid "Subscribers successfully updated"
msgstr "Iscritti aggiornati con successo"

#: app/Http/Controllers/FormsController.php:271
msgid "Subscription Form"
msgstr "Modulo di iscrizione"

#: app/Hooks/CLI/Commands.php:140
msgid "Subscription Payments"
msgstr "Pagamenti di abbonamento"

#: app/Services/Funnel/Triggers/FluentFormSubmissionTrigger.php:112
#: app/Services/Funnel/Triggers/UserRegistrationTrigger.php:46
msgid "Subscription Status"
msgstr "Stato dell'abbonamento"

#: app/Http/Controllers/FunnelController.php:666
msgid "Subscription status is required"
msgstr "Stato dell'abbonamento richiesto"

#: app/Services/TransStrings.php:1308
msgid "Subtract Options"
msgstr "Opzioni di sottrazione"

#: app/Services/TransStrings.php:1309
msgid "Subtract Value"
msgstr "Valore di sottrazione"

#: app/Services/TransStrings.php:1310
msgid "Success"
msgstr "Successo"

#: app/Hooks/Handlers/ExternalPages.php:115
#: app/Hooks/Handlers/ExternalPages.php:170
msgid "success"
msgstr "successo"

#: app/Http/Controllers/SubscriberController.php:335
msgid "Successfully added the subscriber."
msgstr "Iscritto aggiunto con successo."

#: app/Http/Controllers/WebhookController.php:76
msgid "Successfully created the WebHook"
msgstr "Webhook creato con successo"

#: app/Http/Controllers/WebhookController.php:96
msgid "Successfully deleted the webhook"
msgstr "Webhook eliminato con successo"

#: app/Http/Controllers/ListsController.php:218
msgid "Successfully removed the list."
msgstr "Lista rimossa con successo."

#: app/Http/Controllers/TagsController.php:223
msgid "Successfully removed the tag."
msgstr "Tag rimosso con successo."

#: app/Http/Controllers/ListsController.php:98
#: app/Http/Controllers/ListsController.php:155
msgid "Successfully saved the list."
msgstr "Lista salvata con successo."

#: app/Http/Controllers/TagsController.php:105
#: app/Http/Controllers/TagsController.php:159
msgid "Successfully saved the tag."
msgstr "Tag salvato con successo."

#: app/Http/Controllers/TagsController.php:203
msgid "Successfully saved the tags."
msgstr "Tag salvati con successo."

#: app/Http/Controllers/SubscriberController.php:273
msgid "Successfully updated the "
msgstr "Aggiornamento effettuato con successo per "

#: app/Http/Controllers/WebhookController.php:86
msgid "Successfully updated the webhook"
msgstr "Webhook aggiornato con successo"

#: app/Hooks/Handlers/CountryNames.php:861
msgid "Sudan"
msgstr "Sudan"

#: app/Services/TransStrings.php:1311
msgid "Suggested text to include:"
msgstr "Testo suggerito da includere:"

#: app/Services/TransStrings.php:719
msgid "Suggesting value: Between 600 to 800"
msgstr "Valore suggerito: Tra 600 e 800"

#: app/Services/TransStrings.php:1312
msgid "Summary"
msgstr "Sommario"

#: app/Services/TransStrings.php:1313
msgid "Sunday"
msgstr "Domenica"

#: app/Services/TransStrings.php:1314
msgid "Super fast UI powered by VueJS"
msgstr "Interfaccia utente super veloce grazie a VueJS"

#: app/Services/TransStrings.php:93
msgid "Supercharge your Email Marketing and Automation with FluentCRM Pro"
msgstr "Potenzia il tuo Email Marketing e Automazione con FluentCRM Pro"

#: fluent-crm.php:45
msgid "Support"
msgstr "Supporto"

#: app/Services/Helper.php:129
msgid "Support Tickets"
msgstr "Ticket di supporto"

#: app/Services/TransStrings.php:1315
msgid "Support Tickets from"
msgstr "Ticket di supporto da"

#: app/Hooks/Handlers/CountryNames.php:865
msgid "Suriname"
msgstr "Suriname"

#: app/Hooks/Handlers/CountryNames.php:869
msgid "Svalbard and Jan Mayen"
msgstr "Svalbard e Jan Mayen"

#: app/Hooks/Handlers/CountryNames.php:873
msgid "Swaziland"
msgstr "Swaziland"

#: app/Hooks/Handlers/CountryNames.php:877
msgid "Sweden"
msgstr "Svezia"

#: app/Hooks/Handlers/CountryNames.php:881
msgid "Switzerland"
msgstr "Svizzera"

#: app/Services/TransStrings.php:161
msgid "Sync new steps to completed contacts"
msgstr "Sincronizza i nuovi passi con i contatti completati"

#: app/Services/TransStrings.php:1316
msgid "Sync new steps to existing completed contacts"
msgstr "Sincronizza i nuovi passi con i contatti completati esistenti"

#: app/Http/Controllers/FunnelController.php:849
msgid "Synced successfully"
msgstr "Sincronizzato con successo"

#: app/Services/TransStrings.php:769
msgid "Syncing may take few seconds to minutes based on your customers size in"
msgstr ""
"La sincronizzazione potrebbe richiedere da pochi secondi a minuti a seconda "
"del numero di clienti"

#: app/Services/TransStrings.php:1317
msgid "Syncing..."
msgstr "Sincronizzazione in corso..."

#: app/Hooks/Handlers/CountryNames.php:885
msgid "Syria"
msgstr "Siria"

#: app/Services/TransStrings.php:1318
msgid "System Defined"
msgstr "Definito dal sistema"

#: app/Services/TransStrings.php:1319
msgid "Tag"
msgstr "Tag"

#: app/Services/TransStrings.php:1644
msgid "tag"
msgstr "tag"

#: app/Services/Funnel/Benchmarks/TagAppliedBenchmark.php:23
#: app/Services/Funnel/Benchmarks/TagAppliedBenchmark.php:48
msgid "Tag Applied"
msgstr "Tag applicato"

#: app/Services/TransStrings.php:1320
msgid "Tag Name"
msgstr "Nome del tag"

#: app/Services/Funnel/Benchmarks/RemoveFromTagBenchmark.php:23
msgid "Tag Removed"
msgstr "Tag rimosso"

#: app/Services/Funnel/Benchmarks/RemoveFromTagBenchmark.php:48
msgid "Tag Removed From Contact"
msgstr "Tag rimosso dal contatto"

#: app/Services/Stats.php:39 app/Services/TransStrings.php:1322
#: app/Services/Helper.php:1013 app/Hooks/CLI/Commands.php:154
#: app/Hooks/CLI/Commands.php:368 app/Hooks/CLI/Commands.php:576
#: app/Hooks/Handlers/AdminMenu.php:104 app/Hooks/Handlers/AdminMenu.php:105
#: app/Hooks/Handlers/AdminMenu.php:328
#: app/Hooks/Handlers/EventTrackingHandler.php:269
msgid "Tags"
msgstr "Tag"

#: app/Services/TransStrings.php:1645
msgid "tags"
msgstr "tag"

#: app/Services/TransStrings.php:1321
msgid "Tags are like Lists but more ways to filter your contacts inside a list"
msgstr ""
"I tag sono simili alle liste ma offrono più modi per filtrare i contatti "
"all'interno di una lista"

#: app/Services/TransStrings.php:1541
msgid ""
"Tags are like Segment Lists but you it will let you filter your contacts in "
"more targeted way. Let's Create Some tags."
msgstr ""
"I tag sono come le liste di segmenti ma consentono di filtrare i contatti in "
"modo più mirato. Creiamo alcuni tag."

#: app/Services/RoleBasedTagging.php:59
msgid "Tags to be added"
msgstr "Tag da aggiungere"

#: app/Services/RoleBasedTagging.php:60
msgid "Tags to be removed"
msgstr "Tag da rimuovere"

#: app/Hooks/Handlers/CountryNames.php:889
msgid "Taiwan"
msgstr "Taiwan"

#: app/Hooks/Handlers/CountryNames.php:893
msgid "Tajikistan"
msgstr "Tagikistan"

#: app/Hooks/Handlers/CountryNames.php:897
msgid "Tanzania"
msgstr "Tanzania"

#: app/Services/TransStrings.php:1323
msgid "Target Full URL"
msgstr "URL completo di destinazione"

#: app/Services/TransStrings.php:1324
msgid "Target URL"
msgstr "URL di destinazione"

#: app/Services/RoleBasedTagging.php:58
msgid "Target User Role"
msgstr "Ruolo utente di destinazione"

#: app/Services/Funnel/Triggers/UserRegistrationTrigger.php:82
msgid "Targeted User Roles"
msgstr "Ruoli utente di destinazione"

#: app/Services/TransStrings.php:1327
msgid "Template export is only available on pro version of FluentCRM"
msgstr ""
"L'esportazione dei modelli è disponibile solo nella versione pro di FluentCRM"

#: app/Services/TransStrings.php:1325
msgid "Template Name"
msgstr "Nome del modello"

#: app/Http/Controllers/TemplateController.php:161
msgid "Template successfully created"
msgstr "Modello creato con successo"

#: app/Http/Controllers/TemplateController.php:193
#, fuzzy
#| msgid "Segment successfully duplicated"
msgid "Template successfully duplicated"
msgstr "Modello duplicato con successo"

#: app/Http/Controllers/TemplateController.php:244
msgid "Template successfully updated"
msgstr "Modello aggiornato con successo"

#: app/Services/TransStrings.php:1326
msgid "Template Title"
msgstr "Titolo del modello"

#: app/Services/TransStrings.php:1328
msgid "Test email successfully sent to"
msgstr "Email di test inviata con successo a"

#: app/Http/Controllers/CampaignController.php:727
msgid "Test email successfully sent to "
msgstr "Email di test inviata con successo a "

#: app/Services/TransStrings.php:1329
msgid "Text Color"
msgstr "Colore del testo"

#: app/Hooks/Handlers/CountryNames.php:901
msgid "Thailand"
msgstr "Thailandia"

#: app/Services/TransStrings.php:1330
msgid "Thank you again for configuring your own CRM in WordPress."
msgstr "Grazie ancora per aver configurato il tuo CRM in WordPress."

#: app/Services/TransStrings.php:1591
msgid ""
"Thank you again for configuring your own CRM in WordPress. To collect lead "
"via form we are suggesting to install"
msgstr ""
"Grazie ancora per aver configurato il tuo CRM in WordPress. Per raccogliere "
"contatti tramite modulo ti suggeriamo di installare"

#: app/Services/TransStrings.php:1647
msgid ""
"Thank you for choosing FluentCRM. An easier way to manage email marketing "
"campaigns! This quick setup wizard will help you configure the basic "
"settings."
msgstr ""
"Grazie per aver scelto FluentCRM. Un modo più semplice per gestire le "
"campagne di email marketing! Questa procedura guidata di configurazione "
"rapida ti aiuterà a configurare le impostazioni di base."

#: app/Hooks/Handlers/AdminMenu.php:266
#, php-format
msgid "Thank you for using <a href=\"%s\">FluentCRM</a>."
msgstr "Grazie per aver utilizzato <a href=\"%s\">FluentCRM</a>."

#: app/Services/Funnel/BaseTrigger.php:63
msgid ""
"The actions will run even the contact's status is not in subscribed status."
msgstr ""
"Le azioni verranno eseguite anche se lo stato del contatto non è 'iscritto'."

#: app/Hooks/Handlers/CampaignGuard.php:45
msgid ""
"The campaign has been locked and not deletable due to it's current status"
msgstr ""
"La campagna è stata bloccata e non può essere eliminata a causa del suo "
"stato attuale"

#: app/Hooks/Handlers/CampaignGuard.php:28
msgid ""
"The campaign has been locked and not modifiable due to it's current status"
msgstr ""
"La campagna è stata bloccata e non può essere modificata a causa del suo "
"stato attuale"

#: app/Services/TransStrings.php:1331
msgid "The email address is required!"
msgstr "L'indirizzo email è obbligatorio!"

#: app/Services/TransStrings.php:1648
msgid "the email will be triggered from the starting date"
msgstr "l'email sarà inviata a partire dalla data di inizio"

#: app/Hooks/Handlers/ExternalPages.php:489
msgid "The emails are inappropriate"
msgstr "Le email sono inappropriate"

#: app/Hooks/Handlers/ExternalPages.php:490
msgid "The emails are spam"
msgstr "Le email sono spam"

#: app/Services/TransStrings.php:1421
msgid "The emails will be sent based on your set date and time."
msgstr "Le email verranno inviate in base alla data e all'ora impostate."

#: app/Http/Controllers/CsvController.php:44
msgid "The file must be a valid CSV."
msgstr "Il file deve essere un CSV valido."

#: app/Hooks/Handlers/ExternalPages.php:1029
msgid ""
"The new email has been used to another account. Please use a new email "
"address"
msgstr ""
"L'indirizzo email è già in uso su un altro account. Si prega di utilizzare "
"un nuovo indirizzo email"

#: app/Services/TransStrings.php:162
msgid "The new steps will start processing within 5 minutes once you confirm."
msgstr ""
"I nuovi passaggi inizieranno l'elaborazione entro 5 minuti dalla conferma."

#: app/Http/Controllers/SettingsController.php:519
#, fuzzy
#| msgid "The provided JSON file is not valid"
msgid "The provided hook name is not valid"
msgstr "Il nome dell'hook fornito non è valido"

#: app/Services/TransStrings.php:589
msgid ""
"The provided url will be used to redirect a user when unsubscribe. Leave "
"blank if you do not want a redirect"
msgstr ""
"L'URL fornito verrà utilizzato per reindirizzare un utente in caso di "
"disiscrizione. Lasciare vuoto se non si desidera il reindirizzamento"

#: app/Services/TransStrings.php:1332
msgid "The referrer: (e.g. google, newsletter)"
msgstr "Il referrer: (es. google, newsletter)"

#: app/Http/Controllers/FunnelController.php:682
msgid "The status already completed state"
msgstr "Lo stato è già impostato come completato"

#: app/Services/TransStrings.php:88
msgid "The subscriber is already present in the database."
msgstr "L'iscritto è già presente nel database."

#: app/Http/Controllers/TemplateController.php:301
msgid "The template has been deleted successfully."
msgstr "Il modello è stato eliminato con successo."

#: app/Http/Controllers/DocsController.php:77
msgid ""
"The Ultimate SMTP and SES Plugin for WordPress. Connect with any SMTP, "
"SendGrid, Mailgun, SES, Sendinblue, PepiPost, Google, Microsoft and more."
msgstr ""
"Il plugin SMTP e SES definitivo per WordPress. Connettiti a qualsiasi SMTP, "
"SendGrid, Mailgun, SES, Sendinblue, PepiPost, Google, Microsoft e altro "
"ancora."

#: app/Services/TransStrings.php:1492
msgid ""
"These are your goal/trigger item that your user will do and you can measure "
"these steps or adding into this funnel"
msgstr ""
"Questi sono i tuoi obiettivi/trigger che l'utente eseguirà e puoi misurare "
"questi passaggi o aggiungerli in questo funnel"

#: app/Services/TransStrings.php:410
msgid ""
"These Lists will be applied to the contact whenever this link is clicked"
msgstr ""
"Queste liste saranno applicate al contatto ogni volta che questo link viene "
"cliccato"

#: app/Services/TransStrings.php:411
msgid "These tags will be applied to the contact whenever this link is clicked"
msgstr ""
"Questi tag verranno applicati al contatto ogni volta che si clicca su questo "
"link"

#: app/Services/TransStrings.php:413
msgid ""
"These tags will be removed from the contact whenever this link is clicked"
msgstr ""
"Questi tag verranno rimossi dal contatto ogni volta che questo link viene "
"cliccato"

#: app/Services/TransStrings.php:412
msgid "These Tags will be removed to a contact whenever this link is clicked"
msgstr ""
"Questi tag verranno rimossi da un contatto ogni volta che si clicca su "
"questo link"

#: app/Http/Controllers/SubscriberController.php:943
#: app/Http/Controllers/SubscriberController.php:1105
msgid "This action requires FluentCRM Pro"
msgstr "Questa azione richiede FluentCRM Pro"

#: app/Services/TransStrings.php:1590
msgid ""
"This automation trigger is available on Pro version of FluentCRM. Please "
"download and install FluentCRM Pro"
msgstr ""
"Questo trigger di automazione è disponibile nella versione Pro di FluentCRM. "
"Si prega di scaricare e installare FluentCRM Pro"

#: app/Services/TransStrings.php:1422
msgid "This campaign has been scheduled."
msgstr "Questa campagna è stata programmata."

#: app/Services/TransStrings.php:1423
msgid "This campaign is now on \"Paused\" state. No Emails will be sent"
msgstr ""
"Questa campagna è ora in stato di \"Sospeso\". Non verranno inviate email"

#: app/Services/TransStrings.php:1334
msgid "This email footer text will be used to all your email"
msgstr "Questo testo nel footer verrà utilizzato su tutte le tue email"

#: app/Services/TransStrings.php:1335
msgid "This email footer text will be used to this email only"
msgstr "Questo testo nel footer verrà utilizzato solo su questa email"

#: app/Services/TransStrings.php:101
msgid "This email will be used to send important notification to administrator"
msgstr ""
"Questa email verrà utilizzata per inviare notifiche importanti "
"all'amministratore"

#: app/Services/TransStrings.php:1336
msgid "This feature only available on FluentCRM Pro."
msgstr "Questa funzione è disponibile solo su FluentCRM Pro."

#: app/Http/Controllers/FunnelController.php:830
#: app/Http/Controllers/FunnelController.php:838
msgid "This feature require latest version of FluentCRM Pro version"
msgstr "Questa funzione richiede l'ultima versione di FluentCRM Pro"

#: app/Services/TransStrings.php:1500
msgid "This form will be created in Fluent Forms and you can customize anytime"
msgstr ""
"Questo modulo verrà creato in Fluent Forms e puoi personalizzarlo in "
"qualsiasi momento"

#: app/Services/Funnel/Triggers/FluentFormSubmissionTrigger.php:32
msgid ""
"This Funnel will be initiated when a new form submission has been submitted"
msgstr "Questo funnel verrà avviato quando verrà inviato un nuovo modulo"

#: app/Services/Funnel/Triggers/FluentFormSubmissionTrigger.php:62
msgid ""
"This Funnel will be initiated when a new form submission has been submitted."
msgstr "Questo funnel verrà avviato quando verrà inviato un nuovo modulo."

#: app/Services/Funnel/Triggers/UserRegistrationTrigger.php:24
#: app/Services/Funnel/Triggers/UserRegistrationTrigger.php:40
msgid ""
"This Funnel will be initiated when a new user has been registered in your "
"site"
msgstr ""
"Questo funnel verrà avviato quando un nuovo utente verrà registrato sul tuo "
"sito"

#: app/Services/TransStrings.php:810
msgid ""
"This is a premium feature. Please download the FluentCRM Pro to activate "
"this feature."
msgstr ""
"Questa è una funzione premium. Scarica FluentCRM Pro per attivare questa "
"funzione."

#: app/Services/TransStrings.php:1337
msgid "This is a pro feature"
msgstr "Questa è una funzione pro"

#: app/Http/Controllers/SettingsController.php:155
msgid "This message will be shown after a subscriber confirm subscription"
msgstr ""
"Questo messaggio verrà mostrato dopo che un iscritto ha confermato "
"l'iscrizione"

#: app/Services/TransStrings.php:1333
msgid "This Month"
msgstr "Questo Mese"

#: app/Services/TransStrings.php:1338
msgid "This quarter"
msgstr "Questo trimestre"

#: app/Services/TransStrings.php:643
msgid ""
"This section contains all the advanced features that you can enable or "
"disable."
msgstr ""
"Questa sezione contiene tutte le funzionalità avanzate che puoi abilitare o "
"disabilitare."

#: app/Services/TransStrings.php:24
msgid ""
"This subject will not be used to send emails. Please set A/B testing subjects"
msgstr ""
"Questo oggetto non verrà utilizzato per inviare email. Si prega di impostare "
"oggetti per test A/B"

#: app/Services/TransStrings.php:1649
msgid "this value will be skipped"
msgstr "questo valore verrà saltato"

#: app/Services/TransStrings.php:1643
msgid "This will be used for your email campaign, Subscriber's front pages"
msgstr ""
"Questo verrà utilizzato per le tue campagne email, le pagine frontali degli "
"iscritti"

#: app/Services/TransStrings.php:1051
msgid "This will filter all available contacts"
msgstr "Questo filtrerà tutti i contatti disponibili"

#: app/Services/Funnel/Benchmarks/ListAppliedBenchmark.php:24
#: app/Services/Funnel/Benchmarks/ListAppliedBenchmark.php:49
msgid "This will run when selected lists have been applied to a contact"
msgstr ""
"Questo verrà eseguito quando le liste selezionate sono state applicate a un "
"contatto"

#: app/Services/Funnel/Benchmarks/RemoveFromListBenchmark.php:24
#: app/Services/Funnel/Benchmarks/RemoveFromListBenchmark.php:49
msgid "This will run when selected lists have been removed from a contact"
msgstr ""
"Questo verrà eseguito quando le liste selezionate sono state rimosse da un "
"contatto"

#: app/Services/Funnel/Benchmarks/TagAppliedBenchmark.php:24
#: app/Services/Funnel/Benchmarks/TagAppliedBenchmark.php:49
msgid "This will run when selected Tags have been applied to a contact"
msgstr ""
"Questo verrà eseguito quando i tag selezionati sono stati applicati a un "
"contatto"

#: app/Services/Funnel/Benchmarks/RemoveFromTagBenchmark.php:24
#: app/Services/Funnel/Benchmarks/RemoveFromTagBenchmark.php:49
msgid "This will run when selected Tags have been removed from a contact"
msgstr ""
"Questo verrà eseguito quando i tag selezionati sono stati rimossi da un "
"contatto"

#: app/Services/TransStrings.php:1339
msgid "ThriveArchitect"
msgstr "ThriveArchitect"

#: app/Services/TransStrings.php:1340
msgid "ThriveThemes integration"
msgstr "Integrazione ThriveThemes"

#: app/Hooks/Handlers/AdminMenu.php:851
msgid "Thumbnail"
msgstr "Miniatura"

#: app/Services/TransStrings.php:1341
msgid "Thursday"
msgstr "Giovedì"

#: app/Services/TransStrings.php:1650
msgid "time unit"
msgstr "unità di tempo"

#: app/Models/Subscriber.php:723
msgid "Timezone"
msgstr "Fuso orario"

#: app/Hooks/Handlers/CountryNames.php:905
msgid "Timor-Leste"
msgstr "Timor Est"

#: app/Services/TransStrings.php:1342
msgid "Tips"
msgstr "Suggerimenti"

#: app/Services/TransStrings.php:1343 app/Services/Helper.php:1874
#: app/Http/Controllers/CampaignAnalyticsController.php:104
#: app/Hooks/Handlers/PrefFormHandler.php:44
msgid "Title"
msgstr "Titolo"

#: app/Services/TransStrings.php:1344
msgid "Title field is required"
msgstr "Il campo titolo è obbligatorio"

#: app/Services/TransStrings.php:1345
msgid "Title of the Recurring Campaign"
msgstr "Titolo della campagna ricorrente"

#: app/Services/TransStrings.php:1346
msgid "To"
msgstr "A"

#: app/Services/TransStrings.php:1396
msgid "To Activate this module please upgrade to pro"
msgstr ""
"Per attivare questo modulo, si prega di eseguire l'aggiornamento alla "
"versione pro"

#: app/Services/TransStrings.php:1651
msgid "to enable this feature"
msgstr "per abilitare questa funzione"

#: app/Services/TransStrings.php:1652
msgid "to insert dynamic tags"
msgstr "per inserire tag dinamici"

#: app/Services/TransStrings.php:1653
msgid "to insert post/page links"
msgstr "per inserire link a post/pagine"

#: app/Services/TransStrings.php:1654
msgid "to make this feature work"
msgstr "per far funzionare questa funzione"

#: app/Services/TransStrings.php:1655
msgid "to re-sync the data again"
msgstr "per sincronizzare nuovamente i dati"

#: app/Services/TransStrings.php:1656
msgid "to see All Available Blocks"
msgstr "per visualizzare tutti i blocchi disponibili"

#: app/Services/TransStrings.php:1657
msgid "to see all the available blocks"
msgstr "per visualizzare tutti i blocchi disponibili"

#: app/Services/TransStrings.php:1658
msgid "to see smart tags"
msgstr "per visualizzare i tag smart"

#: app/Services/TransStrings.php:1347
msgid "To use this feature you need FluentCRM Pro."
msgstr "Per utilizzare questa funzione è necessario FluentCRM Pro."

#: app/Hooks/Handlers/CountryNames.php:909
msgid "Togo"
msgstr "Togo"

#: app/Hooks/Handlers/CountryNames.php:913
msgid "Tokelau"
msgstr "Tokelau"

#: app/Services/TransStrings.php:1348
msgid "Tomorrow"
msgstr "Domani"

#: app/Hooks/Handlers/CountryNames.php:917
msgid "Tonga"
msgstr "Tonga"

#: app/Services/TransStrings.php:1349
msgid "Tools"
msgstr "Strumenti"

#: app/Services/TransStrings.php:1350
msgid "Top Selling Products"
msgstr "Prodotti più venduti"

#: app/Services/TransStrings.php:1687
#: app/Http/Controllers/CampaignAnalyticsController.php:107
#: app/Http/Controllers/CampaignAnalyticsController.php:158
#: app/Http/Controllers/CampaignAnalyticsController.php:176
#: app/Hooks/Handlers/PurchaseHistory.php:139
#: app/Hooks/Handlers/PurchaseHistory.php:355
msgid "Total"
msgstr "Totale"

#: app/Services/TransStrings.php:1659
msgid "total"
msgstr "totale"

#: app/Services/TransStrings.php:1351
msgid "Total Clicks"
msgstr "Totale clic"

#: app/Services/TransStrings.php:1352
msgid "Total Contact"
msgstr "Contatti totali"

#: app/Services/TransStrings.php:1353
msgid "Total Emails"
msgstr "Email totali"

#: app/Services/TransStrings.php:1354
msgid "Total Found Result:"
msgstr "Risultato totale trovato:"

#: app/Services/TransStrings.php:1355
msgid "Total Inserted:"
msgstr "Totale inserito:"

#: app/Services/TransStrings.php:1356
msgid "Total Order Count"
msgstr "Conteggio totale degli ordini"

#: app/Services/Helper.php:1209 app/Services/Helper.php:1263
msgid "Total Order Count (Pro Required)"
msgstr "Numero totale degli ordini (richiesto Pro)"

#: app/Services/Helper.php:1269
msgid "Total Order Value (Pro Required)"
msgstr "Valore totale degli ordini (Richiesto Pro)"

#: app/Services/Helper.php:1215
msgid "Total Order value (Pro Required)"
msgstr "Valore totale degli ordini (Richiesto Pro)"

#: app/Services/TransStrings.php:1357
msgid "Total Recipients"
msgstr "Destinatari totali"

#: app/Services/Helper.php:1320
msgid "Total Referrals (Pro Required)"
msgstr "Referral totali (Richiesto Pro)"

#: app/Services/TransStrings.php:687
msgid "Total Revenue from this funnel"
msgstr "Entrate totali da questo funnel"

#: app/Services/TransStrings.php:849
msgid "Total Skipped (Including Invalid):"
msgstr "Totale saltato (compresi quelli non validi):"

#: app/Hooks/CLI/Commands.php:572
msgid "Total Students"
msgstr "Totale studenti"

#: app/Services/TransStrings.php:1358
msgid "Total Updated:"
msgstr "Totale aggiornato:"

#: app/Services/TransStrings.php:1359
msgid "Total:"
msgstr "Totale:"

#: app/Functions/helpers.php:622
msgid "Transaction"
msgstr "Transazione"

#: app/Functions/helpers.php:501 app/Functions/helpers.php:548
msgid "Transactional"
msgstr "Transazionale"

#: app/Services/TransStrings.php:1360
msgid "Transfer Data From Other CRM"
msgstr "Trasferisci dati da un altro CRM"

#: app/Services/CrmMigrator/ActiveCampaignMigrator.php:14
msgid "Transfer your ActiveCampaign tags and contacts to FluentCRM"
msgstr "Trasferisci i tuoi tag e contatti di ActiveCampaign su FluentCRM"

#: app/Services/TransStrings.php:1660
msgid ""
"Transfer your contacts, tags and associate data from your current email "
"marketing software to FluentCRM in minutes"
msgstr ""
"Trasferisci i tuoi contatti, tag e dati associati dal tuo attuale software "
"di email marketing a FluentCRM in pochi minuti"

#: app/Services/CrmMigrator/DripMigrator.php:14
msgid "Transfer your Drip tags and contacts to FluentCRM"
msgstr "Trasferisci i tuoi tag e contatti di Drip su FluentCRM"

#: app/Services/CrmMigrator/MailChimpMigrator.php:18
msgid ""
"Transfer your mailchimp lists, tags and contacts from MailChimp to FluentCRM"
msgstr "Trasferisci le tue liste, tag e contatti di MailChimp su FluentCRM"

#: app/Services/TransStrings.php:1361
msgid "Trigger"
msgstr "Trigger"

#: app/Http/Controllers/FunnelController.php:181
msgid "Trigger name is same"
msgstr "Il nome del trigger è lo stesso"

#: app/Services/TransStrings.php:1362
msgid "Trigger Title"
msgstr "Titolo del trigger"

#: app/Hooks/Handlers/CountryNames.php:921
msgid "Trinidad and Tobago"
msgstr "Trinidad e Tobago"

#: app/Services/TransStrings.php:319
msgid "Try to get company logo automatically from the given website url"
msgstr ""
"Prova a ottenere automaticamente il logo dell'azienda dall'URL del sito web "
"fornito"

#: app/Services/TransStrings.php:1364
msgid "Trying fallback save. Please Wait..."
msgstr "Tentativo di salvataggio di riserva. Attendere prego..."

#: app/Services/TransStrings.php:1365
msgid "Tuesday"
msgstr "Martedì"

#: app/Hooks/Handlers/CountryNames.php:925
msgid "Tunisia"
msgstr "Tunisia"

#: app/Hooks/Handlers/CountryNames.php:929
msgid "Turkey"
msgstr "Turchia"

#: app/Hooks/Handlers/CountryNames.php:933
msgid "Turkmenistan"
msgstr "Turkmenistan"

#: app/Hooks/Handlers/CountryNames.php:937
msgid "Turks and Caicos Islands"
msgstr "Isole Turks e Caicos"

#: app/Http/Controllers/ImporterController.php:252
msgid "TutorLMS"
msgstr "TutorLMS"

#: app/Services/TransStrings.php:1366
msgid "TutorLMS Integration:"
msgstr "Integrazione con TutorLMS:"

#: app/Hooks/Handlers/CountryNames.php:941
msgid "Tuvalu"
msgstr "Tuvalu"

#: app/Functions/helpers.php:624
msgid "Tweet"
msgstr "Tweet"

#: app/Services/TransStrings.php:1367
msgid "Twitter Handle URL"
msgstr "URL dell'handle Twitter"

#: app/Models/Company.php:69
msgid "Twitter URL"
msgstr "URL di Twitter"

#: app/Services/TransStrings.php:1368
msgid "Twitter Url"
msgstr "URL di Twitter"

#: app/Models/Company.php:57 app/Services/TransStrings.php:1369
#: app/Services/Helper.php:1004 app/Services/Helper.php:1857
#: app/Hooks/Handlers/EventTrackingHandler.php:260
msgid "Type"
msgstr "Tipo"

#: app/Services/TransStrings.php:1370
msgid "Type and Enter..."
msgstr "Digita e premi Invio..."

#: app/Hooks/Handlers/AdminBar.php:73
msgid "Type and press enter"
msgstr "Digita e premi Invio"

#: app/Services/TransStrings.php:1662
msgid "type and press enter"
msgstr "digita e premi invio"

#: app/Services/TransStrings.php:231 app/Services/TransStrings.php:1646
msgid ""
"Type custom email to send test or leave blank to send current user email"
msgstr ""
"Digita l'email personalizzata per inviare un test o lascia vuoto per inviare "
"all'email dell'utente corrente"

#: app/Services/TransStrings.php:1663
msgid "type days"
msgstr "digita giorni"

#: app/Hooks/Handlers/AdminBar.php:74
msgid "Type to search contacts"
msgstr "Digita per cercare contatti"

#: app/Hooks/Handlers/CountryNames.php:945
msgid "Uganda"
msgstr "Uganda"

#: app/Hooks/Handlers/CountryNames.php:949
msgid "Ukraine"
msgstr "Ucraina"

#: app/Services/TransStrings.php:1373
msgid "Uncheck this if you want to trigger automation events"
msgstr "Deseleziona questa opzione se desideri attivare eventi di automazione"

#: app/Services/TransStrings.php:1374
msgid "Unique Clicks"
msgstr "Click unici"

#: app/Hooks/Handlers/CountryNames.php:953
msgid "United Arab Emirates"
msgstr "Emirati Arabi Uniti"

#: app/Hooks/Handlers/CountryNames.php:957
msgid "United Kingdom (UK)"
msgstr "Regno Unito (UK)"

#: app/Hooks/Handlers/CountryNames.php:961
msgid "United States (US)"
msgstr "Stati Uniti (US)"

#: app/Hooks/Handlers/CountryNames.php:965
msgid "United States (US) Minor Outlying Islands"
msgstr "Isole Minori Esterne degli Stati Uniti (US)"

#: app/Services/TransStrings.php:1375
msgid "Unknown error"
msgstr "Errore sconosciuto"

#: app/Services/TransStrings.php:1664
msgid "unknown error. Please check your csv first"
msgstr "Errore sconosciuto. Si prega di controllare prima il CSV"

#: app/Services/Helper.php:1343
msgid "Unpaid Earnings (Pro Required)"
msgstr "Guadagni non pagati (Richiesto Pro)"

#: app/Services/TransStrings.php:1377 app/Hooks/Handlers/ExternalPages.php:321
#: app/Hooks/Handlers/ExternalPages.php:325
#: app/views/external/manage_subscription_form.php:39
#: app/views/external/unsubscribe.php:19
#: app/Services/Libs/Parser/ShortcodeParser.php:226
msgid "Unsubscribe"
msgstr "Annulla iscrizione"

#: app/Models/CampaignUrlMetric.php:150
msgid "Unsubscribe (%d)"
msgstr "Annulla iscrizione (%d)"

#: app/Services/Helper.php:209
msgid "Unsubscribe Hyperlink HTML"
msgstr "HTML del link di disiscrizione"

#: app/Services/Helper.php:206
msgid "Unsubscribe URL"
msgstr "URL di disiscrizione"

#: app/Functions/helpers.php:500 app/Functions/helpers.php:547
#: app/Services/TransStrings.php:1378 app/Services/TransStrings.php:1665
#: app/Hooks/Handlers/ExternalPages.php:574
msgid "Unsubscribed"
msgstr "Disiscritto"

#: app/Services/TransStrings.php:1379
msgid "Unsubscribers"
msgstr "Disiscritti"

#: app/Services/TransStrings.php:1381
msgid "Update"
msgstr "Aggiorna"

#: app/Services/TransStrings.php:1382
msgid "Update Companies"
msgstr "Aggiorna aziende"

#: app/Services/TransStrings.php:1383
msgid "Update Contact"
msgstr "Aggiorna contatto"

#: app/Services/TransStrings.php:1384
msgid "Update Custom Field"
msgstr "Aggiorna campo personalizzato"

#: app/Services/TransStrings.php:1385
msgid "Update Field"
msgstr "Aggiorna campo"

#: app/Services/TransStrings.php:1391
msgid "Update if already in DB"
msgstr "Aggiorna se già presente nel DB"

#: app/Services/Funnel/FunnelHelper.php:31
msgid "Update if Exist"
msgstr "Aggiorna se esiste"

#: app/Services/TransStrings.php:1392 app/Hooks/Handlers/PrefFormHandler.php:54
#: app/Hooks/Handlers/PrefFormHandler.php:124
msgid "Update info"
msgstr "Aggiorna info"

#: app/Services/TransStrings.php:1386
msgid "Update Note"
msgstr "Aggiorna nota"

#: app/views/external/manage_subscription_form.php:35
msgid "Update Profile"
msgstr "Aggiorna profilo"

#: app/Services/TransStrings.php:1387
msgid "Update Segment"
msgstr "Aggiorna segmento"

#: app/Services/TransStrings.php:1388
msgid "Update Settings"
msgstr "Aggiorna impostazioni"

#: app/Services/TransStrings.php:1389
msgid "Update Smart Link"
msgstr "Aggiorna Smart Link"

#: app/Services/TransStrings.php:1390
msgid "Update Subscribers"
msgstr "Aggiorna iscritti"

#: app/views/external/manage_subscription.php:8
#: app/views/external/manage_subscription.php:27
msgid "Update your preferences"
msgstr "Aggiorna le tue preferenze"

#: app/Services/TransStrings.php:1393
msgid "Updated At"
msgstr "Aggiornato il"

#: app/Services/TransStrings.php:1394
msgid "Updating "
msgstr "Aggiornamento in corso "

#: app/Services/TransStrings.php:1395
msgid "Upgrade to FluentCRM Pro"
msgstr "Aggiorna a FluentCRM Pro"

#: fluent-crm.php:50
msgid "Upgrade to Pro"
msgstr "Aggiorna a Pro"

#: app/Services/TransStrings.php:1397
msgid "Upload"
msgstr "Carica"

#: app/Services/TransStrings.php:1398
msgid "Upload CSV"
msgstr "Carica CSV"

#: app/Services/TransStrings.php:1399
msgid "Upload Your CSV file"
msgstr "Carica il tuo file CSV"

#: app/Services/TransStrings.php:1371
msgid "URL"
msgstr "URL"

#: app/Services/TransStrings.php:1372
msgid "URL after redirect"
msgstr "URL dopo il reindirizzamento"

#: app/Hooks/Handlers/CountryNames.php:969
msgid "Uruguay"
msgstr "Uruguay"

#: app/Services/TransStrings.php:1090
msgid ""
"Use Basic Authorization for REST API requests. Please check the official "
"documentation for more details"
msgstr ""
"Usa l'autenticazione di base per le richieste REST API. Controlla la "
"documentazione ufficiale per maggiori dettagli"

#: app/Services/Funnel/Actions/SendEmailAction.php:76
msgid "Use comma separated values for multiple"
msgstr "Usa valori separati da virgole per multipli"

#: app/Services/TransStrings.php:1400
msgid "Use Custom Email Footer"
msgstr "Usa piè di pagina email personalizzato"

#: app/Services/TransStrings.php:1401
msgid "Use Email Template"
msgstr "Usa modello email"

#: app/Services/TransStrings.php:1402
msgid "Use Global Email Footer"
msgstr "Usa piè di pagina email globale"

#: app/Services/TransStrings.php:1493
msgid "Use the blocks to create separate path for your segmented contacts"
msgstr ""
"Usa i blocchi per creare percorsi separati per i tuoi contatti segmentati"

#: app/Services/TransStrings.php:256
msgid ""
"Use the following shortcode in a page/post to show the list of past campaigns"
msgstr ""
"Usa il seguente shortcode in una pagina/post per mostrare l'elenco delle "
"campagne passate"

#: app/Services/TransStrings.php:1667
msgid ""
"Use the Pre-Defined Dynamic Segment which will give your WordPress user "
"lists or Ecommerce Customers or even Affiliates."
msgstr ""
"Usa il Segmento Dinamico Predefinito che fornirà elenchi di utenti WordPress,"
" Clienti Ecommerce o anche Affiliati."

#: app/Services/TransStrings.php:1551
msgid ""
"Use this tool only and only if you understand fully. This will delete all "
"your CRM specific data from the system"
msgstr ""
"Usa questo strumento solo se ne comprendi appieno l'uso. Questo eliminerà "
"tutti i dati specifici del CRM dal sistema"

#: app/Services/TransStrings.php:1403
msgid "Use to differentiate ads"
msgstr "Usa per differenziare gli annunci"

#: app/Services/TransStrings.php:1404
msgid "Use your dynamic segment to broadcast Email Campaigns"
msgstr "Usa il tuo segmento dinamico per trasmettere Campagne Email"

#: app/Services/TransStrings.php:515
msgid "Use your dynamic segments to broadcast Email Campaigns"
msgstr "Usa i tuoi segmenti dinamici per trasmettere Campagne Email"

#: app/Services/TransStrings.php:1412
msgid "User data from database"
msgstr "Dati utente dal database"

#: app/Services/TransStrings.php:1407
msgid "User Email"
msgstr "Email Utente"

#: app/Services/TransStrings.php:1408
msgid "User Email Address"
msgstr "Indirizzo Email Utente"

#: app/Services/TransStrings.php:1409 app/Services/Helper.php:160
msgid "User ID"
msgstr "ID Utente"

#: app/Services/TransStrings.php:1410
#, fuzzy
#| msgid "User Role"
msgid "User Roles:"
msgstr "Ruoli Utente:"

#: app/Services/TransStrings.php:1411
msgid "User Signup On Form"
msgstr "Registrazione Utente sul Form"

#: app/Services/AutoSubscribe.php:28
msgid "User Signup Optin Settings"
msgstr "Impostazioni di Optin per Registrazione Utente"

#: app/Hooks/Handlers/CountryNames.php:973
msgid "Uzbekistan"
msgstr "Uzbekistan"

#: app/Http/Controllers/SettingsController.php:245
msgid "Valid"
msgstr "Valido"

#: app/Hooks/Handlers/ExternalPages.php:792
msgid "Validation failed."
msgstr "Validazione fallita."

#: app/Services/TransStrings.php:1413
msgid "Value"
msgstr "Valore"

#: app/Http/Controllers/CompanyController.php:294
#: app/Http/Controllers/CompanyController.php:298
#: app/Http/Controllers/SubscriberController.php:182
#: app/Http/Controllers/SubscriberController.php:186
msgid "Value is not valid"
msgstr "Il valore non è valido"

#: app/Hooks/Handlers/CountryNames.php:977
msgid "Vanuatu"
msgstr "Vanuatu"

#: app/Hooks/Handlers/CountryNames.php:981
msgid "Vatican"
msgstr "Vaticano"

#: app/Hooks/Handlers/CountryNames.php:985
msgid "Venezuela"
msgstr "Venezuela"

#: app/Services/TransStrings.php:1414
msgid "Verified Email Senders"
msgstr "Mittenti Email Verificati"

#: app/Services/TransStrings.php:1415
msgid "Verify License"
msgstr "Verifica Licenza"

#: app/Hooks/Handlers/ExternalPages.php:89
msgid "verify_key verification failed"
msgstr "Verifica della chiave di verifica fallita"

#: app/Services/Stats.php:115
msgid "Video Tutorials (Free)"
msgstr "Video Tutorial (Gratuiti)"

#: app/Hooks/Handlers/CountryNames.php:989
msgid "Vietnam"
msgstr "Vietnam"

#: app/Services/TransStrings.php:1425
#: app/Http/Controllers/CampaignAnalyticsController.php:159
msgid "View"
msgstr "Visualizza"

#: app/Services/TransStrings.php:1426
msgid "View All"
msgstr "Visualizza Tutto"

#: app/Services/TransStrings.php:1427
#, fuzzy
#| msgid "Send Campaign Email"
msgid "View Campaign Emails"
msgstr "Visualizza Email della Campagna"

#: app/Services/TransStrings.php:1428
msgid "View Company"
msgstr "Visualizza Azienda"

#: app/Services/Stats.php:75 app/Services/TransStrings.php:1429
msgid "View Contacts"
msgstr "Visualizza Contatti"

#: app/Services/TransStrings.php:438
msgid "View Email Sending Service Settings"
msgstr "Visualizza Impostazioni Servizio Invio Email"

#: app/Services/TransStrings.php:1430
msgid "View Emails"
msgstr "Visualizza Email"

#: fluent-crm.php:44
#, fuzzy
#| msgid "View FluentCRM documentation"
msgid "View FluentCRM Documentation"
msgstr "Visualizza la documentazione di FluentCRM"

#: app/Services/TransStrings.php:1431
msgid "View Full Profile"
msgstr "Visualizza Profilo Completo"

#: app/Services/TransStrings.php:1432
msgid "View GridPane Article"
msgstr "Visualizza Articolo GridPane"

#: app/Services/Helper.php:208
msgid "View On Browser URL"
msgstr "Visualizza URL nel Browser"

#: app/Hooks/Handlers/PurchaseHistory.php:109
#: app/Hooks/Handlers/PurchaseHistory.php:275
#: app/Hooks/Handlers/PurchaseHistory.php:308
msgid "View Order"
msgstr "Visualizza Ordine"

#: app/Http/Controllers/CampaignAnalyticsController.php:128
msgid "View Order Details"
msgstr "Visualizza Dettagli Ordine"

#: app/Services/TransStrings.php:1433
msgid "View Report"
msgstr "Visualizza Report"

#: app/Services/TransStrings.php:1434
msgid "View Reports"
msgstr "Visualizza Report"

#: app/Services/TransStrings.php:1435
msgid "View Sequence"
msgstr "Visualizza Sequenza"

#: app/Services/TransStrings.php:1436
#, fuzzy
#| msgid "Set Sequence Emails"
msgid "View Sequence Emails"
msgstr "Visualizza Email della Sequenza"

#: app/Services/TransStrings.php:1437
msgid "View Settings"
msgstr "Visualizza Impostazioni"

#: app/Services/TransStrings.php:1438
msgid "View Subscribers"
msgstr "Visualizza Iscritti"

#: app/Hooks/Handlers/CountryNames.php:993
msgid "Virgin Islands (British)"
msgstr "Isole Vergini (Britanniche)"

#: app/Hooks/Handlers/CountryNames.php:997
msgid "Virgin Islands (US)"
msgstr "Isole Vergini (US)"

#: app/Services/Helper.php:340
msgid "Visual Builder"
msgstr "Builder Visivo"

#: app/Services/TransStrings.php:1439
msgid ""
"Visual Email Builder is a Pro feature. Please upgrade to pro to use this "
"feature"
msgstr ""
"Il Builder Email Visivo è una funzione Pro. Effettua l'upgrade a Pro per "
"utilizzarla"

#: app/Services/TransStrings.php:1440
msgid "Visually design your email with Drag & Drop Builder"
msgstr "Progetta visivamente la tua email con il Drag & Drop Builder"

#: app/Services/Helper.php:520
msgid "Vivid cyan blue"
msgstr "Blu ciano vivido"

#: app/Services/Helper.php:510
msgid "Vivid green cyan"
msgstr "Verde ciano vivido"

#: app/Services/Helper.php:525
msgid "Vivid purple"
msgstr "Viola vivido"

#: app/Services/TransStrings.php:1444
msgid "Wait "
msgstr "Attendi"

#: app/Services/Funnel/Actions/WaitTimeAction.php:93
msgid "Wait by Custom Field"
msgstr "Attendi per Campo Personalizzato"

#: app/Services/Funnel/Actions/WaitTimeAction.php:81
msgid "Wait by period"
msgstr "Attendi per periodo"

#: app/Services/Funnel/Actions/WaitTimeAction.php:89
msgid "Wait by Weekday"
msgstr "Attendi per Giorno della Settimana"

#: app/Services/Funnel/Actions/WaitTimeAction.php:24
#: app/Services/Funnel/Actions/WaitTimeAction.php:73
msgid "Wait defined timespan before execute the next action"
msgstr "Attendi il tempo definito prima di eseguire la prossima azione"

#: app/Services/Funnel/Actions/WaitTimeAction.php:98
msgid "Wait Time"
msgstr "Tempo di Attesa"

#: app/Services/Funnel/Actions/WaitTimeAction.php:108
msgid "Wait Time Unit"
msgstr "Unità di Tempo di Attesa"

#: app/Services/TransStrings.php:1445
msgid "Wait until "
msgstr "Attendi fino a "

#: app/Services/Funnel/Actions/WaitTimeAction.php:85
msgid "Wait Until Date"
msgstr "Attendi fino alla Data"

#: app/Services/TransStrings.php:1446
msgid "Wait until next "
msgstr "Attendi fino al prossimo "

#: app/Services/Funnel/Actions/WaitTimeAction.php:23
#: app/Services/Funnel/Actions/WaitTimeAction.php:72
msgid "Wait X Days/Hours"
msgstr "Attendi X Giorni/Ore"

#: app/Services/TransStrings.php:688
msgid "Waiting for double opt-in confirmation"
msgstr "In attesa della conferma del double opt-in"

#: app/Hooks/Handlers/CountryNames.php:1001
msgid "Wallis and Futuna"
msgstr "Wallis e Futuna"

#: app/Services/TransStrings.php:1447
msgid "Want to deactivate this license?"
msgstr "Vuoi disattivare questa licenza?"

#: app/Services/TransStrings.php:1448
#, fuzzy
#| msgid "Earnings"
msgid "Warning"
msgstr "Avviso"

#: app/Services/TransStrings.php:1241
msgid "We will send marketing tips and advanced usage of FluentCRM (Monthly)"
msgstr ""
"Invieremo consigli di marketing e sull'uso avanzato di FluentCRM "
"(Mensilmente)"

#: app/Hooks/Handlers/ExternalPages.php:322
msgid "We're sorry to see you go!"
msgstr "Ci dispiace vederti andare via!"

#: app/Hooks/Handlers/ExternalPages.php:476
msgid ""
"We've sent an email to your inbox that contains a link to email management "
"from. Please check your email address to get the link."
msgstr ""
"Abbiamo inviato un'email alla tua casella di posta contenente un link per la "
"gestione delle email. Controlla il tuo indirizzo email per ottenere il link."

#: app/Hooks/Handlers/ExternalPages.php:427
msgid ""
"We've sent an email to your inbox that contains a link to unsubscribe from "
"our mailing list. Please check your email address and unsubscribe."
msgstr ""
"Abbiamo inviato un'email alla tua casella di posta con un link per annullare "
"l'iscrizione alla nostra lista. Controlla il tuo indirizzo email e annulla "
"l'iscrizione."

#: app/Services/TransStrings.php:1451
msgid "Webhook Info"
msgstr "Informazioni Webhook"

#: app/Hooks/Handlers/ExternalPages.php:748
msgid "Webhook must need to be as POST Method"
msgstr "Il Webhook deve essere configurato come Metodo POST"

#: app/Services/TransStrings.php:1452
msgid "Webhook Settings"
msgstr "Impostazioni Webhook"

#: app/Services/TransStrings.php:1453
msgid "Website"
msgstr "Sito Web"

#: app/Models/Company.php:70 app/Services/TransStrings.php:1671
msgid "Website URL"
msgstr "URL del Sito Web"

#: app/Services/TransStrings.php:1454
msgid "Wednesday"
msgstr "Mercoledì"

#: app/Services/TransStrings.php:1455
msgid "Weekly"
msgstr "Settimanale"

#: app/Services/TransStrings.php:1456
msgid "Weeks"
msgstr "Settimane"

#: app/Services/TransStrings.php:1457
msgid "Welcome"
msgstr "Benvenuto"

#: app/Services/TransStrings.php:1458
msgid "Welcome to FluentCRM!"
msgstr "Benvenuto su FluentCRM!"

#: app/Hooks/Handlers/CountryNames.php:1005
msgid "Western Sahara"
msgstr "Sahara Occidentale"

#: app/Services/TransStrings.php:1673
msgid "what we collect"
msgstr "cosa raccogliamo"

#: app/Services/TransStrings.php:1459
msgid "When you send the emails?"
msgstr "Quando invii le email?"

#: app/Services/TransStrings.php:1460
msgid "Which time you would like to schedule"
msgstr "A che ora vorresti programmare"

#: app/Services/Helper.php:485
msgid "White"
msgstr "Bianco"

#: app/Services/TransStrings.php:1461
msgid "Will be applied to all the imported contacts"
msgstr "Verrà applicato a tutti i contatti importati"

#: app/Services/TransStrings.php:724
msgid "Will be applied to the contacts who does not have any mapped tags in"
msgstr "Sarà applicato ai contatti che non hanno tag mappati in"

#: app/Services/TransStrings.php:1675
msgid "will be created automatically in FluentCRM"
msgstr "sarà creato automaticamente in FluentCRM"

#: app/Services/TransStrings.php:1676
msgid "will be deleted"
msgstr "sarà eliminato"

#: app/Services/TransStrings.php:1677
msgid "will be replaced with dynamic values."
msgstr "verrà sostituito con valori dinamici."

#: app/Services/TransStrings.php:1601
msgid ""
"will be shown here, Currently no Form Submissions found for this subscriber"
msgstr ""
"sarà mostrato qui, Attualmente nessuna Sottomissione di Moduli trovata per "
"questo iscritto"

#: app/Services/TransStrings.php:1024
msgid ""
"will be shown here, Currently no purchase history found for this contact"
msgstr ""
"sarà mostrato qui, Attualmente nessuna cronologia di acquisti trovata per "
"questo contatto"

#: app/Services/TransStrings.php:1603
msgid "will be shown here, Currently no tickets found for this subscriber"
msgstr ""
"sarà mostrato qui, Attualmente nessun ticket trovato per questo iscritto"

#: app/Services/TransStrings.php:1462
msgid "Will be stored from your last saved email contents"
msgstr "Sarà memorizzato dai contenuti della tua ultima email salvata"

#: app/Http/Controllers/ImporterController.php:270
#, fuzzy
#| msgid "Wishlist Member"
msgid "Wishlist member"
msgstr "Membro Wishlist"

#: app/Services/TransStrings.php:1463
msgid "Wishlist Members."
msgstr "Membri di Wishlist."

#: app/Services/TransStrings.php:1678
msgid "with API key"
msgstr "con chiave API"

#: app/Services/TransStrings.php:321
msgid ""
"With company module, you can manage Companies/Businesses and assign contacts "
"to companies."
msgstr ""
"Con il modulo aziendale, puoi gestire Aziende/Attività e assegnare contatti "
"alle aziende."

#: app/Services/TransStrings.php:1679
msgid ""
"With FluentCRM Pro, you can integrate with other plugins like E-commerce, "
"Membership, LMS (25+ integrations) along with advanced reports, sequence "
"(drip) emails, advanced automation and unlock lots of useful features."
msgstr ""
"Con FluentCRM Pro, puoi integrarti con altri plugin come E-commerce, "
"Membership, LMS (oltre 25 integrazioni), insieme a report avanzati, email a "
"sequenza (drip), automazione avanzata e sbloccare molte funzioni utili."

#: app/Services/TransStrings.php:1680
msgid "within days"
msgstr "entro giorni"

#: app/Services/Helper.php:447 app/Services/Helper.php:1204
msgid "WooCommerce"
msgstr "WooCommerce"

#: app/Services/AutoSubscribe.php:257
msgid "Woocommerce Checkout Subscription Field"
msgstr "Campo di Abbonamento Checkout WooCommerce"

#: app/Services/TransStrings.php:1464
msgid "WooCommerce Integration:"
msgstr "Integrazione WooCommerce:"

#: app/Services/Helper.php:446
msgid "Woocommerce Purchase History"
msgstr "Cronologia Acquisti WooCommerce"

#: app/Services/TransStrings.php:1465
msgid "WordPress Default"
msgstr "Predefinito di WordPress"

#: app/Http/Controllers/DocsController.php:86
msgid ""
"WordPress Helpdesk and Customer Support Ticket Plugin. Provide awesome "
"support and manage customer queries right from your WordPress dashboard."
msgstr ""
"Plugin per assistenza clienti e gestione ticket di supporto di WordPress. "
"Fornisce supporto eccezionale e gestisci le richieste dei clienti "
"direttamente dal tuo dashboard WordPress."

#: app/Services/Funnel/Triggers/UserRegistrationTrigger.php:22
msgid "WordPress Triggers"
msgstr "Trigger WordPress"

#: app/Services/TransStrings.php:1466
#: app/Http/Controllers/ImporterController.php:30
msgid "WordPress Users"
msgstr "Utenti WordPress"

#: app/Services/TransStrings.php:1467
msgid "Working"
msgstr "In esecuzione"

#. Author of the plugin
msgid "WP Email Newsletter Team - FluentCRM"
msgstr "WP Email Newsletter Team - FluentCRM"

#: app/Services/Helper.php:951
msgid "WP User ID"
msgstr "ID Utente WP"

#: app/Services/Helper.php:1029
msgid "WP User Role"
msgstr "Ruolo Utente WP"

#: app/Services/RoleBasedTagging.php:45
msgid "WP User Role Based Tag Mapping"
msgstr "Mappatura Tag Basata sul Ruolo Utente WP"

#: app/Services/TransStrings.php:1441
msgid "WP Users"
msgstr "Utenti WP"

#: app/Services/TransStrings.php:1442
msgid "WPFusion Integration"
msgstr "Integrazione WPFusion"

#: app/Services/TransStrings.php:1468
msgid "Year"
msgstr "Anno"

#: app/Services/TransStrings.php:1469
msgid "Year to Date"
msgstr "Anno fino ad oggi"

#: app/Hooks/Handlers/CountryNames.php:1013
msgid "Yemen"
msgstr "Yemen"

#: app/Services/TransStrings.php:1470 app/Services/Helper.php:1307
msgid "Yes"
msgstr "Sì"

#: app/Services/TransStrings.php:1471
msgid "Yes, Count me in!"
msgstr "Sì, Contami!"

#: app/Services/TransStrings.php:1472
msgid "Yes, Delete and Reset All CRM Data"
msgstr "Sì, Elimina e Reimposta Tutti i Dati CRM"

#: app/Services/TransStrings.php:1473
msgid "Yes, I want to delete Old Logs"
msgstr "Sì, voglio eliminare i Vecchi Log"

#: app/Http/Controllers/FormsController.php:186
msgid "You are successfully subscribed to our email list"
msgstr "Ti sei iscritto con successo alla nostra lista email"

#: app/Hooks/Handlers/ExternalPages.php:288
#: app/Hooks/Handlers/ExternalPages.php:578
msgid "You are successfully unsubscribed from the email list"
msgstr "Ti sei disiscritto con successo dalla lista email"

#: app/Services/TransStrings.php:1443
msgid "You can also use WP CLI to sync the data if you have lots of customers."
msgstr ""
"Puoi anche utilizzare WP CLI per sincronizzare i dati se hai molti clienti."

#: app/Services/TransStrings.php:1539
msgid ""
"You can create as many dynamic segment as you want and send email campaign "
"only a selected segment. It will let you target specific audiences and "
"increase your conversion rate."
msgstr ""
"Puoi creare quanti segmenti dinamici desideri e inviare campagne email solo "
"a un segmento selezionato. Ti permetterà di raggiungere pubblici specifici e "
"aumentare il tasso di conversione."

#: app/Services/TransStrings.php:434
msgid ""
"You can customize your footer in this section. If no custom footer being "
"added here, global footer will be added to this email."
msgstr ""
"Puoi personalizzare il piè di pagina in questa sezione. Se non viene "
"aggiunto un piè di pagina personalizzato qui, verrà utilizzato il piè di "
"pagina globale per questa email."

#: app/Services/CrmMigrator/ActiveCampaignMigrator.php:41
msgid "You can find Account ID Settings -> Developer -> API Access"
msgstr ""
"Puoi trovare l'ID account in Impostazioni -> Sviluppatore -> Accesso API"

#: app/Services/CrmMigrator/DripMigrator.php:40
msgid "You can find Account ID Settings -> General Info -> Account ID"
msgstr ""
"Puoi trovare l'ID account in Impostazioni -> Informazioni generali -> ID "
"account"

#: app/Services/CrmMigrator/ActiveCampaignMigrator.php:34
msgid "You can find your API key at ActiveCampaign Settings -> Developer"
msgstr ""
"Puoi trovare la tua chiave API nelle Impostazioni di ActiveCampaign -> "
"Sviluppatore"

#: app/Services/CrmMigrator/ConvertKitMigrator.php:34
#, fuzzy
#| msgid "You can find your API key at ConvertKit"
msgid "You can find your API key at ConvertKit "
msgstr "Puoi trovare la tua chiave API in ConvertKit"

#: app/Services/CrmMigrator/DripMigrator.php:33
msgid "You can find your API key at Drip Profile -> User Info -> API Token"
msgstr ""
"Puoi trovare la tua chiave API nel Profilo Drip -> Informazioni utente -> "
"Token API"

#: app/Services/CrmMigrator/MailChimpMigrator.php:36
msgid "You can find your API key at MailChimp Account -> Extras -> API keys"
msgstr ""
"Puoi trovare la tua chiave API nel tuo Account MailChimp -> Extra -> Chiavi "
"API"

#: app/Services/CrmMigrator/MailerLiteMigrator.php:33
msgid "You can find your API key at MailerLite"
msgstr "Puoi trovare la tua chiave API in MailerLite"

#: app/Services/CrmMigrator/ConvertKitMigrator.php:41
msgid ""
"You can find your API Secret key at ConvertKit Account -> Settings -> "
"Advanced"
msgstr ""
"Puoi trovare la tua chiave API segreta in ConvertKit Account -> Impostazioni "
"-> Avanzate"

#: app/Services/TransStrings.php:1579
msgid ""
"You can import your exported automation JSON file here. Please upload your "
"JSON file to get started"
msgstr ""
"Puoi importare il tuo file JSON di automazione esportato qui. Carica il tuo "
"file JSON per iniziare"

#: app/Services/TransStrings.php:594
msgid ""
"You can import your exported email sequence JSON file here. Please upload "
"your JSON file"
msgstr ""
"Puoi importare qui il tuo file JSON di sequenza email esportato. Carica il "
"tuo file JSON"

#: app/Services/TransStrings.php:431
msgid "You can not change the slug once save a custom field"
msgstr "Non puoi cambiare lo slug una volta salvato un campo personalizzato"

#: app/Services/TransStrings.php:1474
msgid "You can not reverse this action."
msgstr "Non puoi annullare questa azione."

#: app/Http/Controllers/CampaignController.php:1094
msgid ""
"You can only pause a campaign if it is on \"Working\" state, Please reload "
"this page"
msgstr ""
"Puoi mettere in pausa una campagna solo se è nello stato \"In corso\", "
"Ricarica questa pagina"

#: app/Http/Controllers/CampaignController.php:1121
msgid ""
"You can only resume a campaign if it is on \"paused\" state, Please reload "
"this page"
msgstr ""
"Puoi riprendere una campagna solo se è in stato di \"Pausa\", Ricarica "
"questa pagina"

#: app/Http/Controllers/CampaignController.php:1216
#: app/Http/Controllers/CampaignController.php:1222
msgid ""
"You can only un-schedule a campaign if it is on \"scheduled\" state, Please "
"reload this page"
msgstr ""
"Puoi annullare la programmazione di una campagna solo se è in stato di "
"\"Programmato\", Ricarica questa pagina"

#: app/Services/TransStrings.php:1633
msgid ""
"You can setup your lists to segment your contacts. For Example VIP Customers,"
" Product Users, WordPress Users etc"
msgstr ""
"Puoi impostare le tue liste per segmentare i tuoi contatti. Ad esempio "
"Clienti VIP, Utenti di prodotti, Utenti WordPress ecc."

#: app/Services/TransStrings.php:25
msgid ""
"You can split test your Email Subject Line and Test which Subject Lines got "
"more open and click rate. This is a pro feature"
msgstr ""
"Puoi fare uno split test della tua linea dell'oggetto email e verificare "
"quali oggetti hanno ottenuto più aperture e clic. Questa è una funzionalità "
"pro"

#: app/Services/TransStrings.php:1242
msgid ""
"You can subscribe to our bi-monthly newsletter where we will email you all "
"about FluentCRM."
msgstr ""
"Puoi iscriverti alla nostra newsletter bimestrale, dove ti informeremo su "
"tutto ciò che riguarda FluentCRM."

#: app/Services/TransStrings.php:179
msgid "You can use fallback value using | separator. Example:"
msgstr "Puoi usare un valore di fallback utilizzando il separatore |. Esempio:"

#: app/Services/TransStrings.php:1475
msgid "You cannot upload more than one file."
msgstr "Non puoi caricare più di un file."

#: app/Services/TransStrings.php:1476
msgid "You have to add"
msgstr "Devi aggiungere"

#: app/Services/TransStrings.php:1477
msgid "You license key is valid and activated"
msgstr "La tua chiave di licenza è valida e attivata"

#: app/Services/TransStrings.php:1544
msgid ""
"You may also use these custom contact fields. Copy the keys in the right "
"column and paste it into the app just like other contact fields."
msgstr ""
"Puoi anche utilizzare questi campi di contatto personalizzati. Copia le "
"chiavi nella colonna di destra e incollale nell'app come altri campi di "
"contatto."

#: app/Services/TransStrings.php:1363
msgid ""
"You may change the trigger for this automation. Once you change, Please "
"reconfigure the trigger settings"
msgstr ""
"Puoi modificare il trigger per questa automazione. Una volta modificato, "
"riconfigura le impostazioni del trigger."

#: app/Services/TransStrings.php:1528
msgid ""
"You may cleanup old data like email logs, email click and open logs easily "
"using this tool."
msgstr ""
"Puoi pulire vecchi dati come log delle email, log di clic e aperture email "
"facilmente con questo strumento."

#: app/Services/TransStrings.php:1478
msgid "You need pro version to use this feature"
msgstr "Hai bisogno della versione pro per utilizzare questa funzione"

#: app/Services/TransStrings.php:164
msgid "You need to have FluentCRM Pro to use this feature"
msgstr "Devi avere FluentCRM Pro per utilizzare questa funzione"

#: app/Services/TransStrings.php:1479
msgid "You should provide your business address"
msgstr "Devi fornire il tuo indirizzo aziendale"

#: app/Services/TransStrings.php:1480
msgid "Your Browser does not support JS copy. Please copy manually"
msgstr "Il tuo browser non supporta la copia JS. Copia manualmente, per favore"

#: app/Services/TransStrings.php:1523
msgid ""
"Your Business Address will be used in Emails. The address is required as per "
"anti-spam rules"
msgstr ""
"L'indirizzo della tua azienda sarà utilizzato nelle email. L'indirizzo è "
"richiesto in conformità con le norme anti-spam."

#: app/Services/TransStrings.php:1524
msgid "Your Business Logo, It will be used in Public Facing pages"
msgstr "Il logo della tua azienda verrà utilizzato nelle pagine pubbliche."

#: app/Services/TransStrings.php:1525
msgid ""
"Your Business name will be used in Emails, Unsubscribe Pages and public "
"front interfaces"
msgstr ""
"Il nome della tua azienda sarà utilizzato nelle email, nelle pagine di "
"disiscrizione e nelle interfacce pubbliche."

#: app/Services/TransStrings.php:1486
#: app/Http/Controllers/CampaignController.php:517
msgid "Your campaign email has been scheduled"
msgstr "La tua email della campagna è stata programmata"

#: app/Http/Controllers/SettingsController.php:116
msgid "Your double-optin email pre header"
msgstr "L'intestazione di anteprima della tua email di doppia conferma"

#: app/Http/Controllers/SettingsController.php:110
msgid "Your double-optin email subject"
msgstr "L'oggetto della tua email di doppia conferma"

#: app/Services/TransStrings.php:1481 app/Hooks/Handlers/ExternalPages.php:323
#: app/views/external/manage_subscription_form.php:9
#: app/views/external/manage_subscription_request_form.php:38
#: app/views/external/unsubscribe_request_form.php:38
msgid "Your Email Address"
msgstr "Il tuo indirizzo email"

#: app/Hooks/Handlers/ExternalPages.php:463
msgid "Your Email preferences URL"
msgstr "URL delle tue preferenze email"

#: app/Services/TransStrings.php:1424
msgid "Your emails are sending right now...."
msgstr "Le tue email sono in fase di invio..."

#: app/Services/ExternalIntegrations/FluentForm/Bootstrap.php:119
msgid "Your Feed Name"
msgstr "Nome del tuo feed"

#: app/Services/TransStrings.php:1501
msgid "Your form has been created successfully"
msgstr "Il tuo modulo è stato creato con successo"

#: app/Hooks/Handlers/PrefFormHandler.php:285
msgid "Your information has been updated"
msgstr "Le tue informazioni sono state aggiornate"

#: app/Services/TransStrings.php:1482
msgid "Your License Key"
msgstr "La tua chiave di licenza"

#: app/Services/TransStrings.php:1487
msgid "Your link title so you do not forget it"
msgstr "Il titolo del tuo link per non dimenticarlo"

#: app/Services/TransStrings.php:1483 app/Services/Helper.php:1876
msgid "Your Note Title"
msgstr "Titolo della tua nota"

#: app/Http/Controllers/MigratorController.php:55
msgid "Your provided API key is valid"
msgstr "La tua chiave API fornita è valida"

#: app/Hooks/Handlers/ExternalPages.php:1074
msgid "Your provided information has been successfully updated"
msgstr "Le informazioni fornite sono state aggiornate con successo"

#: app/Services/TransStrings.php:545
msgid "Your provided priority will be converted to percent and it's relative"
msgstr "La priorità fornita verrà convertita in percentuale ed è relativa"

#: app/Services/TransStrings.php:447
msgid "Your selected date and time format will be shown on different views."
msgstr ""
"Il formato di data e ora selezionato verrà mostrato in diverse "
"visualizzazioni."

#: app/Services/TransStrings.php:1484
msgid "Your Selected logs will be deleted permanently"
msgstr "I log selezionati verranno eliminati in modo permanente"

#: app/Services/TransStrings.php:1488
#, fuzzy
#| msgid "Your Target URL"
msgid "Your target URL"
msgstr "URL di destinazione"

#: app/Services/TransStrings.php:1485
msgid "Your URL Note"
msgstr "Nota sull'URL"

#: app/Services/TransStrings.php:1268
msgid "Your WordPress & FluentCRM is set to send using FluentSMTP Plugin"
msgstr ""
"Il tuo WordPress e FluentCRM sono impostati per inviare tramite il plugin "
"FluentSMTP"

#: app/Hooks/Handlers/CountryNames.php:1017
msgid "Zambia"
msgstr "Zambia"

#: app/Hooks/Handlers/CountryNames.php:1021
msgid "Zimbabwe"
msgstr "Zimbabwe"

#: app/Services/TransStrings.php:1489 app/Hooks/Handlers/PrefFormHandler.php:52
msgid "ZIP Code"
msgstr "CAP"

#: app/Services/TransStrings.php:1490
#: app/Hooks/Handlers/PrefFormHandler.php:477
msgid "Zip Code"
msgstr "CAP"

#: app/Hooks/Handlers/CountryNames.php:29
msgid "Åland Islands"
msgstr "Isole Åland"
