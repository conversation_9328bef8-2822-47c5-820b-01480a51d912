msgid ""
msgstr ""
"Project-Id-Version: FluentCRM Pro\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-19 13:57+0000\n"
"PO-Revision-Date: 2024-10-14 10:00+0000\n"
"Last-Translator: \n"
"Language-Team: Italiano\n"
"Language: it-IT\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Loco https://localise.biz/\n"
"X-Loco-Version: 2.6.11; wp-6.6.2\n"
"X-Domain: fluentcampaign-pro"

#: app/Http/Controllers/RecurringCampaignController.php:120
msgid ""
"##crm.manage_subscription_url## or ##crm.unsubscribe_url## string is "
"required for compliance. Please include unsubscription or manage "
"subscription link"
msgstr ""
"È necessario includere il collegamento ##crm.manage_subscription_url## o "
"##crm.unsubscribe_url## per la conformità. Si prega di includere il "
"collegamento per annullare l'iscrizione o gestire l'abbonamento."

#: app/Http/Controllers/CampaignsProController.php:37
msgid "%d Emails has been scheduled to resend"
msgstr "Sono state programmate %d email da inviare nuovamente"

#: app/Services/PostParser/views/latest-post/layout-6.php:30
#: app/Services/PostParser/views/latest-post/layout-7.php:32
#: app/Services/PostParser/views/latest-post/layout-5.php:20
#: app/Services/PostParser/views/latest-post/layout-4.php:26
#: app/Services/PostParser/views/latest-post/layout-3.php:48
#: app/Services/PostParser/views/latest-post/layout-2.php:52
#: app/Services/PostParser/views/latest-post/default.php:31
msgid "(no title)"
msgstr "(senza titolo)"

#: app/Services/PostParser/views/latest-post/layout-6.php:47
#: app/Services/PostParser/views/latest-post/layout-6.php:50
#: app/Services/PostParser/views/latest-post/layout-4.php:57
#: app/Services/PostParser/views/latest-post/layout-3.php:34
#: app/Services/PostParser/views/latest-post/layout-2.php:38
msgid "-"
msgstr "-"

#: app/Http/Controllers/DynamicSegmentController.php:315
#: app/Http/Controllers/SequenceController.php:97
#: app/Http/Controllers/RecurringCampaignController.php:208
#: app/Http/Controllers/SequenceMailController.php:93
msgid "[Duplicate] "
msgstr "[Duplicato] "

#: app/Services/Integrations/Edd/EddSubscriptionActiveBenchmark.php:24
msgid "[EDD] Recurring Subscription Active"
msgstr "[EDD] Abbonamento ricorrente attivo"

#: app/Services/Integrations/LearnDash/LdInit.php:161
msgid "[FluentCRM] Apply Tags on course completion"
msgstr "[FluentCRM] Applica tag al completamento del corso"

#: app/Services/Integrations/LearnDash/LdInit.php:150
msgid "[FluentCRM] Apply Tags on course enrollment"
msgstr "[FluentCRM] Applica tag all'iscrizione al corso"

#: app/Services/Integrations/LearnDash/LdInit.php:190
msgid "[FluentCRM] Apply Tags on group enrollment"
msgstr "[FluentCRM] Applica tag all'iscrizione al gruppo"

#: app/Services/Integrations/LearnDash/LdInit.php:201
msgid "[FluentCRM] Remove Tags on group leave"
msgstr "[FluentCRM] Rimuovi tag all'uscita dal gruppo"

#: app/Services/Integrations/RCP/RCPMembershipTrigger.php:40
#: app/Services/Integrations/WishlistMember/WishlistMembershipTrigger.php:40
#: app/Services/Integrations/MemberPress/MembershipTrigger.php:40
msgid "A member added to a membership level"
msgstr "Un membro è stato aggiunto a un livello di appartenenza"

#: app/Services/Integrations/MemberPress/SubscriptionExpiredTrigger.php:39
msgid "A Subscription expired"
msgstr "Un abbonamento è scaduto"

#: app/Modules/AbandonCart/AbandonCart.php:23
#: app/Modules/AbandonCart/AbandonCart.php:24
#: app/Modules/AbandonCart/AbandonCart.php:37
msgid "Abandoned Carts"
msgstr "Carrelli abbandonati"

#: app/Services/Integrations/Edd/EddCommerceHelper.php:192
msgid "Activated Sites"
msgstr "Siti attivati"

#: app/Services/Integrations/AffiliateWP/DeepIntegration.php:82
msgid "Active"
msgstr "Attivo"

#: app/Services/DynamicSegments/AffiliateWPSegment.php:20
msgid "Active Affiliates (AffiliateWP)"
msgstr "Affiliati attivi (AffiliateWP)"

#: app/Services/DynamicSegments/AffiliateWPSegment.php:21
msgid "Active Affiliates who are also in the contact list as subscribed"
msgstr "Affiliati attivi che sono anche nella lista contatti come iscritti"

#: app/Services/Integrations/Edd/AdvancedReport.php:159
msgid "Active Licenses"
msgstr "Licenze attive"

#: app/Services/Integrations/Edd/AutomationConditions.php:118
msgid "Active Subscription"
msgstr "Abbonamento attivo"

#: app/Http/Controllers/DynamicSegmentController.php:193
msgid ""
"Activity on your site login, email link click or various other activities"
msgstr "Attività sul sito come accesso, clic su link email o altre attività"

#: app/Services/Funnel/Actions/AddActivityAction.php:56
msgid "Activity Title"
msgstr "Titolo dell'attività"

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:76
#: app/Services/Funnel/Conditions/FunnelCondition.php:57
msgid "Add Condition to check your contact's properties"
msgstr "Aggiungi condizione per verificare le proprietà del contatto"

#: app/Services/Funnel/Triggers/TrackingEventRecordedTrigger.php:94
msgid "Add Condition to check your event tracking properties"
msgstr "Aggiungi condizione per verificare le proprietà di tracciamento eventi"

#: app/Services/Funnel/Actions/AddEventTrackerAction.php:27
msgid "Add Event Tracking"
msgstr "Aggiungi tracciamento eventi"

#: app/Services/Funnel/Actions/AddEventTrackerAction.php:28
msgid "Add Event Tracking for Contact"
msgstr "Aggiungi tracciamento eventi per contatto"

#: app/Services/Funnel/Actions/AddEventTrackerAction.php:42
msgid "Add Event Tracking to Contact Profile"
msgstr "Aggiungi tracciamento eventi al profilo del contatto"

#: app/Services/Integrations/WooCommerce/AddOrderNoteAction.php:22
#: app/Services/Integrations/WooCommerce/AddOrderNoteAction.php:48
msgid "Add Note to WooCommerce Order"
msgstr "Aggiungi nota all'ordine WooCommerce"

#: app/Services/Funnel/Actions/AddActivityAction.php:22
msgid "Add Notes & Activity"
msgstr "Aggiungi note e attività"

#: app/Services/Funnel/Actions/AddActivityAction.php:44
msgid "Add Notes or Activity to Contact"
msgstr "Aggiungi note o attività al contatto"

#: app/Services/Funnel/Actions/AddActivityAction.php:23
#: app/Services/Funnel/Actions/AddActivityAction.php:45
msgid "Add Notes or Activity to the Contact Profile"
msgstr "Aggiungi note o attività al profilo del contatto"

#: app/Services/Integrations/ElementorFormIntegration/FormWidget.php:90
msgid "Add Only"
msgstr "Aggiungi solo"

#: app/Services/Integrations/WooCommerce/AddOrderNoteAction.php:21
#: app/Services/Integrations/WooCommerce/AddOrderNoteAction.php:47
msgid "Add Order Note"
msgstr "Aggiungi nota all'ordine"

#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:113
#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:147
#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:184
#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:221
#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:258
msgid "Add Tags"
msgstr "Aggiungi tag"

#: app/Http/Controllers/CampaignsProController.php:29
msgid "Added to resend from failed"
msgstr "Aggiunto per il reinvio da fallito"

#: app/Hooks/Handlers/DataExporter.php:531
#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:155
#: app/Services/Funnel/Conditions/FunnelCondition.php:87
msgid "Address Line 1"
msgstr "Indirizzo, linea 1"

#: app/Hooks/Handlers/DataExporter.php:532
#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:160
#: app/Services/Funnel/Conditions/FunnelCondition.php:92
msgid "Address Line 2"
msgstr "Indirizzo, linea 2"

#: app/Services/Integrations/BuddyPress/BBInit.php:91
#: app/Services/Integrations/BuddyPress/BBInit.php:147
msgid "Admin"
msgstr "Amministratore"

#: app/Services/Funnel/Triggers/TrackingEventRecordedTrigger.php:84
msgid ""
"Advanced Event Tracking Conditions (Will check all tracking event for the "
"contact)"
msgstr ""
"Condizioni avanzate di tracciamento eventi (controllerà tutti gli eventi di "
"tracciamento per il contatto)"

#: app/Services/Integrations/AffiliateWP/DeepIntegration.php:67
msgid "Affiliate ID"
msgstr "ID affiliato"

#: app/Services/Integrations/AffiliateWP/DeepIntegration.php:223
msgid "Affiliates Sync"
msgstr "Sincronizzazione affiliati"

#: app/Services/Integrations/AffiliateWP/AffiliateWPAffActiveTrigger.php:47
#: app/Services/Integrations/AffiliateWP/DeepIntegration.php:53
#: app/Services/Integrations/AffiliateWP/DeepIntegration.php:174
msgid "AffiliateWP"
msgstr "AffiliateWP"

#: app/Services/Integrations/WooCommerce/WooImporter.php:157
#: app/Services/Integrations/Edd/EddImporter.php:151
msgid ""
"After this sync you can import by product by product and provide appropriate "
"tags"
msgstr ""
"Dopo questa sincronizzazione, puoi importare prodotto per prodotto e fornire "
"i tag appropriati"

#: app/Services/Integrations/LifterLms/AdvancedReport.php:40
#: app/Services/Integrations/WooCommerce/AdvancedReport.php:52
#: app/Services/Integrations/LearnDash/AdvancedReport.php:40
#: app/Services/Integrations/Edd/AdvancedReport.php:44
#: app/Services/Integrations/TutorLms/AdvancedReport.php:40
msgid "All"
msgstr "Tutti"

#: app/Services/Integrations/Edd/EddCommerceHelper.php:155
msgid "All licenses"
msgstr "Tutte le licenze"

#: app/Services/Integrations/RCP/RCPMembershipTrigger.php:52
#: app/Services/Integrations/RCP/RCPMembershipCancelTrigger.php:52
#: app/Services/Integrations/RCP/RCPMembershipExpireTrigger.php:52
#: app/Services/Integrations/LifterLms/LessonCompletedTrigger.php:56
#: app/Services/Integrations/LifterLms/CourseCompletedTrigger.php:52
#: app/Services/Integrations/LifterLms/CourseEnrollTrigger.php:56
#: app/Services/Integrations/LifterLms/MembershipEnrollTrigger.php:56
#: app/Services/Integrations/WooCommerce/WooSubscriptionStartedTrigger.php:83
#: app/Services/Integrations/WooCommerce/WooOrderCompletedTrigger.php:52
#: app/Services/Integrations/WooCommerce/WooOrderStatusChangeTrigger.php:52
#: app/Services/Integrations/WooCommerce/WooSubscriptionRenewalFailedTrigger.php:83
#: app/Services/Integrations/WooCommerce/WooOrderRefundedTrigger.php:52
#: app/Services/Integrations/WooCommerce/WooOrderSuccessTrigger.php:54
#: app/Services/Integrations/WooCommerce/WooSubscriptionExpiredTrigger.php:83
#: app/Services/Integrations/WooCommerce/WooSubscriptionRenewalPaymentTrigger.php:83
#: app/Services/Integrations/PMPro/PMProPMProMembershipTrigger.php:52
#: app/Services/Integrations/PMPro/PMProPMProExpiryLevelTrigger.php:52
#: app/Services/Integrations/PMPro/PMProPMProCancelLevelTrigger.php:52
#: app/Services/Integrations/SureCart/SureCartPaymentRefundTrigger.php:52
#: app/Services/Integrations/SureCart/SureCartPaymentSuccessTrigger.php:53
#: app/Services/Integrations/LearnDash/GroupEnrollTrigger.php:51
#: app/Services/Integrations/LearnDash/CourseLeaveTrigger.php:54
#: app/Services/Integrations/LearnDash/TopicCompletedTrigger.php:50
#: app/Services/Integrations/LearnDash/LessonCompletedTrigger.php:51
#: app/Services/Integrations/LearnDash/CourseCompletedTrigger.php:51
#: app/Services/Integrations/LearnDash/CourseEnrollTrigger.php:53
#: app/Services/Integrations/Edd/EddPaymentSuccessTrigger.php:52
#: app/Services/Integrations/Edd/EddLicenseExpiredTrigger.php:51
#: app/Services/Integrations/Edd/EddRecurringExpired.php:52
#: app/Services/Integrations/Edd/EddRecurringPaymentTrigger.php:52
#: app/Services/Integrations/Edd/EddPaymentRefundTrigger.php:52
#: app/Services/Integrations/WishlistMember/WishlistMembershipTrigger.php:52
#: app/Services/Integrations/TutorLms/LessonCompletedTrigger.php:52
#: app/Services/Integrations/TutorLms/CourseCompletedTrigger.php:52
#: app/Services/Integrations/TutorLms/CourseEnrollTrigger.php:52
#: app/Services/Integrations/MemberPress/MembershipTrigger.php:52
#: app/Services/Integrations/MemberPress/SubscriptionExpiredTrigger.php:51
#: app/Services/Integrations/AffiliateWP/AffiliateWPAffActiveTrigger.php:75
#: app/Services/Funnel/Triggers/UserLoginTrigger.php:46
msgid "An Automated double-optin email will be sent for new subscribers"
msgstr ""
"Verrà inviata un'email di doppio opt-in automatica per i nuovi iscritti"

#: app/Http/Controllers/RecurringCampaignController.php:58
#: app/Http/Controllers/RecurringCampaignController.php:144
msgid ""
"Another campaign with the same name already exist. Please provide a "
"different name"
msgstr ""
"Esiste già una campagna con lo stesso nome. Si prega di fornire un nome "
"diverso"

#: app/Services/Integrations/WooCommerce/WooOrderStatusChangeTrigger.php:79
msgid "Any"
msgstr "Qualsiasi"

#: app/Services/Integrations/WooCommerce/Helper.php:51
#: app/Services/Integrations/Edd/Helper.php:54
msgid "Any type of purchase"
msgstr "Qualsiasi tipo di acquisto"

#: app/Services/Integrations/WooCommerce/DeepIntegration.php:597
#: app/Services/Integrations/Edd/DeepIntegration.php:263
msgid "AOV"
msgstr "Valore medio ordine (AOV)"

#: app/Services/Integrations/BuddyPress/Group.php:39
#: app/Services/Integrations/BuddyPress/BBMemberType.php:58
#: app/Services/Integrations/Edd/EddMetaBoxes.php:49
#: app/Services/Integrations/Edd/EddMetaBoxes.php:103
msgid "Apply Tags"
msgstr "Applica tag"

#: app/Services/Integrations/LifterLms/LifterInit.php:375
msgid "Apply Tags on Course Completed"
msgstr "Applica tag al completamento del corso"

#: app/Services/Integrations/LearnPress/LearnPressInit.php:152
msgid "Apply Tags on Course Completion"
msgstr "Applica tag al completamento del corso"

#: app/Services/Integrations/LifterLms/LifterInit.php:183
#: app/Services/Integrations/TutorLms/TutorLmsInit.php:154
msgid "Apply Tags on course completion"
msgstr "Applica tag al completamento del corso"

#: app/Services/Integrations/LearnPress/LearnPressInit.php:131
msgid "Apply Tags on Course Enrollment"
msgstr "Applica tag all'iscrizione al corso"

#: app/Services/Integrations/LifterLms/LifterInit.php:170
#: app/Services/Integrations/TutorLms/TutorLmsInit.php:140
msgid "Apply Tags on course enrollment"
msgstr "Applica tag all'iscrizione al corso"

#: app/Services/Integrations/LearnPress/LearnPressInit.php:191
msgid "Apply Tags on Lesson Completion"
msgstr "Applica tag al completamento della lezione"

#: app/Services/Integrations/LifterLms/LifterInit.php:241
msgid "Apply Tags on Membership enrollment"
msgstr "Applica tag all'iscrizione all'abbonamento"

#: app/Services/Integrations/LearnPress/LearnPressInit.php:167
msgid "Apply these tags on course completion"
msgstr "Applica questi tag al completamento del corso"

#: app/Services/Integrations/LearnPress/LearnPressInit.php:147
msgid "Apply these tags on course enrollment"
msgstr "Applica questi tag all'iscrizione al corso"

#: app/Services/Integrations/Edd/EddMetaBoxes.php:115
msgid "Apply these tags when purchase this variation"
msgstr "Applica questi tag quando si acquista questa variazione"

#: app/Services/Integrations/Edd/EddMetaBoxes.php:63
msgid "Apply these tags when purchased"
msgstr "Applica questi tag all'acquisto"

#: app/Http/Controllers/ManagerController.php:135
msgid "Associate user could not be found"
msgstr "Impossibile trovare l'utente associato"

#: app/Http/Controllers/ManagerController.php:62
#: app/Http/Controllers/ManagerController.php:103
msgid "Associate user could not be found with this email"
msgstr "Impossibile trovare l'utente associato con questa email"

#: app/Modules/AbandonCart/Woo/WooCartTrackingInit.php:673
msgid "Automatically cancelled because a cart has been recovered"
msgstr "Annullato automaticamente perché un carrello è stato recuperato"

#: app/Modules/AbandonCart/Woo/WooCartTrackingInit.php:640
msgid "Automatically cancelled because the cart has been lost"
msgstr "Annullato automaticamente perché il carrello è andato perso"

#: app/Services/Integrations/LifterLms/LifterInit.php:192
msgid ""
"Automatically remove tags defined in \"Apply Tags\" if course enrollment is "
"cancelled."
msgstr ""
"Rimuovi automaticamente i tag definiti in \"Applica tag\" se l'iscrizione al "
"corso viene annullata."

#: app/Services/Integrations/LifterLms/LifterInit.php:250
msgid ""
"Automatically remove tags defined in \"Apply Tags\" if membership is "
"cancelled."
msgstr ""
"Rimuovi automaticamente i tag definiti in \"Applica tag\" se l'abbonamento "
"viene annullato."

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:38
msgid "Automation Priority"
msgstr "Priorità dell'automazione"

#: app/Services/Integrations/WooCommerce/AdvancedReport.php:121
#: app/Services/Integrations/Edd/AdvancedReport.php:123
msgid "Average Revenue Per Customer"
msgstr "Entrate medie per cliente"

#: app/Services/Integrations/BuddyPress/BBInit.php:89
#: app/Services/Integrations/BuddyPress/BBInit.php:145
msgid "Banned"
msgstr "Bannato"

#: app/Http/Controllers/DynamicSegmentController.php:119
msgid "Before"
msgstr "Prima"

#: app/Services/Integrations/BuddyPress/BBInit.php:34
#: app/Services/Integrations/BuddyPress/BBInit.php:55
msgid "BuddyBoss Groups"
msgstr "Gruppi BuddyBoss"

#: app/Services/Integrations/BuddyPress/BBInit.php:31
#: app/Services/Integrations/BuddyPress/BBInit.php:52
msgid "BuddyPress Groups"
msgstr "Gruppi BuddyPress"

#: app/Http/Controllers/RecurringCampaignController.php:197
#, php-format
msgid "Campaign status has been changed to %s"
msgstr "Lo stato della campagna è stato modificato in %s"

#: app/Services/Funnel/Actions/RemoveFromFunnelAction.php:23
#: app/Services/Funnel/Actions/RemoveFromFunnelAction.php:36
msgid "Cancel Automations"
msgstr "Annulla automazioni"

#: app/Services/Funnel/Actions/RemoveFromEmailSequenceAction.php:22
msgid "Cancel Sequence Emails"
msgstr "Annulla le email della sequenza"

#: app/Services/Funnel/Actions/RemoveFromEmailSequenceAction.php:23
msgid "Cancel Sequence Emails for the contact"
msgstr "Annulla le email della sequenza per il contatto"

#: app/Services/Funnel/Actions/RemoveFromFunnelAction.php:62
msgid "Cancelled by Automation ID: "
msgstr "Annullato dall'ID dell'automazione: "

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:23
#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:32
msgid "Cart Abandoned"
msgstr "Carrello abbandonato"

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:102
msgid "Cart Data"
msgstr "Dati del carrello"

#: app/Modules/AbandonCart/Woo/WooCartTrackingInit.php:275
msgid "Cart data has been updated"
msgstr "I dati del carrello sono stati aggiornati"

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:116
msgid "Cart Items"
msgstr "Articoli nel carrello"

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:125
msgid "Cart Items Categories"
msgstr "Categorie degli articoli nel carrello"

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:111
msgid "Cart Items Count"
msgstr "Conteggio articoli nel carrello"

#: app/Modules/AbandonCart/AbandonCart.php:70
msgid "Cart Recovered (All Time)"
msgstr "Carrelli recuperati (sempre)"

#: app/Modules/AbandonCart/AbandonCart.php:65
msgid "Cart Recovered (This Month)"
msgstr "Carrelli recuperati (questo mese)"

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:106
msgid "Cart Total"
msgstr "Totale carrello"

#: app/Services/Funnel/Actions/ChangeUserRoleAction.php:37
msgid "Change connected user role"
msgstr "Cambia ruolo dell'utente connesso"

#: app/Services/Integrations/WooCommerce/OrderStatusChangeAction.php:20
#: app/Services/Integrations/WooCommerce/OrderStatusChangeAction.php:42
msgid "Change Order Status"
msgstr "Cambia stato dell'ordine"

#: app/Services/Integrations/WooCommerce/OrderStatusChangeAction.php:21
#: app/Services/Integrations/WooCommerce/OrderStatusChangeAction.php:43
msgid "Change status of the current order in WooCommerce"
msgstr "Cambia lo stato dell'ordine corrente in WooCommerce"

#: app/Services/Funnel/Actions/ChangeUserRoleAction.php:36
msgid "Change User Role"
msgstr "Cambia ruolo utente"

#: app/Services/Funnel/Actions/ChangeUserRoleAction.php:21
msgid "Change WP User Role"
msgstr "Cambia ruolo utente WP"

#: app/Services/Funnel/Conditions/FunnelCondition.php:28
#: app/Services/Funnel/Conditions/FunnelCondition.php:42
msgid "Check Condition"
msgstr "Verifica condizione"

#: app/Services/Funnel/Conditions/FunnelCondition.php:29
#: app/Services/Funnel/Conditions/FunnelCondition.php:43
msgid "Check If the contact match specific data properties"
msgstr "Verifica se il contatto corrisponde a specifiche proprietà dei dati"

#: app/Services/PluginManager/LicenseManager.php:53
msgid "Check Update"
msgstr "Controlla aggiornamento"

#: app/Http/Controllers/DynamicSegmentController.php:153
#: app/Hooks/Handlers/DataExporter.php:534
#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:165
#: app/Services/Funnel/Conditions/FunnelCondition.php:97
msgid "City"
msgstr "Città"

#: app/Services/PluginManager/LicenseManager.php:345
msgid "Click Here to purchase another license"
msgstr "Clicca qui per acquistare un'altra licenza"

#: app/Services/PluginManager/LicenseManager.php:362
msgid "Click Here to Renew Your License"
msgstr "Clicca qui per rinnovare la tua licenza"

#: app/Services/Integrations/BuddyPress/BBInit.php:158
msgid "Community Groups"
msgstr "Gruppi della comunità"

#: app/Hooks/Handlers/DataExporter.php:548
msgid "Companies"
msgstr "Aziende"

#: app/Services/Integrations/CRM/CompanyAppliedTrigger.php:28
#: app/Services/Integrations/CRM/CompanyAppliedTrigger.php:45
msgid "Company Applied"
msgstr "Azienda associata"

#: app/Services/Integrations/CRM/RemoveFromCompanyTrigger.php:29
msgid "Company Removed"
msgstr "Azienda rimossa"

#: app/Services/BaseAdvancedReport.php:50
#: app/Services/BaseAdvancedReport.php:147
#: app/Services/BaseAdvancedReport.php:199
#: app/Services/Integrations/WooCommerce/AdvancedReport.php:181
#: app/Services/Integrations/WooCommerce/AdvancedReport.php:208
#: app/Services/Integrations/CRM/AdvancedReport.php:101
#: app/Services/Integrations/Edd/AdvancedReport.php:228
#: app/Services/Integrations/Edd/AdvancedReport.php:251
msgid "Compare Range"
msgstr "Confronta intervallo"

#: app/Services/Integrations/LifterLms/LifterInit.php:132
#: app/Services/Integrations/LearnDash/LdInit.php:122
msgid "Completed At"
msgstr "Completato il"

#: app/Services/Integrations/LearnDash/CourseCompletedTrigger.php:39
msgid "Completes a Course"
msgstr "Completa un corso"

#: app/Services/Integrations/LearnDash/LessonCompletedTrigger.php:39
msgid "Completes a Lesson"
msgstr "Completa una lezione"

#: app/Services/Integrations/LearnDash/TopicCompletedTrigger.php:38
msgid "Completes a Topic"
msgstr "Completa un argomento"

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:72
#: app/Services/Funnel/Triggers/TrackingEventRecordedTrigger.php:89
#: app/Services/Funnel/Conditions/FunnelCondition.php:53
msgid "Condition"
msgstr "Condizione"

#: app/Http/Controllers/DynamicSegmentController.php:130
msgid "Conditions"
msgstr "Condizioni"

#: app/Services/Integrations/BuddyPress/BBInit.php:109
msgid "Confirmation Status"
msgstr "Stato di conferma"

#: app/Services/Integrations/BuddyPress/BBInit.php:100
msgid "Confirmed"
msgstr "Confermato"

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:136
#: app/Services/Funnel/Conditions/FunnelCondition.php:68
msgid "Contact"
msgstr "Contatto"

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:263
#: app/Services/Funnel/Conditions/FunnelCondition.php:195
msgid "Contact Activities"
msgstr "Attività del contatto"

#: app/Services/Integrations/CRM/CompanyAppliedTrigger.php:65
msgid "contact added in all of the selected companies"
msgstr "contatto aggiunto a tutte le aziende selezionate"

#: app/Services/Integrations/CRM/ListAppliedTrigger.php:66
msgid "contact added in all of the selected lists"
msgstr "contatto aggiunto a tutte le liste selezionate"

#: app/Services/Integrations/CRM/TagAppliedTrigger.php:66
msgid "contact added in all of the selected tags"
msgstr "contatto aggiunto a tutti i tag selezionati"

#: app/Services/Integrations/CRM/CompanyAppliedTrigger.php:61
msgid "contact added in any of the selected companies"
msgstr "contatto aggiunto a una delle aziende selezionate"

#: app/Services/Integrations/CRM/ListAppliedTrigger.php:62
msgid "contact added in any of the selected lists"
msgstr "contatto aggiunto a una delle liste selezionate"

#: app/Services/Integrations/CRM/TagAppliedTrigger.php:62
msgid "contact added in any of the selected tags"
msgstr "contatto aggiunto a uno dei tag selezionati"

#: app/Services/Integrations/CRM/ContactCreatedTrigger.php:28
#: app/Services/Integrations/CRM/ContactCreatedTrigger.php:42
msgid "Contact Created"
msgstr "Contatto creato"

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:71
#: app/Services/Funnel/Triggers/TrackingEventRecordedTrigger.php:88
#: app/Services/Funnel/Conditions/FunnelCondition.php:52
msgid "Contact Data"
msgstr "Dati del contatto"

#: app/Http/Controllers/DynamicSegmentController.php:135
msgid "Contact Email"
msgstr "Email del contatto"

#: app/Services/Integrations/CRM/AdvancedReport.php:42
msgid "Contact Growth"
msgstr "Crescita dei contatti"

#: app/Services/Funnel/Actions/UpdateContactPropertyAction.php:47
msgid "Contact Property"
msgstr "Proprietà del contatto"

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:233
#: app/Services/Funnel/Conditions/FunnelCondition.php:165
msgid "Contact Segment"
msgstr "Segmento del contatto"

#: app/Services/Funnel/Actions/UpdateContactPropertyAction.php:193
#: app/Services/Funnel/Actions/UpdateContactPropertyAction.php:195
msgid "Contact Source"
msgstr "Fonte del contatto"

#: app/Services/Integrations/WooCommerce/WooSubscriptionStartedTrigger.php:78
#: app/Services/Integrations/WooCommerce/WooSubscriptionRenewalFailedTrigger.php:78
#: app/Services/Integrations/WooCommerce/WooSubscriptionExpiredTrigger.php:78
#: app/Services/Integrations/WooCommerce/WooSubscriptionRenewalPaymentTrigger.php:78
#: app/Services/Integrations/Edd/EddRecurringExpired.php:47
msgid "Contact Status"
msgstr "Stato del contatto"

#: app/Hooks/Handlers/DataExporter.php:540
#: app/Services/Funnel/Actions/UpdateContactPropertyAction.php:188
msgid "Contact Type"
msgstr "Tipo di contatto"

#: app/Services/Funnel/Triggers/ContactBirthDayTrigger.php:24
#: app/Services/Funnel/Triggers/ContactBirthDayTrigger.php:33
msgid "Contact's Birthday"
msgstr "Compleanno del contatto"

#: app/Services/Integrations/WooCommerce/WooOrderSuccessBenchmark.php:91
#: app/Services/Integrations/SureCart/SureCartPaymentSuccessBenchmark.php:71
#: app/Services/Integrations/Edd/EddOrderSuccessBenchmark.php:90
#: app/Services/Integrations/Edd/EddSubscriptionActiveBenchmark.php:71
#: app/Services/Funnel/Benchmarks/EmailSequenceCompletedBenchmark.php:61
#: app/Services/Funnel/Benchmarks/LinkClickBenchmark.php:82
msgid ""
"Contacts can enter directly to this sequence point. If you enable this then "
"any contact meet with goal will enter in this goal point."
msgstr ""
"I contatti possono accedere direttamente a questo punto della sequenza. Se "
"abilitato, qualsiasi contatto che soddisfa l'obiettivo entrerà in questo "
"punto obiettivo."

#: app/Services/Funnel/Benchmarks/LinkClickBenchmark.php:66
msgid "Contacts will be redirected to this link."
msgstr "I contatti verranno reindirizzati a questo link."

#: app/Http/Controllers/DynamicSegmentController.php:110
msgid "Contains"
msgstr "Contiene"

#: app/Services/Funnel/Benchmarks/LinkClickBenchmark.php:57
msgid "Copy This Link"
msgstr "Copia questo link"

#: app/Http/Controllers/DynamicSegmentController.php:167
#: app/Hooks/Handlers/DataExporter.php:536
#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:180
#: app/Services/Integrations/WooCommerce/AutomationConditions.php:64
#: app/Services/Integrations/WooCommerce/AutomationConditions.php:186
#: app/Services/Funnel/Conditions/FunnelCondition.php:112
#: app/Services/Funnel/Actions/UpdateContactPropertyAction.php:198
msgid "Country"
msgstr "Paese"

#: app/Services/Integrations/LifterLms/CourseCompletedTrigger.php:24
#: app/Services/Integrations/LifterLms/AutomationConditions.php:41
#: app/Services/Integrations/LearnDash/CourseCompletedTrigger.php:23
#: app/Services/Integrations/LearnDash/AutomationConditions.php:44
#: app/Services/Integrations/TutorLms/CourseCompletedTrigger.php:24
#: app/Services/Integrations/TutorLms/AutomationConditions.php:30
msgid "Course Completed"
msgstr "Corso completato"

#: app/Services/Integrations/LifterLms/CourseEnrollTrigger.php:28
#: app/Services/Integrations/LearnDash/CourseEnrollTrigger.php:23
#: app/Services/Integrations/TutorLms/CourseEnrollTrigger.php:24
msgid "Course Enrolled"
msgstr "Corso iscritto"

#: app/Services/Integrations/TutorLms/AutomationConditions.php:22
msgid "Course Enrollment"
msgstr "Iscrizione al corso"

#: app/Services/Integrations/LearnPress/LearnPressInit.php:92
msgid "Course ID"
msgstr "ID corso"

#: app/Services/Integrations/LearnDash/CourseLeaveTrigger.php:24
msgid "Course Left"
msgstr "Corso lasciato"

#: app/Services/Integrations/LifterLms/LifterInit.php:127
#: app/Services/Integrations/LearnPress/LearnPressInit.php:93
#: app/Services/Integrations/LearnDash/LdInit.php:119
#: app/Services/Integrations/TutorLms/TutorLmsInit.php:106
msgid "Course Name"
msgstr "Nome del corso"

#: app/Services/Integrations/LifterLms/AdvancedReport.php:43
#: app/Services/Integrations/LifterLms/LifterInit.php:64
#: app/Services/Integrations/LearnPress/LearnPressInit.php:46
#: app/Services/Integrations/LearnDash/AdvancedReport.php:43
#: app/Services/Integrations/LearnDash/LdInit.php:66
#: app/Services/Integrations/TutorLms/AdvancedReport.php:43
#: app/Services/Integrations/TutorLms/TutorLmsInit.php:52
msgid "Courses"
msgstr "Corsi"

#: app/Services/Integrations/WooCommerce/CreateCouponAction.php:23
#: app/Services/Integrations/WooCommerce/CreateCouponAction.php:60
msgid "Create Coupon"
msgstr "Crea coupon"

#: app/Services/Integrations/WooCommerce/CreateCouponAction.php:24
#: app/Services/Integrations/WooCommerce/CreateCouponAction.php:61
msgid "Create WooCommerce Coupon Code"
msgstr "Crea codice coupon WooCommerce"

#: app/Services/Funnel/Actions/UserRegistrationAction.php:46
msgid "Create WordPress User"
msgstr "Crea utente WordPress"

#: app/Services/Funnel/Actions/UserRegistrationAction.php:23
msgid "Create WP User"
msgstr "Crea utente WP"

#: app/Services/Funnel/Actions/UserRegistrationAction.php:24
#: app/Services/Funnel/Actions/UserRegistrationAction.php:47
msgid ""
"Create WP User with a role if user is not already registered with contact "
"email"
msgstr ""
"Crea utente WP con un ruolo se l'utente non è già registrato con l'email di "
"contatto"

#: app/Hooks/Handlers/DataExporter.php:544
#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:226
#: app/Services/Funnel/Conditions/FunnelCondition.php:158
msgid "Created At"
msgstr "Creato il"

#: app/Http/Controllers/DynamicSegmentController.php:187
msgid "Created at"
msgstr "Creato il"

#: app/Services/Integrations/Integrations.php:120
#: app/Services/Integrations/CRM/RemoveFromCompanyTrigger.php:28
#: app/Services/Integrations/CRM/ListAppliedTrigger.php:27
#: app/Services/Integrations/CRM/ContactCreatedTrigger.php:27
#: app/Services/Integrations/CRM/TagAppliedTrigger.php:27
#: app/Services/Integrations/CRM/CompanyAppliedTrigger.php:27
#: app/Services/Integrations/CRM/RemoveFromListTrigger.php:28
#: app/Services/Integrations/CRM/RemoveFromTagTrigger.php:28
#: app/Services/Funnel/Triggers/ContactBirthDayTrigger.php:23
#: app/Services/Funnel/Triggers/TrackingEventRecordedTrigger.php:30
#: app/Services/Funnel/Conditions/FunnelABTesting.php:25
#: app/Services/Funnel/Actions/RemoveFromFunnelAction.php:22
#: app/Services/Funnel/Actions/HTTPSendDataAction.php:26
#: app/Services/Funnel/Actions/UpdateContactPropertyAction.php:22
#: app/Services/Funnel/Actions/AddActivityAction.php:21
#: app/Services/Funnel/Actions/AddEventTrackerAction.php:26
#: app/Services/Funnel/Actions/EndFunnel.php:20
msgid "CRM"
msgstr "CRM"

#: app/Services/Integrations/CRM/AdvancedReport.php:38
msgid "CRM - Advanced Reports"
msgstr "CRM - Report avanzati"

#: app/Services/BaseAdvancedReport.php:46
#: app/Services/BaseAdvancedReport.php:143
#: app/Services/BaseAdvancedReport.php:195
#: app/Services/Integrations/WooCommerce/AdvancedReport.php:177
#: app/Services/Integrations/WooCommerce/AdvancedReport.php:204
#: app/Services/Integrations/CRM/AdvancedReport.php:97
#: app/Services/Integrations/Edd/AdvancedReport.php:224
#: app/Services/Integrations/Edd/AdvancedReport.php:247
msgid "Current Range"
msgstr "Intervallo attuale"

#: app/Services/Funnel/Actions/HTTPSendDataAction.php:104
msgid "Custom Data"
msgstr "Dati personalizzati"

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:328
#: app/Services/Funnel/Conditions/FunnelCondition.php:260
msgid "Custom Fields"
msgstr "Campi personalizzati"

#: app/Services/Funnel/Actions/UserRegistrationAction.php:61
msgid "Custom Password"
msgstr "Password personalizzata"

#: app/Services/DynamicSegments/CustomSegment.php:50
msgid "Custom Segments with custom filters on Subscriber data"
msgstr ""
"Segmenti personalizzati con filtri personalizzati sui dati degli iscritti"

#: app/Services/Funnel/Actions/UserRegistrationAction.php:73
msgid "Custom Username (optional)"
msgstr "Nome utente personalizzato (opzionale)"

#: app/Services/Integrations/WooCommerce/AdvancedReport.php:45
#: app/Services/Integrations/Edd/AdvancedReport.php:37
msgid "Customer Growth"
msgstr "Crescita clienti"

#: app/Services/Integrations/WooCommerce/DeepIntegration.php:546
#: app/Services/Integrations/Edd/DeepIntegration.php:218
msgid "Customer Since"
msgstr "Cliente dal"

#: app/Services/Integrations/WooCommerce/DeepIntegration.php:106
#: app/Services/Integrations/Edd/DeepIntegration.php:155
msgid "Customer Summary"
msgstr "Riepilogo cliente"

#: app/Services/Funnel/Actions/HTTPSendDataAction.php:111
msgid "Data Key"
msgstr "Chiave dati"

#: app/Services/Funnel/Actions/HTTPSendDataAction.php:61
msgid "Data Send Method"
msgstr "Metodo di invio dati"

#: app/Services/Funnel/Actions/HTTPSendDataAction.php:112
msgid "Data Value"
msgstr "Valore dati"

#: app/Hooks/Handlers/DataExporter.php:542
msgid "Date Of Birth"
msgstr "Data di nascita"

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:216
#: app/Services/Funnel/Conditions/FunnelCondition.php:148
msgid "Date of Birth"
msgstr "Data di nascita"

#: app/Services/Funnel/Actions/AddActivityAction.php:62
msgid "Description"
msgstr "Descrizione"

#: app/Services/Integrations/LifterLms/AddToMembershipAction.php:49
#: app/Services/Integrations/LifterLms/AddToCourseAction.php:50
#: app/Services/Integrations/LearnDash/AddToGroupAction.php:50
#: app/Services/Integrations/LearnDash/AddToCourseAction.php:50
#: app/Services/Integrations/TutorLms/AddToCourseAction.php:49
msgid "Do not enroll the course if contact is not an existing WordPress User"
msgstr ""
"Non iscrivere al corso se il contatto non è un utente WordPress esistente"

#: app/Services/PluginManager/LicenseManager.php:51
msgid "Docs"
msgstr "Documentazione"

#: app/Services/Integrations/ElementorFormIntegration/FormWidget.php:101
msgid "Double Opt-in"
msgstr "Doppio opt-in"

#: app/Modules/AbandonCart/AbandonCartController.php:122
msgid "Draft Revenue"
msgstr "Entrate previste"

#: app/Services/Integrations/AffiliateWP/DeepIntegration.php:89
msgid "Earnings"
msgstr "Guadagni"

#: app/Services/Integrations/Edd/EddInit.php:57
msgid "Earnings (All Time)"
msgstr "Guadagni (sempre)"

#: app/Services/Integrations/Edd/EddInit.php:53
msgid "Earnings (Current Month)"
msgstr "Guadagni (mese corrente)"

#: app/Services/Integrations/Edd/EddInit.php:49
msgid "Earnings (Today)"
msgstr "Guadagni (oggi)"

#: app/Services/Integrations/Edd/EddPaymentSuccessTrigger.php:23
#: app/Services/Integrations/Edd/EddLicenseExpiredTrigger.php:23
#: app/Services/Integrations/Edd/EddRecurringExpired.php:23
#: app/Services/Integrations/Edd/EddRecurringPaymentTrigger.php:23
#: app/Services/Integrations/Edd/DeepIntegration.php:365
#: app/Services/Integrations/Edd/EddPaymentRefundTrigger.php:24
msgid "Easy Digital Downloads"
msgstr "Easy Digital Downloads"

#: app/Services/Integrations/Edd/AdvancedReport.php:87
msgid "Easy Digital Downloads - Advanced Reports"
msgstr "Easy Digital Downloads - Report avanzati"

#: app/Services/DynamicSegments/EddActiveCustomerSegment.php:20
msgid "Easy Digital Downloads Customers"
msgstr "Clienti Easy Digital Downloads"

#: app/Services/Integrations/Edd/DeepIntegration.php:367
msgid "Easy Digital Downloads customers are not synced with FluentCRM yet."
msgstr ""
"I clienti di Easy Digital Downloads non sono ancora sincronizzati con "
"FluentCRM."

#: app/Services/Integrations/Edd/EddImporter.php:150
msgid "Easy Digital Downloads Data Sync"
msgstr "Sincronizzazione dati Easy Digital Downloads"

#: app/Services/Integrations/Edd/AutomationConditions.php:21
#: app/Services/Integrations/Edd/DeepIntegration.php:278
msgid "EDD"
msgstr "EDD"

#: app/Services/Integrations/Edd/AutomationConditions.php:21
msgid "EDD (Sync Required)"
msgstr "EDD (Sincronizzazione richiesta)"

#: app/Services/Integrations/Edd/EddPaymentSuccessTrigger.php:24
msgid "Edd - New Order Success"
msgstr "EDD - Nuovo ordine completato"

#: app/Services/DynamicSegments/EddActiveCustomerSegment.php:21
msgid "EDD customers who are also in the contact list as subscribed"
msgstr "Clienti EDD che sono anche nella lista contatti come iscritti"

#: app/Services/Integrations/Edd/EddSubscriptionActiveBenchmark.php:47
msgid "EDD Recurring Subscription Active"
msgstr "Abbonamento ricorrente EDD attivo"

#: app/Hooks/Handlers/DataExporter.php:529
#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:150
#: app/Services/Funnel/Conditions/FunnelCondition.php:82
#: app/Services/Funnel/Actions/RemoveFromEmailSequenceAction.php:21
#: app/Services/Funnel/Actions/SendCampaignEmailAction.php:22
#: app/Services/Funnel/Actions/AddEmailSequenceAction.php:23
msgid "Email"
msgstr "Email"

#: app/Hooks/Handlers/FLBuilderServiceFluentCrm.php:161
msgid "Email Address is not valid"
msgstr "Indirizzo email non valido"

#: app/Http/Controllers/RecurringCampaignController.php:355
msgid "Email body has been successfully updated"
msgstr "Corpo dell'email aggiornato con successo"

#: app/Http/Controllers/RecurringCampaignController.php:346
msgid "Email body is required"
msgstr "Il corpo dell'email è richiesto"

#: app/Http/Controllers/RecurringCampaignController.php:130
msgid "Email data has been updated"
msgstr "I dati dell'email sono stati aggiornati"

#: app/Http/Controllers/CampaignsProController.php:91
msgid "Email has been resent"
msgstr "L'email è stata rinviata"

#: app/Services/Funnel/Actions/SendCampaignEmailAction.php:76
msgid "Email has been triggered by Automation Funnel ID: "
msgstr "L'email è stata attivata dall'ID del funnel di automazione: "

#: app/Hooks/Handlers/CampaignArchiveFront.php:22
msgid "Email Newsletter Archive features is not enabled"
msgstr "Le funzionalità di archivio newsletter via email non sono abilitate"

#: app/Hooks/Handlers/CampaignArchiveFront.php:33
msgid "Email Newsletter could not be found"
msgstr "La newsletter email non è stata trovata"

#: app/Services/Integrations/CRM/AdvancedReport.php:45
msgid "Email Sending Stats"
msgstr "Statistiche di invio email"

#: app/Services/Funnel/Benchmarks/EmailSequenceCompletedBenchmark.php:23
#: app/Services/Funnel/Benchmarks/EmailSequenceCompletedBenchmark.php:38
msgid "Email Sequence Completed"
msgstr "Sequenza email completata"

#: app/Http/Controllers/SequenceController.php:187
#: app/Http/Controllers/SequenceMailController.php:183
msgid "Email sequence successfully deleted"
msgstr "Sequenza email eliminata con successo"

#: app/Http/Controllers/RecurringCampaignController.php:328
#, php-format
msgid "Email status has been changed to %s"
msgstr "Lo stato dell'email è stato modificato in %s"

#: app/Http/Controllers/DynamicSegmentController.php:228
msgid "Enable Last Email Activity Filter"
msgstr "Abilita filtro ultima attività email"

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:82
msgid ""
"Enable this to prevent the automation from running multiple times for the "
"same contact if it is currently active in this automation"
msgstr ""
"Abilita questa opzione per impedire che l'automazione venga eseguita più "
"volte per lo stesso contatto se è attualmente attiva in questa automazione"

#: app/Services/Funnel/Actions/EndFunnel.php:21
#: app/Services/Funnel/Actions/EndFunnel.php:33
msgid "End This Funnel Here"
msgstr "Termina questo funnel qui"

#: app/Services/Integrations/LifterLms/AddToCourseAction.php:24
#: app/Services/Integrations/LifterLms/AddToCourseAction.php:38
#: app/Services/Integrations/LearnDash/AddToCourseAction.php:23
#: app/Services/Integrations/LearnDash/AddToCourseAction.php:37
#: app/Services/Integrations/TutorLms/AddToCourseAction.php:23
#: app/Services/Integrations/TutorLms/AddToCourseAction.php:37
msgid "Enroll the contact to a specific LMS Course"
msgstr "Iscrivi il contatto a un corso LMS specifico"

#: app/Services/Integrations/LearnDash/AddToGroupAction.php:23
#: app/Services/Integrations/LearnDash/AddToGroupAction.php:37
msgid "Enroll the contact to a specific LMS Group"
msgstr "Iscrivi il contatto a un gruppo LMS specifico"

#: app/Services/Integrations/LifterLms/AddToMembershipAction.php:23
#: app/Services/Integrations/LifterLms/AddToMembershipAction.php:37
msgid "Enroll the contact to a specific LMS Membership Group"
msgstr "Iscrivi il contatto a un gruppo di appartenenza LMS specifico"

#: app/Services/Integrations/LifterLms/AddToCourseAction.php:37
#: app/Services/Integrations/LearnDash/AddToCourseAction.php:36
#: app/Services/Integrations/TutorLms/AddToCourseAction.php:36
msgid "Enroll To a Course"
msgstr "Iscrivi a un corso"

#: app/Services/Integrations/LearnDash/AddToGroupAction.php:36
msgid "Enroll To a Group"
msgstr "Iscrivi a un gruppo"

#: app/Services/Integrations/LifterLms/AddToMembershipAction.php:22
#: app/Services/Integrations/LifterLms/AddToMembershipAction.php:36
msgid "Enroll To a Membership Group"
msgstr "Iscrivi a un gruppo di appartenenza"

#: app/Services/Integrations/LifterLms/AddToCourseAction.php:23
#: app/Services/Integrations/LearnDash/AddToCourseAction.php:22
#: app/Services/Integrations/TutorLms/AddToCourseAction.php:22
msgid "Enroll To Course"
msgstr "Iscrivi al corso"

#: app/Services/Integrations/LearnDash/AddToGroupAction.php:22
msgid "Enroll To Group"
msgstr "Iscrivi al gruppo"

#: app/Services/Integrations/LifterLms/LifterInit.php:128
msgid "Enrolled At"
msgstr "Iscritto il"

#: app/Services/Integrations/LifterLms/AutomationConditions.php:71
#: app/Services/Integrations/LifterLms/DeepIntegration.php:119
#: app/Services/Integrations/LearnDash/AutomationConditions.php:75
#: app/Services/Integrations/LearnDash/DeepIntegration.php:110
#: app/Services/Integrations/TutorLms/DeepIntegration.php:437
msgid "Enrollment Categories"
msgstr "Categorie di iscrizione"

#: app/Services/Integrations/LifterLms/AutomationConditions.php:32
#: app/Services/Integrations/LifterLms/DeepIntegration.php:100
#: app/Services/Integrations/LearnDash/AutomationConditions.php:35
#: app/Services/Integrations/LearnDash/DeepIntegration.php:90
#: app/Services/Integrations/TutorLms/DeepIntegration.php:428
msgid "Enrollment Courses"
msgstr "Corsi di iscrizione"

#: app/Services/Integrations/LearnDash/AutomationConditions.php:53
#: app/Services/Integrations/LearnDash/DeepIntegration.php:99
msgid "Enrollment Groups"
msgstr "Gruppi di iscrizione"

#: app/Services/Integrations/LifterLms/CourseEnrollTrigger.php:44
msgid "Enrollment in a course in LifterLMS"
msgstr "Iscrizione a un corso in LifterLMS"

#: app/Services/Integrations/TutorLms/CourseEnrollTrigger.php:40
msgid "Enrollment in a course in TutorLMS"
msgstr "Iscrizione a un corso in TutorLMS"

#: app/Services/Integrations/LifterLms/MembershipEnrollTrigger.php:44
msgid "Enrollment in a Membership in LifterLMS"
msgstr "Iscrizione a un'appartenenza in LifterLMS"

#: app/Services/Integrations/PMPro/PMProPMProMembershipTrigger.php:40
msgid "Enrollment in a Membership Level in PMPro"
msgstr "Iscrizione a un livello di appartenenza in PMPro"

#: app/Services/Integrations/LifterLms/AutomationConditions.php:50
#: app/Services/Integrations/LifterLms/DeepIntegration.php:109
msgid "Enrollment Memberships"
msgstr "Appartenenze di iscrizione"

#: app/Services/Integrations/LifterLms/AutomationConditions.php:80
#: app/Services/Integrations/LifterLms/DeepIntegration.php:128
#: app/Services/Integrations/LearnDash/AutomationConditions.php:84
#: app/Services/Integrations/LearnDash/DeepIntegration.php:119
#: app/Services/Integrations/TutorLms/DeepIntegration.php:446
msgid "Enrollment Tags"
msgstr "Tag di iscrizione"

#: app/Services/Integrations/LifterLms/AdvancedReport.php:36
#: app/Services/Integrations/LearnDash/AdvancedReport.php:36
#: app/Services/Integrations/TutorLms/AdvancedReport.php:36
msgid "Enrollments"
msgstr "Iscrizioni"

#: app/Services/Integrations/LearnDash/CourseEnrollTrigger.php:41
msgid "Enrolls in a Course"
msgstr "Si iscrive a un corso"

#: app/Services/Integrations/LearnDash/GroupEnrollTrigger.php:39
msgid "Enrolls in a Group"
msgstr "Si iscrive a un gruppo"

#: app/Http/Controllers/DynamicSegmentController.php:108
msgid "Equal"
msgstr "Uguale"

#: app/Services/Funnel/Actions/UserRegistrationAction.php:131
#, fuzzy
#| msgid "Error when creating new User."
msgid "Error when creating new User. "
msgstr "Errore durante la creazione di un nuovo utente."

#: app/Services/Funnel/Conditions/FunnelABTesting.php:27
#: app/Services/Funnel/Conditions/FunnelABTesting.php:42
msgid "Evenly split the contacts or choose how to distribute them"
msgstr "Dividi equamente i contatti o scegli come distribuirli"

#: app/Services/Funnel/Actions/AddEventTrackerAction.php:43
msgid "Event Tracking for Contact to the Contact Profile"
msgstr "Tracciamento evento per il contatto al profilo del contatto"

#: app/Services/Funnel/Actions/AddEventTrackerAction.php:48
msgid "Event Tracking Key"
msgstr "Chiave di tracciamento evento"

#: app/Services/Funnel/Actions/AddEventTrackerAction.php:55
msgid "Event Tracking Title"
msgstr "Titolo del tracciamento evento"

#: app/Services/Integrations/Edd/AdvancedReport.php:72
msgid "Expired"
msgstr "Scaduto"

#: app/Services/Integrations/Edd/AdvancedReport.php:164
msgid "Expired Licenses"
msgstr "Licenze scadute"

#: app/Http/Controllers/DynamicSegmentController.php:223
msgid "Filter By Email Activities"
msgstr "Filtra per attività email"

#: app/Http/Controllers/DynamicSegmentController.php:224
msgid ""
"Filter your contacts by from email open or email link click metrics. Leave "
"the values blank for not applying"
msgstr ""
"Filtra i tuoi contatti in base alle metriche di apertura email o clic su "
"link email. Lascia i valori vuoti per non applicare"

#: app/Services/Integrations/LifterLms/AutomationConditions.php:65
#: app/Services/Integrations/LifterLms/DeepIntegration.php:94
#: app/Services/Integrations/LearnDash/AutomationConditions.php:69
#: app/Services/Integrations/LearnDash/DeepIntegration.php:84
#: app/Services/Integrations/TutorLms/DeepIntegration.php:422
msgid "First Enrollment Date"
msgstr "Data prima iscrizione"

#: app/Http/Controllers/DynamicSegmentController.php:141
#: app/Hooks/Handlers/DataExporter.php:527
#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:140
#: app/Services/Funnel/Conditions/FunnelCondition.php:72
msgid "First Name"
msgstr "Nome"

#: app/Services/Integrations/WooCommerce/AutomationConditions.php:83
#: app/Services/Integrations/WooCommerce/DeepIntegration.php:226
#: app/Services/Integrations/Edd/AutomationConditions.php:56
#: app/Services/Integrations/Edd/DeepIntegration.php:303
msgid "First Order Date"
msgstr "Data primo ordine"

#. Author of the plugin
msgid "Fluent CRM"
msgstr "Fluent CRM"

#: app/Services/Integrations/LifterLms/LifterInit.php:161
#: app/Services/Integrations/LifterLms/LifterInit.php:232
#: app/Services/Integrations/LifterLms/LifterInit.php:366
#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:55
#: app/Services/Integrations/ElementorFormIntegration/FormWidget.php:30
#: app/Services/Integrations/ElementorFormIntegration/FormWidget.php:43
#: app/Services/Integrations/BuddyPress/Group.php:15
msgid "FluentCRM"
msgstr "FluentCRM"

#: app/Services/Integrations/TutorLms/TutorLmsInit.php:117
msgid "FluentCRM - Course Tags"
msgstr "FluentCRM - Tag dei corsi"

#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:107
msgid "FluentCRM Integration"
msgstr "Integrazione FluentCRM"

#. Name of the plugin
msgid "FluentCRM Pro"
msgstr "FluentCRM Pro"

#: app/Services/Integrations/WooCommerce/WooInit.php:193
#: app/Services/Integrations/Edd/EddInit.php:130
msgid "FluentCRM Profile"
msgstr "Profilo FluentCRM"

#: app/Services/Integrations/LearnPress/LearnPressInit.php:106
#: app/Services/Integrations/LearnPress/LearnPressInit.php:110
#: app/Services/Integrations/BuddyPress/BBMemberType.php:53
#: app/Services/Integrations/BuddyPress/BBMemberType.php:219
#: app/Services/Integrations/Edd/EddMetaBoxes.php:29
#: app/Services/Integrations/Edd/EddMetaBoxes.php:100
msgid "FluentCRM Settings"
msgstr "Impostazioni FluentCRM"

#: app/Services/Integrations/WooCommerce/WooOrderCompletedTrigger.php:97
#: app/Services/Integrations/WooCommerce/WooOrderSuccessTrigger.php:99
#: app/Services/Integrations/Edd/EddPaymentSuccessTrigger.php:104
#: app/Services/Integrations/Edd/EddPaymentRefundTrigger.php:104
msgid "For what type of purchase you want to run this funnel"
msgstr "Per quale tipo di acquisto vuoi eseguire questo funnel"

#: app/Services/Integrations/WooCommerce/WooOrderSuccessBenchmark.php:76
#: app/Services/Integrations/Edd/EddOrderSuccessBenchmark.php:75
msgid "For what type of purchase you want to run this goal"
msgstr "Per quale tipo di acquisto vuoi eseguire questo obiettivo"

#: app/Services/Integrations/WooCommerce/Helper.php:59
#: app/Services/Integrations/Edd/Helper.php:62
msgid "From 2nd Purchase"
msgstr "Dal 2° acquisto"

#: app/Services/Funnel/Actions/HTTPSendDataAction.php:100
msgid "Full Subscriber Data (Raw)"
msgstr "Dati completi dell'iscritto (grezzi)"

#: app/Services/Funnel/Actions/ChangeUserRoleAction.php:69
msgid ""
"Funnel Skipped because administrator user role can not be set for security "
"reason"
msgstr ""
"Funnel saltato poiché il ruolo amministratore non può essere impostato per "
"motivi di sicurezza"

#: app/Services/Integrations/LifterLms/AddToCourseAction.php:89
#: app/Services/Integrations/LearnDash/AddToCourseAction.php:89
#: app/Services/Integrations/TutorLms/AddToCourseAction.php:89
msgid "Funnel Skipped because no course found"
msgstr "Funnel saltato poiché non è stato trovato alcun corso"

#: app/Services/Integrations/LifterLms/AddToMembershipAction.php:88
#: app/Services/Integrations/LearnDash/AddToGroupAction.php:89
msgid "Funnel Skipped because no group found"
msgstr "Funnel saltato poiché non è stato trovato alcun gruppo"

#: app/Services/Funnel/Actions/UpdateUserMetaAction.php:61
#: app/Services/Funnel/Actions/ChangeUserRoleAction.php:59
msgid "Funnel Skipped because no user found with the email address"
msgstr ""
"Funnel saltato poiché non è stato trovato alcun utente con questo indirizzo "
"email"

#: app/Services/Funnel/Actions/HTTPSendDataAction.php:173
msgid "Funnel Skipped because provided url is not valid"
msgstr "Funnel saltato poiché l'URL fornito non è valido"

#: app/Services/Funnel/Actions/UserRegistrationAction.php:100
msgid "Funnel Skipped because user already exist in the database"
msgstr "Funnel saltato poiché l'utente esiste già nel database"

#: app/Services/Integrations/LifterLms/RemoveFromMembershipAction.php:57
#: app/Services/Integrations/LifterLms/RemoveFromCourseAction.php:57
#: app/Services/Integrations/LifterLms/AddToMembershipAction.php:79
#: app/Services/Integrations/LifterLms/AddToCourseAction.php:80
#: app/Services/Integrations/LearnDash/AddToGroupAction.php:80
#: app/Services/Integrations/LearnDash/AddToCourseAction.php:80
#: app/Services/Integrations/TutorLms/RemoveFromCourseAction.php:57
#: app/Services/Integrations/TutorLms/AddToCourseAction.php:79
msgid "Funnel Skipped because user could not be found"
msgstr "Funnel saltato poiché l'utente non è stato trovato"

#: app/Services/Integrations/LearnDash/RemoveFromCourseAction.php:59
msgid "Funnel Skipped because user/course could not be found"
msgstr "Funnel saltato poiché utente/corso non è stato trovato"

#: app/Services/Integrations/LearnDash/RemoveFromGroupAction.php:57
msgid "Funnel Skipped because user/group could not be found"
msgstr "Funnel saltato poiché utente/gruppo non è stato trovato"

#: app/Services/Funnel/Triggers/ContactBirthDayTrigger.php:25
#: app/Services/Funnel/Triggers/ContactBirthDayTrigger.php:34
msgid "Funnel will be initiated on the day of contact's birthday"
msgstr "Il funnel verrà avviato nel giorno del compleanno del contatto"

#: app/Services/Funnel/Actions/UserRegistrationAction.php:57
msgid "Generate Password Automatically"
msgstr "Genera password automaticamente"

#: app/Services/Funnel/Actions/HTTPSendDataAction.php:69
msgid "GET Method"
msgstr "Metodo GET"

#: app/Services/Integrations/LifterLms/LifterInit.php:130
msgid "Grade"
msgstr "Valutazione"

#: app/Services/Integrations/LearnPress/LearnPressInit.php:95
msgid "Graduation Status"
msgstr "Stato della laurea/diploma"

#: app/Services/Integrations/WooCommerce/AdvancedReport.php:40
#: app/Services/Integrations/Edd/AdvancedReport.php:32
msgid "Gross Volume"
msgstr "Volume lordo"

#: app/Services/Integrations/LearnDash/GroupEnrollTrigger.php:23
msgid "Group Enrolled"
msgstr "Iscrizione al gruppo"

#: app/Services/Integrations/BuddyPress/BBInit.php:106
msgid "Group ID"
msgstr "ID del gruppo"

#: app/Services/Integrations/BuddyPress/BBInit.php:107
msgid "Group Name"
msgstr "Nome del gruppo"

#: app/Services/Funnel/Actions/HTTPSendDataAction.php:140
msgid "Header Key"
msgstr "Chiave header"

#: app/Services/Funnel/Actions/HTTPSendDataAction.php:141
msgid "Header Value"
msgstr "Valore header"

#: app/Services/PluginManager/LicenseManager.php:52
msgid "Help & Support"
msgstr "Assistenza & supporto"

#. URI of the plugin
#. Author URI of the plugin
msgid "https://fluentcrm.com"
msgstr "https://fluentcrm.com"

#: app/Hooks/Handlers/DataExporter.php:524
#: app/Services/Integrations/LifterLms/LifterInit.php:126
#: app/Services/Integrations/LearnDash/LdInit.php:118
#: app/Services/Integrations/TutorLms/TutorLmsInit.php:105
msgid "ID"
msgstr "ID"

#: app/Services/Integrations/LifterLms/LessonCompletedTrigger.php:80
#: app/Services/Integrations/LifterLms/CourseCompletedTrigger.php:76
#: app/Services/Integrations/LifterLms/CourseEnrollTrigger.php:81
#: app/Services/Integrations/LifterLms/MembershipEnrollTrigger.php:81
#: app/Services/Integrations/PMPro/PMProPMProMembershipTrigger.php:77
#: app/Services/Integrations/LearnDash/GroupEnrollTrigger.php:76
#: app/Services/Integrations/LearnDash/CourseLeaveTrigger.php:79
#: app/Services/Integrations/LearnDash/TopicCompletedTrigger.php:76
#: app/Services/Integrations/LearnDash/LessonCompletedTrigger.php:76
#: app/Services/Integrations/LearnDash/CourseCompletedTrigger.php:75
#: app/Services/Integrations/LearnDash/CourseEnrollTrigger.php:78
#: app/Services/Integrations/TutorLms/LessonCompletedTrigger.php:76
#: app/Services/Integrations/TutorLms/CourseCompletedTrigger.php:76
#: app/Services/Integrations/TutorLms/CourseEnrollTrigger.php:77
#: app/Services/Integrations/AffiliateWP/AffiliateWPAffActiveTrigger.php:104
#: app/Services/Funnel/Triggers/UserLoginTrigger.php:69
msgid "If Contact Already Exist?"
msgstr "Se il contatto esiste già?"

#: app/Services/Integrations/SureCart/SureCartPaymentRefundTrigger.php:77
#: app/Services/Integrations/SureCart/SureCartPaymentSuccessTrigger.php:78
#: app/Services/Integrations/Edd/EddPaymentSuccessTrigger.php:79
#: app/Services/Integrations/Edd/EddRecurringExpired.php:77
#: app/Services/Integrations/Edd/EddRecurringPaymentTrigger.php:78
#: app/Services/Integrations/Edd/EddPaymentRefundTrigger.php:79
msgid "If Contact Exist?"
msgstr "Se il contatto esiste?"

#: app/Services/Funnel/Actions/ChangeUserRoleAction.php:22
msgid "If user exist with the contact email then you can change user role"
msgstr ""
"Se l'utente esiste con l'email di contatto, puoi cambiare il ruolo "
"dell'utente"

#: app/Services/Funnel/Actions/ChangeUserRoleAction.php:49
msgid ""
"If you disable this then it will append the selected role with existing "
"roles."
msgstr ""
"Se disabiliti questa opzione, il ruolo selezionato verrà aggiunto ai ruoli "
"esistenti."

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:88
msgid ""
"If you enable, then it will only run this automation for subscribed contacts"
msgstr "Se abilitato, eseguirà questa automazione solo per i contatti iscritti"

#: app/Services/FunnelMultiConditionTrait.php:21
#: app/Services/Integrations/RCP/RCPMembershipTrigger.php:85
#: app/Services/Integrations/RCP/RCPMembershipCancelTrigger.php:85
#: app/Services/Integrations/RCP/RCPMembershipExpireTrigger.php:85
#: app/Services/Integrations/LifterLms/LessonCompletedTrigger.php:96
#: app/Services/Integrations/LifterLms/CourseCompletedTrigger.php:92
#: app/Services/Integrations/LifterLms/CourseEnrollTrigger.php:96
#: app/Services/Integrations/LifterLms/MembershipEnrollTrigger.php:98
#: app/Services/Integrations/WooCommerce/WooSubscriptionStartedTrigger.php:63
#: app/Services/Integrations/WooCommerce/WooOrderCompletedTrigger.php:103
#: app/Services/Integrations/WooCommerce/WooOrderStatusChangeTrigger.php:113
#: app/Services/Integrations/WooCommerce/WooSubscriptionRenewalFailedTrigger.php:63
#: app/Services/Integrations/WooCommerce/WooOrderRefundedTrigger.php:95
#: app/Services/Integrations/WooCommerce/WooOrderSuccessTrigger.php:105
#: app/Services/Integrations/WooCommerce/WooSubscriptionExpiredTrigger.php:63
#: app/Services/Integrations/WooCommerce/WooSubscriptionRenewalPaymentTrigger.php:63
#: app/Services/Integrations/PMPro/PMProPMProMembershipTrigger.php:92
#: app/Services/Integrations/PMPro/PMProPMProExpiryLevelTrigger.php:85
#: app/Services/Integrations/PMPro/PMProPMProCancelLevelTrigger.php:85
#: app/Services/Integrations/SureCart/SureCartPaymentRefundTrigger.php:93
#: app/Services/Integrations/SureCart/SureCartPaymentSuccessTrigger.php:94
#: app/Services/Integrations/LearnDash/GroupEnrollTrigger.php:93
#: app/Services/Integrations/LearnDash/CourseLeaveTrigger.php:96
#: app/Services/Integrations/LearnDash/TopicCompletedTrigger.php:107
#: app/Services/Integrations/LearnDash/LessonCompletedTrigger.php:100
#: app/Services/Integrations/LearnDash/CourseCompletedTrigger.php:92
#: app/Services/Integrations/LearnDash/CourseEnrollTrigger.php:95
#: app/Services/Integrations/Edd/EddPaymentSuccessTrigger.php:110
#: app/Services/Integrations/Edd/EddLicenseExpiredTrigger.php:85
#: app/Services/Integrations/Edd/EddRecurringExpired.php:93
#: app/Services/Integrations/Edd/EddRecurringPaymentTrigger.php:102
#: app/Services/Integrations/Edd/EddPaymentRefundTrigger.php:110
#: app/Services/Integrations/WishlistMember/WishlistMembershipTrigger.php:85
#: app/Services/Integrations/TutorLms/LessonCompletedTrigger.php:91
#: app/Services/Integrations/TutorLms/CourseCompletedTrigger.php:91
#: app/Services/Integrations/TutorLms/CourseEnrollTrigger.php:92
#: app/Services/Integrations/MemberPress/MembershipTrigger.php:85
#: app/Services/Integrations/MemberPress/SubscriptionExpiredTrigger.php:84
#: app/Services/Integrations/AffiliateWP/AffiliateWPAffActiveTrigger.php:86
#: app/Services/Funnel/Triggers/UserLoginTrigger.php:86
msgid ""
"If you enable, then it will restart the automation for a contact if the "
"contact already in the automation. Otherwise, It will just skip if already "
"exist"
msgstr ""
"Se abilitato, riavvierà l'automazione per un contatto se il contatto è già "
"nell'automazione. Altrimenti, verrà ignorato se già esistente"

#: app/Services/Funnel/Triggers/TrackingEventRecordedTrigger.php:100
msgid ""
"If you enable, then it will restart the automation for a contact if the "
"contact already in the automation. Please note that, if the automation "
"status is active it will not restart."
msgstr ""
"Se abilitato, riavvierà l'automazione per un contatto se il contatto è già "
"nell'automazione. Nota che, se lo stato dell'automazione è attivo, non verrà "
"riavviata."

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:39
msgid ""
"If you have multiple automation for abandon cart, you can set the priority. "
"The higher the priority means it will match earlier. Only one abandon cart "
"automation will run per abandonment depends on your conditional logics."
msgstr ""
"Se hai più automazioni per il carrello abbandonato, puoi impostare la "
"priorità. Più alta è la priorità, prima verrà eseguita. Solo un'automazione "
"per il carrello abbandonato verrà eseguita per abbandono a seconda della "
"logica condizionale."

#: app/Services/Funnel/Actions/UserRegistrationAction.php:63
msgid "If you leave blank then auto generated password will be set"
msgstr "Se lasci vuoto, verrà impostata una password generata automaticamente"

#: app/Services/Funnel/Actions/UserRegistrationAction.php:74
msgid ""
"If you leave blank then email will be used as username. If provided username "
"is not available then email address will be used for username"
msgstr ""
"Se lasci vuoto, l'email verrà utilizzata come nome utente. Se il nome utente "
"fornito non è disponibile, verrà utilizzato l'indirizzo email come nome "
"utente"

#: app/Services/Funnel/Actions/UserRegistrationAction.php:83
msgid ""
"If you want to map user meta properties you can add that here. This is "
"totally optional"
msgstr ""
"Se desideri mappare le proprietà meta dell'utente, puoi aggiungerle qui. "
"Questo è totalmente opzionale"

#: app/Services/Integrations/WooCommerce/WooImporter.php:120
#: app/Services/Integrations/Edd/EddImporter.php:114
#, php-format
msgid "Import %s Customers Now"
msgstr "Importa ora %s clienti"

#: app/Services/Integrations/RCP/RCPImporter.php:141
#: app/Services/Integrations/PMPro/PMProImporter.php:139
#: app/Services/Integrations/BuddyPress/BbImporter.php:202
#: app/Services/Integrations/WishlistMember/WishlistMemberImporter.php:137
#: app/Services/Integrations/MemberPress/MemberPressImporter.php:131
#, php-format
msgid "Import %s Members Now"
msgstr "Importa ora %s membri"

#: app/Services/Integrations/LifterLms/LifterImporter.php:181
#: app/Services/Integrations/LearnPress/LearnPressImporter.php:131
#: app/Services/Integrations/LearnDash/LearnDashImporter.php:184
#: app/Services/Integrations/TutorLms/TutorImporter.php:137
#, php-format
msgid "Import %s Students Now"
msgstr "Importa ora %s studenti"

#: app/Services/Integrations/RCP/RCPImporter.php:110
#: app/Services/Integrations/LifterLms/LifterImporter.php:134
#: app/Services/Integrations/LearnPress/LearnPressImporter.php:103
#: app/Services/Integrations/PMPro/PMProImporter.php:108
#: app/Services/Integrations/LearnDash/LearnDashImporter.php:137
#: app/Services/Integrations/WishlistMember/WishlistMemberImporter.php:109
#: app/Services/Integrations/TutorLms/TutorImporter.php:109
#: app/Services/Integrations/MemberPress/MemberPressImporter.php:100
msgid "Import by"
msgstr "Importa per"

#: app/Services/Integrations/LifterLms/LifterImporter.php:140
#: app/Services/Integrations/LearnDash/LearnDashImporter.php:143
#: app/Services/Integrations/TutorLms/TutorImporter.php:115
msgid "Import By Courses"
msgstr "Importa per corsi"

#: app/Services/Integrations/LearnDash/LearnDashImporter.php:147
#: app/Services/Integrations/BuddyPress/BbImporter.php:165
msgid "Import By Member Groups"
msgstr "Importa per gruppi di membri"

#: app/Services/Integrations/BuddyPress/BbImporter.php:161
msgid "Import By Member Type"
msgstr "Importa per tipo di membro"

#: app/Services/Integrations/RCP/RCPImporter.php:116
#: app/Services/Integrations/PMPro/PMProImporter.php:114
#: app/Services/Integrations/WishlistMember/WishlistMemberImporter.php:115
#: app/Services/Integrations/MemberPress/MemberPressImporter.php:106
msgid "Import By Membership Level"
msgstr "Importa per livello di abbonamento"

#: app/Services/Integrations/LifterLms/LifterImporter.php:144
msgid "Import By Memberships"
msgstr "Importa per abbonamenti"

#: app/Http/Controllers/DynamicSegmentController.php:114
#: app/Http/Controllers/DynamicSegmentController.php:201
#: app/Http/Controllers/DynamicSegmentController.php:212
msgid "In"
msgstr "In"

#: app/Services/Integrations/AffiliateWP/DeepIntegration.php:83
msgid "Inactive"
msgstr "Inattivo"

#: app/Services/Integrations/WooCommerce/AdvancedReport.php:85
msgid "Individual Product Sales values are excluded Tax & Shipping amounts"
msgstr ""
"I valori delle vendite dei singoli prodotti escludono le tasse e le spese di "
"spedizione"

#: app/Http/Controllers/CampaignsProController.php:175
msgid "invalid selection"
msgstr "selezione non valida"

#: app/Hooks/Handlers/DataExporter.php:537
msgid "IP Address"
msgstr "Indirizzo IP"

#: app/Services/Integrations/AffiliateWP/DeepIntegration.php:58
msgid "Is Affiliate"
msgstr "È affiliato"

#: app/Services/Integrations/WooCommerce/AutomationConditions.php:73
msgid "Is guest?"
msgstr "È ospite?"

#: app/Modules/AbandonCart/AbandonCartModel.php:144
msgid "Item"
msgstr "Articolo"

#: app/Services/Integrations/LifterLms/MembershipEnrollTrigger.php:28
msgid "Joined Membership"
msgstr "Iscrizione all'abbonamento"

#: app/Http/Controllers/DynamicSegmentController.php:234
#: app/Http/Controllers/DynamicSegmentController.php:240
msgid "Keep days 0/Blank for disable"
msgstr "Imposta 0/giorni vuoto per disabilitare"

#: app/Services/Integrations/WooCommerce/WooOrderCompletedTrigger.php:90
#: app/Services/Integrations/WooCommerce/WooOrderSuccessBenchmark.php:69
#: app/Services/Integrations/WooCommerce/WooOrderRefundedTrigger.php:89
#: app/Services/Integrations/WooCommerce/WooOrderSuccessTrigger.php:92
#: app/Services/Integrations/Edd/EddPaymentSuccessTrigger.php:97
#: app/Services/Integrations/Edd/EddOrderSuccessBenchmark.php:68
#: app/Services/Integrations/Edd/EddRecurringPaymentTrigger.php:96
#: app/Services/Integrations/Edd/EddPaymentRefundTrigger.php:97
msgid "Keep it blank to run to any category products"
msgstr "Lascia vuoto per eseguire su qualsiasi categoria di prodotti"

#: app/Services/Integrations/LifterLms/CourseEnrollTrigger.php:90
#: app/Services/Integrations/LearnDash/CourseLeaveTrigger.php:90
#: app/Services/Integrations/LearnDash/CourseEnrollTrigger.php:89
#: app/Services/Integrations/TutorLms/CourseCompletedTrigger.php:85
#: app/Services/Integrations/TutorLms/CourseEnrollTrigger.php:86
msgid "Keep it blank to run to any Course Enrollment"
msgstr "Lascia vuoto per eseguire su qualsiasi iscrizione al corso"

#: app/Services/Integrations/LearnDash/CourseCompletedTrigger.php:86
msgid "Keep it blank to run to any courses"
msgstr "Lascia vuoto per eseguire su qualsiasi corso"

#: app/Services/Integrations/LearnDash/GroupEnrollTrigger.php:87
msgid "Keep it blank to run to any group Enrollment"
msgstr "Lascia vuoto per eseguire su qualsiasi iscrizione a gruppi"

#: app/Services/Integrations/LifterLms/LessonCompletedTrigger.php:90
#: app/Services/Integrations/LifterLms/CourseCompletedTrigger.php:86
msgid "Keep it blank to run to any Lesson"
msgstr "Lascia vuoto per eseguire su qualsiasi lezione"

#: app/Services/Integrations/TutorLms/LessonCompletedTrigger.php:85
msgid "Keep it blank to run to any Lesson Complete"
msgstr "Lascia vuoto per eseguire su qualsiasi completamento lezione"

#: app/Services/Integrations/RCP/RCPMembershipCancelTrigger.php:79
msgid "Keep it blank to run to any Level Cancellation"
msgstr "Lascia vuoto per eseguire su qualsiasi annullamento di livello"

#: app/Services/Integrations/PMPro/PMProPMProExpiryLevelTrigger.php:79
#: app/Services/Integrations/PMPro/PMProPMProCancelLevelTrigger.php:79
msgid "Keep it blank to run to any Level cancellation"
msgstr "Lascia vuoto per eseguire su qualsiasi annullamento di livello"

#: app/Services/Integrations/RCP/RCPMembershipTrigger.php:79
#: app/Services/Integrations/PMPro/PMProPMProMembershipTrigger.php:86
#: app/Services/Integrations/WishlistMember/WishlistMembershipTrigger.php:79
#: app/Services/Integrations/MemberPress/MembershipTrigger.php:79
msgid "Keep it blank to run to any Level Enrollment"
msgstr "Lascia vuoto per eseguire su qualsiasi iscrizione di livello"

#: app/Services/Integrations/RCP/RCPMembershipExpireTrigger.php:79
msgid "Keep it blank to run to any Level Expiration"
msgstr "Lascia vuoto per eseguire su qualsiasi scadenza di livello"

#: app/Services/Integrations/LifterLms/MembershipEnrollTrigger.php:92
msgid "Keep it blank to run to any Membership"
msgstr "Lascia vuoto per eseguire su qualsiasi abbonamento"

#: app/Services/Integrations/Edd/EddPaymentRefundTrigger.php:89
msgid "Keep it blank to run to any product"
msgstr "Lascia vuoto per eseguire su qualsiasi prodotto"

#: app/Services/Integrations/WooCommerce/WooOrderCompletedTrigger.php:82
#: app/Services/Integrations/WooCommerce/WooOrderSuccessBenchmark.php:61
#: app/Services/Integrations/WooCommerce/WooOrderSuccessTrigger.php:84
#: app/Services/Integrations/SureCart/SureCartPaymentSuccessBenchmark.php:56
#: app/Services/Integrations/SureCart/SureCartPaymentRefundTrigger.php:87
#: app/Services/Integrations/SureCart/SureCartPaymentSuccessTrigger.php:88
#: app/Services/Integrations/Edd/EddPaymentSuccessTrigger.php:89
#: app/Services/Integrations/Edd/EddRecurringExpired.php:87
#: app/Services/Integrations/Edd/EddOrderSuccessBenchmark.php:60
#: app/Services/Integrations/Edd/EddRecurringPaymentTrigger.php:88
#: app/Services/Integrations/Edd/EddSubscriptionActiveBenchmark.php:56
msgid "Keep it blank to run to any product purchase"
msgstr "Lascia vuoto per eseguire su qualsiasi acquisto di prodotto"

#: app/Services/Integrations/WooCommerce/WooOrderRefundedTrigger.php:81
msgid "Keep it blank to run to any product refund"
msgstr "Lascia vuoto per eseguire su qualsiasi rimborso di prodotto"

#: app/Services/Integrations/WooCommerce/WooOrderStatusChangeTrigger.php:95
msgid "Keep it blank to run to any product status changed"
msgstr "Lascia vuoto per eseguire su qualsiasi cambio di stato del prodotto"

#: app/Services/Integrations/MemberPress/SubscriptionExpiredTrigger.php:78
msgid "Keep it blank to run to any subscription cancellation"
msgstr "Lascia vuoto per eseguire su qualsiasi cancellazione di abbonamento"

#: app/Services/Integrations/WooCommerce/WooSubscriptionExpiredTrigger.php:57
msgid "Keep it blank to run to any subscription expire"
msgstr "Lascia vuoto per eseguire su qualsiasi scadenza di abbonamento"

#: app/Services/Integrations/WooCommerce/WooSubscriptionRenewalFailedTrigger.php:57
msgid "Keep it blank to run to any subscription renewal failed"
msgstr "Lascia vuoto per eseguire su qualsiasi rinnovo di abbonamento fallito"

#: app/Services/Integrations/WooCommerce/WooSubscriptionRenewalPaymentTrigger.php:57
msgid "Keep it blank to run to any subscription renewal payment"
msgstr ""
"Lascia vuoto per eseguire su qualsiasi pagamento di rinnovo di abbonamento"

#: app/Services/Integrations/WooCommerce/WooSubscriptionStartedTrigger.php:57
msgid "Keep it blank to run to any subscription starts"
msgstr "Lascia vuoto per eseguire su qualsiasi avvio di abbonamento"

#: app/Services/Integrations/LearnDash/TopicCompletedTrigger.php:101
msgid "Keep it blank to run to any Topic for that lesson"
msgstr "Lascia vuoto per eseguire su qualsiasi argomento per quella lezione"

#: app/Hooks/Handlers/DataExporter.php:543
#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:221
#: app/Services/Funnel/Conditions/FunnelCondition.php:153
msgid "Last Activity"
msgstr "Ultima attività"

#: app/Http/Controllers/DynamicSegmentController.php:192
msgid "Last Contact Activity"
msgstr "Ultima attività di contatto"

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:272
#: app/Services/Funnel/Conditions/FunnelCondition.php:204
msgid "Last Email Clicked"
msgstr "Ultima email cliccata"

#: app/Http/Controllers/DynamicSegmentController.php:238
msgid "Last Email Link Clicked"
msgstr "Ultimo link dell'email cliccato"

#: app/Http/Controllers/DynamicSegmentController.php:232
msgid "Last Email Open"
msgstr "Ultima email aperta"

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:277
#: app/Services/Funnel/Conditions/FunnelCondition.php:209
msgid "Last Email Open (approximately)"
msgstr "Ultima apertura dell'email (approssimativa)"

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:267
#: app/Services/Funnel/Conditions/FunnelCondition.php:199
msgid "Last Email Sent"
msgstr "Ultima email inviata"

#: app/Services/Integrations/LifterLms/AutomationConditions.php:59
#: app/Services/Integrations/LifterLms/DeepIntegration.php:88
#: app/Services/Integrations/LearnDash/AutomationConditions.php:63
#: app/Services/Integrations/LearnDash/DeepIntegration.php:78
#: app/Services/Integrations/TutorLms/DeepIntegration.php:416
msgid "Last Enrollment Date"
msgstr "Data dell'ultimo iscrizione"

#: app/Http/Controllers/DynamicSegmentController.php:147
#: app/Hooks/Handlers/DataExporter.php:528
#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:145
#: app/Services/Funnel/Conditions/FunnelCondition.php:77
msgid "Last Name"
msgstr "Cognome"

#: app/Services/Integrations/WooCommerce/DeepIntegration.php:555
#: app/Services/Integrations/Edd/DeepIntegration.php:227
msgid "Last Order"
msgstr "Ultimo ordine"

#: app/Services/Integrations/WooCommerce/AutomationConditions.php:90
#: app/Services/Integrations/WooCommerce/DeepIntegration.php:219
#: app/Services/Integrations/Edd/AutomationConditions.php:50
#: app/Services/Integrations/Edd/DeepIntegration.php:297
msgid "Last Order Date"
msgstr "Data dell'ultimo ordine"

#: app/Services/Integrations/AffiliateWP/DeepIntegration.php:107
msgid "Last Payout Date"
msgstr "Data dell'ultimo pagamento"

#: app/Services/Integrations/BuddyPress/BBInit.php:110
msgid "Last Update"
msgstr "Ultimo aggiornamento"

#: app/Services/Integrations/LearnDash/GroupEnrollTrigger.php:22
#: app/Services/Integrations/LearnDash/GroupEnrollTrigger.php:105
#: app/Services/Integrations/LearnDash/CourseLeaveTrigger.php:23
#: app/Services/Integrations/LearnDash/TopicCompletedTrigger.php:22
#: app/Services/Integrations/LearnDash/TopicCompletedTrigger.php:120
#: app/Services/Integrations/LearnDash/LessonCompletedTrigger.php:22
#: app/Services/Integrations/LearnDash/LessonCompletedTrigger.php:111
#: app/Services/Integrations/LearnDash/CourseCompletedTrigger.php:22
#: app/Services/Integrations/LearnDash/AutomationConditions.php:30
#: app/Services/Integrations/LearnDash/RemoveFromCourseAction.php:21
#: app/Services/Integrations/LearnDash/DeepIntegration.php:33
#: app/Services/Integrations/LearnDash/DeepIntegration.php:151
#: app/Services/Integrations/LearnDash/DeepIntegration.php:162
#: app/Services/Integrations/LearnDash/AddToGroupAction.php:21
#: app/Services/Integrations/LearnDash/CourseEnrollTrigger.php:22
#: app/Services/Integrations/LearnDash/AddToCourseAction.php:21
#: app/Services/Integrations/LearnDash/RemoveFromGroupAction.php:21
msgid "LearnDash"
msgstr "LearnDash"

#: app/Services/Integrations/LearnDash/AutomationConditions.php:30
msgid "LearnDash (Sync Required)"
msgstr "LearnDash (Sincronizzazione richiesta)"

#: app/Services/Integrations/LearnDash/AdvancedReport.php:57
msgid "LearnDash - Advanced Reports"
msgstr "LearnDash - Report avanzati"

#: app/Services/Integrations/LearnDash/LdInit.php:78
msgid "LearnDash Courses"
msgstr "Corsi LearnDash"

#: app/Services/Integrations/LearnDash/DeepIntegration.php:164
msgid "LearnDash students are not synced with FluentCRM yet."
msgstr "Gli studenti di LearnDash non sono ancora sincronizzati con FluentCRM."

#: app/Services/Integrations/LearnPress/LearnPressInit.php:58
msgid "LearnPress Courses"
msgstr "Corsi LearnPress"

#: app/Services/Integrations/WooCommerce/WooSmartCodeParse.php:356
msgid "Leave a review"
msgstr "Lascia una recensione"

#: app/Services/Funnel/Triggers/UserLoginTrigger.php:80
msgid "Leave blank to run for all user roles"
msgstr "Lascia vuoto per eseguire su tutti i ruoli utente"

#: app/Services/Integrations/LearnDash/LessonCompletedTrigger.php:94
msgid "Leave empty to target any lesson of this course"
msgstr "Lascia vuoto per includere qualsiasi lezione di questo corso"

#: app/Services/Integrations/LearnDash/CourseLeaveTrigger.php:42
msgid "Leave from a Course"
msgstr "Abbandona un corso"

#: app/Services/Integrations/LifterLms/LessonCompletedTrigger.php:28
#: app/Services/Integrations/LearnDash/LessonCompletedTrigger.php:23
#: app/Services/Integrations/TutorLms/LessonCompletedTrigger.php:24
msgid "Lesson Completed"
msgstr "Lezione completata"

#: app/Services/Integrations/Edd/EddLicenseExpiredTrigger.php:24
msgid "License Expired"
msgstr "Licenza scaduta"

#: app/Services/Integrations/Edd/EddLicenseExpiredTrigger.php:39
msgid "License Expired in EDD"
msgstr "Licenza scaduta in EDD"

#: app/Services/Integrations/WooCommerce/DeepIntegration.php:584
#: app/Services/Integrations/Edd/DeepIntegration.php:254
msgid "Lifetime Value"
msgstr "Valore a vita"

#: app/Services/Integrations/LifterLms/LessonCompletedTrigger.php:27
#: app/Services/Integrations/LifterLms/LessonCompletedTrigger.php:108
#: app/Services/Integrations/LifterLms/CourseCompletedTrigger.php:23
#: app/Services/Integrations/LifterLms/CourseCompletedTrigger.php:104
#: app/Services/Integrations/LifterLms/RemoveFromMembershipAction.php:21
#: app/Services/Integrations/LifterLms/AutomationConditions.php:27
#: app/Services/Integrations/LifterLms/RemoveFromCourseAction.php:21
#: app/Services/Integrations/LifterLms/DeepIntegration.php:59
#: app/Services/Integrations/LifterLms/DeepIntegration.php:83
#: app/Services/Integrations/LifterLms/DeepIntegration.php:157
#: app/Services/Integrations/LifterLms/Helper.php:192
#: app/Services/Integrations/LifterLms/CourseEnrollTrigger.php:27
#: app/Services/Integrations/LifterLms/CourseEnrollTrigger.php:108
#: app/Services/Integrations/LifterLms/AddToMembershipAction.php:21
#: app/Services/Integrations/LifterLms/AddToCourseAction.php:22
#: app/Services/Integrations/LifterLms/MembershipEnrollTrigger.php:27
#: app/Services/Integrations/LifterLms/MembershipEnrollTrigger.php:110
msgid "LifterLMS"
msgstr "LifterLMS"

#: app/Services/Integrations/LifterLms/AutomationConditions.php:27
msgid "LifterLMS (Sync Required)"
msgstr "LifterLMS (Sincronizzazione richiesta)"

#: app/Services/Integrations/LifterLms/AdvancedReport.php:57
msgid "LifterLMS - Advanced Reports"
msgstr "LifterLMS - Report avanzati"

#: app/Services/Integrations/LifterLms/LifterInit.php:76
msgid "LifterLMS Courses"
msgstr "Corsi LifterLMS"

#: app/Services/Integrations/LifterLms/DeepIntegration.php:159
msgid "LifterLMS students are not synced with FluentCRM yet."
msgstr "Gli studenti di LifterLMS non sono ancora sincronizzati con FluentCRM."

#: app/Services/Funnel/Benchmarks/LinkClickBenchmark.php:35
#: app/Services/Funnel/Benchmarks/LinkClickBenchmark.php:51
msgid "Link Click"
msgstr "Click sul link"

#: app/Services/Integrations/CRM/AdvancedReport.php:48
msgid "Link Clicks Stats"
msgstr "Statistiche click sui link"

#: app/Http/Controllers/CampaignsProController.php:125
msgid "Links are required"
msgstr "I link sono obbligatori"

#: app/Services/Integrations/CRM/ListAppliedTrigger.php:28
#: app/Services/Integrations/CRM/ListAppliedTrigger.php:45
msgid "List Applied"
msgstr "Lista applicata"

#: app/Services/Integrations/CRM/RemoveFromListTrigger.php:29
msgid "List Removed"
msgstr "Lista rimossa"

#: app/Http/Controllers/DynamicSegmentController.php:210
#: app/Hooks/Handlers/DataExporter.php:546
#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:245
#: app/Services/Funnel/Conditions/FunnelCondition.php:177
msgid "Lists"
msgstr "Liste"

#: app/Modules/AbandonCart/AbandonCartController.php:117
msgid "Lost Revenue"
msgstr "Entrate perse"

#: app/Http/Controllers/ManagerController.php:84
msgid "Manager has been added"
msgstr "Il manager è stato aggiunto"

#: app/Http/Controllers/ManagerController.php:144
msgid "Manager has been removed"
msgstr "Il manager è stato rimosso"

#: app/Http/Controllers/ManagerController.php:124
msgid "Manager has been updated"
msgstr "Il manager è stato aggiornato"

#: app/Http/Controllers/DynamicSegmentController.php:247
msgid "Match Any One Condition"
msgstr "Corrisponde a una condizione qualsiasi"

#: app/Http/Controllers/DynamicSegmentController.php:246
msgid "Match Both Open and Click Condition"
msgstr "Corrisponde sia alla condizione di apertura che di click"

#: app/Http/Controllers/DynamicSegmentController.php:243
msgid "Match Type"
msgstr "Tipo di corrispondenza"

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:73
#: app/Services/Funnel/Triggers/TrackingEventRecordedTrigger.php:90
#: app/Services/Funnel/Conditions/FunnelCondition.php:54
msgid "Match Value"
msgstr "Valore di corrispondenza"

#: app/Services/Integrations/BuddyPress/BBInit.php:86
#: app/Services/Integrations/BuddyPress/BBInit.php:142
msgid "Member"
msgstr "Membro"

#: app/Services/Integrations/MemberPress/MembershipTrigger.php:23
#: app/Services/Integrations/MemberPress/MembershipTrigger.php:104
#: app/Services/Integrations/MemberPress/SubscriptionExpiredTrigger.php:23
#: app/Services/Integrations/MemberPress/SubscriptionExpiredTrigger.php:108
msgid "MemberPress"
msgstr "MemberPress"

#: app/Services/Integrations/RCP/RCPMembershipCancelTrigger.php:24
msgid "Membership cancelled"
msgstr "Abbonamento annullato"

#: app/Services/Integrations/RCP/RCPMembershipTrigger.php:24
#: app/Services/Integrations/PMPro/PMProPMProMembershipTrigger.php:24
#: app/Services/Integrations/WishlistMember/WishlistMembershipTrigger.php:24
#: app/Services/Integrations/MemberPress/MembershipTrigger.php:24
msgid "Membership Enrolled"
msgstr "Iscrizione all'abbonamento"

#: app/Services/Integrations/RCP/RCPMembershipExpireTrigger.php:24
msgid "Membership expired"
msgstr "Abbonamento scaduto"

#: app/Services/Integrations/LearnDash/AdvancedReport.php:46
msgid "Membership Groups"
msgstr "Gruppi di abbonamento"

#: app/Services/Integrations/RCP/RCPMembershipCancelTrigger.php:40
msgid "Membership is cancelled"
msgstr "L'abbonamento è annullato"

#: app/Services/Integrations/RCP/RCPMembershipExpireTrigger.php:40
msgid "Membership is expired"
msgstr "L'abbonamento è scaduto"

#: app/Services/Integrations/RCP/AutomationConditions.php:24
#: app/Services/Integrations/PMPro/AutomationConditions.php:24
#: app/Services/Integrations/WishlistMember/AutomationConditions.php:24
msgid "Membership Level"
msgstr "Livello di abbonamento"

#: app/Services/Integrations/PMPro/PMProPMProCancelLevelTrigger.php:24
#: app/Services/Integrations/PMPro/PMProPMProCancelLevelTrigger.php:40
msgid "Membership Level Cancelled"
msgstr "Livello di abbonamento annullato"

#: app/Services/Integrations/PMPro/PMProPMProExpiryLevelTrigger.php:40
msgid "Membership Level Expiration"
msgstr "Scadenza del livello di abbonamento"

#: app/Services/Integrations/PMPro/PMProPMProExpiryLevelTrigger.php:24
msgid "Membership Level Expired"
msgstr "Livello di abbonamento scaduto"

#: app/Services/Integrations/LifterLms/AdvancedReport.php:46
msgid "Memberships"
msgstr "Abbonamenti"

#: app/Services/Funnel/Actions/UpdateUserMetaAction.php:49
#: app/Services/Funnel/Actions/UserRegistrationAction.php:82
msgid "Meta key"
msgstr "Chiave meta"

#: app/Services/Funnel/Actions/UpdateUserMetaAction.php:48
#: app/Services/Funnel/Actions/UserRegistrationAction.php:81
msgid "Meta Value"
msgstr "Valore meta"

#: app/Services/Funnel/Triggers/TrackingEventRecordedTrigger.php:60
msgid "Minimum Event Count"
msgstr "Numero minimo di eventi"

#: app/Services/Funnel/Triggers/TrackingEventRecordedTrigger.php:58
msgid "Minimum Occurrence Count of the selected event"
msgstr "Numero minimo di occorrenze per l'evento selezionato"

#: app/Services/Integrations/BuddyPress/BBInit.php:93
#: app/Services/Integrations/BuddyPress/BBInit.php:149
msgid "Moderator"
msgstr "Moderatore"

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:208
#: app/Services/Funnel/Conditions/FunnelCondition.php:140
msgid "Name Prefix (Title)"
msgstr "Prefisso nome (Titolo)"

#: app/Services/Integrations/AffiliateWP/AffiliateWPAffActiveTrigger.php:63
msgid "New Affiliate Approved/Active Register"
msgstr "Nuovo affiliato approvato/registrazione attiva"

#: app/Services/Integrations/AffiliateWP/AffiliateWPAffActiveTrigger.php:48
msgid "New Affiliate Joined"
msgstr "Nuovo affiliato unito"

#: app/Services/Integrations/Edd/EddPaymentSuccessTrigger.php:40
msgid "New Edd Order (paid) has been places"
msgstr "Nuovo ordine Edd (pagato) è stato effettuato"

#: app/Services/Integrations/WooCommerce/WooOrderSuccessTrigger.php:26
msgid "New Order (Processing)"
msgstr "Nuovo ordine (In lavorazione)"

#: app/Services/Integrations/Edd/EddOrderSuccessBenchmark.php:24
msgid "New Order Success"
msgstr "Nuovo ordine completato"

#: app/Services/Integrations/Edd/EddOrderSuccessBenchmark.php:51
msgid "New Order Success in EDD"
msgstr "Nuovo ordine completato in EDD"

#: app/Hooks/Handlers/RecurringCampaignHandler.php:232
#, php-format
msgid "New recurring email has been created in your site %s"
msgstr "È stata creata una nuova email ricorrente sul tuo sito %s"

#: app/Services/Integrations/SureCart/SureCartPaymentSuccessTrigger.php:41
msgid "New SureCart Order (paid) has been places"
msgstr "Nuovo ordine SureCart (pagato) è stato effettuato"

#: app/Services/Integrations/WooCommerce/WooOrderSuccessTrigger.php:42
msgid "New WooCommerce Order (Processing) has been places"
msgstr "Nuovo ordine WooCommerce (In lavorazione) è stato effettuato"

#: app/Services/Integrations/RCP/RCPImporter.php:140
#: app/Services/Integrations/LifterLms/LifterImporter.php:180
#: app/Services/Integrations/WooCommerce/WooImporter.php:166
#: app/Services/Integrations/LearnPress/LearnPressImporter.php:130
#: app/Services/Integrations/PMPro/PMProImporter.php:138
#: app/Services/Integrations/LearnDash/LearnDashImporter.php:183
#: app/Services/Integrations/BuddyPress/BbImporter.php:201
#: app/Services/Integrations/Edd/EddImporter.php:160
#: app/Services/Integrations/WishlistMember/WishlistMemberImporter.php:136
#: app/Services/Integrations/TutorLms/TutorImporter.php:136
#: app/Services/Integrations/MemberPress/MemberPressImporter.php:130
#: app/Services/Integrations/AffiliateWP/DeepIntegration.php:228
msgid "Next [Review Data]"
msgstr "Successivo [Rivedi dati]"

#: app/Services/Integrations/AffiliateWP/DeepIntegration.php:62
msgid "No"
msgstr "No"

#: app/Hooks/Handlers/Cleanup.php:84
msgid "No action found to sync"
msgstr "Nessuna azione trovata per la sincronizzazione"

#: app/Modules/AbandonCart/AbandonCartController.php:70
msgid "No carts selected to delete"
msgstr "Nessun carrello selezionato per la cancellazione"

#: app/Services/Integrations/LifterLms/LifterInit.php:81
#: app/Services/Integrations/LifterLms/LifterInit.php:94
#: app/Services/Integrations/LifterLms/LifterInit.php:101
#: app/Services/Integrations/LearnPress/LearnPressInit.php:63
#: app/Services/Integrations/LearnPress/LearnPressInit.php:76
#: app/Services/Integrations/LearnDash/LdInit.php:83
#: app/Services/Integrations/LearnDash/LdInit.php:92
#: app/Services/Integrations/TutorLms/TutorLmsInit.php:69
#: app/Services/Integrations/TutorLms/TutorLmsInit.php:76
msgid "No enrolled courses found for this contact"
msgstr "Nessun corso iscritto trovato per questo contatto"

#: app/Services/Funnel/Actions/EndFunnel.php:22
msgid "No further action will run once a contact hit this point"
msgstr ""
"Nessuna ulteriore azione verrà eseguita una volta che un contatto ha "
"raggiunto questo punto"

#: app/Services/Integrations/BuddyPress/BBInit.php:68
#: app/Services/Integrations/BuddyPress/BBInit.php:80
msgid "No groups found for this contact"
msgstr "Nessun gruppo trovato per questo contatto"

#: app/Services/Funnel/Actions/HTTPSendDataAction.php:129
msgid "No Headers"
msgstr "Nessuna intestazione"

#: app/Hooks/Handlers/CampaignArchiveFront.php:39
msgid "No published email newsletter found"
msgstr "Nessuna newsletter pubblicata trovata"

#: app/Http/Controllers/SequenceController.php:210
msgid "No Subscribers found based on your selection"
msgstr "Nessun iscritto trovato in base alla tua selezione"

#: app/Services/Funnel/Actions/HTTPSendDataAction.php:194
msgid "No valid body data found"
msgstr "Nessun dato valido nel corpo trovato"

#: app/Services/Integrations/BuddyPress/BBInit.php:100
msgid "Not Confirmed"
msgstr "Non confermato"

#: app/Http/Controllers/DynamicSegmentController.php:111
msgid "Not Contains"
msgstr "Non contiene"

#: app/Http/Controllers/DynamicSegmentController.php:109
msgid "Not Equal"
msgstr "Non uguale"

#: app/Http/Controllers/DynamicSegmentController.php:115
#: app/Http/Controllers/DynamicSegmentController.php:202
#: app/Http/Controllers/DynamicSegmentController.php:213
msgid "Not In"
msgstr "Non in"

#: app/Services/Integrations/WooCommerce/AddOrderNoteAction.php:42
msgid "Note to Customer"
msgstr "Nota al cliente"

#: app/Services/Integrations/WooCommerce/AdvancedReport.php:64
#: app/Services/Integrations/Edd/AdvancedReport.php:56
msgid "Onetime Items"
msgstr "Articoli unici"

#: app/Services/Integrations/ElementorFormIntegration/FormWidget.php:91
msgid "Only add new contacts, don't update existing ones."
msgstr "Aggiungi solo nuovi contatti, non aggiornare quelli esistenti."

#: app/Services/Integrations/WooCommerce/Helper.php:55
#: app/Services/Integrations/Edd/Helper.php:58
msgid "Only for first purchase"
msgstr "Solo per il primo acquisto"

#: app/Services/Funnel/Actions/UserRoleRemoveAction.php:43
msgid ""
"Only if user is not Administrator Role then the selected role will be "
"applied. After removing the role, if user does not have any role then "
"subscriber role will be added."
msgstr ""
"Solo se l'utente non ha il ruolo di Amministratore, il ruolo selezionato "
"verrà applicato. Dopo aver rimosso il ruolo, se l'utente non ha alcun ruolo "
"verrà aggiunto il ruolo di iscritto."

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:87
msgid "Only run this automation for subscribed contacts"
msgstr "Esegui questa automazione solo per i contatti iscritti"

#: app/Modules/AbandonCart/AbandonCartController.php:127
msgid "Optout Revenue"
msgstr "Entrate da opt-out"

#: app/Services/Integrations/WooCommerce/WooOrderCompletedTrigger.php:88
#: app/Services/Integrations/WooCommerce/WooOrderSuccessBenchmark.php:67
#: app/Services/Integrations/WooCommerce/WooOrderRefundedTrigger.php:87
#: app/Services/Integrations/WooCommerce/WooOrderSuccessTrigger.php:90
#: app/Services/Integrations/Edd/EddPaymentSuccessTrigger.php:95
#: app/Services/Integrations/Edd/EddRecurringPaymentTrigger.php:94
#: app/Services/Integrations/Edd/EddPaymentRefundTrigger.php:95
msgid "OR Target Product Categories"
msgstr "Oppure categorie di prodotto target"

#: app/Services/Integrations/Edd/EddOrderSuccessBenchmark.php:66
msgid "Or Target Product Categories"
msgstr "Oppure categorie di prodotto target"

#: app/Services/Integrations/WooCommerce/WooOrderCompletedTrigger.php:24
msgid "Order Completed"
msgstr "Ordine completato"

#: app/Services/Integrations/WooCommerce/DeepIntegration.php:572
#: app/Services/Integrations/Edd/DeepIntegration.php:242
msgid "Order Count"
msgstr "Conteggio ordini"

#: app/Services/Integrations/WooCommerce/AddOrderNoteAction.php:53
msgid "Order Note"
msgstr "Nota dell'ordine"

#: app/Services/Integrations/WooCommerce/AddOrderNoteAction.php:58
msgid "Order Note Type"
msgstr "Tipo di nota dell'ordine"

#: app/Services/Integrations/SureCart/SureCartPaymentSuccessBenchmark.php:24
#: app/Services/Integrations/SureCart/SureCartPaymentSuccessBenchmark.php:47
msgid "Order Received in SureCart"
msgstr "Ordine ricevuto in SureCart"

#: app/Services/Integrations/WooCommerce/WooOrderSuccessBenchmark.php:25
#: app/Services/Integrations/WooCommerce/WooOrderSuccessBenchmark.php:52
msgid "Order Received in WooCommerce"
msgstr "Ordine ricevuto in WooCommerce"

#: app/Services/Integrations/WooCommerce/WooOrderRefundedTrigger.php:24
msgid "Order Refunded"
msgstr "Ordine rimborsato"

#: app/Services/Integrations/WooCommerce/AutomationConditions.php:207
msgid "Order Status"
msgstr "Stato dell'ordine"

#: app/Services/Integrations/WooCommerce/WooOrderStatusChangeTrigger.php:24
msgid "Order Status Changed"
msgstr "Stato dell'ordine modificato"

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:339
#: app/Services/Funnel/Conditions/FunnelCondition.php:272
msgid "Other"
msgstr "Altro"

#: app/Services/Funnel/Actions/HTTPSendDataAction.php:27
msgid "Outgoing Webhook"
msgstr "Webhook in uscita"

#: app/Services/Integrations/Edd/EddCommerceHelper.php:107
msgid "Overall Sales"
msgstr "Vendite complessive"

#: app/Services/Integrations/Edd/EddCommerceHelper.php:30
msgid "Paid Customers"
msgstr "Clienti paganti"

#: app/Services/DynamicSegments/PMProMembersSegment.php:21
msgid "Paid Membership Members"
msgstr "Membri con abbonamento a pagamento"

#: app/Services/DynamicSegments/PMProMembersSegment.php:22
msgid ""
"Paid Membership Members customers who are also in the contact list as "
"subscribed"
msgstr ""
"Clienti con abbonamento a pagamento presenti anche nella lista contatti come "
"iscritti"

#: app/Services/Integrations/PMPro/AutomationConditions.php:19
msgid "Paid Membership Pro"
msgstr "Abbonamento a pagamento Pro"

#: app/Services/Integrations/PMPro/PMProPMProMembershipTrigger.php:23
#: app/Services/Integrations/PMPro/PMProPMProExpiryLevelTrigger.php:23
#: app/Services/Integrations/PMPro/PMProPMProCancelLevelTrigger.php:23
msgid "Paid Memberships Pro"
msgstr "Abbonamenti a pagamento Pro"

#: app/Services/Funnel/Actions/UserRegistrationAction.php:56
msgid "Password"
msgstr "Password"

#: app/Services/Funnel/Benchmarks/LinkClickBenchmark.php:60
msgid ""
"Paste this link in any email or page. When a contact click this link then it "
"will be recorded and redirect to the url as provided bellow."
msgstr ""
"Incolla questo link in qualsiasi email o pagina. Quando un contatto clicca "
"su questo link, sarà registrato e reindirizzato all'URL fornito."

#: app/Services/Funnel/Actions/RemoveFromFunnelAction.php:24
msgid "Pause/Cancel another automation for contact"
msgstr "Metti in pausa/annulla un'altra automazione per il contatto"

#: app/Services/Integrations/WooCommerce/AutomationConditions.php:201
msgid "Payment Gateway"
msgstr "Gateway di pagamento"

#: app/Services/Integrations/Edd/EddPaymentRefundTrigger.php:25
msgid "Payment Refunded"
msgstr "Pagamento rimborsato"

#: app/Services/Integrations/Edd/EddPaymentRefundTrigger.php:40
msgid "Payment Refunded in EDD"
msgstr "Pagamento rimborsato in EDD"

#: app/Services/Integrations/AffiliateWP/DeepIntegration.php:84
msgid "Pending"
msgstr "In sospeso"

#: app/Hooks/Handlers/DataExporter.php:538
#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:189
#: app/Services/Funnel/Conditions/FunnelCondition.php:121
msgid "Phone"
msgstr "Telefono"

#: app/Services/Integrations/BuddyPress/BBMemberType.php:231
msgid ""
"Please add at least one member to this Member type to add FluentCRM Tag "
"Settings"
msgstr ""
"Per favore aggiungi almeno un membro a questo tipo di membro per aggiungere "
"le impostazioni dei tag di FluentCRM"

#: app/Services/Integrations/LifterLms/AdvancedReport.php:63
#: app/Services/Integrations/WooCommerce/AdvancedReport.php:92
#: app/Services/Integrations/LearnDash/AdvancedReport.php:63
#: app/Services/Integrations/Edd/AdvancedReport.php:94
#: app/Services/Integrations/TutorLms/AdvancedReport.php:60
msgid "Please enable data sync first from FluentCRM"
msgstr "Abilita prima la sincronizzazione dei dati da FluentCRM"

#: app/Services/Funnel/Actions/HTTPSendDataAction.php:115
msgid "Please map the data for custom sending data type"
msgstr "Mappa i dati per il tipo di dati di invio personalizzato"

#: app/Services/Funnel/Actions/HTTPSendDataAction.php:144
msgid "Please map the data for request headers"
msgstr "Mappa i dati per le intestazioni della richiesta"

#: app/Services/Integrations/LifterLms/LifterImporter.php:149
#: app/Services/Integrations/LearnPress/LearnPressImporter.php:114
#: app/Services/Integrations/LearnDash/LearnDashImporter.php:152
#: app/Services/Integrations/TutorLms/TutorImporter.php:120
msgid "Please map your Courses and associate FluentCRM Tags"
msgstr "Mappa i tuoi corsi e associa i tag FluentCRM"

#: app/Services/Integrations/BuddyPress/BbImporter.php:185
msgid "Please map your Group and associate FluentCRM Tags"
msgstr "Mappa il tuo gruppo e associa i tag FluentCRM"

#: app/Services/Integrations/LifterLms/LifterImporter.php:164
#: app/Services/Integrations/LearnDash/LearnDashImporter.php:167
msgid "Please map your LearnDash Group and associate FluentCRM Tags"
msgstr "Mappa il tuo gruppo LearnDash e associa i tag FluentCRM"

#: app/Services/Integrations/BuddyPress/BbImporter.php:170
msgid "Please map your Member Type and associate FluentCRM Tags"
msgstr "Mappa il tuo tipo di membro e associa i tag FluentCRM"

#: app/Services/Integrations/RCP/RCPImporter.php:121
#: app/Services/Integrations/PMPro/PMProImporter.php:119
#: app/Services/Integrations/WishlistMember/WishlistMemberImporter.php:120
#: app/Services/Integrations/MemberPress/MemberPressImporter.php:111
msgid "Please map your Membership Levels and associate FluentCRM Tags"
msgstr "Mappa i tuoi livelli di abbonamento e associa i tag FluentCRM"

#: app/Services/Integrations/WooCommerce/WooImporter.php:135
#: app/Services/Integrations/Edd/EddImporter.php:129
msgid "Please map your Product and associate FluentCRM Tags"
msgstr "Mappa il tuo prodotto e associa i tag FluentCRM"

#: app/Services/Funnel/Triggers/TrackingEventRecordedTrigger.php:64
msgid ""
"Please note, this trigger will start if the contact is in subscribed status. "
"Otherwise, it will skip this automation."
msgstr ""
"Nota, questo trigger inizierà solo se il contatto è in stato di iscrizione. "
"Altrimenti, salterà questa automazione."

#: app/Http/Controllers/DynamicSegmentController.php:289
#: app/Http/Controllers/DynamicSegmentController.php:333
msgid "Please provide segment title"
msgstr "Fornisci il titolo del segmento"

#: app/Services/Funnel/Actions/UpdateUserMetaAction.php:50
msgid "Please provide the meta key and meta value. You can use smart tags too"
msgstr "Fornisci la chiave meta e il valore meta. Puoi usare anche i tag smart"

#: app/Services/Funnel/Benchmarks/LinkClickBenchmark.php:67
msgid "Please provide the url to where the contact will be redirected"
msgstr "Fornisci l'URL a cui sarà reindirizzato il contatto"

#: app/Services/Funnel/Actions/HTTPSendDataAction.php:78
msgid "Please provide valid URL in where you want to send the data"
msgstr "Fornisci un URL valido a cui vuoi inviare i dati"

#: app/Services/Funnel/Benchmarks/LinkClickBenchmark.php:56
msgid "Please save to get the sharable link"
msgstr "Salva per ottenere il link condivisibile"

#: app/Services/Integrations/LearnPress/LearnPressImporter.php:104
#: app/Services/Integrations/TutorLms/TutorImporter.php:110
msgid "Please select import by course enrollment"
msgstr "Seleziona importazione per iscrizione al corso"

#: app/Services/Integrations/LearnDash/LearnDashImporter.php:138
msgid "Please select import by group or course enrollment"
msgstr "Seleziona importazione per iscrizione a gruppo o corso"

#: app/Services/Integrations/RCP/RCPImporter.php:111
#: app/Services/Integrations/PMPro/PMProImporter.php:109
#: app/Services/Integrations/WishlistMember/WishlistMemberImporter.php:110
#: app/Services/Integrations/MemberPress/MemberPressImporter.php:101
msgid "Please select import by Membership Level"
msgstr "Seleziona importazione per livello di abbonamento"

#: app/Services/Integrations/LifterLms/LifterImporter.php:135
msgid "Please select import by Membership or course enrollment"
msgstr "Seleziona importazione per abbonamento o iscrizione al corso"

#: app/Services/Integrations/BuddyPress/BbImporter.php:156
msgid "Please select Member Type or Member group that you want to import"
msgstr "Seleziona tipo di membro o gruppo di membri da importare"

#: app/Services/Funnel/Benchmarks/EmailSequenceCompletedBenchmark.php:46
msgid "Please select the email sequences that need to be watched for completed"
msgstr "Seleziona le sequenze email da monitorare per completamento"

#: app/Services/Integrations/LifterLms/LessonCompletedTrigger.php:81
#: app/Services/Integrations/LifterLms/CourseCompletedTrigger.php:77
#: app/Services/Integrations/LifterLms/CourseEnrollTrigger.php:82
#: app/Services/Integrations/LifterLms/MembershipEnrollTrigger.php:82
#: app/Services/Integrations/PMPro/PMProPMProMembershipTrigger.php:78
#: app/Services/Integrations/SureCart/SureCartPaymentRefundTrigger.php:78
#: app/Services/Integrations/SureCart/SureCartPaymentSuccessTrigger.php:79
#: app/Services/Integrations/LearnDash/GroupEnrollTrigger.php:77
#: app/Services/Integrations/LearnDash/CourseLeaveTrigger.php:80
#: app/Services/Integrations/LearnDash/TopicCompletedTrigger.php:77
#: app/Services/Integrations/LearnDash/LessonCompletedTrigger.php:77
#: app/Services/Integrations/LearnDash/CourseCompletedTrigger.php:76
#: app/Services/Integrations/LearnDash/CourseEnrollTrigger.php:79
#: app/Services/Integrations/Edd/EddPaymentSuccessTrigger.php:80
#: app/Services/Integrations/Edd/EddRecurringExpired.php:78
#: app/Services/Integrations/Edd/EddRecurringPaymentTrigger.php:79
#: app/Services/Integrations/Edd/EddPaymentRefundTrigger.php:80
#: app/Services/Integrations/TutorLms/LessonCompletedTrigger.php:77
#: app/Services/Integrations/TutorLms/CourseCompletedTrigger.php:77
#: app/Services/Integrations/TutorLms/CourseEnrollTrigger.php:78
#: app/Services/Integrations/AffiliateWP/AffiliateWPAffActiveTrigger.php:105
#: app/Services/Funnel/Triggers/UserLoginTrigger.php:70
msgid ""
"Please specify what will happen if the subscriber already exist in the "
"database"
msgstr "Specifica cosa accadrà se l'iscritto è già presente nel database"

#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:110
msgid ""
"Please specify which tags will be added/removed to the contact when purchase"
msgstr ""
"Specifica quali tag saranno aggiunti/rimossi al contatto quando acquista"

#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:144
msgid ""
"Please specify which tags will be added/removed to the contact when refunded"
msgstr ""
"Specifica quali tag saranno aggiunti/rimossi al contatto quando viene "
"rimborsato"

#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:181
msgid ""
"Please specify which tags will be added/removed to the contact when renewal "
"payment failed"
msgstr ""
"Specifica quali tag saranno aggiunti/rimossi al contatto quando il pagamento "
"di rinnovo fallisce"

#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:218
msgid ""
"Please specify which tags will be added/removed to the contact when "
"subscription is cancelled"
msgstr ""
"Specificare quali tag verranno aggiunti/rimossi al contatto quando "
"l'abbonamento viene annullato"

#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:255
msgid ""
"Please specify which tags will be added/removed to the contact when "
"subscription is expired"
msgstr ""
"Specificare quali tag verranno aggiunti/rimossi al contatto quando "
"l'abbonamento scade"

#: fluentcampaign-pro.php:31
msgid "Please update FluentCRM to latest version"
msgstr "Aggiorna FluentCRM all'ultima versione"

#: app/Services/Funnel/Actions/HTTPSendDataAction.php:65
msgid "POST Method"
msgstr "Metodo POST"

#: app/Hooks/Handlers/DataExporter.php:533
#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:175
#: app/Services/Funnel/Conditions/FunnelCondition.php:107
msgid "Postal Code"
msgstr "CAP"

#: app/Modules/AbandonCart/AbandonCartModel.php:146
msgid "Price"
msgstr "Prezzo"

#: app/Hooks/Handlers/DataExporter.php:549
msgid "Primary Company"
msgstr "Azienda principale"

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:36
msgid "Priority of this abandon cart automation trigger"
msgstr "Priorità di questo trigger di automazione carrello abbandonato"

#: app/Services/Integrations/WooCommerce/AddOrderNoteAction.php:38
msgid "Private Note"
msgstr "Nota privata"

#. Description of the plugin
msgid "Pro Email Automation and Integration Addon for FluentCRM"
msgstr "Addon di automazione email professionale e integrazione per FluentCRM"

#: app/Modules/AbandonCart/AbandonCartController.php:112
msgid "Processing Revenue"
msgstr "Entrate in elaborazione"

#: app/Services/Integrations/WooCommerce/WooSmartCodeParse.php:268
#: app/Services/Integrations/Edd/EddSmartCodeParse.php:308
msgid "Product"
msgstr "Prodotto"

#: app/Services/Integrations/WooCommerce/AutomationConditions.php:169
msgid "Products in Order"
msgstr "Prodotti nell'ordine"

#: app/Services/Integrations/LifterLms/LifterInit.php:131
#: app/Services/Integrations/TutorLms/TutorLmsInit.php:108
msgid "Progress"
msgstr "Progresso"

#: app/Services/Funnel/Actions/UpdateContactPropertyAction.php:48
msgid "Property Value"
msgstr "Valore della proprietà"

#: app/Services/Funnel/Actions/UserRegistrationAction.php:62
msgid "Provide Custom User Password"
msgstr "Fornisci una password personalizzata per l'utente"

#: app/Services/Integrations/WooCommerce/WooOrderCompletedTrigger.php:94
#: app/Services/Integrations/WooCommerce/WooOrderSuccessBenchmark.php:73
#: app/Services/Integrations/WooCommerce/WooOrderSuccessTrigger.php:96
#: app/Services/Integrations/Edd/EddPaymentSuccessTrigger.php:101
#: app/Services/Integrations/Edd/EddOrderSuccessBenchmark.php:72
#: app/Services/Integrations/Edd/EddPaymentRefundTrigger.php:101
msgid "Purchase Type"
msgstr "Tipo di acquisto"

#: app/Services/Integrations/WooCommerce/AutomationConditions.php:97
#: app/Services/Integrations/WooCommerce/DeepIntegration.php:255
#: app/Services/Integrations/Edd/AutomationConditions.php:62
#: app/Services/Integrations/Edd/DeepIntegration.php:318
msgid "Purchased Categories"
msgstr "Categorie acquistate"

#: app/Services/Integrations/WooCommerce/AutomationConditions.php:177
msgid "Purchased From Categories"
msgstr "Acquistato dalle categorie"

#: app/Services/Integrations/WooCommerce/AutomationConditions.php:35
#: app/Services/Integrations/WooCommerce/DeepIntegration.php:242
msgid "Purchased Product Variations"
msgstr "Varianti di prodotto acquistate"

#: app/Services/Integrations/WooCommerce/AutomationConditions.php:26
#: app/Services/Integrations/WooCommerce/DeepIntegration.php:115
#: app/Services/Integrations/WooCommerce/DeepIntegration.php:233
#: app/Services/Integrations/Edd/AutomationConditions.php:26
#: app/Services/Integrations/Edd/DeepIntegration.php:166
#: app/Services/Integrations/Edd/DeepIntegration.php:309
msgid "Purchased Products"
msgstr "Prodotti acquistati"

#: app/Services/Integrations/WooCommerce/AutomationConditions.php:107
#: app/Services/Integrations/WooCommerce/DeepIntegration.php:265
#: app/Services/Integrations/Edd/AutomationConditions.php:71
#: app/Services/Integrations/Edd/DeepIntegration.php:327
msgid "Purchased Tags"
msgstr "Tag acquistati"

#: app/Modules/AbandonCart/AbandonCartModel.php:145
msgid "Quantity"
msgstr "Quantità"

#: app/Services/Integrations/RCP/AutomationConditions.php:19
msgid "RCP"
msgstr "RCP"

#: app/Services/Funnel/Actions/AddEmailSequenceAction.php:49
msgid "Re-assign Sequence Emails?"
msgstr "Riassegnare le email della sequenza?"

#: app/Modules/AbandonCart/AbandonCartController.php:107
msgid "Recovered Revenue"
msgstr "Entrate recuperate"

#: app/Modules/AbandonCart/AbandonCartController.php:132
msgid "Recovery Rate"
msgstr "Tasso di recupero"

#: app/Services/Integrations/WooCommerce/AdvancedReport.php:61
#: app/Services/Integrations/Edd/AdvancedReport.php:53
msgid "Recurring (renew only)"
msgstr "Ricorrente (solo rinnovo)"

#: app/Http/Controllers/RecurringCampaignController.php:275
msgid "Recurring Email campaign has been deleted"
msgstr "Campagna email ricorrente eliminata"

#: app/Services/Integrations/Edd/EddRecurringExpired.php:24
#: app/Services/Integrations/Edd/EddRecurringExpired.php:40
msgid "Recurring Subscription Expired"
msgstr "Abbonamento ricorrente scaduto"

#: app/Services/Funnel/Benchmarks/LinkClickBenchmark.php:64
msgid "Redirect To"
msgstr "Reindirizza a"

#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:143
msgid "Refund Actions"
msgstr "Azioni di rimborso"

#: app/Services/Integrations/AffiliateWP/DeepIntegration.php:101
msgid "Registration Date"
msgstr "Data di registrazione"

#: app/Services/Funnel/Actions/HTTPSendDataAction.php:76
#: app/Services/Funnel/Actions/HTTPSendDataAction.php:77
msgid "Remote URL"
msgstr "URL remoto"

#: app/Services/Funnel/Actions/RemoveFromEmailSequenceAction.php:34
msgid "Remove Email Sequences"
msgstr "Rimuovi sequenze email"

#: app/Services/Integrations/LifterLms/RemoveFromCourseAction.php:22
#: app/Services/Integrations/LifterLms/RemoveFromCourseAction.php:34
#: app/Services/Integrations/TutorLms/RemoveFromCourseAction.php:22
#: app/Services/Integrations/TutorLms/RemoveFromCourseAction.php:34
msgid "Remove From a Course"
msgstr "Rimuovi da un corso"

#: app/Services/Integrations/LearnDash/RemoveFromGroupAction.php:34
msgid "Remove From a Group"
msgstr "Rimuovi da un gruppo"

#: app/Services/Integrations/LifterLms/RemoveFromMembershipAction.php:22
msgid "Remove From a LMS Membership"
msgstr "Rimuovi da un abbonamento LMS"

#: app/Services/Integrations/LifterLms/RemoveFromMembershipAction.php:34
msgid "Remove From a Membership"
msgstr "Rimuovi da un abbonamento"

#: app/Services/Integrations/CRM/RemoveFromCompanyTrigger.php:46
msgid "Remove From Company"
msgstr "Rimuovi dall'azienda"

#: app/Services/Integrations/LearnDash/RemoveFromCourseAction.php:22
#: app/Services/Integrations/LearnDash/RemoveFromCourseAction.php:36
msgid "Remove From Course"
msgstr "Rimuovi dal corso"

#: app/Services/Integrations/LearnDash/RemoveFromGroupAction.php:22
msgid "Remove From Group"
msgstr "Rimuovi dal gruppo"

#: app/Services/Integrations/CRM/RemoveFromListTrigger.php:46
msgid "Remove From List"
msgstr "Rimuovi dalla lista"

#: app/Services/Integrations/CRM/RemoveFromTagTrigger.php:46
msgid "Remove From Tags"
msgstr "Rimuovi dai tag"

#: app/Services/Integrations/Edd/EddMetaBoxes.php:121
msgid "Remove selected Tags on refund"
msgstr "Rimuovi i tag selezionati in caso di rimborso"

#: app/Services/Integrations/LifterLms/LifterInit.php:191
#: app/Services/Integrations/LifterLms/LifterInit.php:249
#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:127
#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:160
#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:199
#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:236
#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:273
msgid "Remove Tags"
msgstr "Rimuovi tag"

#: app/Services/Integrations/BuddyPress/Group.php:55
#: app/Services/Integrations/BuddyPress/BBMemberType.php:77
msgid "Remove Tags on leave defined in \"Apply Tags\""
msgstr "Rimuovi tag all'uscita definiti in \"Applica tag\""

#: app/Services/Integrations/Edd/EddMetaBoxes.php:68
msgid "Remove Tags on refund defined in \"Apply Tags\""
msgstr "Rimuovi tag in caso di rimborso definiti in \"Applica tag\""

#: app/Services/Integrations/LifterLms/RemoveFromCourseAction.php:23
#: app/Services/Integrations/LifterLms/RemoveFromCourseAction.php:35
#: app/Services/Integrations/LearnDash/RemoveFromCourseAction.php:23
#: app/Services/Integrations/LearnDash/RemoveFromCourseAction.php:37
#: app/Services/Integrations/TutorLms/RemoveFromCourseAction.php:23
#: app/Services/Integrations/TutorLms/RemoveFromCourseAction.php:35
msgid "Remove the contact from a specific LMS Course"
msgstr "Rimuovi il contatto da un corso specifico LMS"

#: app/Services/Integrations/LearnDash/RemoveFromGroupAction.php:23
#: app/Services/Integrations/LearnDash/RemoveFromGroupAction.php:35
msgid "Remove the contact from a specific LMS Group"
msgstr "Rimuovi il contatto da un gruppo specifico LMS"

#: app/Services/Integrations/LifterLms/RemoveFromMembershipAction.php:35
msgid "Remove the contact from a specific LMS Membership"
msgstr "Rimuovi il contatto da un abbonamento specifico LMS"

#: app/Services/Integrations/LifterLms/RemoveFromMembershipAction.php:23
msgid "Remove the contact from a specific LMS Membership Group"
msgstr "Rimuovi il contatto da un gruppo di abbonamento specifico LMS"

#: app/Services/Funnel/Actions/UserRoleRemoveAction.php:22
#: app/Services/Funnel/Actions/UserRoleRemoveAction.php:33
msgid "Remove the Selected Role of User"
msgstr "Rimuovi il ruolo selezionato dell'utente"

#: app/Services/Funnel/Actions/UserRoleRemoveAction.php:21
msgid "Remove WP User Role"
msgstr "Rimuovi il ruolo utente di WP"

#: app/Services/Integrations/WooCommerce/WooSubscriptionRenewalFailedTrigger.php:25
msgid "Renewal Payment Failed"
msgstr "Pagamento del rinnovo fallito"

#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:180
msgid "Renewal Payment Failed Actions"
msgstr "Azioni per il fallimento del pagamento del rinnovo"

#: app/Services/Integrations/WooCommerce/WooSubscriptionRenewalPaymentTrigger.php:25
#: app/Services/Integrations/Edd/EddRecurringPaymentTrigger.php:24
#: app/Services/Integrations/Edd/EddRecurringPaymentTrigger.php:40
msgid "Renewal Payment Received"
msgstr "Pagamento del rinnovo ricevuto"

#: app/Services/Integrations/WooCommerce/WooSubscriptionExpiredTrigger.php:25
#: app/Services/Integrations/WooCommerce/WooSubscriptionExpiredTrigger.php:71
msgid "Renewal Subscription Expired"
msgstr "Abbonamento di rinnovo scaduto"

#: app/Services/Integrations/WooCommerce/AdvancedReport.php:76
#: app/Services/Integrations/Edd/AdvancedReport.php:69
msgid "Renews"
msgstr "Rinnovi"

#: app/Services/Funnel/Actions/ChangeUserRoleAction.php:47
msgid "Replace Existing Role"
msgstr "Sostituisci il ruolo esistente"

#: app/Services/Funnel/Actions/ChangeUserRoleAction.php:48
msgid "Replace user role "
msgstr "Sostituisci il ruolo dell'utente"

#: app/Services/Funnel/Actions/HTTPSendDataAction.php:96
msgid "Request Body"
msgstr "Corpo della richiesta"

#: app/Services/Funnel/Actions/HTTPSendDataAction.php:109
msgid "Request Body Data"
msgstr "Dati del corpo della richiesta"

#: app/Services/Funnel/Actions/HTTPSendDataAction.php:82
msgid "Request Format"
msgstr "Formato della richiesta"

#: app/Services/Funnel/Actions/HTTPSendDataAction.php:125
msgid "Request Header"
msgstr "Intestazione della richiesta"

#: app/Services/Funnel/Actions/HTTPSendDataAction.php:138
msgid "Request Headers Data"
msgstr "Dati delle intestazioni della richiesta"

#: app/Services/FunnelMultiConditionTrait.php:20
#: app/Services/Integrations/RCP/RCPMembershipTrigger.php:84
#: app/Services/Integrations/RCP/RCPMembershipCancelTrigger.php:84
#: app/Services/Integrations/RCP/RCPMembershipExpireTrigger.php:84
#: app/Services/Integrations/LifterLms/LessonCompletedTrigger.php:95
#: app/Services/Integrations/LifterLms/CourseCompletedTrigger.php:91
#: app/Services/Integrations/LifterLms/CourseEnrollTrigger.php:95
#: app/Services/Integrations/LifterLms/MembershipEnrollTrigger.php:97
#: app/Services/Integrations/WooCommerce/WooSubscriptionStartedTrigger.php:62
#: app/Services/Integrations/WooCommerce/WooOrderCompletedTrigger.php:102
#: app/Services/Integrations/WooCommerce/WooOrderStatusChangeTrigger.php:112
#: app/Services/Integrations/WooCommerce/WooSubscriptionRenewalFailedTrigger.php:62
#: app/Services/Integrations/WooCommerce/WooOrderRefundedTrigger.php:94
#: app/Services/Integrations/WooCommerce/WooOrderSuccessTrigger.php:104
#: app/Services/Integrations/WooCommerce/WooSubscriptionExpiredTrigger.php:62
#: app/Services/Integrations/WooCommerce/WooSubscriptionRenewalPaymentTrigger.php:62
#: app/Services/Integrations/PMPro/PMProPMProMembershipTrigger.php:91
#: app/Services/Integrations/PMPro/PMProPMProExpiryLevelTrigger.php:84
#: app/Services/Integrations/PMPro/PMProPMProCancelLevelTrigger.php:84
#: app/Services/Integrations/SureCart/SureCartPaymentRefundTrigger.php:92
#: app/Services/Integrations/SureCart/SureCartPaymentSuccessTrigger.php:93
#: app/Services/Integrations/LearnDash/GroupEnrollTrigger.php:92
#: app/Services/Integrations/LearnDash/CourseLeaveTrigger.php:95
#: app/Services/Integrations/LearnDash/TopicCompletedTrigger.php:106
#: app/Services/Integrations/LearnDash/LessonCompletedTrigger.php:99
#: app/Services/Integrations/LearnDash/CourseCompletedTrigger.php:91
#: app/Services/Integrations/LearnDash/CourseEnrollTrigger.php:94
#: app/Services/Integrations/Edd/EddPaymentSuccessTrigger.php:109
#: app/Services/Integrations/Edd/EddLicenseExpiredTrigger.php:84
#: app/Services/Integrations/Edd/EddRecurringExpired.php:92
#: app/Services/Integrations/Edd/EddRecurringPaymentTrigger.php:101
#: app/Services/Integrations/Edd/EddPaymentRefundTrigger.php:109
#: app/Services/Integrations/WishlistMember/WishlistMembershipTrigger.php:84
#: app/Services/Integrations/TutorLms/LessonCompletedTrigger.php:90
#: app/Services/Integrations/TutorLms/CourseCompletedTrigger.php:90
#: app/Services/Integrations/TutorLms/CourseEnrollTrigger.php:91
#: app/Services/Integrations/MemberPress/MembershipTrigger.php:84
#: app/Services/Integrations/MemberPress/SubscriptionExpiredTrigger.php:83
#: app/Services/Integrations/AffiliateWP/AffiliateWPAffActiveTrigger.php:85
#: app/Services/Funnel/Triggers/TrackingEventRecordedTrigger.php:99
#: app/Services/Funnel/Triggers/UserLoginTrigger.php:85
msgid ""
"Restart the Automation Multiple times for a contact for this event. (Only "
"enable if you want to restart automation for the same contact)"
msgstr ""
"Riavvia l'automazione più volte per un contatto per questo evento. (Abilita "
"solo se desideri riavviare l'automazione per lo stesso contatto)"

#: app/Services/Funnel/Actions/AddEmailSequenceAction.php:50
msgid ""
"Restart the sequence emails if the contact already in the email sequence"
msgstr ""
"Riavvia le email della sequenza se il contatto è già nella sequenza email"

#: app/Services/Integrations/RCP/RCPMembershipTrigger.php:23
#: app/Services/Integrations/RCP/RCPMembershipCancelTrigger.php:23
#: app/Services/Integrations/RCP/RCPMembershipExpireTrigger.php:23
msgid "Restrict Content Pro"
msgstr "Restrict Content Pro"

#: app/Services/Integrations/CRM/ListAppliedTrigger.php:57
#: app/Services/Integrations/CRM/TagAppliedTrigger.php:57
#: app/Services/Integrations/CRM/CompanyAppliedTrigger.php:56
msgid "Run When"
msgstr "Esegui Quando"

#: app/Services/Integrations/WooCommerce/WooInit.php:114
msgid "Sales (This Month)"
msgstr "Vendite (Questo mese)"

#: app/Services/Integrations/WooCommerce/WooInit.php:110
msgid "Sales (Today)"
msgstr "Vendite (Oggi)"

#: app/Services/Funnel/Actions/SendCampaignEmailAction.php:35
msgid "Schedule Campaign Email"
msgstr "Pianifica Email di Campagna"

#: app/Http/Controllers/DynamicSegmentController.php:306
msgid "Segment has been created"
msgstr "Il segmento è stato creato"

#: app/Http/Controllers/DynamicSegmentController.php:352
msgid "Segment has been updated"
msgstr "Il segmento è stato aggiornato"

#: app/Http/Controllers/DynamicSegmentController.php:322
msgid "Segment successfully duplicated"
msgstr "Segmento duplicato con successo"

#: app/Services/Integrations/LifterLms/LifterImporter.php:151
#: app/Services/Integrations/LifterLms/LifterImporter.php:153
#: app/Services/Integrations/LearnPress/LearnPressImporter.php:116
#: app/Services/Integrations/LearnPress/LearnPressImporter.php:118
#: app/Services/Integrations/LearnDash/LearnDashImporter.php:154
#: app/Services/Integrations/LearnDash/LearnDashImporter.php:156
#: app/Services/Integrations/TutorLms/TutorImporter.php:122
#: app/Services/Integrations/TutorLms/TutorImporter.php:124
#, php-format
msgid "Select %s Course"
msgstr "Seleziona il corso %s"

#: app/Services/Integrations/LifterLms/LifterImporter.php:166
#: app/Services/Integrations/LearnDash/LearnDashImporter.php:169
#: app/Services/Integrations/LearnDash/LearnDashImporter.php:171
#: app/Services/Integrations/BuddyPress/BbImporter.php:187
#: app/Services/Integrations/BuddyPress/BbImporter.php:189
#, php-format
msgid "Select %s Group"
msgstr "Seleziona il gruppo %s"

#: app/Services/Integrations/BuddyPress/BbImporter.php:155
#, php-format
msgid "Select %s Import Type"
msgstr "Seleziona il tipo di importazione %s"

#: app/Services/Integrations/BuddyPress/BbImporter.php:172
#: app/Services/Integrations/BuddyPress/BbImporter.php:174
#, php-format
msgid "Select %s Member Type"
msgstr "Seleziona il tipo di membro %s"

#: app/Services/Integrations/RCP/RCPImporter.php:123
#: app/Services/Integrations/RCP/RCPImporter.php:125
#: app/Services/Integrations/LifterLms/LifterImporter.php:168
#: app/Services/Integrations/PMPro/PMProImporter.php:121
#: app/Services/Integrations/PMPro/PMProImporter.php:123
#: app/Services/Integrations/WishlistMember/WishlistMemberImporter.php:122
#: app/Services/Integrations/WishlistMember/WishlistMemberImporter.php:124
#: app/Services/Integrations/MemberPress/MemberPressImporter.php:113
#: app/Services/Integrations/MemberPress/MemberPressImporter.php:115
#, php-format
msgid "Select %s Membership"
msgstr "Seleziona l'abbonamento %s"

#: app/Services/Integrations/WooCommerce/WooImporter.php:137
#: app/Services/Integrations/WooCommerce/WooImporter.php:139
#: app/Services/Integrations/Edd/EddImporter.php:131
#: app/Services/Integrations/Edd/EddImporter.php:133
#, php-format
msgid "Select %s Product"
msgstr "Seleziona il prodotto %s"

#: app/Services/Integrations/LifterLms/RemoveFromCourseAction.php:42
#: app/Services/Integrations/TutorLms/RemoveFromCourseAction.php:42
msgid "Select a course that you want to remove from"
msgstr "Seleziona un corso da cui desideri rimuovere"

#: app/Services/Integrations/LifterLms/RemoveFromMembershipAction.php:42
msgid "Select a Membership Group that you want to remove from"
msgstr "Seleziona un gruppo di abbonamento da cui desideri rimuovere"

#: app/Services/Funnel/Actions/AddActivityAction.php:49
msgid "Select Activity Type"
msgstr "Seleziona il tipo di attività"

#: app/Services/Funnel/Actions/RemoveFromFunnelAction.php:42
msgid "Select Automations"
msgstr "Seleziona le automazioni"

#: app/Services/Funnel/Actions/RemoveFromFunnelAction.php:40
msgid "Select Automations that you want to cancel"
msgstr "Seleziona le automazioni che desideri annullare"

#: app/Services/Funnel/Actions/SendCampaignEmailAction.php:42
msgid "Select Campaign"
msgstr "Seleziona campagna"

#: app/Services/Funnel/Actions/SendCampaignEmailAction.php:43
msgid "Select Campaign Email"
msgstr "Seleziona email di campagna"

#: app/Services/Integrations/CRM/RemoveFromCompanyTrigger.php:53
#: app/Services/Integrations/CRM/CompanyAppliedTrigger.php:52
msgid "Select Companies"
msgstr "Seleziona le aziende"

#: app/Services/Integrations/CRM/RemoveFromCompanyTrigger.php:54
#: app/Services/Integrations/CRM/CompanyAppliedTrigger.php:53
msgid "Select Company"
msgstr "Seleziona azienda"

#: app/Http/Controllers/DynamicSegmentController.php:131
msgid ""
"Select conditions which will define this segment. All Conditions will be "
"applied to filter"
msgstr ""
"Seleziona le condizioni che definiranno questo segmento. Tutte le condizioni "
"verranno applicate al filtro"

#: app/Services/Integrations/LifterLms/RemoveFromCourseAction.php:43
#: app/Services/Integrations/LifterLms/AddToCourseAction.php:46
#: app/Services/Integrations/LearnDash/RemoveFromCourseAction.php:46
#: app/Services/Integrations/LearnDash/AddToCourseAction.php:46
#: app/Services/Integrations/TutorLms/RemoveFromCourseAction.php:43
#: app/Services/Integrations/TutorLms/AddToCourseAction.php:45
msgid "Select Course"
msgstr "Seleziona corso"

#: app/Services/Integrations/LifterLms/AddToCourseAction.php:45
#: app/Services/Integrations/LearnDash/RemoveFromCourseAction.php:45
#: app/Services/Integrations/LearnDash/AddToCourseAction.php:45
#: app/Services/Integrations/TutorLms/AddToCourseAction.php:44
msgid "Select Course to Enroll"
msgstr "Seleziona il corso per l'iscrizione"

#: app/Services/Integrations/LearnDash/TopicCompletedTrigger.php:85
#: app/Services/Integrations/LearnDash/LessonCompletedTrigger.php:85
msgid "Select Course to find out Lesson"
msgstr "Seleziona corso per trovare la lezione"

#: app/Services/Funnel/Actions/AddEmailSequenceAction.php:44
msgid "Select Email Sequence"
msgstr "Seleziona sequenza di email"

#: app/Services/Integrations/RCP/RCPImporter.php:126
#: app/Services/Integrations/LifterLms/LifterImporter.php:154
#: app/Services/Integrations/LifterLms/LifterImporter.php:169
#: app/Services/Integrations/WooCommerce/WooImporter.php:140
#: app/Services/Integrations/LearnPress/LearnPressImporter.php:119
#: app/Services/Integrations/PMPro/PMProImporter.php:124
#: app/Services/Integrations/LearnDash/LearnDashImporter.php:157
#: app/Services/Integrations/LearnDash/LearnDashImporter.php:172
#: app/Services/Integrations/BuddyPress/BbImporter.php:175
#: app/Services/Integrations/BuddyPress/BbImporter.php:190
#: app/Services/Integrations/Edd/EddImporter.php:134
#: app/Services/Integrations/WishlistMember/WishlistMemberImporter.php:125
#: app/Services/Integrations/TutorLms/TutorImporter.php:125
#: app/Services/Integrations/MemberPress/MemberPressImporter.php:116
msgid "Select FluentCRM Tag"
msgstr "Seleziona tag FluentCRM"

#: app/Services/Integrations/RCP/RCPImporter.php:124
#: app/Services/Integrations/LifterLms/LifterImporter.php:152
#: app/Services/Integrations/LifterLms/LifterImporter.php:167
#: app/Services/Integrations/WooCommerce/WooImporter.php:138
#: app/Services/Integrations/LearnPress/LearnPressImporter.php:117
#: app/Services/Integrations/PMPro/PMProImporter.php:122
#: app/Services/Integrations/LearnDash/LearnDashImporter.php:155
#: app/Services/Integrations/LearnDash/LearnDashImporter.php:170
#: app/Services/Integrations/BuddyPress/BbImporter.php:173
#: app/Services/Integrations/Edd/EddImporter.php:132
#: app/Services/Integrations/WishlistMember/WishlistMemberImporter.php:123
#: app/Services/Integrations/TutorLms/TutorImporter.php:123
#: app/Services/Integrations/MemberPress/MemberPressImporter.php:114
msgid "Select FluentCRM Tag that will be applied"
msgstr "Seleziona il tag FluentCRM che verrà applicato"

#: app/Services/Integrations/LifterLms/CourseCompletedTrigger.php:83
#: app/Services/Integrations/LifterLms/CourseEnrollTrigger.php:88
#: app/Services/Integrations/LearnDash/CourseLeaveTrigger.php:89
#: app/Services/Integrations/LearnDash/CourseCompletedTrigger.php:85
#: app/Services/Integrations/LearnDash/CourseEnrollTrigger.php:88
#: app/Services/Integrations/TutorLms/CourseCompletedTrigger.php:83
#: app/Services/Integrations/TutorLms/CourseEnrollTrigger.php:84
msgid "Select for which Courses this automation will run"
msgstr "Seleziona per quali corsi verrà eseguita questa automazione"

#: app/Services/Integrations/LearnDash/GroupEnrollTrigger.php:86
msgid "Select for which groups this automation will run"
msgstr "Seleziona per quali gruppi verrà eseguita questa automazione"

#: app/Services/Integrations/LifterLms/LessonCompletedTrigger.php:87
#: app/Services/Integrations/TutorLms/LessonCompletedTrigger.php:83
msgid "Select for which Lessons this automation will run"
msgstr "Seleziona per quali lezioni verrà eseguita questa automazione"

#: app/Services/Integrations/RCP/RCPMembershipTrigger.php:77
#: app/Services/Integrations/RCP/RCPMembershipCancelTrigger.php:77
#: app/Services/Integrations/RCP/RCPMembershipExpireTrigger.php:77
#: app/Services/Integrations/PMPro/PMProPMProMembershipTrigger.php:84
#: app/Services/Integrations/PMPro/PMProPMProExpiryLevelTrigger.php:77
#: app/Services/Integrations/PMPro/PMProPMProCancelLevelTrigger.php:77
#: app/Services/Integrations/WishlistMember/WishlistMembershipTrigger.php:77
#: app/Services/Integrations/MemberPress/MembershipTrigger.php:77
#: app/Services/Integrations/MemberPress/SubscriptionExpiredTrigger.php:76
msgid "Select for which Membership Levels this automation will run"
msgstr ""
"Seleziona per quali livelli di abbonamento verrà eseguita questa automazione"

#: app/Services/Integrations/LifterLms/MembershipEnrollTrigger.php:91
msgid "Select for which Memberships this automation will run"
msgstr "Seleziona per quali abbonamenti verrà eseguita questa automazione"

#: app/Services/Integrations/WooCommerce/WooOrderCompletedTrigger.php:89
#: app/Services/Integrations/WooCommerce/WooOrderRefundedTrigger.php:88
#: app/Services/Integrations/WooCommerce/WooOrderSuccessTrigger.php:91
#: app/Services/Integrations/Edd/EddPaymentSuccessTrigger.php:96
#: app/Services/Integrations/Edd/EddRecurringPaymentTrigger.php:95
#: app/Services/Integrations/Edd/EddPaymentRefundTrigger.php:96
msgid "Select for which product category the automation will run"
msgstr "Seleziona per quale categoria di prodotto verrà eseguita l'automazione"

#: app/Services/Integrations/WooCommerce/WooOrderSuccessBenchmark.php:68
#: app/Services/Integrations/Edd/EddOrderSuccessBenchmark.php:67
msgid "Select for which product category the goal will run"
msgstr "Seleziona per quale categoria di prodotto verrà eseguito l'obiettivo"

#: app/Services/Integrations/Edd/EddLicenseExpiredTrigger.php:78
msgid "Select for which product this automation will run"
msgstr "Seleziona per quale prodotto verrà eseguita l'automazione"

#: app/Services/Integrations/WooCommerce/WooOrderCompletedTrigger.php:81
#: app/Services/Integrations/WooCommerce/WooOrderRefundedTrigger.php:80
#: app/Services/Integrations/WooCommerce/WooOrderSuccessTrigger.php:83
#: app/Services/Integrations/SureCart/SureCartPaymentSuccessBenchmark.php:55
#: app/Services/Integrations/SureCart/SureCartPaymentRefundTrigger.php:86
#: app/Services/Integrations/SureCart/SureCartPaymentSuccessTrigger.php:87
#: app/Services/Integrations/Edd/EddPaymentSuccessTrigger.php:88
#: app/Services/Integrations/Edd/EddRecurringExpired.php:86
#: app/Services/Integrations/Edd/EddRecurringPaymentTrigger.php:87
#: app/Services/Integrations/Edd/EddPaymentRefundTrigger.php:88
msgid "Select for which products this automation will run"
msgstr "Seleziona per quali prodotti verrà eseguita questa automazione"

#: app/Services/Integrations/WooCommerce/WooOrderSuccessBenchmark.php:60
#: app/Services/Integrations/Edd/EddOrderSuccessBenchmark.php:59
#: app/Services/Integrations/Edd/EddSubscriptionActiveBenchmark.php:55
#: app/Services/Funnel/Benchmarks/LinkClickBenchmark.php:59
msgid "Select for which products this goal will run"
msgstr "Seleziona per quali prodotti verrà eseguito questo obiettivo"

#: app/Services/Integrations/WooCommerce/WooSubscriptionStartedTrigger.php:56
#: app/Services/Integrations/WooCommerce/WooSubscriptionRenewalFailedTrigger.php:56
#: app/Services/Integrations/WooCommerce/WooSubscriptionExpiredTrigger.php:56
#: app/Services/Integrations/WooCommerce/WooSubscriptionRenewalPaymentTrigger.php:56
msgid "Select for which subscription products this automation will run"
msgstr ""
"Seleziona per quali prodotti in abbonamento verrà eseguita questa automazione"

#: app/Services/Integrations/LearnDash/TopicCompletedTrigger.php:98
msgid "Select for which Topics this automation will run"
msgstr "Seleziona per quali argomenti verrà eseguita questa automazione"

#: app/Services/Integrations/LearnDash/AddToGroupAction.php:46
#: app/Services/Integrations/LearnDash/RemoveFromGroupAction.php:44
msgid "Select LearnDash Group"
msgstr "Seleziona gruppo LearnDash"

#: app/Services/Integrations/LearnDash/AddToGroupAction.php:45
msgid "Select LearnDash Group to Enroll"
msgstr "Seleziona gruppo LearnDash per l'iscrizione"

#: app/Services/Integrations/LearnDash/RemoveFromGroupAction.php:43
msgid "Select LearnDash Group to un-enroll contact"
msgstr "Seleziona gruppo LearnDash per disiscrivere il contatto"

#: app/Services/Integrations/LearnDash/TopicCompletedTrigger.php:91
msgid "Select Lesson to find out the available topics"
msgstr "Seleziona lezione per scoprire gli argomenti disponibili"

#: app/Services/Integrations/LearnDash/LessonCompletedTrigger.php:92
msgid "Select Lesson to find out Topic"
msgstr "Seleziona lezione per trovare l'argomento"

#: app/Services/Integrations/LifterLms/AddToMembershipAction.php:45
msgid "Select LifterLMS Membership Group"
msgstr "Seleziona gruppo di abbonamento LifterLMS"

#: app/Services/Integrations/LifterLms/AddToMembershipAction.php:44
msgid "Select LifterLMS Membership Group to Enroll"
msgstr "Seleziona gruppo di abbonamento LifterLMS per l'iscrizione"

#: app/Services/Integrations/CRM/ListAppliedTrigger.php:53
#: app/Services/Integrations/CRM/RemoveFromListTrigger.php:54
msgid "Select List"
msgstr "Seleziona lista"

#: app/Services/Integrations/CRM/ListAppliedTrigger.php:52
#: app/Services/Integrations/CRM/RemoveFromListTrigger.php:53
#: app/Services/Integrations/ElementorFormIntegration/FormWidget.php:62
msgid "Select Lists"
msgstr "Seleziona liste"

#: app/Services/Integrations/LifterLms/RemoveFromMembershipAction.php:43
msgid "Select Membership"
msgstr "Seleziona abbonamento"

#: app/Services/Integrations/WooCommerce/AddOrderNoteAction.php:59
msgid "Select Note Type for the reference Order."
msgstr "Seleziona il tipo di nota per l'ordine di riferimento."

#: app/Services/Funnel/Actions/UserRoleRemoveAction.php:34
msgid "Select Role that you want to remove from targeted Contact"
msgstr "Seleziona il ruolo che desideri rimuovere dal contatto target"

#: app/Services/Funnel/Triggers/UserLoginTrigger.php:78
msgid "Select Roles"
msgstr "Seleziona ruoli"

#: app/Services/Funnel/Actions/AddEmailSequenceAction.php:45
msgid "Select Sequence Email"
msgstr "Seleziona email della sequenza"

#: app/Services/Funnel/Benchmarks/EmailSequenceCompletedBenchmark.php:45
#: app/Services/Funnel/Actions/RemoveFromEmailSequenceAction.php:41
msgid "Select Sequences"
msgstr "Seleziona sequenze"

#: app/Services/Integrations/RCP/RCPMembershipTrigger.php:48
#: app/Services/Integrations/RCP/RCPMembershipCancelTrigger.php:48
#: app/Services/Integrations/RCP/RCPMembershipExpireTrigger.php:48
#: app/Services/Integrations/LifterLms/LessonCompletedTrigger.php:52
#: app/Services/Integrations/LifterLms/CourseCompletedTrigger.php:48
#: app/Services/Integrations/LifterLms/CourseEnrollTrigger.php:52
#: app/Services/Integrations/LifterLms/MembershipEnrollTrigger.php:52
#: app/Services/Integrations/WooCommerce/WooSubscriptionStartedTrigger.php:79
#: app/Services/Integrations/WooCommerce/WooOrderCompletedTrigger.php:48
#: app/Services/Integrations/WooCommerce/WooOrderStatusChangeTrigger.php:48
#: app/Services/Integrations/WooCommerce/WooSubscriptionRenewalFailedTrigger.php:79
#: app/Services/Integrations/WooCommerce/WooOrderRefundedTrigger.php:48
#: app/Services/Integrations/WooCommerce/WooOrderSuccessTrigger.php:50
#: app/Services/Integrations/WooCommerce/WooSubscriptionExpiredTrigger.php:79
#: app/Services/Integrations/WooCommerce/WooSubscriptionRenewalPaymentTrigger.php:79
#: app/Services/Integrations/PMPro/PMProPMProMembershipTrigger.php:48
#: app/Services/Integrations/PMPro/PMProPMProExpiryLevelTrigger.php:48
#: app/Services/Integrations/PMPro/PMProPMProCancelLevelTrigger.php:48
#: app/Services/Integrations/SureCart/SureCartPaymentRefundTrigger.php:48
#: app/Services/Integrations/SureCart/SureCartPaymentSuccessTrigger.php:49
#: app/Services/Integrations/LearnDash/GroupEnrollTrigger.php:47
#: app/Services/Integrations/LearnDash/CourseLeaveTrigger.php:50
#: app/Services/Integrations/LearnDash/TopicCompletedTrigger.php:46
#: app/Services/Integrations/LearnDash/LessonCompletedTrigger.php:47
#: app/Services/Integrations/LearnDash/CourseCompletedTrigger.php:47
#: app/Services/Integrations/LearnDash/CourseEnrollTrigger.php:49
#: app/Services/Integrations/Edd/EddPaymentSuccessTrigger.php:48
#: app/Services/Integrations/Edd/EddLicenseExpiredTrigger.php:47
#: app/Services/Integrations/Edd/EddRecurringExpired.php:48
#: app/Services/Integrations/Edd/EddRecurringPaymentTrigger.php:48
#: app/Services/Integrations/Edd/EddPaymentRefundTrigger.php:48
#: app/Services/Integrations/WishlistMember/WishlistMembershipTrigger.php:48
#: app/Services/Integrations/TutorLms/LessonCompletedTrigger.php:48
#: app/Services/Integrations/TutorLms/CourseCompletedTrigger.php:48
#: app/Services/Integrations/TutorLms/CourseEnrollTrigger.php:48
#: app/Services/Integrations/MemberPress/MembershipTrigger.php:48
#: app/Services/Integrations/MemberPress/SubscriptionExpiredTrigger.php:47
#: app/Services/Integrations/AffiliateWP/AffiliateWPAffActiveTrigger.php:71
#: app/Services/Funnel/Triggers/UserLoginTrigger.php:42
msgid "Select Status"
msgstr "Seleziona stato"

#: app/Services/Integrations/CRM/TagAppliedTrigger.php:53
#: app/Services/Integrations/CRM/RemoveFromTagTrigger.php:54
msgid "Select Tag"
msgstr "Seleziona tag"

#: app/Services/Integrations/LifterLms/LifterInit.php:166
#: app/Services/Integrations/LifterLms/LifterInit.php:179
#: app/Services/Integrations/LifterLms/LifterInit.php:237
#: app/Services/Integrations/LifterLms/LifterInit.php:371
#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:114
#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:128
#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:148
#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:161
#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:185
#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:200
#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:222
#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:237
#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:259
#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:274
#: app/Services/Integrations/LearnPress/LearnPressInit.php:136
#: app/Services/Integrations/LearnPress/LearnPressInit.php:156
#: app/Services/Integrations/LearnPress/LearnPressInit.php:196
#: app/Services/Integrations/LearnDash/LdInit.php:164
#: app/Services/Integrations/CRM/TagAppliedTrigger.php:52
#: app/Services/Integrations/CRM/RemoveFromTagTrigger.php:53
#: app/Services/Integrations/BuddyPress/Group.php:40
#: app/Services/Integrations/BuddyPress/BBMemberType.php:62
#: app/Services/Integrations/Edd/EddMetaBoxes.php:53
#: app/Services/Integrations/Edd/EddMetaBoxes.php:105
#: app/Services/Integrations/TutorLms/TutorLmsInit.php:136
#: app/Services/Integrations/TutorLms/TutorLmsInit.php:150
msgid "Select Tags"
msgstr "Seleziona tag"

#: app/Services/Integrations/ElementorFormIntegration/FormWidget.php:79
msgid "Select tags"
msgstr "Seleziona tag"

#: app/Services/Integrations/WooCommerce/WooOrderCompletedTrigger.php:95
#: app/Services/Integrations/WooCommerce/WooOrderSuccessBenchmark.php:74
#: app/Services/Integrations/WooCommerce/WooOrderSuccessTrigger.php:97
#: app/Services/Integrations/Edd/EddPaymentSuccessTrigger.php:102
#: app/Services/Integrations/Edd/EddOrderSuccessBenchmark.php:73
#: app/Services/Integrations/Edd/EddPaymentRefundTrigger.php:102
msgid "Select the purchase type"
msgstr "Seleziona il tipo di acquisto"

#: app/Services/Funnel/Actions/RemoveFromFunnelAction.php:37
msgid "Select which automations will be cancelled from the contact"
msgstr "Seleziona quali automazioni saranno cancellate dal contatto"

#: app/Services/Funnel/Actions/SendCampaignEmailAction.php:36
msgid "Select which campaign email will be scheduled to this contact"
msgstr ""
"Seleziona quale email della campagna verrà pianificata per questo contatto"

#: app/Services/Funnel/Triggers/UserLoginTrigger.php:77
msgid "Select which roles registration will run this automation Funnel"
msgstr ""
"Seleziona quali ruoli d'iscrizione eseguiranno questo funnel di automazione"

#: app/Services/Funnel/Actions/AddEmailSequenceAction.php:38
msgid "Select which sequence will be assigned to this contact"
msgstr "Seleziona quale sequenza verrà assegnata a questo contatto"

#: app/Services/Funnel/Actions/RemoveFromEmailSequenceAction.php:35
msgid "Select which sequences will be removed from this contact"
msgstr "Seleziona quali sequenze verranno rimosse da questo contatto"

#: app/Services/Integrations/BuddyPress/Group.php:46
#: app/Services/Integrations/BuddyPress/BBMemberType.php:68
msgid "selected"
msgstr "selezionato"

#: app/Http/Controllers/RecurringCampaignController.php:229
msgid "Selected Campaign has been successfully duplicated"
msgstr "La campagna selezionata è stata duplicata con successo"

#: app/Modules/AbandonCart/AbandonCartController.php:81
msgid "Selected carts has been deleted successfully"
msgstr "I carrelli selezionati sono stati eliminati con successo"

#: app/Services/Integrations/LearnDash/LdInit.php:207
msgid ""
"selected contact tags (defined in previous field) will be removed when user "
"leave this group"
msgstr ""
"I tag del contatto selezionato (definiti nel campo precedente) verranno "
"rimossi quando l'utente lascia questo gruppo"

#: app/Services/Integrations/LearnDash/LdInit.php:204
msgid "selected contact tags will be removed when user leave this group"
msgstr ""
"I tag del contatto selezionato verranno rimossi quando l'utente lascia "
"questo gruppo"

#: app/Services/Funnel/Actions/ChangeUserRoleAction.php:43
msgid ""
"Selected Role will be applied if there has a user with contact's email "
"address"
msgstr ""
"Il ruolo selezionato sarà applicato se c'è un utente con l'indirizzo email "
"del contatto"

#: app/Http/Controllers/DynamicSegmentController.php:370
msgid "Selected segment has been deleted"
msgstr "Il segmento selezionato è stato eliminato"

#: app/Http/Controllers/SequenceController.php:146
msgid "Selected sequence has been successfully duplicated"
msgstr "La sequenza selezionata è stata duplicata con successo"

#: app/Http/Controllers/SequenceController.php:304
msgid "Selected Sequences has been deleted permanently"
msgstr "Le sequenze selezionate sono state eliminate permanentemente"

#: app/Http/Controllers/SmartLinksController.php:104
msgid "Selected Smart Link has been deleted"
msgstr "Il link intelligente selezionato è stato eliminato"

#: app/Http/Controllers/SequenceController.php:264
msgid "Selected subscribers has been successfully removed from this sequence"
msgstr ""
"Gli iscritti selezionati sono stati rimossi con successo da questa sequenza"

#: app/Services/Integrations/BuddyPress/Group.php:50
msgid "Selected tags will be added to the member on joining"
msgstr ""
"I tag selezionati verranno aggiunti al membro al momento dell'iscrizione"

#: app/Services/Integrations/BuddyPress/BBMemberType.php:72
msgid "Selected tags will be added to the member on joining this member type"
msgstr ""
"I tag selezionati verranno aggiunti al membro quando si iscrive a questo "
"tipo di membro"

#: app/Services/Integrations/LearnDash/LdInit.php:165
msgid "Selected tags will be applied to the contact on course completion"
msgstr ""
"I tag selezionati verranno applicati al contatto al completamento del corso"

#: app/Services/Integrations/LifterLms/LifterInit.php:181
#: app/Services/Integrations/TutorLms/TutorLmsInit.php:152
msgid "Selected tags will be applied to the contact on course completion."
msgstr ""
"I tag selezionati verranno applicati al contatto al completamento del corso."

#: app/Services/Integrations/LearnDash/LdInit.php:153
msgid "Selected tags will be applied to the contact on course enrollment"
msgstr ""
"I tag selezionati verranno applicati al contatto al momento dell'iscrizione "
"al corso"

#: app/Services/Integrations/LifterLms/LifterInit.php:168
#: app/Services/Integrations/TutorLms/TutorLmsInit.php:138
msgid "Selected tags will be applied to the contact on course enrollment."
msgstr ""
"I tag selezionati verranno applicati al contatto al momento dell'iscrizione "
"al corso."

#: app/Services/Integrations/LearnDash/LdInit.php:193
msgid "Selected tags will be applied to the contact on group enrollment"
msgstr ""
"I tag selezionati verranno applicati al contatto al momento dell'iscrizione "
"al gruppo"

#: app/Services/Integrations/LifterLms/LifterInit.php:373
msgid "Selected tags will be applied to the contact on lesson completed."
msgstr ""
"I tag selezionati verranno applicati al contatto al completamento della "
"lezione."

#: app/Services/Integrations/LifterLms/LifterInit.php:239
msgid "Selected tags will be applied to the contact on membership enrollment."
msgstr ""
"I tag selezionati verranno applicati al contatto al momento dell'iscrizione "
"all'abbonamento."

#: app/Services/Funnel/Actions/SendCampaignEmailAction.php:24
msgid "Send an Email from your existing campaign"
msgstr "Invia un'email dalla tua campagna esistente"

#: app/Services/Funnel/Actions/HTTPSendDataAction.php:90
msgid "Send as Form Method"
msgstr "Invia come metodo form"

#: app/Services/Funnel/Actions/HTTPSendDataAction.php:86
msgid "Send as JSON format"
msgstr "Invia come formato JSON"

#: app/Services/Funnel/Actions/AddEmailSequenceAction.php:25
msgid "Send Automated Emails based on your Sequence settings"
msgstr "Invia email automatiche in base alle impostazioni della sequenza"

#: app/Services/Funnel/Actions/SendCampaignEmailAction.php:23
msgid "Send Campaign Email"
msgstr "Invia email di campagna"

#: app/Services/Funnel/Actions/HTTPSendDataAction.php:155
msgid ""
"Send Data as Background Process. (You may enable this if you have lots of "
"tasks)"
msgstr ""
"Invia i dati come processo in background. (Puoi abilitare questa opzione se "
"hai molte attività)"

#: app/Services/Funnel/Actions/HTTPSendDataAction.php:56
msgid "Send Data to External Server"
msgstr "Invia dati a un server esterno"

#: app/Services/Funnel/Actions/HTTPSendDataAction.php:28
#: app/Services/Funnel/Actions/HTTPSendDataAction.php:57
msgid "Send Data to external server via GET or POST Method"
msgstr "Invia dati a un server esterno tramite il metodo GET o POST"

#: app/Services/Integrations/LifterLms/AddToMembershipAction.php:53
#: app/Services/Integrations/LifterLms/AddToCourseAction.php:54
#: app/Services/Integrations/LearnDash/AddToGroupAction.php:54
#: app/Services/Integrations/LearnDash/AddToCourseAction.php:54
#: app/Services/Integrations/TutorLms/AddToCourseAction.php:53
msgid "Send default WordPress Welcome Email for new WordPress users"
msgstr ""
"Invia l'email di benvenuto di WordPress di default per i nuovi utenti "
"WordPress"

#: app/Services/Integrations/ElementorFormIntegration/FormWidget.php:102
msgid ""
"Send Double Optin Email for new or pending contacts. If you don't enable "
"this then contact will be added as subscribed state."
msgstr ""
"Invia un'email di doppio opt-in per i contatti nuovi o in sospeso. Se non lo "
"abiliti, il contatto verrà aggiunto come stato sottoscritto."

#: app/Services/Funnel/Actions/UserRegistrationAction.php:89
msgid "Send WordPress user notification email"
msgstr "Invia l'email di notifica per gli utenti WordPress"

#: app/Http/Controllers/SequenceMailController.php:83
msgid "Sequence email has been created"
msgstr "L'email della sequenza è stata creata"

#: app/Http/Controllers/SequenceMailController.php:116
msgid "Sequence email has been duplicated"
msgstr "L'email della sequenza è stata duplicata"

#: app/Http/Controllers/SequenceMailController.php:165
msgid "Sequence email has been updated"
msgstr "L'email della sequenza è stata aggiornata"

#: app/Http/Controllers/SequenceController.php:49
msgid "Sequence has been created"
msgstr "La sequenza è stata creata"

#: app/Http/Controllers/SequenceController.php:85
msgid "Sequence has been updated"
msgstr "La sequenza è stata aggiornata"

#: app/Services/Funnel/Actions/AddEmailSequenceAction.php:24
#: app/Services/Funnel/Actions/AddEmailSequenceAction.php:37
msgid "Set Sequence Emails"
msgstr "Imposta le email della sequenza"

#: app/Services/Integrations/Edd/AdvancedReport.php:94
msgid "Settings -> Integrations Settings -> Edd"
msgstr "Impostazioni -> Impostazioni di integrazione -> Edd"

#: app/Services/Integrations/LearnDash/AdvancedReport.php:63
msgid "Settings -> Integrations Settings -> LearnDash"
msgstr "Impostazioni -> Impostazioni di integrazione -> LearnDash"

#: app/Services/Integrations/LifterLms/AdvancedReport.php:63
msgid "Settings -> Integrations Settings -> LifterLMS"
msgstr "Impostazioni -> Impostazioni di integrazione -> LifterLMS"

#: app/Services/Integrations/TutorLms/AdvancedReport.php:60
msgid "Settings -> Integrations Settings -> TutorLms"
msgstr "Impostazioni -> Impostazioni di integrazione -> TutorLms"

#: app/Services/Integrations/WooCommerce/AdvancedReport.php:92
msgid "Settings -> Integrations Settings -> WooCommerce"
msgstr "Impostazioni -> Impostazioni di integrazione -> WooCommerce"

#: app/Modules/AbandonCart/SettingsController.php:70
msgid "Settings has been saved successfully"
msgstr "Le impostazioni sono state salvate con successo"

#: app/Http/Controllers/RecurringCampaignController.php:390
msgid "Settings has been successfully updated"
msgstr "Le impostazioni sono state aggiornate con successo"

#: app/Http/Controllers/RecurringCampaignController.php:165
msgid "Settings has been updated"
msgstr "Le impostazioni sono state aggiornate"

#: app/Services/Integrations/WooCommerce/DeepIntegration.php:356
#: app/Services/Integrations/LearnDash/DeepIntegration.php:213
#: app/Services/Integrations/Edd/DeepIntegration.php:416
#: app/Services/Integrations/TutorLms/DeepIntegration.php:125
msgid "Settings have been saved"
msgstr "Le impostazioni sono state salvate"

#: app/Services/Funnel/Actions/UpdateContactPropertyAction.php:46
msgid "Setup contact properties that you want to update"
msgstr "Configura le proprietà del contatto che desideri aggiornare"

#: app/Services/Integrations/WooCommerce/AutomationConditions.php:195
msgid "Shipping Method"
msgstr "Metodo di spedizione"

#: app/Http/Controllers/DynamicSegmentController.php:244
msgid "Should Match Both Open & Click Condition?"
msgstr "Deve soddisfare entrambe le condizioni di apertura e clic?"

#: app/Services/Integrations/WooCommerce/AdvancedReport.php:73
#: app/Services/Integrations/Edd/AdvancedReport.php:66
msgid "Signups"
msgstr "Iscrizioni"

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:81
msgid "Skip this automation if the contact is already in active state."
msgstr "Salta questa automazione se il contatto è già in stato attivo."

#: app/Http/Controllers/SmartLinksController.php:72
msgid "SmartLink has be created"
msgstr "Lo SmartLink è stato creato"

#: app/Http/Controllers/SmartLinksController.php:95
msgid "SmartLink has be updated"
msgstr "Lo SmartLink è stato aggiornato"

#: app/Http/Controllers/SmartLinksController.php:52
msgid "SmartLinks module has been successfully activated"
msgstr "Il modulo SmartLinks è stato attivato con successo"

#: app/Http/Controllers/CampaignsProController.php:20
msgid "Sorry no failed campaign emails found"
msgstr "Spiacente, nessuna email di campagna fallita trovata"

#: app/Http/Controllers/CampaignsProController.php:47
#: app/Http/Controllers/CampaignsProController.php:57
msgid "Sorry! No emails found"
msgstr "Spiacente! Nessuna email trovata"

#: app/Http/Controllers/DynamicSegmentController.php:361
msgid "Sorry! No segment found"
msgstr "Spiacente! Nessun segmento trovato"

#: app/Modules/AbandonCart/Woo/WooCartTrackingInit.php:341
msgid "Sorry, we could not retrieve your cart"
msgstr "Spiacente, non siamo riusciti a recuperare il tuo carrello"

#: app/Http/Controllers/DynamicSegmentController.php:181
#: app/Hooks/Handlers/DataExporter.php:541
msgid "Source"
msgstr "Fonte"

#: app/Services/Integrations/WooCommerce/AutomationConditions.php:127
msgid "Specific Product Purchase Times"
msgstr "Tempi di acquisto del prodotto specifico"

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:66
#: app/Services/Funnel/Conditions/FunnelCondition.php:47
msgid "Specify Matching Conditions"
msgstr "Specifica condizioni corrispondenti"

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:67
#: app/Services/Funnel/Conditions/FunnelCondition.php:48
msgid ""
"Specify which contact properties need to matched. Based on the conditions it "
"will run yes blocks or no blocks"
msgstr ""
"Specifica quali proprietà del contatto devono corrispondere. In base alle "
"condizioni, eseguirà i blocchi di sì o no"

#: app/Services/Funnel/Conditions/FunnelABTesting.php:26
#: app/Services/Funnel/Conditions/FunnelABTesting.php:41
msgid "Split (A/B Testing)"
msgstr "Dividi (A/B Testing)"

#: app/Services/Integrations/LearnPress/LearnPressInit.php:96
#: app/Services/Integrations/LearnDash/LdInit.php:120
#: app/Services/Integrations/TutorLms/TutorLmsInit.php:107
msgid "Started At"
msgstr "Iniziato a"

#: app/Http/Controllers/DynamicSegmentController.php:159
#: app/Hooks/Handlers/DataExporter.php:535
#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:170
#: app/Services/Funnel/Conditions/FunnelCondition.php:102
msgid "State"
msgstr "Stato"

#: app/Hooks/Handlers/DataExporter.php:539
#: app/Services/Integrations/LifterLms/LifterInit.php:129
#: app/Services/Integrations/LearnPress/LearnPressInit.php:94
#: app/Services/Integrations/LearnDash/LdInit.php:121
#: app/Services/Integrations/AffiliateWP/DeepIntegration.php:79
msgid "Status"
msgstr "Stato"

#: app/Services/Integrations/LifterLms/CourseCompletedTrigger.php:40
msgid "Student completes a Course in LifterLMS"
msgstr "Studente completa un corso in LifterLMS"

#: app/Services/Integrations/TutorLms/CourseCompletedTrigger.php:40
msgid "Student completes a Course in TutorLMS"
msgstr "Studente completa un corso in TutorLMS"

#: app/Services/Integrations/LifterLms/LessonCompletedTrigger.php:44
msgid "Student completes a Lesson in LifterLMS"
msgstr "Studente completa una lezione in LifterLMS"

#: app/Services/Integrations/TutorLms/LessonCompletedTrigger.php:40
msgid "Student completes a Lesson in TutorLMS"
msgstr "Studente completa una lezione in TutorLMS"

#: app/Services/Integrations/LifterLms/AdvancedReport.php:51
#: app/Services/Integrations/LearnDash/AdvancedReport.php:51
#: app/Services/Integrations/TutorLms/AdvancedReport.php:48
msgid "Students Growth"
msgstr "Crescita degli studenti"

#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:178
msgid "Subscription"
msgstr "Abbonamento"

#: app/Services/Integrations/WooCommerce/WooSubscriptionStartedTrigger.php:71
msgid "Subscription Activated"
msgstr "Abbonamento attivato"

#: app/Services/Integrations/WooCommerce/WooSubscriptionStartedTrigger.php:25
msgid "Subscription activated"
msgstr "Abbonamento attivato"

#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:217
msgid "Subscription Cancelled Actions"
msgstr "Azioni per annullamento dell'abbonamento"

#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:254
msgid "Subscription Expire Actions"
msgstr "Azioni per scadenza dell'abbonamento"

#: app/Services/Integrations/MemberPress/SubscriptionExpiredTrigger.php:24
msgid "Subscription Expired"
msgstr "Abbonamento scaduto"

#: app/Services/Integrations/WooCommerce/AdvancedReport.php:55
#: app/Services/Integrations/Edd/AdvancedReport.php:47
msgid "Subscription Revenue (All)"
msgstr "Ricavi da abbonamenti (Totale)"

#: app/Http/Controllers/DynamicSegmentController.php:174
#: app/Services/Integrations/RCP/RCPMembershipTrigger.php:47
#: app/Services/Integrations/RCP/RCPMembershipCancelTrigger.php:47
#: app/Services/Integrations/RCP/RCPMembershipExpireTrigger.php:47
#: app/Services/Integrations/LifterLms/LessonCompletedTrigger.php:51
#: app/Services/Integrations/LifterLms/CourseCompletedTrigger.php:47
#: app/Services/Integrations/LifterLms/CourseEnrollTrigger.php:51
#: app/Services/Integrations/LifterLms/MembershipEnrollTrigger.php:51
#: app/Services/Integrations/WooCommerce/WooOrderCompletedTrigger.php:47
#: app/Services/Integrations/WooCommerce/WooOrderStatusChangeTrigger.php:47
#: app/Services/Integrations/WooCommerce/WooOrderRefundedTrigger.php:47
#: app/Services/Integrations/WooCommerce/WooOrderSuccessTrigger.php:49
#: app/Services/Integrations/PMPro/PMProPMProMembershipTrigger.php:47
#: app/Services/Integrations/PMPro/PMProPMProExpiryLevelTrigger.php:47
#: app/Services/Integrations/PMPro/PMProPMProCancelLevelTrigger.php:47
#: app/Services/Integrations/SureCart/SureCartPaymentRefundTrigger.php:47
#: app/Services/Integrations/SureCart/SureCartPaymentSuccessTrigger.php:48
#: app/Services/Integrations/LearnDash/GroupEnrollTrigger.php:46
#: app/Services/Integrations/LearnDash/CourseLeaveTrigger.php:49
#: app/Services/Integrations/LearnDash/TopicCompletedTrigger.php:45
#: app/Services/Integrations/LearnDash/LessonCompletedTrigger.php:46
#: app/Services/Integrations/LearnDash/CourseCompletedTrigger.php:46
#: app/Services/Integrations/LearnDash/CourseEnrollTrigger.php:48
#: app/Services/Integrations/Edd/EddPaymentSuccessTrigger.php:47
#: app/Services/Integrations/Edd/EddLicenseExpiredTrigger.php:46
#: app/Services/Integrations/Edd/EddRecurringPaymentTrigger.php:47
#: app/Services/Integrations/Edd/EddPaymentRefundTrigger.php:47
#: app/Services/Integrations/WishlistMember/WishlistMembershipTrigger.php:47
#: app/Services/Integrations/TutorLms/LessonCompletedTrigger.php:47
#: app/Services/Integrations/TutorLms/CourseCompletedTrigger.php:47
#: app/Services/Integrations/TutorLms/CourseEnrollTrigger.php:47
#: app/Services/Integrations/MemberPress/MembershipTrigger.php:47
#: app/Services/Integrations/MemberPress/SubscriptionExpiredTrigger.php:46
#: app/Services/Integrations/AffiliateWP/AffiliateWPAffActiveTrigger.php:70
#: app/Services/Funnel/Triggers/UserLoginTrigger.php:41
msgid "Subscription Status"
msgstr "Stato dell'abbonamento"

#: app/Services/Integrations/Edd/AdvancedReport.php:61
msgid "Subscriptions"
msgstr "Abbonamenti"

#: app/Services/Integrations/WooCommerce/AdvancedReport.php:58
#: app/Services/Integrations/Edd/AdvancedReport.php:50
msgid "Subscriptions (New)"
msgstr "Abbonamenti (Nuovi)"

#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:109
msgid "Successful Purchase Actions"
msgstr "Azioni per acquisto riuscito"

#: app/Services/Integrations/SureCart/SureCartInit.php:137
msgid "SureCart"
msgstr "SureCart"

#: app/Services/Integrations/SureCart/SureCartPaymentSuccessTrigger.php:25
msgid "SureCart - New Order Success"
msgstr "SureCart - Nuovo ordine completato"

#: app/Services/Integrations/SureCart/SureCartPaymentRefundTrigger.php:24
msgid "SureCart - Order Revoked"
msgstr "SureCart - Ordine annullato"

#: app/Services/Integrations/SureCart/SureCartPaymentRefundTrigger.php:40
msgid "SureCart Order Revoke"
msgstr "Annullamento ordine SureCart"

#: app/Services/Integrations/SureCart/SureCartInit.php:136
msgid "SureCart Purchase History"
msgstr "Cronologia acquisti SureCart"

#: app/Services/Integrations/WooCommerce/WooImporter.php:116
#: app/Services/Integrations/Edd/EddImporter.php:110
#, php-format
msgid "Sync %s Customers Now"
msgstr "Sincronizza %s clienti ora"

#: app/Services/Integrations/AffiliateWP/DeepIntegration.php:214
msgid "Sync AffiliateWP Affiliates Now"
msgstr "Sincronizza gli affiliati di AffiliateWP ora"

#: app/Services/Integrations/Edd/DeepIntegration.php:369
msgid "Sync EDD Customers"
msgstr "Sincronizza i clienti EDD"

#: app/Services/Integrations/Edd/DeepIntegration.php:41
msgid ""
"Sync EDD Customers to FluentCRM to segment them by their purchases, lifetime "
"values and other purchase data."
msgstr ""
"Sincronizza i clienti EDD con FluentCRM per segmentarli in base ai loro "
"acquisti, valore a vita e altri dati di acquisto."

#: app/Services/Integrations/LearnDash/DeepIntegration.php:166
msgid "Sync LearnDash Students"
msgstr "Sincronizza gli studenti LearnDash"

#: app/Services/Integrations/LearnDash/DeepIntegration.php:41
msgid ""
"Sync LearnDash Students to FluentCRM to segment them by their enrollment, "
"membership groups data."
msgstr ""
"Sincronizza gli studenti LearnDash con FluentCRM per segmentarli in base "
"alla loro iscrizione e ai dati dei gruppi di appartenenza."

#: app/Services/Integrations/LifterLms/DeepIntegration.php:161
msgid "Sync LifterLMS Students"
msgstr "Sincronizza gli studenti LifterLMS"

#: app/Services/Integrations/LifterLms/DeepIntegration.php:47
msgid ""
"Sync LifterLMS Students to FluentCRM to segment them by their courses data."
msgstr ""
"Sincronizza gli studenti LifterLMS con FluentCRM per segmentarli in base ai "
"dati dei corsi."

#: app/Services/Integrations/LearnDash/DeepIntegration.php:144
msgid "Sync Required From Settings"
msgstr "Sincronizzazione richiesta dalle impostazioni"

#: app/Services/Integrations/TutorLms/DeepIntegration.php:78
msgid "Sync TutorLMS Students"
msgstr "Sincronizza gli studenti TutorLMS"

#: app/Services/Integrations/TutorLms/DeepIntegration.php:41
msgid ""
"Sync TutorLMS Students to FluentCRM to segment them by their courses data."
msgstr ""
"Sincronizza gli studenti TutorLMS con FluentCRM per segmentarli in base ai "
"dati dei corsi."

#: app/Services/Integrations/WooCommerce/DeepIntegration.php:309
msgid "Sync WooCommerce Customers"
msgstr "Sincronizza i clienti WooCommerce"

#: app/Services/Integrations/WooCommerce/DeepIntegration.php:42
msgid ""
"Sync WooCommerce Customers to FluentCRM to segment them by their purchases, "
"lifetime values and other purchase data."
msgstr ""
"Sincronizza i clienti WooCommerce con FluentCRM per segmentarli in base ai "
"loro acquisti, valore a vita e altri dati di acquisto."

#: app/Services/Integrations/CRM/TagAppliedTrigger.php:28
#: app/Services/Integrations/CRM/TagAppliedTrigger.php:45
msgid "Tag Applied"
msgstr "Tag applicato"

#: app/Services/Integrations/CRM/RemoveFromTagTrigger.php:29
msgid "Tag Removed"
msgstr "Tag rimosso"

#: app/Http/Controllers/DynamicSegmentController.php:199
#: app/Hooks/Handlers/DataExporter.php:547
#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:237
#: app/Services/Funnel/Conditions/FunnelCondition.php:169
msgid "Tags"
msgstr "Tag"

#: app/Services/Integrations/LearnDash/TopicCompletedTrigger.php:84
#: app/Services/Integrations/LearnDash/LessonCompletedTrigger.php:84
msgid "Target Course"
msgstr "Corso di destinazione"

#: app/Services/Integrations/LifterLms/CourseCompletedTrigger.php:82
#: app/Services/Integrations/LifterLms/CourseEnrollTrigger.php:87
#: app/Services/Integrations/LearnDash/CourseLeaveTrigger.php:88
#: app/Services/Integrations/LearnDash/CourseCompletedTrigger.php:84
#: app/Services/Integrations/LearnDash/CourseEnrollTrigger.php:87
#: app/Services/Integrations/TutorLms/CourseCompletedTrigger.php:82
#: app/Services/Integrations/TutorLms/CourseEnrollTrigger.php:83
msgid "Target Courses"
msgstr "Corsi di destinazione"

#: app/Services/Funnel/Triggers/TrackingEventRecordedTrigger.php:44
msgid "Target Event Key"
msgstr "Chiave evento di destinazione"

#: app/Services/Integrations/LearnDash/GroupEnrollTrigger.php:85
msgid "Target Groups"
msgstr "Gruppi di destinazione"

#: app/Services/Integrations/LearnDash/TopicCompletedTrigger.php:90
#: app/Services/Integrations/LearnDash/LessonCompletedTrigger.php:91
msgid "Target Lesson"
msgstr "Lezione di destinazione"

#: app/Services/Integrations/LifterLms/LessonCompletedTrigger.php:86
#: app/Services/Integrations/TutorLms/LessonCompletedTrigger.php:82
msgid "Target Lessons"
msgstr "Lezioni di destinazione"

#: app/Services/Integrations/RCP/RCPMembershipTrigger.php:76
#: app/Services/Integrations/RCP/RCPMembershipCancelTrigger.php:76
#: app/Services/Integrations/RCP/RCPMembershipExpireTrigger.php:76
#: app/Services/Integrations/PMPro/PMProPMProMembershipTrigger.php:83
#: app/Services/Integrations/PMPro/PMProPMProExpiryLevelTrigger.php:76
#: app/Services/Integrations/PMPro/PMProPMProCancelLevelTrigger.php:76
#: app/Services/Integrations/WishlistMember/WishlistMembershipTrigger.php:76
#: app/Services/Integrations/MemberPress/MembershipTrigger.php:76
#: app/Services/Integrations/MemberPress/SubscriptionExpiredTrigger.php:75
msgid "Target Membership Levels"
msgstr "Livelli di abbonamento di destinazione"

#: app/Services/Integrations/LifterLms/MembershipEnrollTrigger.php:90
msgid "Target Memberships"
msgstr "Abbonamenti di destinazione"

#: app/Services/Integrations/Edd/EddLicenseExpiredTrigger.php:77
msgid "Target Product"
msgstr "Prodotto di destinazione"

#: app/Services/Integrations/WooCommerce/WooSubscriptionStartedTrigger.php:55
#: app/Services/Integrations/WooCommerce/WooOrderCompletedTrigger.php:80
#: app/Services/Integrations/WooCommerce/WooOrderStatusChangeTrigger.php:94
#: app/Services/Integrations/WooCommerce/WooOrderSuccessBenchmark.php:59
#: app/Services/Integrations/WooCommerce/WooSubscriptionRenewalFailedTrigger.php:55
#: app/Services/Integrations/WooCommerce/WooOrderRefundedTrigger.php:79
#: app/Services/Integrations/WooCommerce/WooOrderSuccessTrigger.php:82
#: app/Services/Integrations/WooCommerce/WooSubscriptionExpiredTrigger.php:55
#: app/Services/Integrations/WooCommerce/WooSubscriptionRenewalPaymentTrigger.php:55
#: app/Services/Integrations/SureCart/SureCartPaymentSuccessBenchmark.php:54
#: app/Services/Integrations/SureCart/SureCartPaymentRefundTrigger.php:85
#: app/Services/Integrations/SureCart/SureCartPaymentSuccessTrigger.php:86
#: app/Services/Integrations/Edd/EddPaymentSuccessTrigger.php:87
#: app/Services/Integrations/Edd/EddRecurringExpired.php:85
#: app/Services/Integrations/Edd/EddOrderSuccessBenchmark.php:58
#: app/Services/Integrations/Edd/EddRecurringPaymentTrigger.php:86
#: app/Services/Integrations/Edd/EddSubscriptionActiveBenchmark.php:54
#: app/Services/Integrations/Edd/EddPaymentRefundTrigger.php:87
msgid "Target Products"
msgstr "Prodotti di destinazione"

#: app/Services/Integrations/LearnDash/TopicCompletedTrigger.php:97
msgid "Target Topics"
msgstr "Argomenti di destinazione"

#: app/Services/Funnel/Triggers/UserLoginTrigger.php:76
msgid "Targeted User Roles"
msgstr "Ruoli utente di destinazione"

#: app/Hooks/Handlers/DataExporter.php:677
msgid "Templates has been successfully imported"
msgstr "I modelli sono stati importati con successo"

#: app/Services/PluginManager/LicenseManager.php:111
#, php-format
msgid "The %s license needs to be activated. %sActivate Now%s"
msgstr "La licenza %s deve essere attivata. %sAttiva ora%s"

#: app/Hooks/Handlers/DataExporter.php:340
msgid ""
"The Block Action defined in the JSON file is not available on your site."
msgstr ""
"L'azione del blocco definita nel file JSON non è disponibile sul tuo sito."

#: app/Services/PluginManager/LicenseManager.php:347
msgid ""
"The given license key is not valid. Please verify that your license is "
"correct. You may login to wpmanageninja.com account and get your valid "
"license key for your purchase."
msgstr ""
"La chiave di licenza fornita non è valida. Verifica che la tua licenza sia "
"corretta. Puoi accedere al tuo account su wpmanageninja.com e ottenere la "
"chiave di licenza valida per il tuo acquisto."

#: app/Hooks/Handlers/DataExporter.php:307
msgid "The provided JSON file is not valid"
msgstr "Il file JSON fornito non è valido"

#: app/Hooks/Handlers/DataExporter.php:615
msgid "The provided JSON file is not valid."
msgstr "Il file JSON fornito non è valido."

#: app/Hooks/Handlers/DataExporter.php:384
msgid "The provided JSON file is not valid. No valid email sequence found"
msgstr ""
"Il file JSON fornito non è valido. Nessuna sequenza email valida trovata"

#: app/Hooks/Handlers/DataExporter.php:499
msgid ""
"The provided JSON file is not valid. object type is required in the JSON File"
msgstr ""
"Il file JSON fornito non è valido. Nel file JSON è richiesto un tipo di "
"oggetto"

#: app/Hooks/Handlers/DataExporter.php:376
msgid ""
"The provided JSON file is not valid. sequence key is required in the JSON "
"File"
msgstr ""
"Il file JSON fornito non è valido. Nel file JSON è richiesta una chiave di "
"sequenza"

#: app/Hooks/Handlers/DataExporter.php:318
msgid "The trigger defined in the JSON file is not available on your site."
msgstr "Il trigger definito nel file JSON non è disponibile sul tuo sito."

#: app/Services/Funnel/Triggers/ContactBirthDayTrigger.php:38
msgid ""
"This automation will be initiated for contact on his/her birthday. Will only "
"initiated only for subscribed status contacts"
msgstr ""
"Questa automazione verrà avviata per il contatto nel giorno del suo "
"compleanno. Sarà attivata solo per i contatti con stato sottoscritto"

#: app/Services/Funnel/Actions/EndFunnel.php:34
msgid "This automation will be marked as completed in this point for a contact"
msgstr ""
"Questa automazione sarà contrassegnata come completata a questo punto per un "
"contatto"

#: app/Services/Integrations/WooCommerce/WooOrderRefundedTrigger.php:41
msgid "This Automation will start once an order get refunded"
msgstr "Questa automazione inizierà una volta che un ordine viene rimborsato"

#: app/Services/Integrations/WooCommerce/WooSubscriptionRenewalFailedTrigger.php:72
msgid "This Automation will start when a payment fails for a subscription"
msgstr ""
"Questa automazione inizierà quando un pagamento fallisce per un abbonamento"

#: app/Services/Integrations/WooCommerce/WooSubscriptionRenewalPaymentTrigger.php:72
msgid ""
"This Automation will start when a recurring payment received  for a "
"subscription"
msgstr ""
"Questa automazione inizierà quando un pagamento ricorrente viene ricevuto "
"per un abbonamento"

#: app/Services/Integrations/WooCommerce/WooSubscriptionExpiredTrigger.php:72
msgid "This Automation will start when a subscription expires"
msgstr "Questa automazione inizierà quando un abbonamento scade"

#: app/Services/Integrations/WooCommerce/WooSubscriptionStartedTrigger.php:72
msgid ""
"This Automation will start when a woo subscription starts or status changed "
"to active"
msgstr ""
"Questa automazione inizierà quando un abbonamento Woo inizia o lo stato "
"cambia in attivo"

#: app/Services/Integrations/LearnDash/LessonCompletedTrigger.php:25
msgid "This funnel runs a student completes a lesson"
msgstr "Questo funnel si attiva quando uno studente completa una lezione"

#: app/Services/Integrations/LifterLms/CourseEnrollTrigger.php:30
msgid "This funnel runs when a contact is enrolled in a course"
msgstr "Questo funnel si attiva quando un contatto è iscritto a un corso"

#: app/Services/Integrations/RCP/RCPMembershipTrigger.php:26
#: app/Services/Integrations/PMPro/PMProPMProMembershipTrigger.php:26
#: app/Services/Integrations/WishlistMember/WishlistMembershipTrigger.php:26
#: app/Services/Integrations/MemberPress/MembershipTrigger.php:26
msgid "This funnel runs when a member is added to a membership level"
msgstr ""
"Questo funnel si attiva quando un membro viene aggiunto a un livello di "
"abbonamento"

#: app/Services/Integrations/RCP/RCPMembershipExpireTrigger.php:26
#: app/Services/Integrations/PMPro/PMProPMProExpiryLevelTrigger.php:26
msgid "This funnel runs when a membership expires"
msgstr "Questo funnel si attiva quando un abbonamento scade"

#: app/Services/Integrations/RCP/RCPMembershipCancelTrigger.php:26
msgid "This funnel runs when a membership is cancelled"
msgstr "Questo funnel si attiva quando un abbonamento viene annullato"

#: app/Services/Integrations/PMPro/PMProPMProCancelLevelTrigger.php:26
msgid "This funnel runs when a membership level is cancelled"
msgstr ""
"Questo funnel si attiva quando un livello di abbonamento viene annullato"

#: app/Services/Integrations/PMPro/PMProPMProCancelLevelTrigger.php:41
msgid "This funnel runs when a membership level is cancelled for a user"
msgstr ""
"Questo funnel si attiva quando un livello di abbonamento viene annullato per "
"un utente"

#: app/Services/Integrations/LifterLms/CourseCompletedTrigger.php:26
#: app/Services/Integrations/TutorLms/CourseCompletedTrigger.php:26
msgid "This funnel runs when a student completes a Course"
msgstr "Questo funnel si attiva quando uno studente completa un corso"

#: app/Services/Integrations/LearnDash/CourseCompletedTrigger.php:25
msgid "This funnel runs when a student completes a course"
msgstr "Questo funnel si attiva quando uno studente completa un corso"

#: app/Services/Integrations/TutorLms/LessonCompletedTrigger.php:26
msgid "This funnel runs when a student completes a Lesson"
msgstr "Questo funnel si attiva quando uno studente completa una lezione"

#: app/Services/Integrations/LifterLms/LessonCompletedTrigger.php:30
msgid "This funnel runs when a student completes a lesson"
msgstr "Questo funnel si attiva quando uno studente completa una lezione"

#: app/Services/Integrations/LearnDash/TopicCompletedTrigger.php:24
msgid "This funnel runs when a student completes a lesson topic"
msgstr ""
"Questo funnel si attiva quando uno studente completa un argomento di lezione"

#: app/Services/Integrations/LifterLms/MembershipEnrollTrigger.php:30
msgid "This funnel runs when a student has been enrolled in a membership level"
msgstr ""
"Questo funnel si attiva quando uno studente viene iscritto a un livello di "
"abbonamento"

#: app/Services/Integrations/LearnDash/CourseEnrollTrigger.php:25
#: app/Services/Integrations/TutorLms/CourseEnrollTrigger.php:26
msgid "This funnel runs when a student is enrolled in a course"
msgstr "Questo funnel si attiva quando uno studente viene iscritto a un corso"

#: app/Services/Integrations/LearnDash/CourseLeaveTrigger.php:26
msgid "This funnel runs when a student leaves a course"
msgstr "Questo funnel si attiva quando uno studente lascia un corso"

#: app/Services/Integrations/MemberPress/SubscriptionExpiredTrigger.php:25
msgid "This funnel runs when a subscription expires"
msgstr "Questo funnel si attiva quando un abbonamento scade"

#: app/Services/Integrations/LearnDash/GroupEnrollTrigger.php:25
msgid "This funnel runs when a user is enrolled in a group"
msgstr "Questo funnel si attiva quando un utente viene iscritto a un gruppo"

#: app/Services/Funnel/Triggers/TrackingEventRecordedTrigger.php:32
#: app/Services/Funnel/Triggers/TrackingEventRecordedTrigger.php:41
msgid ""
"This Funnel will be initiated a tracking event has been recorded for a "
"contact"
msgstr ""
"Questo funnel verrà avviato quando un evento di tracciamento viene "
"registrato per un contatto."

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:24
#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:33
msgid ""
"This Funnel will be initiated when a cart has been abandoned in WooCommerce"
msgstr ""
"Questo funnel verrà avviato quando un carrello viene abbandonato in "
"WooCommerce."

#: app/Services/Integrations/AffiliateWP/AffiliateWPAffActiveTrigger.php:49
msgid ""
"This funnel will be initiated when a new affiliate gets approved/registered "
"directly"
msgstr ""
"Questo funnel verrà avviato quando un nuovo affiliato viene "
"approvato/registrato direttamente."

#: app/Services/Funnel/Triggers/UserLoginTrigger.php:26
#: app/Services/Funnel/Triggers/UserLoginTrigger.php:35
msgid "This Funnel will be initiated when a user login to your site"
msgstr "Questo funnel verrà avviato quando un utente accede al tuo sito."

#: app/Services/Integrations/AffiliateWP/AffiliateWPAffActiveTrigger.php:64
msgid ""
"This Funnel will be initiated when affiliate will be approved or register as "
"direct approved"
msgstr ""
"Questo funnel verrà avviato quando un affiliato viene approvato o registrato "
"come approvato direttamente."

#: app/Services/Integrations/Edd/EddLicenseExpiredTrigger.php:40
msgid "This Funnel will start a license status get marked as expired"
msgstr ""
"Questo funnel verrà avviato quando lo stato di una licenza viene "
"contrassegnato come scaduto."

#: app/Services/Integrations/WooCommerce/WooOrderSuccessTrigger.php:27
msgid "This funnel will start once a new order will be added as processing"
msgstr ""
"Questo funnel verrà avviato una volta che un nuovo ordine viene aggiunto "
"come in elaborazione."

#: app/Services/Integrations/Edd/EddRecurringExpired.php:41
msgid ""
"This Funnel will start once a Recurring Subscription status changed to "
"expired"
msgstr ""
"Questo funnel verrà avviato una volta che lo stato di un abbonamento "
"ricorrente è cambiato in scaduto."

#: app/Services/Integrations/Edd/EddRecurringPaymentTrigger.php:41
msgid ""
"This Funnel will start once a Renewal Payment received for an active "
"subscription"
msgstr ""
"Questo funnel verrà avviato una volta ricevuto un pagamento di rinnovo per "
"un abbonamento attivo."

#: app/Services/Integrations/Edd/EddPaymentRefundTrigger.php:41
msgid "This Funnel will start once an order get marked as refunded"
msgstr ""
"Questo funnel verrà avviato una volta che un ordine viene contrassegnato "
"come rimborsato."

#: app/Services/Integrations/Edd/EddPaymentSuccessTrigger.php:25
msgid "This funnel will start once new order payment is successful"
msgstr ""
"Questo funnel verrà avviato una volta che il pagamento di un nuovo ordine è "
"andato a buon fine."

#: app/Services/Integrations/SureCart/SureCartPaymentSuccessTrigger.php:42
#: app/Services/Integrations/Edd/EddPaymentSuccessTrigger.php:41
msgid ""
"This Funnel will start once new order will be added as successful payment"
msgstr ""
"Questo funnel verrà avviato una volta che un nuovo ordine viene aggiunto "
"come pagamento completato con successo."

#: app/Services/Integrations/WooCommerce/WooOrderCompletedTrigger.php:41
msgid "This Funnel will start once new order will be marked as completed"
msgstr ""
"Questo funnel verrà avviato una volta che un nuovo ordine viene "
"contrassegnato come completato."

#: app/Services/Integrations/WooCommerce/WooOrderSuccessTrigger.php:43
msgid ""
"This Funnel will start once new order will be marked as successful payment"
msgstr ""
"Questo funnel verrà avviato una volta che un nuovo ordine viene "
"contrassegnato come pagamento completato con successo."

#: app/Services/Integrations/SureCart/SureCartPaymentRefundTrigger.php:41
msgid "This Funnel will start once order will be refunded"
msgstr "Questo funnel verrà avviato una volta che un ordine viene rimborsato."

#: app/Services/Integrations/Edd/EddPaymentRefundTrigger.php:26
msgid "This Funnel will start once payment refunded for an order"
msgstr ""
"Questo funnel verrà avviato una volta che il pagamento di un ordine viene "
"rimborsato."

#: app/Services/Integrations/Edd/EddLicenseExpiredTrigger.php:25
msgid "This funnel will start when a license gets expired"
msgstr "Questo funnel verrà avviato quando una licenza scade."

#: app/Services/Integrations/WishlistMember/WishlistMembershipTrigger.php:41
msgid "This funnel will start when a member is added to a level"
msgstr ""
"Questo funnel verrà avviato quando un membro viene aggiunto a un livello."

#: app/Services/Integrations/RCP/RCPMembershipTrigger.php:41
msgid ""
"This funnel will start when a member is added to a level for the first time"
msgstr ""
"Questo funnel verrà avviato quando un membro viene aggiunto a un livello per "
"la prima volta."

#: app/Services/Integrations/RCP/RCPMembershipCancelTrigger.php:41
msgid "This funnel will start when a membership has been cancelled"
msgstr "Questo funnel verrà avviato quando un abbonamento viene annullato."

#: app/Services/Integrations/RCP/RCPMembershipExpireTrigger.php:41
msgid "This funnel will start when a membership has been expired"
msgstr "Questo funnel verrà avviato quando un abbonamento è scaduto."

#: app/Services/Integrations/PMPro/PMProPMProExpiryLevelTrigger.php:41
msgid "This funnel will start when a membership has been expired for a user"
msgstr "Questo funnel verrà avviato quando un abbonamento scade per un utente."

#: app/Services/Integrations/MemberPress/MembershipTrigger.php:41
msgid ""
"This funnel will start when a membership level get activated for a member"
msgstr ""
"Questo funnel verrà avviato quando un livello di abbonamento viene attivato "
"per un membro."

#: app/Services/Integrations/WooCommerce/WooOrderStatusChangeTrigger.php:41
msgid ""
"This Funnel will start when a Order status will change from one state to "
"another"
msgstr ""
"Questo funnel verrà avviato quando lo stato di un ordine cambia da uno stato "
"all'altro."

#: app/Services/Integrations/WooCommerce/WooSubscriptionRenewalPaymentTrigger.php:27
msgid ""
"This funnel will start when a recurring payment received for a subscription"
msgstr ""
"Questo funnel verrà avviato quando viene ricevuto un pagamento ricorrente "
"per un abbonamento."

#: app/Services/Integrations/Edd/EddRecurringExpired.php:25
msgid "This funnel will start when a recurring subscription gets expired"
msgstr "Questo funnel verrà avviato quando un abbonamento ricorrente scade."

#: app/Services/Integrations/Edd/EddRecurringPaymentTrigger.php:25
msgid ""
"This funnel will start when a renewal payment is received for an active "
"subscription"
msgstr ""
"Questo funnel verrà avviato quando viene ricevuto un pagamento di rinnovo "
"per un abbonamento attivo."

#: app/Services/Integrations/LifterLms/CourseCompletedTrigger.php:41
#: app/Services/Integrations/LearnDash/CourseCompletedTrigger.php:40
#: app/Services/Integrations/TutorLms/CourseCompletedTrigger.php:41
msgid "This Funnel will start when a student completes a Course"
msgstr "Questo funnel verrà avviato quando uno studente completa un corso."

#: app/Services/Integrations/TutorLms/LessonCompletedTrigger.php:41
msgid "This Funnel will start when a student completes a Lesson"
msgstr "Questo funnel verrà avviato quando uno studente completa una lezione."

#: app/Services/Integrations/LifterLms/LessonCompletedTrigger.php:45
#: app/Services/Integrations/LearnDash/LessonCompletedTrigger.php:40
msgid "This Funnel will start when a student completes a lesson"
msgstr "Questo funnel verrà avviato quando uno studente completa una lezione."

#: app/Services/Integrations/LifterLms/MembershipEnrollTrigger.php:45
msgid "This funnel will start when a student is added in a Membership"
msgstr ""
"Questo funnel verrà avviato quando uno studente viene aggiunto a un "
"abbonamento."

#: app/Services/Integrations/LifterLms/CourseEnrollTrigger.php:45
#: app/Services/Integrations/LearnDash/CourseEnrollTrigger.php:42
#: app/Services/Integrations/TutorLms/CourseEnrollTrigger.php:41
msgid "This Funnel will start when a student is enrolled in a course"
msgstr "Questo funnel verrà avviato quando uno studente si iscrive a un corso."

#: app/Services/Integrations/LearnDash/GroupEnrollTrigger.php:40
msgid "This Funnel will start when a student is enrolled in a group"
msgstr ""
"Questo funnel verrà avviato quando uno studente viene iscritto a un gruppo."

#: app/Services/Integrations/LearnDash/CourseLeaveTrigger.php:43
msgid "This Funnel will start when a student is leave from a course"
msgstr "Questo funnel verrà avviato quando uno studente abbandona un corso."

#: app/Services/Integrations/WooCommerce/WooSubscriptionExpiredTrigger.php:27
msgid "This funnel will start when a subscription expires"
msgstr "Questo funnel verrà avviato quando un abbonamento scade."

#: app/Services/Integrations/MemberPress/SubscriptionExpiredTrigger.php:40
msgid "This funnel will start when a subscription has been expired"
msgstr "Questo funnel verrà avviato quando un abbonamento è scaduto."

#: app/Services/Integrations/WooCommerce/WooSubscriptionRenewalFailedTrigger.php:27
msgid "This funnel will start when a subscription payment fails"
msgstr ""
"Questo funnel verrà avviato quando un pagamento di abbonamento fallisce."

#: app/Services/Integrations/LearnDash/TopicCompletedTrigger.php:39
msgid "This funnel will start when a user is completes a lesson topic"
msgstr ""
"Questo funnel verrà avviato quando un utente completa un argomento di "
"lezione."

#: app/Services/Integrations/WooCommerce/WooSubscriptionStartedTrigger.php:27
msgid ""
"This funnel will start when a WooCommerce subscription begins or its status "
"changes to active."
msgstr ""
"Questo funnel verrà avviato quando un abbonamento WooCommerce inizia o il "
"suo stato cambia in attivo."

#: app/Services/Integrations/WooCommerce/WooOrderCompletedTrigger.php:26
msgid "This funnel will start when an order is completed"
msgstr "Questo funnel verrà avviato quando un ordine è completato."

#: app/Services/Integrations/WooCommerce/WooOrderRefundedTrigger.php:26
msgid "This funnel will start when an order is refunded"
msgstr "Questo funnel verrà avviato quando un ordine viene rimborsato."

#: app/Services/Integrations/WooCommerce/WooOrderStatusChangeTrigger.php:25
msgid "This funnel will start when an order status changes"
msgstr "Questo funnel verrà avviato quando lo stato di un ordine cambia."

#: app/Services/Integrations/PMPro/PMProPMProMembershipTrigger.php:41
msgid "This funnel will start when an user is enrolled in Membership Levels"
msgstr ""
"Questo funnel verrà avviato quando un utente viene iscritto a livelli di "
"abbonamento."

#: app/Services/Integrations/SureCart/SureCartPaymentSuccessTrigger.php:26
msgid "This funnel will start when new order payment is successful"
msgstr ""
"Questo funnel verrà avviato quando il pagamento di un nuovo ordine è andato "
"a buon fine."

#: app/Services/Integrations/SureCart/SureCartPaymentRefundTrigger.php:25
msgid "This funnel will start when order will be revoked"
msgstr "Questo funnel verrà avviato quando un ordine verrà revocato."

#: app/Services/DynamicSegments/CustomSegment.php:51
msgid ""
"This is a custom segment and contacts are filter based your provided filters "
"on real time data."
msgstr ""
"Questo è un segmento personalizzato e i contatti sono filtrati in base ai "
"filtri forniti sui dati in tempo reale."

#: app/Modules/AbandonCart/Woo/WooCartTrackingInit.php:603
#, php-format
msgid "This order has been recovered from an FluentCRM abandoned cart. %s"
msgstr ""
"Questo ordine è stato recuperato da un carrello abbandonato di FluentCRM. %s"

#: app/Services/DynamicSegments/AffiliateWPSegment.php:22
msgid ""
"This segment contains all your Subscribed contacts which are also your "
"active Affiliates"
msgstr ""
"Questo segmento contiene tutti i tuoi contatti sottoscritti che sono anche "
"affiliati attivi."

#: app/Services/DynamicSegments/EddActiveCustomerSegment.php:22
msgid ""
"This segment contains all your Subscribed contacts which are also your EDD "
"Customers with atleast one purchase"
msgstr ""
"Questo segmento contiene tutti i tuoi contatti sottoscritti che sono anche "
"clienti EDD con almeno un acquisto."

#: app/Services/DynamicSegments/PMProMembersSegment.php:23
msgid ""
"This segment contains all your Subscribed contacts which are also your Paid "
"Membership Members"
msgstr ""
"Questo segmento contiene tutti i tuoi contatti sottoscritti che sono anche "
"membri di abbonamenti a pagamento."

#: app/Services/DynamicSegments/WooCustomerSegment.php:22
msgid ""
"This segment contains all your Subscribed contacts which are also your "
"WooCommerce Customers"
msgstr ""
"Questo segmento contiene tutti i tuoi contatti sottoscritti che sono anche "
"clienti WooCommerce."

#: app/Services/DynamicSegments/WpUserSegment.php:22
msgid ""
"This segment contains all your Subscribed contacts which are also your "
"WordPress users"
msgstr ""
"Questo segmento contiene tutti i tuoi contatti sottoscritti che sono anche "
"utenti WordPress."

#: app/Services/Funnel/Benchmarks/EmailSequenceCompletedBenchmark.php:24
#: app/Services/Funnel/Benchmarks/EmailSequenceCompletedBenchmark.php:39
msgid "This will run once a selected email sequence is completed for a contact"
msgstr ""
"Questo verrà eseguito una volta completata una sequenza email selezionata "
"per un contatto."

#: app/Services/Funnel/Benchmarks/LinkClickBenchmark.php:36
#: app/Services/Funnel/Benchmarks/LinkClickBenchmark.php:52
msgid "This will run once a subscriber click on this provided link"
msgstr "Questo verrà eseguito quando un iscritto clicca sul link fornito."

#: app/Services/Integrations/Edd/EddSubscriptionActiveBenchmark.php:25
#: app/Services/Integrations/Edd/EddSubscriptionActiveBenchmark.php:48
msgid "This will run once a subscription gets active"
msgstr "Questo verrà eseguito quando un abbonamento diventa attivo."

#: app/Services/Integrations/WooCommerce/WooOrderSuccessBenchmark.php:26
#: app/Services/Integrations/SureCart/SureCartPaymentSuccessBenchmark.php:25
msgid "This will run once new order has been placed as processing"
msgstr ""
"Questo verrà eseguito una volta che un nuovo ordine viene messo in "
"elaborazione."

#: app/Services/Integrations/Edd/EddOrderSuccessBenchmark.php:25
#: app/Services/Integrations/Edd/EddOrderSuccessBenchmark.php:52
msgid "This will run once new order will be placed as completed in EDD"
msgstr ""
"Questo verrà eseguito una volta che un nuovo ordine viene completato in EDD."

#: app/Services/Integrations/WooCommerce/WooOrderSuccessBenchmark.php:53
#: app/Services/Integrations/SureCart/SureCartPaymentSuccessBenchmark.php:48
msgid "This will run once new order will be placed as processing"
msgstr ""
"Questo verrà eseguito una volta che un nuovo ordine viene messo in "
"elaborazione."

#: app/Services/Integrations/CRM/ContactCreatedTrigger.php:29
#: app/Services/Integrations/CRM/ContactCreatedTrigger.php:43
msgid "This will run when a new contact will be added"
msgstr "Questo verrà eseguito quando un nuovo contatto viene aggiunto."

#: app/Services/Integrations/CRM/RemoveFromCompanyTrigger.php:47
msgid ""
"This will run when any of the selected companies have been removed from a "
"contact"
msgstr ""
"Questo verrà eseguito quando una delle aziende selezionate viene rimossa da "
"un contatto."

#: app/Services/Integrations/CRM/RemoveFromListTrigger.php:47
msgid ""
"This will run when any of the selected lists have been removed from a contact"
msgstr ""
"Questo verrà eseguito quando una delle liste selezionate viene rimossa da un "
"contatto."

#: app/Services/Integrations/CRM/RemoveFromTagTrigger.php:47
msgid ""
"This will run when any of the selected tags have been removed from a contact"
msgstr ""
"Questo verrà eseguito quando uno dei tag selezionati viene rimosso da un "
"contatto."

#: app/Services/Integrations/CRM/CompanyAppliedTrigger.php:29
#: app/Services/Integrations/CRM/CompanyAppliedTrigger.php:46
msgid "This will run when selected companies have been applied to a contact"
msgstr ""
"Questo verrà eseguito quando le aziende selezionate vengono applicate a un "
"contatto."

#: app/Services/Integrations/CRM/RemoveFromCompanyTrigger.php:30
msgid "This will run when selected companies have been removed from a contact"
msgstr ""
"Questo verrà eseguito quando le aziende selezionate vengono rimosse da un "
"contatto."

#: app/Services/Integrations/CRM/ListAppliedTrigger.php:29
#: app/Services/Integrations/CRM/ListAppliedTrigger.php:46
msgid "This will run when selected lists have been applied to a contact"
msgstr ""
"Questo verrà eseguito quando le liste selezionate vengono applicate a un "
"contatto."

#: app/Services/Integrations/CRM/RemoveFromListTrigger.php:30
msgid "This will run when selected lists have been removed from a contact"
msgstr ""
"Questo verrà eseguito quando le liste selezionate vengono rimosse da un "
"contatto."

#: app/Services/Integrations/CRM/TagAppliedTrigger.php:29
#: app/Services/Integrations/CRM/TagAppliedTrigger.php:46
msgid "This will run when selected tags have been applied to a contact"
msgstr ""
"Questo verrà eseguito quando i tag selezionati vengono applicati a un "
"contatto."

#: app/Services/Integrations/CRM/RemoveFromTagTrigger.php:30
msgid "This will run when selected Tags have been removed from a contact"
msgstr ""
"Questo verrà eseguito quando i tag selezionati vengono rimossi da un "
"contatto."

#: app/Hooks/Handlers/DataExporter.php:530
msgid "Timezone"
msgstr "Fuso orario"

#: app/Hooks/Handlers/DataExporter.php:526
msgid "Title"
msgstr "Titolo"

#: app/Services/Integrations/Edd/DeepIntegration.php:368
msgid ""
"To sync and enable deep integration with Easy Digital Downloads customers "
"with FluentCRM, please configure and enable sync."
msgstr ""
"Per sincronizzare e abilitare l'integrazione avanzata con i clienti di Easy "
"Digital Downloads su FluentCRM, configura e abilita la sincronizzazione."

#: app/Services/Integrations/LearnDash/DeepIntegration.php:165
msgid ""
"To sync and enable deep integration with LearnDash students with FluentCRM, "
"please configure and enable sync."
msgstr ""
"Per sincronizzare e abilitare l'integrazione avanzata con gli studenti di "
"LearnDash su FluentCRM, configura e abilita la sincronizzazione."

#: app/Services/Integrations/LifterLms/DeepIntegration.php:160
msgid ""
"To sync and enable deep integration with LifterLMS students with FluentCRM, "
"please configure and enable sync."
msgstr ""
"Per sincronizzare e abilitare l'integrazione avanzata con gli studenti di "
"LifterLMS su FluentCRM, configura e abilita la sincronizzazione."

#: app/Services/Integrations/TutorLms/DeepIntegration.php:77
msgid ""
"To sync and enable deep integration with TutorLMS students with FluentCRM, "
"please configure and enable sync."
msgstr ""
"Per sincronizzare e abilitare l'integrazione avanzata con gli studenti di "
"TutorLMS su FluentCRM, configura e abilita la sincronizzazione."

#: app/Services/Integrations/WooCommerce/DeepIntegration.php:308
msgid ""
"To sync and enable deep integration with WooCommerce customers with "
"FluentCRM, please configure and enable sync."
msgstr ""
"Per sincronizzare e abilitare l'integrazione avanzata con i clienti di "
"WooCommerce su FluentCRM, configura e abilita la sincronizzazione."

#: app/Services/Integrations/Edd/AdvancedReport.php:94
msgid "to view in details edd reports"
msgstr "per visualizzare in dettaglio i report di EDD"

#: app/Services/Integrations/LearnDash/AdvancedReport.php:63
msgid "to view in details LearnDash reports"
msgstr "per visualizzare in dettaglio i report di LearnDash"

#: app/Services/Integrations/LifterLms/AdvancedReport.php:63
msgid "to view in details LifterLMS reports"
msgstr "per visualizzare in dettaglio i report di LifterLMS"

#: app/Services/Integrations/TutorLms/AdvancedReport.php:60
msgid "to view in details TutorLms reports"
msgstr "per visualizzare in dettaglio i report di TutorLMS"

#: app/Services/Integrations/WooCommerce/AdvancedReport.php:92
msgid "to view in details WooCommerce reports"
msgstr "per visualizzare in dettaglio i report di WooCommerce"

#: app/Services/Integrations/LearnDash/TopicCompletedTrigger.php:23
msgid "Topic Completed"
msgstr "Argomento completato"

#: app/Services/Integrations/WooCommerce/WooSmartCodeParse.php:269
#: app/Services/Integrations/Edd/EddSmartCodeParse.php:309
msgid "Total"
msgstr "Totale"

#: app/Services/Integrations/Edd/AdvancedReport.php:155
msgid "Total Activated Sites (Licenses)"
msgstr "Totale siti attivati (licenze)"

#: app/Services/Integrations/LifterLms/AdvancedReport.php:73
#: app/Services/Integrations/LearnDash/AdvancedReport.php:73
#: app/Services/Integrations/TutorLms/AdvancedReport.php:70
msgid "Total Course Enrollments"
msgstr "Totale iscrizioni ai corsi"

#: app/Services/Integrations/WooCommerce/AdvancedReport.php:117
#: app/Services/Integrations/Edd/AdvancedReport.php:119
msgid "Total Customers"
msgstr "Totale clienti"

#: app/Services/Integrations/Edd/EddCommerceHelper.php:182
msgid "Total Lifetime Licenses"
msgstr "Totale licenze a vita"

#: app/Services/Integrations/LifterLms/AdvancedReport.php:78
#: app/Services/Integrations/LearnDash/AdvancedReport.php:78
msgid "Total Membership Enrollments"
msgstr "Totale iscrizioni ai membri"

#: app/Services/Integrations/WooCommerce/AutomationConditions.php:48
#: app/Services/Integrations/WooCommerce/DeepIntegration.php:203
#: app/Services/Integrations/Edd/AutomationConditions.php:36
#: app/Services/Integrations/Edd/DeepIntegration.php:283
msgid "Total Order Count"
msgstr "Conteggio totale degli ordini"

#: app/Services/Integrations/WooCommerce/AutomationConditions.php:56
#: app/Services/Integrations/WooCommerce/AutomationConditions.php:163
#: app/Services/Integrations/WooCommerce/DeepIntegration.php:211
#: app/Services/Integrations/Edd/AutomationConditions.php:43
#: app/Services/Integrations/Edd/DeepIntegration.php:290
msgid "Total Order Value"
msgstr "Valore totale dell'ordine"

#: app/Services/Integrations/WooCommerce/AdvancedReport.php:113
#: app/Services/Integrations/Edd/AdvancedReport.php:115
#: app/Services/Integrations/Edd/EddCommerceHelper.php:34
msgid "Total Orders"
msgstr "Totale ordini"

#: app/Services/Integrations/Edd/EddCommerceHelper.php:187
msgid "Total Recurring Licenses"
msgstr "Totale licenze ricorrenti"

#: app/Services/Integrations/AffiliateWP/DeepIntegration.php:73
msgid "Total Referrals"
msgstr "Totale referral"

#: app/Services/Integrations/WooCommerce/AdvancedReport.php:108
#: app/Services/Integrations/Edd/AdvancedReport.php:110
msgid "Total Revenue"
msgstr "Totale entrate"

#: app/Services/Integrations/Edd/EddCommerceHelper.php:26
msgid "Total Sales"
msgstr "Totale vendite"

#: app/Services/Integrations/LifterLms/AdvancedReport.php:68
#: app/Services/Integrations/LearnDash/AdvancedReport.php:68
#: app/Services/Integrations/TutorLms/AdvancedReport.php:65
msgid "Total Students"
msgstr "Totale studenti"

#: app/Services/Funnel/Triggers/TrackingEventRecordedTrigger.php:31
#: app/Services/Funnel/Triggers/TrackingEventRecordedTrigger.php:40
msgid "Tracking Event Recorded"
msgstr "Evento di tracciamento registrato"

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:69
#: app/Services/Funnel/Triggers/TrackingEventRecordedTrigger.php:86
#: app/Services/Funnel/Conditions/FunnelCondition.php:50
msgid "True if all conditions match"
msgstr "Vero se tutte le condizioni corrispondono"

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:70
#: app/Services/Funnel/Triggers/TrackingEventRecordedTrigger.php:87
#: app/Services/Funnel/Conditions/FunnelCondition.php:51
msgid "True if any of the conditions match"
msgstr "Vero se una qualsiasi delle condizioni corrisponde"

#: app/Services/Integrations/TutorLms/LessonCompletedTrigger.php:23
#: app/Services/Integrations/TutorLms/LessonCompletedTrigger.php:107
#: app/Services/Integrations/TutorLms/CourseCompletedTrigger.php:23
#: app/Services/Integrations/TutorLms/CourseCompletedTrigger.php:107
#: app/Services/Integrations/TutorLms/AutomationConditions.php:17
#: app/Services/Integrations/TutorLms/RemoveFromCourseAction.php:21
#: app/Services/Integrations/TutorLms/DeepIntegration.php:26
#: app/Services/Integrations/TutorLms/DeepIntegration.php:74
#: app/Services/Integrations/TutorLms/Helper.php:115
#: app/Services/Integrations/TutorLms/CourseEnrollTrigger.php:23
#: app/Services/Integrations/TutorLms/CourseEnrollTrigger.php:104
#: app/Services/Integrations/TutorLms/AddToCourseAction.php:21
msgid "TutorLMS"
msgstr "TutorLMS"

#: app/Services/Integrations/TutorLms/AdvancedReport.php:54
msgid "TutorLms - Advanced Reports"
msgstr "TutorLMS - Report avanzati"

#: app/Services/Integrations/TutorLms/TutorLmsInit.php:64
msgid "TutorLMS Courses"
msgstr "Corsi TutorLMS"

#: app/Services/Integrations/TutorLms/DeepIntegration.php:76
msgid "TutorLMS students are not synced with FluentCRM yet."
msgstr "Gli studenti di TutorLMS non sono ancora sincronizzati con FluentCRM."

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:199
#: app/Services/Integrations/BuddyPress/BBInit.php:108
#: app/Services/Funnel/Conditions/FunnelCondition.php:131
msgid "Type"
msgstr "Tipo"

#: app/Services/Integrations/WooCommerce/AddOrderNoteAction.php:54
msgid ""
"Type the note that you want to add to the reference order. You can also use "
"smart tags"
msgstr ""
"Digita la nota che desideri aggiungere all'ordine di riferimento. Puoi anche "
"utilizzare tag intelligenti."

#: app/Services/Integrations/AffiliateWP/DeepIntegration.php:95
msgid "Unpaid Earnings"
msgstr "Guadagni non pagati"

#: app/Services/Integrations/CRM/AdvancedReport.php:51
msgid "Unsubscribe Stats"
msgstr "Statistiche di cancellazione"

#: app/Services/Funnel/Actions/UpdateContactPropertyAction.php:23
#: app/Services/Funnel/Actions/UpdateContactPropertyAction.php:40
msgid "Update Contact Property"
msgstr "Aggiorna proprietà contatto"

#: app/Services/Funnel/Actions/UpdateContactPropertyAction.php:24
#: app/Services/Funnel/Actions/UpdateContactPropertyAction.php:41
msgid "Update custom fields or few main property of a contact"
msgstr ""
"Aggiorna campi personalizzati o alcune proprietà principali di un contatto"

#: app/Services/Funnel/Actions/UpdateUserMetaAction.php:23
#: app/Services/Funnel/Actions/UpdateUserMetaAction.php:41
msgid "Update WordPress User Meta Data"
msgstr "Aggiorna Meta Dati Utente di WordPress"

#: app/Services/Funnel/Actions/UpdateUserMetaAction.php:22
#: app/Services/Funnel/Actions/UpdateUserMetaAction.php:40
msgid "Update WP User Meta"
msgstr "Aggiorna WP User Meta"

#: app/Hooks/Handlers/DataExporter.php:545
msgid "Updated At"
msgstr "Aggiornato il"

#: app/Services/Integrations/WooCommerce/AutomationConditions.php:117
#: app/Services/Integrations/WooCommerce/DeepIntegration.php:275
#: app/Services/Integrations/Edd/AutomationConditions.php:80
#: app/Services/Integrations/Edd/DeepIntegration.php:336
msgid "Used Coupons"
msgstr "Buoni utilizzati"

#: app/Services/Integrations/LearnDash/AddToCourseAction.php:109
msgid "User already in the course"
msgstr "Utente già iscritto al corso"

#: app/Services/Integrations/LifterLms/AddToMembershipAction.php:114
#: app/Services/Integrations/LearnDash/AddToGroupAction.php:109
msgid "User already in the group"
msgstr "Utente già nel gruppo"

#: app/Services/Integrations/LifterLms/AddToCourseAction.php:111
#: app/Services/Integrations/LearnDash/AddToCourseAction.php:118
msgid "User could not be enrolled to the selected course"
msgstr "Impossibile iscrivere l'utente al corso selezionato"

#: app/Services/Integrations/TutorLms/AddToCourseAction.php:120
msgid ""
"User could not be enrolled to the selected course. Maybe course is already "
"enrolled or Tutor failed to enroll the course"
msgstr ""
"Impossibile iscrivere l'utente al corso selezionato. Potrebbe essere già "
"iscritto o Tutor ha fallito l'iscrizione."

#: app/Services/Integrations/LearnDash/RemoveFromCourseAction.php:76
msgid "User could not be removed from the selected course"
msgstr "Impossibile rimuovere l'utente dal corso selezionato"

#: app/Services/Integrations/LearnDash/RemoveFromCourseAction.php:67
msgid "User does not have this course access"
msgstr "L'utente non ha accesso a questo corso"

#: app/Hooks/Handlers/DataExporter.php:525
msgid "User ID"
msgstr "ID Utente"

#: app/Services/Funnel/Triggers/UserLoginTrigger.php:25
#: app/Services/Funnel/Triggers/UserLoginTrigger.php:34
msgid "User Login"
msgstr "Accesso utente"

#: app/Services/Funnel/Actions/UpdateUserMetaAction.php:46
#: app/Services/Funnel/Actions/UserRegistrationAction.php:79
msgid "User Meta Key"
msgstr "Chiave Meta Utente"

#: app/Services/Funnel/Actions/UpdateUserMetaAction.php:44
#: app/Services/Funnel/Actions/UserRegistrationAction.php:77
msgid "User Meta Mapping"
msgstr "Mappatura Meta Utente"

#: app/Services/Funnel/Actions/UpdateUserMetaAction.php:47
#: app/Services/Funnel/Actions/UserRegistrationAction.php:80
msgid "User Meta Value"
msgstr "Valore Meta Utente"

#: app/Services/Funnel/Actions/UserRegistrationAction.php:88
msgid "User Notification"
msgstr "Notifica Utente"

#: app/Services/Funnel/Actions/UserRegistrationAction.php:51
#: app/Services/Funnel/Actions/ChangeUserRoleAction.php:41
#: app/Services/Funnel/Actions/UserRoleRemoveAction.php:38
msgid "User Role"
msgstr "Ruolo Utente"

#: app/Services/Integrations/Edd/AutomationConditions.php:106
msgid "Valid License"
msgstr "Licenza valida"

#: app/Services/PluginManager/LicenseManager.php:51
msgid "View FluentCRM documentation"
msgstr "Visualizza la documentazione di FluentCRM"

#: app/Services/Integrations/LifterLms/DeepIntegration.php:48
#: app/Services/Integrations/WooCommerce/DeepIntegration.php:43
#: app/Services/Integrations/LearnDash/DeepIntegration.php:42
#: app/Services/Integrations/Edd/DeepIntegration.php:42
#: app/Services/Integrations/TutorLms/DeepIntegration.php:42
msgid "View Settings"
msgstr "Visualizza impostazioni"

#: app/Hooks/Handlers/VisualEmailBuilderHandler.php:78
msgid "Visual Builder"
msgstr "Builder Visivo"

#: app/Services/Integrations/WishlistMember/WishlistMembershipTrigger.php:23
#: app/Services/Integrations/WishlistMember/WishlistMembershipTrigger.php:101
#: app/Services/Integrations/WishlistMember/AutomationConditions.php:19
msgid "Wishlist Member"
msgstr "Wishlist Member"

#: app/Services/Integrations/Edd/DeepIntegration.php:366
msgid ""
"With EDD deep integration with FluentCRM, you easily segment your purchases, "
"lifetime values, purchase dates and target your customers more efficiently."
msgstr ""
"Con l'integrazione avanzata di EDD su FluentCRM, puoi segmentare facilmente "
"i tuoi acquisti, valori a vita, date di acquisto e mirare meglio ai tuoi "
"clienti."

#: app/Services/Funnel/Actions/HTTPSendDataAction.php:133
msgid "With Headers"
msgstr "Con intestazioni"

#: app/Services/Integrations/LearnDash/DeepIntegration.php:163
msgid ""
"With LearnDash deep integration with FluentCRM, you easily segment your "
"students by their enrollment, course dates and target your students more "
"efficiently."
msgstr ""
"Con l'integrazione avanzata di LearnDash su FluentCRM, puoi segmentare "
"facilmente gli studenti in base alle iscrizioni, date dei corsi e mirare "
"meglio agli studenti."

#: app/Services/Integrations/LifterLms/DeepIntegration.php:158
msgid ""
"With LifterLMS deep integration with FluentCRM, you easily segment your "
"students by their enrollment, course dates and target your students more "
"efficiently."
msgstr ""
"Con l'integrazione avanzata di LifterLMS su FluentCRM, puoi segmentare "
"facilmente gli studenti in base alle iscrizioni, date dei corsi e mirare "
"meglio agli studenti."

#: app/Services/Integrations/TutorLms/DeepIntegration.php:75
msgid ""
"With TutorLMS deep integration with FluentCRM, you easily segment your "
"students by their enrollment, course dates and target your students more "
"efficiently."
msgstr ""
"Con l'integrazione avanzata di TutorLMS su FluentCRM, puoi segmentare "
"facilmente gli studenti in base alle iscrizioni, date dei corsi e mirare "
"meglio agli studenti."

#: app/Services/Integrations/WooCommerce/DeepIntegration.php:306
msgid ""
"With WooCommerce deep integration with FluentCRM, you easily segment your "
"purchases, lifetime values, purchase dates and target your customers more "
"efficiently."
msgstr ""
"Con l'integrazione avanzata di WooCommerce su FluentCRM, puoi segmentare "
"facilmente i tuoi acquisti, valori a vita, date di acquisto e mirare meglio "
"ai tuoi clienti."

#: app/Http/Controllers/DynamicSegmentController.php:118
msgid "Within"
msgstr "Entro"

#: app/Services/Integrations/WooCommerce/AutomationConditions.php:213
msgid "Woo Current Order"
msgstr "Ordine Corrente Woo"

#: app/Services/Integrations/WooCommerce/WooInit.php:338
msgid "Woo Subscriptions (%d)"
msgstr "Abbonamenti Woo (%d)"

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:22
#: app/Services/Integrations/WooCommerce/WooSubscriptionStartedTrigger.php:24
#: app/Services/Integrations/WooCommerce/WooOrderCompletedTrigger.php:23
#: app/Services/Integrations/WooCommerce/AutomationConditions.php:154
#: app/Services/Integrations/WooCommerce/WooOrderStatusChangeTrigger.php:23
#: app/Services/Integrations/WooCommerce/AddOrderNoteAction.php:20
#: app/Services/Integrations/WooCommerce/WooSubscriptionRenewalFailedTrigger.php:24
#: app/Services/Integrations/WooCommerce/OrderStatusChangeAction.php:19
#: app/Services/Integrations/WooCommerce/DeepIntegration.php:32
#: app/Services/Integrations/WooCommerce/DeepIntegration.php:198
#: app/Services/Integrations/WooCommerce/DeepIntegration.php:305
#: app/Services/Integrations/WooCommerce/WooOrderRefundedTrigger.php:23
#: app/Services/Integrations/WooCommerce/WooOrderSuccessTrigger.php:25
#: app/Services/Integrations/WooCommerce/CreateCouponAction.php:22
#: app/Services/Integrations/WooCommerce/WooSubscriptionExpiredTrigger.php:24
#: app/Services/Integrations/WooCommerce/WooSubscriptionRenewalPaymentTrigger.php:24
msgid "WooCommerce"
msgstr "WooCommerce"

#: app/Services/Integrations/WooCommerce/AdvancedReport.php:84
msgid "WooCommerce - Advanced Reports"
msgstr "WooCommerce - Report avanzati"

#: app/Services/DynamicSegments/WooCustomerSegment.php:20
msgid "WooCommerce Customers"
msgstr "Clienti WooCommerce"

#: app/Services/Integrations/WooCommerce/DeepIntegration.php:307
msgid "WooCommerce customers are not synced with FluentCRM yet."
msgstr "I clienti WooCommerce non sono ancora sincronizzati con FluentCRM."

#: app/Services/DynamicSegments/WooCustomerSegment.php:21
msgid "WooCommerce customers who are also in the contact list as subscribed"
msgstr "Clienti WooCommerce che sono anche nella lista contatti come iscritti"

#: app/Services/Integrations/WooCommerce/WooOrderCompletedTrigger.php:40
msgid "WooCommerce Order has been completed"
msgstr "Ordine WooCommerce completato"

#: app/Services/Integrations/WooCommerce/WooOrderRefundedTrigger.php:40
msgid "WooCommerce Order has been refunded"
msgstr "Ordine WooCommerce rimborsato"

#: app/Services/Integrations/WooCommerce/WooOrderStatusChangeTrigger.php:40
msgid "WooCommerce Order Status Changed"
msgstr "Stato ordine WooCommerce cambiato"

#: app/Services/Integrations/WooCommerce/WooSubscriptionRenewalFailedTrigger.php:71
msgid "WooCommerce Renewal Payment failed"
msgstr "Pagamento di rinnovo WooCommerce fallito"

#: app/Services/Integrations/WooCommerce/WooSubscriptionRenewalPaymentTrigger.php:71
msgid "WooCommerce Renewal Payment received"
msgstr "Pagamento di rinnovo WooCommerce ricevuto"

#: app/Services/Funnel/Actions/UpdateUserMetaAction.php:21
#: app/Services/Funnel/Actions/UserRegistrationAction.php:22
#: app/Services/Funnel/Actions/ChangeUserRoleAction.php:20
#: app/Services/Funnel/Actions/UserRoleRemoveAction.php:20
msgid "WordPress"
msgstr "WordPress"

#: app/Services/Funnel/Triggers/UserLoginTrigger.php:24
msgid "WordPress Triggers"
msgstr "Trigger WordPress"

#: app/Services/Integrations/LifterLms/AddToMembershipAction.php:62
#: app/Services/Integrations/LifterLms/AddToCourseAction.php:63
#: app/Services/Integrations/LearnDash/AddToGroupAction.php:63
#: app/Services/Integrations/LearnDash/AddToCourseAction.php:63
#: app/Services/Integrations/TutorLms/AddToCourseAction.php:62
msgid ""
"WordPress user will be created if no user found with the contact's email "
"address"
msgstr ""
"Verrà creato un utente WordPress se nessun utente è trovato con l'email del "
"contatto."

#: app/Services/DynamicSegments/WpUserSegment.php:20
msgid "WordPress Users"
msgstr "Utenti WordPress"

#: app/Services/DynamicSegments/WpUserSegment.php:21
msgid "WordPress users who are also in the contact list as subscribed"
msgstr "Utenti WordPress che sono anche nella lista contatti come iscritti"

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:194
#: app/Services/Funnel/Conditions/FunnelCondition.php:126
msgid "WP User ID"
msgstr "ID Utente WP"

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:253
#: app/Services/Funnel/Conditions/FunnelCondition.php:185
msgid "WP User Role"
msgstr "Ruolo Utente WP"

#: app/Services/Integrations/AffiliateWP/DeepIntegration.php:61
msgid "Yes"
msgstr "Sì"

#: app/Http/Controllers/CampaignsProController.php:100
msgid "You can do this action if campaign is in archived status only"
msgstr "Puoi fare questa azione solo se la campagna è in stato archiviato."

#: app/Services/Integrations/AffiliateWP/DeepIntegration.php:224
msgid ""
"You can sync all your Affiliates into FluentCRM. After this sync you can "
"segment your contacts easily"
msgstr ""
"Puoi sincronizzare tutti i tuoi affiliati su FluentCRM. Dopo questa "
"sincronizzazione, puoi segmentare facilmente i tuoi contatti."

#: app/Services/Integrations/Edd/EddImporter.php:151
msgid ""
"You can sync all your Easy Digital Downloads Customers into FluentCRM and "
"all future customers and purchase data will be synced."
msgstr ""
"Puoi sincronizzare tutti i tuoi clienti di Easy Digital Downloads su "
"FluentCRM e tutti i futuri clienti e dati di acquisto saranno sincronizzati."

#: app/Services/Integrations/WooCommerce/WooImporter.php:157
msgid ""
"You can sync all your WooCommerce Customers into FluentCRM and all future "
"customers and purchase data will be synced."
msgstr ""
"Puoi sincronizzare tutti i tuoi clienti WooCommerce su FluentCRM e tutti i "
"futuri clienti e dati di acquisto saranno sincronizzati."

#: app/Services/Integrations/LifterLms/LifterInit.php:87
msgid "You do not have permission to access this student\\'s reports"
msgstr "Non hai il permesso di accedere ai report di questo studente."

#: app/Modules/AbandonCart/Woo/WooCartTrackingInit.php:143
msgid "You have opted out from cart tracking"
msgstr "Hai scelto di non monitorare il carrello"

#: app/Services/Integrations/LearnDash/TopicCompletedTrigger.php:86
#: app/Services/Integrations/LearnDash/LessonCompletedTrigger.php:86
msgid "You must select a course"
msgstr "Devi selezionare un corso"

#: app/Services/Integrations/LearnDash/TopicCompletedTrigger.php:93
msgid "You must select a topic"
msgstr "Devi selezionare un argomento"

#: app/Modules/AbandonCart/Woo/WooCartTrackingInit.php:450
msgid "Your cart has been restored"
msgstr "Il tuo carrello è stato ripristinato"

#: app/Http/Controllers/LicenseController.php:57
msgid "Your license key has been successfully deactivated"
msgstr "La tua chiave di licenza è stata disattivata con successo"

#: app/Http/Controllers/LicenseController.php:40
msgid "Your license key has been successfully updated"
msgstr "La tua chiave di licenza è stata aggiornata con successo"

#: app/Services/Funnel/Benchmarks/LinkClickBenchmark.php:65
msgid "Your Target URL"
msgstr "URL di destinazione"
