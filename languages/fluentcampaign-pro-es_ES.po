msgid ""
msgstr ""
"Project-Id-Version: FluentCRM Pro\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-03-07 14:25+0000\n"
"PO-Revision-Date: 2024-10-16 12:22+0000\n"
"Last-Translator: \n"
"Language-Team: Spanish (Spain)\n"
"Language: es_ES\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Loco https://localise.biz/\n"
"X-Loco-Version: 2.6.1; wp-5.9.2\n"
"X-Domain: fluentcampaign-pro"

#: app/Http/Controllers/RecurringCampaignController.php:120
msgid ""
"##crm.manage_subscription_url## or ##crm.unsubscribe_url## string is "
"required for compliance. Please include unsubscription or manage "
"subscription link"
msgstr ""

#: app/Http/Controllers/CampaignsProController.php:37
msgid "%d Emails has been scheduled to resend"
msgstr "se ha programado el reenvío de %d Emails"

#: app/Services/PostParser/views/latest-post/layout-6.php:32
#: app/Services/PostParser/views/latest-post/layout-7.php:34
#: app/Services/PostParser/views/latest-post/layout-5.php:20
#: app/Services/PostParser/views/latest-post/layout-4.php:26
#: app/Services/PostParser/views/latest-post/layout-3.php:48
#: app/Services/PostParser/views/latest-post/layout-2.php:53
#: app/Services/PostParser/views/latest-post/default.php:33
msgid "(no title)"
msgstr ""

#: app/Services/PostParser/views/latest-post/layout-6.php:49
#: app/Services/PostParser/views/latest-post/layout-6.php:52
#: app/Services/PostParser/views/latest-post/layout-4.php:57
#: app/Services/PostParser/views/latest-post/layout-3.php:34
#: app/Services/PostParser/views/latest-post/layout-2.php:39
msgid "-"
msgstr ""

#: app/Http/Controllers/DynamicSegmentController.php:315
#: app/Http/Controllers/SequenceController.php:97
#: app/Http/Controllers/RecurringCampaignController.php:208
#: app/Http/Controllers/SequenceMailController.php:93
msgid "[Duplicate] "
msgstr "[Duplicado]"

#: app/Services/Integrations/Edd/EddSubscriptionActiveBenchmark.php:24
msgid "[EDD] Recurring Subscription Active"
msgstr "[EDD] Suscripción Recurrente Activa"

#: app/Services/Integrations/LearnDash/LdInit.php:161
msgid "[FluentCRM] Apply Tags on course completion"
msgstr "[FluentCRM] Aplicar etiquetas al finalizar el curso"

#: app/Services/Integrations/LearnDash/LdInit.php:150
msgid "[FluentCRM] Apply Tags on course enrollment"
msgstr "[FluentCRM] Aplicar etiquetas en la inscripción de cursos"

#: app/Services/Integrations/LearnDash/LdInit.php:190
msgid "[FluentCRM] Apply Tags on group enrollment"
msgstr "[FluentCRM] Aplicar etiquetas en la inscripción de grupos"

#: app/Services/Integrations/LearnDash/LdInit.php:201
msgid "[FluentCRM] Remove Tags on group leave"
msgstr "[FluentCRM] Eliminar etiquetas al salir del grupo"

#: app/Services/Integrations/RCP/RCPMembershipTrigger.php:40
#: app/Services/Integrations/WishlistMember/WishlistMembershipTrigger.php:40
#: app/Services/Integrations/MemberPress/MembershipTrigger.php:40
msgid "A member added to a membership level"
msgstr "Un miembro añadido a un nivel de membresía"

#: app/Services/Integrations/MemberPress/SubscriptionExpiredTrigger.php:39
msgid "A Subscription expired"
msgstr "A La suscripción ha caducado"

#: app/Modules/AbandonCart/AbandonCart.php:23
#: app/Modules/AbandonCart/AbandonCart.php:24
#: app/Modules/AbandonCart/AbandonCart.php:37
msgid "Abandoned Carts"
msgstr ""

#: app/Services/Integrations/Edd/EddCommerceHelper.php:192
msgid "Activated Sites"
msgstr "Sitios activados"

#: app/Services/Integrations/AffiliateWP/DeepIntegration.php:82
msgid "Active"
msgstr "Activo"

#: app/Services/DynamicSegments/AffiliateWPSegment.php:20
msgid "Active Affiliates (AffiliateWP)"
msgstr "Afiliados activos (AffiliateWP)"

#: app/Services/DynamicSegments/AffiliateWPSegment.php:21
msgid "Active Affiliates who are also in the contact list as subscribed"
msgstr ""
"Afiliados activos que también están en la lista de contactos como suscritos"

#: app/Services/Integrations/Edd/AdvancedReport.php:159
msgid "Active Licenses"
msgstr "Licencias activas"

#: app/Services/Integrations/Edd/AutomationConditions.php:118
msgid "Active Subscription"
msgstr "Suscripción activa"

#: app/Http/Controllers/DynamicSegmentController.php:193
msgid ""
"Activity on your site login, email link click or various other activities"
msgstr ""
"Actividad en el inicio de sesión de su sitio, clic en el enlace de correo "
"electrónico o varias otras actividades"

#: app/Services/Funnel/Actions/AddActivityAction.php:56
msgid "Activity Title"
msgstr "Título de la actividad"

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:76
#: app/Services/Funnel/Conditions/FunnelCondition.php:57
msgid "Add Condition to check your contact's properties"
msgstr "Añadir condición para comprobar las propiedades del contacto"

#: app/Services/Funnel/Triggers/TrackingEventRecordedTrigger.php:94
msgid "Add Condition to check your event tracking properties"
msgstr ""

#: app/Services/Funnel/Actions/AddEventTrackerAction.php:27
msgid "Add Event Tracking"
msgstr ""

#: app/Services/Funnel/Actions/AddEventTrackerAction.php:28
msgid "Add Event Tracking for Contact"
msgstr ""

#: app/Services/Funnel/Actions/AddEventTrackerAction.php:42
msgid "Add Event Tracking to Contact Profile"
msgstr ""

#: app/Services/Integrations/WooCommerce/AddOrderNoteAction.php:22
#: app/Services/Integrations/WooCommerce/AddOrderNoteAction.php:48
msgid "Add Note to WooCommerce Order"
msgstr "Añadir nota al pedido de WooCommerce"

#: app/Services/Funnel/Actions/AddActivityAction.php:22
msgid "Add Notes & Activity"
msgstr "Añadir notas y actividad"

#: app/Services/Funnel/Actions/AddActivityAction.php:44
msgid "Add Notes or Activity to Contact"
msgstr "Añadir notas o actividad al contacto"

#: app/Services/Funnel/Actions/AddActivityAction.php:23
#: app/Services/Funnel/Actions/AddActivityAction.php:45
msgid "Add Notes or Activity to the Contact Profile"
msgstr "Añadir notas o actividad al perfil del contacto"

#: app/Services/Integrations/ElementorFormIntegration/FormWidget.php:90
msgid "Add Only"
msgstr "Añadir sólo"

#: app/Services/Integrations/WooCommerce/AddOrderNoteAction.php:21
#: app/Services/Integrations/WooCommerce/AddOrderNoteAction.php:47
msgid "Add Order Note"
msgstr "Añadir nota de pedido"

#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:113
#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:147
#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:184
#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:221
#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:258
msgid "Add Tags"
msgstr "Añadir etiquetas"

#: app/Http/Controllers/CampaignsProController.php:29
msgid "Added to resend from failed"
msgstr "Añadido al reenvío desde el fracaso"

#: app/Hooks/Handlers/DataExporter.php:549
#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:155
#: app/Services/Funnel/Conditions/FunnelCondition.php:87
msgid "Address Line 1"
msgstr "Dirección Línea 1"

#: app/Hooks/Handlers/DataExporter.php:550
#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:160
#: app/Services/Funnel/Conditions/FunnelCondition.php:92
msgid "Address Line 2"
msgstr "Dirección Línea 2"

#: app/Services/Integrations/BuddyPress/BBInit.php:91
#: app/Services/Integrations/BuddyPress/BBInit.php:147
msgid "Admin"
msgstr "Administración"

#: app/Services/Funnel/Triggers/TrackingEventRecordedTrigger.php:84
msgid ""
"Advanced Event Tracking Conditions (Will check all tracking event for the "
"contact)"
msgstr ""

#: app/Services/Integrations/AffiliateWP/DeepIntegration.php:67
msgid "Affiliate ID"
msgstr "ID de afiliado"

#: app/Services/Integrations/AffiliateWP/DeepIntegration.php:223
msgid "Affiliates Sync"
msgstr "Sincronización de afiliados"

#: app/Services/Integrations/AffiliateWP/AffiliateWPAffActiveTrigger.php:47
#: app/Services/Integrations/AffiliateWP/DeepIntegration.php:53
#: app/Services/Integrations/AffiliateWP/DeepIntegration.php:174
msgid "AffiliateWP"
msgstr "AffiliateWP"

#: app/Services/Integrations/WooCommerce/WooImporter.php:157
#: app/Services/Integrations/Edd/EddImporter.php:151
msgid ""
"After this sync you can import by product by product and provide appropriate "
"tags"
msgstr ""
"Después de esta sincronización puede importar por producto y proporcionar "
"las etiquetas adecuadas"

#: app/Services/Integrations/LifterLms/AdvancedReport.php:40
#: app/Services/Integrations/WooCommerce/AdvancedReport.php:52
#: app/Services/Integrations/LearnDash/AdvancedReport.php:40
#: app/Services/Integrations/Edd/AdvancedReport.php:44
#: app/Services/Integrations/TutorLms/AdvancedReport.php:40
msgid "All"
msgstr "Todo"

#: app/Services/Integrations/Edd/EddCommerceHelper.php:155
msgid "All licenses"
msgstr "Todas las licencias"

#: app/Services/Integrations/RCP/RCPMembershipTrigger.php:52
#: app/Services/Integrations/RCP/RCPMembershipCancelTrigger.php:52
#: app/Services/Integrations/RCP/RCPMembershipExpireTrigger.php:52
#: app/Services/Integrations/LifterLms/LessonCompletedTrigger.php:56
#: app/Services/Integrations/LifterLms/CourseCompletedTrigger.php:52
#: app/Services/Integrations/LifterLms/CourseEnrollTrigger.php:56
#: app/Services/Integrations/LifterLms/MembershipEnrollTrigger.php:56
#: app/Services/Integrations/WooCommerce/WooSubscriptionStartedTrigger.php:83
#: app/Services/Integrations/WooCommerce/WooOrderCompletedTrigger.php:52
#: app/Services/Integrations/WooCommerce/WooOrderStatusChangeTrigger.php:52
#: app/Services/Integrations/WooCommerce/WooSubscriptionRenewalFailedTrigger.php:83
#: app/Services/Integrations/WooCommerce/WooOrderRefundedTrigger.php:52
#: app/Services/Integrations/WooCommerce/WooOrderSuccessTrigger.php:54
#: app/Services/Integrations/WooCommerce/WooSubscriptionExpiredTrigger.php:83
#: app/Services/Integrations/WooCommerce/WooSubscriptionRenewalPaymentTrigger.php:83
#: app/Services/Integrations/PMPro/PMProPMProMembershipTrigger.php:52
#: app/Services/Integrations/PMPro/PMProPMProExpiryLevelTrigger.php:52
#: app/Services/Integrations/PMPro/PMProPMProCancelLevelTrigger.php:52
#: app/Services/Integrations/SureCart/SureCartPaymentRefundTrigger.php:52
#: app/Services/Integrations/SureCart/SureCartPaymentSuccessTrigger.php:53
#: app/Services/Integrations/LearnDash/GroupEnrollTrigger.php:51
#: app/Services/Integrations/LearnDash/CourseLeaveTrigger.php:54
#: app/Services/Integrations/LearnDash/TopicCompletedTrigger.php:50
#: app/Services/Integrations/LearnDash/LessonCompletedTrigger.php:51
#: app/Services/Integrations/LearnDash/CourseCompletedTrigger.php:51
#: app/Services/Integrations/LearnDash/CourseEnrollTrigger.php:53
#: app/Services/Integrations/Edd/EddPaymentSuccessTrigger.php:52
#: app/Services/Integrations/Edd/EddLicenseExpiredTrigger.php:51
#: app/Services/Integrations/Edd/EddRecurringExpired.php:52
#: app/Services/Integrations/Edd/EddRecurringPaymentTrigger.php:52
#: app/Services/Integrations/Edd/EddPaymentRefundTrigger.php:52
#: app/Services/Integrations/WishlistMember/WishlistMembershipTrigger.php:52
#: app/Services/Integrations/TutorLms/LessonCompletedTrigger.php:52
#: app/Services/Integrations/TutorLms/CourseCompletedTrigger.php:52
#: app/Services/Integrations/TutorLms/CourseEnrollTrigger.php:52
#: app/Services/Integrations/MemberPress/MembershipTrigger.php:52
#: app/Services/Integrations/MemberPress/SubscriptionExpiredTrigger.php:51
#: app/Services/Integrations/AffiliateWP/AffiliateWPAffActiveTrigger.php:75
#: app/Services/Funnel/Triggers/UserLoginTrigger.php:46
msgid "An Automated double-optin email will be sent for new subscribers"
msgstr ""
"Se enviará un correo electrónico automático de doble apertura para los "
"nuevos suscriptores"

#: app/Http/Controllers/RecurringCampaignController.php:58
#: app/Http/Controllers/RecurringCampaignController.php:144
msgid ""
"Another campaign with the same name already exist. Please provide a "
"different name"
msgstr ""

#: app/Services/Integrations/WooCommerce/WooOrderStatusChangeTrigger.php:79
msgid "Any"
msgstr "Cualquier"

#: app/Services/Integrations/WooCommerce/Helper.php:51
#: app/Services/Integrations/Edd/Helper.php:54
msgid "Any type of purchase"
msgstr "Cualquier tipo de compra"

#: app/Services/Integrations/WooCommerce/DeepIntegration.php:597
#: app/Services/Integrations/Edd/DeepIntegration.php:263
msgid "AOV"
msgstr "AOV"

#: app/Services/Integrations/BuddyPress/Group.php:39
#: app/Services/Integrations/BuddyPress/BBMemberType.php:58
#: app/Services/Integrations/Edd/EddMetaBoxes.php:49
#: app/Services/Integrations/Edd/EddMetaBoxes.php:103
msgid "Apply Tags"
msgstr "Aplicar etiquetas"

#: app/Services/Integrations/LifterLms/LifterInit.php:375
msgid "Apply Tags on Course Completed"
msgstr "Aplicar etiquetas en el curso completado"

#: app/Services/Integrations/LearnPress/LearnPressInit.php:152
msgid "Apply Tags on Course Completion"
msgstr "Aplicar etiquetas al finalizar el curso"

#: app/Services/Integrations/LifterLms/LifterInit.php:183
#: app/Services/Integrations/TutorLms/TutorLmsInit.php:154
msgid "Apply Tags on course completion"
msgstr "Aplicar etiquetas al finalizar el curso"

#: app/Services/Integrations/LearnPress/LearnPressInit.php:131
msgid "Apply Tags on Course Enrollment"
msgstr "Aplicar etiquetas en la inscripción al curso"

#: app/Services/Integrations/LifterLms/LifterInit.php:170
#: app/Services/Integrations/TutorLms/TutorLmsInit.php:140
msgid "Apply Tags on course enrollment"
msgstr "Aplicar Etiquetas en la inscripción del curso"

#: app/Services/Integrations/LearnPress/LearnPressInit.php:191
msgid "Apply Tags on Lesson Completion"
msgstr "Aplicar etiquetas al finalizar la lección"

#: app/Services/Integrations/LifterLms/LifterInit.php:241
msgid "Apply Tags on Membership enrollment"
msgstr "Aplicar etiquetas en la inscripción a la membresía"

#: app/Services/Integrations/LearnPress/LearnPressInit.php:167
msgid "Apply these tags on course completion"
msgstr "Aplicar estas etiquetas al finalizar el curso"

#: app/Services/Integrations/LearnPress/LearnPressInit.php:147
msgid "Apply these tags on course enrollment"
msgstr "Aplicar estas etiquetas en la inscripción al curso"

#: app/Services/Integrations/Edd/EddMetaBoxes.php:115
msgid "Apply these tags when purchase this variation"
msgstr "Aplique estas etiquetas al comprar esta variación"

#: app/Services/Integrations/Edd/EddMetaBoxes.php:63
msgid "Apply these tags when purchased"
msgstr "Aplique estas etiquetas cuando las compre"

#: app/Http/Controllers/ManagerController.php:135
msgid "Associate user could not be found"
msgstr "No se ha podido encontrar el usuario asociado"

#: app/Http/Controllers/ManagerController.php:62
#: app/Http/Controllers/ManagerController.php:103
msgid "Associate user could not be found with this email"
msgstr ""
"No se ha podido encontrar al usuario asociado con este correo electrónico"

#: app/Modules/AbandonCart/Woo/WooCartTrackingInit.php:677
msgid "Automatically cancelled because a cart has been recovered"
msgstr ""

#: app/Modules/AbandonCart/Woo/WooCartTrackingInit.php:644
msgid "Automatically cancelled because the cart has been lost"
msgstr ""

#: app/Services/Integrations/LifterLms/LifterInit.php:192
msgid ""
"Automatically remove tags defined in \"Apply Tags\" if course enrollment is "
"cancelled."
msgstr ""
"Eliminar automáticamente las etiquetas definidas en \"Aplicar etiquetas\" si "
"se cancela la inscripción al curso."

#: app/Services/Integrations/LifterLms/LifterInit.php:250
msgid ""
"Automatically remove tags defined in \"Apply Tags\" if membership is "
"cancelled."
msgstr ""
"Eliminar automáticamente las etiquetas definidas en \"Aplicar etiquetas\" si "
"se cancela la membresía."

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:38
msgid "Automation Priority"
msgstr ""

#: app/Services/Integrations/WooCommerce/AdvancedReport.php:121
#: app/Services/Integrations/Edd/AdvancedReport.php:123
msgid "Average Revenue Per Customer"
msgstr "Ingresos medios por cliente"

#: app/Services/Integrations/BuddyPress/BBInit.php:89
#: app/Services/Integrations/BuddyPress/BBInit.php:145
msgid "Banned"
msgstr "Prohibido"

#: app/Http/Controllers/DynamicSegmentController.php:119
msgid "Before"
msgstr "Antes de"

#: app/Services/Integrations/BuddyPress/BBInit.php:34
#: app/Services/Integrations/BuddyPress/BBInit.php:55
msgid "BuddyBoss Groups"
msgstr "Grupos BuddyBoss"

#: app/Services/Integrations/BuddyPress/BBInit.php:31
#: app/Services/Integrations/BuddyPress/BBInit.php:52
msgid "BuddyPress Groups"
msgstr "Grupos de BuddyPress"

#: app/Http/Controllers/RecurringCampaignController.php:197
#, php-format
msgid "Campaign status has been changed to %s"
msgstr ""

#: app/Services/Funnel/Actions/RemoveFromFunnelAction.php:23
#: app/Services/Funnel/Actions/RemoveFromFunnelAction.php:36
msgid "Cancel Automations"
msgstr "Cancelar automatizaciones"

#: app/Services/Funnel/Actions/RemoveFromEmailSequenceAction.php:22
msgid "Cancel Sequence Emails"
msgstr "Cancelación de los correos electrónicos de la secuencia"

#: app/Services/Funnel/Actions/RemoveFromEmailSequenceAction.php:23
msgid "Cancel Sequence Emails for the contact"
msgstr "Cancelar la secuencia de correos electrónicos para el contacto"

#: app/Services/Funnel/Actions/RemoveFromFunnelAction.php:62
msgid "Cancelled by Automation ID: "
msgstr "Cancelado por ID de automatización"

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:23
#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:32
msgid "Cart Abandoned"
msgstr ""

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:102
msgid "Cart Data"
msgstr ""

#: app/Modules/AbandonCart/Woo/WooCartTrackingInit.php:276
msgid "Cart data has been updated"
msgstr ""

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:116
msgid "Cart Items"
msgstr ""

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:125
msgid "Cart Items Categories"
msgstr ""

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:111
msgid "Cart Items Count"
msgstr ""

#: app/Modules/AbandonCart/AbandonCart.php:70
msgid "Cart Recovered (All Time)"
msgstr ""

#: app/Modules/AbandonCart/AbandonCart.php:65
msgid "Cart Recovered (This Month)"
msgstr ""

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:106
msgid "Cart Total"
msgstr ""

#: app/Services/Funnel/Actions/ChangeUserRoleAction.php:37
msgid "Change connected user role"
msgstr "Cambiar el rol del usuario conectado"

#: app/Services/Integrations/WooCommerce/OrderStatusChangeAction.php:20
#: app/Services/Integrations/WooCommerce/OrderStatusChangeAction.php:42
msgid "Change Order Status"
msgstr "Estado de la orden de cambio"

#: app/Services/Integrations/WooCommerce/OrderStatusChangeAction.php:21
#: app/Services/Integrations/WooCommerce/OrderStatusChangeAction.php:43
msgid "Change status of the current order in WooCommerce"
msgstr "Cambiar el estado del pedido actual en WooCommerce"

#: app/Services/Funnel/Actions/ChangeUserRoleAction.php:36
msgid "Change User Role"
msgstr "Cambiar el rol del usuario"

#: app/Services/Funnel/Actions/ChangeUserRoleAction.php:21
msgid "Change WP User Role"
msgstr "Cambiar el rol de usuario de WP"

#: app/Services/Funnel/Conditions/FunnelCondition.php:28
#: app/Services/Funnel/Conditions/FunnelCondition.php:42
msgid "Check Condition"
msgstr "Comprobar el estado"

#: app/Services/Funnel/Conditions/FunnelCondition.php:29
#: app/Services/Funnel/Conditions/FunnelCondition.php:43
msgid "Check If the contact match specific data properties"
msgstr ""
"Comprobar si el contacto coincide con las propiedades de los datos "
"específicos"

#: app/Services/PluginManager/LicenseManager.php:53
msgid "Check Update"
msgstr "Comprobar la actualización"

#: app/Http/Controllers/DynamicSegmentController.php:153
#: app/Hooks/Handlers/DataExporter.php:552
#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:165
#: app/Services/Funnel/Conditions/FunnelCondition.php:97
msgid "City"
msgstr "Ciudad"

#: app/Services/PluginManager/LicenseManager.php:345
msgid "Click Here to purchase another license"
msgstr "Haga clic aquí para comprar otra licencia"

#: app/Services/PluginManager/LicenseManager.php:362
msgid "Click Here to Renew Your License"
msgstr "Haga clic aquí para renovar su licencia"

#: app/Services/Integrations/BuddyPress/BBInit.php:158
msgid "Community Groups"
msgstr ""

#: app/Hooks/Handlers/DataExporter.php:566
msgid "Companies"
msgstr ""

#: app/Services/Integrations/CRM/CompanyAppliedTrigger.php:28
#: app/Services/Integrations/CRM/CompanyAppliedTrigger.php:45
msgid "Company Applied"
msgstr ""

#: app/Services/Integrations/CRM/RemoveFromCompanyTrigger.php:29
msgid "Company Removed"
msgstr ""

#: app/Services/BaseAdvancedReport.php:50
#: app/Services/BaseAdvancedReport.php:147
#: app/Services/BaseAdvancedReport.php:199
#: app/Services/Integrations/WooCommerce/AdvancedReport.php:181
#: app/Services/Integrations/WooCommerce/AdvancedReport.php:208
#: app/Services/Integrations/CRM/AdvancedReport.php:101
#: app/Services/Integrations/Edd/AdvancedReport.php:228
#: app/Services/Integrations/Edd/AdvancedReport.php:251
msgid "Compare Range"
msgstr "Comparar la gama"

#: app/Services/Integrations/LifterLms/LifterInit.php:132
#: app/Services/Integrations/LearnDash/LdInit.php:122
msgid "Completed At"
msgstr "Completado en"

#: app/Services/Integrations/LearnDash/CourseCompletedTrigger.php:39
msgid "Completes a Course"
msgstr "Completa un curso"

#: app/Services/Integrations/LearnDash/LessonCompletedTrigger.php:39
msgid "Completes a Lesson"
msgstr "Completa una lección"

#: app/Services/Integrations/LearnDash/TopicCompletedTrigger.php:38
msgid "Completes a Topic"
msgstr "Completa un tema"

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:72
#: app/Services/Funnel/Triggers/TrackingEventRecordedTrigger.php:89
#: app/Services/Funnel/Conditions/FunnelCondition.php:53
msgid "Condition"
msgstr "Condición"

#: app/Http/Controllers/DynamicSegmentController.php:130
msgid "Conditions"
msgstr "Condiciones"

#: app/Services/Integrations/BuddyPress/BBInit.php:109
msgid "Confirmation Status"
msgstr "Estado de la confirmación"

#: app/Services/Integrations/BuddyPress/BBInit.php:100
msgid "Confirmed"
msgstr "Confirmado"

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:136
#: app/Services/Funnel/Conditions/FunnelCondition.php:68
msgid "Contact"
msgstr "Contacte con"

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:263
#: app/Services/Funnel/Conditions/FunnelCondition.php:195
msgid "Contact Activities"
msgstr "Actividades de contacto"

#: app/Services/Integrations/CRM/CompanyAppliedTrigger.php:65
msgid "contact added in all of the selected companies"
msgstr ""

#: app/Services/Integrations/CRM/ListAppliedTrigger.php:66
msgid "contact added in all of the selected lists"
msgstr "contacto añadido en todas las listas seleccionadas"

#: app/Services/Integrations/CRM/TagAppliedTrigger.php:66
msgid "contact added in all of the selected tags"
msgstr "contacto añadido en todas las etiquetas seleccionadas"

#: app/Services/Integrations/CRM/CompanyAppliedTrigger.php:61
msgid "contact added in any of the selected companies"
msgstr ""

#: app/Services/Integrations/CRM/ListAppliedTrigger.php:62
msgid "contact added in any of the selected lists"
msgstr "contacto añadido en cualquiera de las listas seleccionadas"

#: app/Services/Integrations/CRM/TagAppliedTrigger.php:62
msgid "contact added in any of the selected tags"
msgstr "contacto añadido en cualquiera de las etiquetas seleccionadas"

#: app/Services/Integrations/CRM/ContactCreatedTrigger.php:28
#: app/Services/Integrations/CRM/ContactCreatedTrigger.php:42
msgid "Contact Created"
msgstr ""

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:71
#: app/Services/Funnel/Triggers/TrackingEventRecordedTrigger.php:88
#: app/Services/Funnel/Conditions/FunnelCondition.php:52
msgid "Contact Data"
msgstr "Datos de contacto"

#: app/Http/Controllers/DynamicSegmentController.php:135
msgid "Contact Email"
msgstr "Correo electrónico de contacto"

#: app/Services/Integrations/CRM/AdvancedReport.php:42
msgid "Contact Growth"
msgstr "Crecimiento del contacto"

#: app/Services/Funnel/Actions/UpdateContactPropertyAction.php:47
msgid "Contact Property"
msgstr "Contacto con la propiedad"

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:233
#: app/Services/Funnel/Conditions/FunnelCondition.php:165
msgid "Contact Segment"
msgstr "Segmento de contacto"

#: app/Services/Funnel/Actions/UpdateContactPropertyAction.php:193
#: app/Services/Funnel/Actions/UpdateContactPropertyAction.php:195
msgid "Contact Source"
msgstr "Contacto con la fuente"

#: app/Services/Integrations/WooCommerce/WooSubscriptionStartedTrigger.php:78
#: app/Services/Integrations/WooCommerce/WooSubscriptionRenewalFailedTrigger.php:78
#: app/Services/Integrations/WooCommerce/WooSubscriptionExpiredTrigger.php:78
#: app/Services/Integrations/WooCommerce/WooSubscriptionRenewalPaymentTrigger.php:78
#: app/Services/Integrations/Edd/EddRecurringExpired.php:47
msgid "Contact Status"
msgstr "Estado del contacto"

#: app/Hooks/Handlers/DataExporter.php:558
#: app/Services/Funnel/Actions/UpdateContactPropertyAction.php:188
msgid "Contact Type"
msgstr "Tipo de contacto"

#: app/Services/Funnel/Triggers/ContactBirthDayTrigger.php:24
#: app/Services/Funnel/Triggers/ContactBirthDayTrigger.php:33
msgid "Contact's Birthday"
msgstr "Cumpleaños de la persona de contacto"

#: app/Services/Integrations/WooCommerce/WooOrderSuccessBenchmark.php:91
#: app/Services/Integrations/SureCart/SureCartPaymentSuccessBenchmark.php:71
#: app/Services/Integrations/Edd/EddOrderSuccessBenchmark.php:90
#: app/Services/Integrations/Edd/EddSubscriptionActiveBenchmark.php:71
#: app/Services/Funnel/Benchmarks/EmailSequenceCompletedBenchmark.php:61
#: app/Services/Funnel/Benchmarks/LinkClickBenchmark.php:82
msgid ""
"Contacts can enter directly to this sequence point. If you enable this then "
"any contact meet with goal will enter in this goal point."
msgstr ""

#: app/Services/Funnel/Benchmarks/LinkClickBenchmark.php:66
msgid "Contacts will be redirected to this link."
msgstr "Los contactos serán redirigidos a este enlace."

#: app/Http/Controllers/DynamicSegmentController.php:110
msgid "Contains"
msgstr "Contiene"

#: app/Services/Funnel/Benchmarks/LinkClickBenchmark.php:57
msgid "Copy This Link"
msgstr "Copiar este enlace"

#: app/Http/Controllers/DynamicSegmentController.php:167
#: app/Hooks/Handlers/DataExporter.php:554
#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:180
#: app/Services/Integrations/WooCommerce/AutomationConditions.php:64
#: app/Services/Integrations/WooCommerce/AutomationConditions.php:186
#: app/Services/Funnel/Conditions/FunnelCondition.php:112
#: app/Services/Funnel/Actions/UpdateContactPropertyAction.php:198
msgid "Country"
msgstr "País"

#: app/Services/Integrations/LifterLms/CourseCompletedTrigger.php:24
#: app/Services/Integrations/LifterLms/AutomationConditions.php:41
#: app/Services/Integrations/LearnDash/CourseCompletedTrigger.php:23
#: app/Services/Integrations/LearnDash/AutomationConditions.php:44
#: app/Services/Integrations/TutorLms/CourseCompletedTrigger.php:24
#: app/Services/Integrations/TutorLms/AutomationConditions.php:30
msgid "Course Completed"
msgstr "Curso completado"

#: app/Services/Integrations/LifterLms/CourseEnrollTrigger.php:28
#: app/Services/Integrations/LearnDash/CourseEnrollTrigger.php:23
#: app/Services/Integrations/TutorLms/CourseEnrollTrigger.php:24
msgid "Course Enrolled"
msgstr ""

#: app/Services/Integrations/TutorLms/AutomationConditions.php:22
msgid "Course Enrollment"
msgstr "Inscripción en el curso"

#: app/Services/Integrations/LearnPress/LearnPressInit.php:92
msgid "Course ID"
msgstr "ID del curso"

#: app/Services/Integrations/LearnDash/CourseLeaveTrigger.php:24
msgid "Course Left"
msgstr ""

#: app/Services/Integrations/LifterLms/LifterInit.php:127
#: app/Services/Integrations/LearnPress/LearnPressInit.php:93
#: app/Services/Integrations/LearnDash/LdInit.php:119
#: app/Services/Integrations/TutorLms/TutorLmsInit.php:106
msgid "Course Name"
msgstr "Nombre del curso"

#: app/Services/Integrations/LifterLms/AdvancedReport.php:43
#: app/Services/Integrations/LifterLms/LifterInit.php:64
#: app/Services/Integrations/LearnPress/LearnPressInit.php:46
#: app/Services/Integrations/LearnDash/AdvancedReport.php:43
#: app/Services/Integrations/LearnDash/LdInit.php:66
#: app/Services/Integrations/TutorLms/AdvancedReport.php:43
#: app/Services/Integrations/TutorLms/TutorLmsInit.php:52
msgid "Courses"
msgstr "Cursos"

#: app/Services/Integrations/WooCommerce/CreateCouponAction.php:23
#: app/Services/Integrations/WooCommerce/CreateCouponAction.php:60
msgid "Create Coupon"
msgstr "Crear un cupón"

#: app/Services/Integrations/WooCommerce/CreateCouponAction.php:24
#: app/Services/Integrations/WooCommerce/CreateCouponAction.php:61
msgid "Create WooCommerce Coupon Code"
msgstr ""

#: app/Services/Funnel/Actions/UserRegistrationAction.php:46
msgid "Create WordPress User"
msgstr "Crear usuario de WordPress"

#: app/Services/Funnel/Actions/UserRegistrationAction.php:23
msgid "Create WP User"
msgstr "Crear usuario WP"

#: app/Services/Funnel/Actions/UserRegistrationAction.php:24
#: app/Services/Funnel/Actions/UserRegistrationAction.php:47
msgid ""
"Create WP User with a role if user is not already registered with contact "
"email"
msgstr ""
"Crear usuario WP con un rol si el usuario no está ya registrado con el "
"correo electrónico de contacto"

#: app/Hooks/Handlers/DataExporter.php:562
#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:226
#: app/Services/Funnel/Conditions/FunnelCondition.php:158
msgid "Created At"
msgstr "Creado en"

#: app/Http/Controllers/DynamicSegmentController.php:187
msgid "Created at"
msgstr "Creado en"

#: app/Services/Integrations/Integrations.php:120
#: app/Services/Integrations/CRM/RemoveFromCompanyTrigger.php:28
#: app/Services/Integrations/CRM/ListAppliedTrigger.php:27
#: app/Services/Integrations/CRM/ContactCreatedTrigger.php:27
#: app/Services/Integrations/CRM/TagAppliedTrigger.php:27
#: app/Services/Integrations/CRM/CompanyAppliedTrigger.php:27
#: app/Services/Integrations/CRM/RemoveFromListTrigger.php:28
#: app/Services/Integrations/CRM/RemoveFromTagTrigger.php:28
#: app/Services/Funnel/Triggers/ContactBirthDayTrigger.php:23
#: app/Services/Funnel/Triggers/TrackingEventRecordedTrigger.php:30
#: app/Services/Funnel/Conditions/FunnelABTesting.php:25
#: app/Services/Funnel/Actions/RemoveFromFunnelAction.php:22
#: app/Services/Funnel/Actions/HTTPSendDataAction.php:26
#: app/Services/Funnel/Actions/UpdateContactPropertyAction.php:22
#: app/Services/Funnel/Actions/AddActivityAction.php:21
#: app/Services/Funnel/Actions/AddEventTrackerAction.php:26
#: app/Services/Funnel/Actions/EndFunnel.php:20
msgid "CRM"
msgstr "CRM"

#: app/Services/Integrations/CRM/AdvancedReport.php:38
msgid "CRM - Advanced Reports"
msgstr "CRM - Informes avanzados"

#: app/Services/BaseAdvancedReport.php:46
#: app/Services/BaseAdvancedReport.php:143
#: app/Services/BaseAdvancedReport.php:195
#: app/Services/Integrations/WooCommerce/AdvancedReport.php:177
#: app/Services/Integrations/WooCommerce/AdvancedReport.php:204
#: app/Services/Integrations/CRM/AdvancedReport.php:97
#: app/Services/Integrations/Edd/AdvancedReport.php:224
#: app/Services/Integrations/Edd/AdvancedReport.php:247
msgid "Current Range"
msgstr "Gama actual"

#: app/Services/Funnel/Actions/HTTPSendDataAction.php:104
msgid "Custom Data"
msgstr "Datos personalizados"

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:328
#: app/Services/Funnel/Conditions/FunnelCondition.php:260
msgid "Custom Fields"
msgstr "Campos personalizados"

#: app/Services/Funnel/Actions/UserRegistrationAction.php:61
msgid "Custom Password"
msgstr "Contraseña personalizada"

#: app/Services/DynamicSegments/CustomSegment.php:50
msgid "Custom Segments with custom filters on Subscriber data"
msgstr ""
"Segmentos personalizados con filtros personalizados en los datos de los "
"abonados"

#: app/Services/Funnel/Actions/UserRegistrationAction.php:73
msgid "Custom Username (optional)"
msgstr ""

#: app/Services/Integrations/WooCommerce/AdvancedReport.php:45
#: app/Services/Integrations/Edd/AdvancedReport.php:37
msgid "Customer Growth"
msgstr "Crecimiento de la clientela"

#: app/Services/Integrations/WooCommerce/DeepIntegration.php:546
#: app/Services/Integrations/Edd/DeepIntegration.php:218
msgid "Customer Since"
msgstr "Cliente desde"

#: app/Services/Integrations/WooCommerce/DeepIntegration.php:106
#: app/Services/Integrations/Edd/DeepIntegration.php:155
msgid "Customer Summary"
msgstr "Resumen del cliente"

#: app/Services/Funnel/Actions/HTTPSendDataAction.php:111
msgid "Data Key"
msgstr "Clave de datos"

#: app/Services/Funnel/Actions/HTTPSendDataAction.php:61
msgid "Data Send Method"
msgstr "Método de envío de datos"

#: app/Services/Funnel/Actions/HTTPSendDataAction.php:112
msgid "Data Value"
msgstr "Valor de los datos"

#: app/Hooks/Handlers/DataExporter.php:560
msgid "Date Of Birth"
msgstr "Fecha de nacimiento"

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:216
#: app/Services/Funnel/Conditions/FunnelCondition.php:148
msgid "Date of Birth"
msgstr ""

#: app/Services/Funnel/Actions/AddActivityAction.php:62
msgid "Description"
msgstr "Descripción"

#: app/Services/Integrations/LifterLms/AddToMembershipAction.php:49
#: app/Services/Integrations/LifterLms/AddToCourseAction.php:50
#: app/Services/Integrations/LearnDash/AddToGroupAction.php:50
#: app/Services/Integrations/LearnDash/AddToCourseAction.php:50
#: app/Services/Integrations/TutorLms/AddToCourseAction.php:49
msgid "Do not enroll the course if contact is not an existing WordPress User"
msgstr ""
"No inscriba el curso si el contacto no es un usuario existente de WordPress"

#: app/Services/PluginManager/LicenseManager.php:51
msgid "Docs"
msgstr "Docs"

#: app/Services/Integrations/ElementorFormIntegration/FormWidget.php:101
msgid "Double Opt-in"
msgstr "Doble Opt-in"

#: app/Modules/AbandonCart/AbandonCartController.php:146
msgid "Draft Revenue"
msgstr ""

#: app/Services/Integrations/AffiliateWP/DeepIntegration.php:89
msgid "Earnings"
msgstr "Ganancias"

#: app/Services/Integrations/Edd/EddInit.php:57
msgid "Earnings (All Time)"
msgstr "Ganancias (todo el tiempo)"

#: app/Services/Integrations/Edd/EddInit.php:53
msgid "Earnings (Current Month)"
msgstr "Ingresos (mes en curso)"

#: app/Services/Integrations/Edd/EddInit.php:49
msgid "Earnings (Today)"
msgstr "Beneficios (hoy)"

#: app/Services/Integrations/Edd/EddPaymentSuccessTrigger.php:23
#: app/Services/Integrations/Edd/EddLicenseExpiredTrigger.php:23
#: app/Services/Integrations/Edd/EddRecurringExpired.php:23
#: app/Services/Integrations/Edd/EddRecurringPaymentTrigger.php:23
#: app/Services/Integrations/Edd/DeepIntegration.php:365
#: app/Services/Integrations/Edd/EddPaymentRefundTrigger.php:24
msgid "Easy Digital Downloads"
msgstr "Descargas digitales fáciles"

#: app/Services/Integrations/Edd/AdvancedReport.php:87
msgid "Easy Digital Downloads - Advanced Reports"
msgstr "Easy Digital Downloads - Informes avanzados"

#: app/Services/DynamicSegments/EddActiveCustomerSegment.php:20
msgid "Easy Digital Downloads Customers"
msgstr "Clientes de Easy Digital Downloads"

#: app/Services/Integrations/Edd/DeepIntegration.php:367
msgid "Easy Digital Downloads customers are not synced with FluentCRM yet."
msgstr ""
"Los clientes de Easy Digital Downloads aún no están sincronizados con "
"FluentCRM."

#: app/Services/Integrations/Edd/EddImporter.php:150
msgid "Easy Digital Downloads Data Sync"
msgstr "Sincronización de datos de Easy Digital Downloads"

#: app/Services/Integrations/Edd/AutomationConditions.php:21
#: app/Services/Integrations/Edd/DeepIntegration.php:278
msgid "EDD"
msgstr "EDD"

#: app/Services/Integrations/Edd/AutomationConditions.php:21
msgid "EDD (Sync Required)"
msgstr "EDD (Sincronización requerida)"

#: app/Services/Integrations/Edd/EddPaymentSuccessTrigger.php:24
msgid "Edd - New Order Success"
msgstr "Edd - Éxito del nuevo orden"

#: app/Services/DynamicSegments/EddActiveCustomerSegment.php:21
msgid "EDD customers who are also in the contact list as subscribed"
msgstr ""
"Clientes de EDD que también están en la lista de contactos como suscritos"

#: app/Services/Integrations/Edd/EddSubscriptionActiveBenchmark.php:47
msgid "EDD Recurring Subscription Active"
msgstr "Suscripción periódica EDD Activa"

#: app/Hooks/Handlers/DataExporter.php:547
#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:150
#: app/Services/Funnel/Conditions/FunnelCondition.php:82
#: app/Services/Funnel/Actions/RemoveFromEmailSequenceAction.php:21
#: app/Services/Funnel/Actions/SendCampaignEmailAction.php:22
#: app/Services/Funnel/Actions/AddEmailSequenceAction.php:23
msgid "Email"
msgstr "Envíe un correo electrónico a"

#: app/Hooks/Handlers/FLBuilderServiceFluentCrm.php:161
msgid "Email Address is not valid"
msgstr "La dirección de correo electrónico no es válida"

#: app/Http/Controllers/RecurringCampaignController.php:355
msgid "Email body has been successfully updated"
msgstr ""

#: app/Http/Controllers/RecurringCampaignController.php:346
msgid "Email body is required"
msgstr ""

#: app/Http/Controllers/RecurringCampaignController.php:130
msgid "Email data has been updated"
msgstr ""

#: app/Http/Controllers/CampaignsProController.php:91
msgid "Email has been resent"
msgstr "El correo electrónico ha sido reenviado"

#: app/Services/Funnel/Actions/SendCampaignEmailAction.php:76
msgid "Email has been triggered by Automation Funnel ID: "
msgstr ""
"El correo electrónico ha sido activado por el ID del embudo de automatización"

#: app/Hooks/Handlers/CampaignArchiveFront.php:22
msgid "Email Newsletter Archive features is not enabled"
msgstr ""
"Las funciones de archivo de los boletines de correo electrónico no están "
"activadas"

#: app/Hooks/Handlers/CampaignArchiveFront.php:33
msgid "Email Newsletter could not be found"
msgstr "No se pudo encontrar la Newsletter del correo electrónico"

#: app/Services/Integrations/CRM/AdvancedReport.php:45
msgid "Email Sending Stats"
msgstr "Estadísticas de envío de correo electrónico"

#: app/Services/Funnel/Benchmarks/EmailSequenceCompletedBenchmark.php:23
#: app/Services/Funnel/Benchmarks/EmailSequenceCompletedBenchmark.php:38
msgid "Email Sequence Completed"
msgstr "Secuencia de correo electrónico completada"

#: app/Http/Controllers/SequenceController.php:187
#: app/Http/Controllers/SequenceMailController.php:183
msgid "Email sequence successfully deleted"
msgstr "Secuencia de correo electrónico eliminada con éxito"

#: app/Http/Controllers/RecurringCampaignController.php:328
#, php-format
msgid "Email status has been changed to %s"
msgstr ""

#: app/Http/Controllers/DynamicSegmentController.php:228
msgid "Enable Last Email Activity Filter"
msgstr "Activar el filtro de la última actividad del correo electrónico"

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:82
msgid ""
"Enable this to prevent the automation from running multiple times for the "
"same contact if it is currently active in this automation"
msgstr ""

#: app/Services/Funnel/Actions/EndFunnel.php:21
#: app/Services/Funnel/Actions/EndFunnel.php:33
msgid "End This Funnel Here"
msgstr "Termina este embudo aquí"

#: app/Services/Integrations/LifterLms/AddToCourseAction.php:24
#: app/Services/Integrations/LifterLms/AddToCourseAction.php:38
#: app/Services/Integrations/LearnDash/AddToCourseAction.php:23
#: app/Services/Integrations/LearnDash/AddToCourseAction.php:37
#: app/Services/Integrations/TutorLms/AddToCourseAction.php:23
#: app/Services/Integrations/TutorLms/AddToCourseAction.php:37
msgid "Enroll the contact to a specific LMS Course"
msgstr ""

#: app/Services/Integrations/LearnDash/AddToGroupAction.php:23
#: app/Services/Integrations/LearnDash/AddToGroupAction.php:37
msgid "Enroll the contact to a specific LMS Group"
msgstr ""

#: app/Services/Integrations/LifterLms/AddToMembershipAction.php:23
#: app/Services/Integrations/LifterLms/AddToMembershipAction.php:37
msgid "Enroll the contact to a specific LMS Membership Group"
msgstr ""

#: app/Services/Integrations/LifterLms/AddToCourseAction.php:37
#: app/Services/Integrations/LearnDash/AddToCourseAction.php:36
#: app/Services/Integrations/TutorLms/AddToCourseAction.php:36
msgid "Enroll To a Course"
msgstr ""

#: app/Services/Integrations/LearnDash/AddToGroupAction.php:36
msgid "Enroll To a Group"
msgstr ""

#: app/Services/Integrations/LifterLms/AddToMembershipAction.php:22
#: app/Services/Integrations/LifterLms/AddToMembershipAction.php:36
msgid "Enroll To a Membership Group"
msgstr ""

#: app/Services/Integrations/LifterLms/AddToCourseAction.php:23
#: app/Services/Integrations/LearnDash/AddToCourseAction.php:22
#: app/Services/Integrations/TutorLms/AddToCourseAction.php:22
msgid "Enroll To Course"
msgstr ""

#: app/Services/Integrations/LearnDash/AddToGroupAction.php:22
msgid "Enroll To Group"
msgstr ""

#: app/Services/Integrations/LifterLms/LifterInit.php:128
msgid "Enrolled At"
msgstr "Inscrito en"

#: app/Services/Integrations/LifterLms/AutomationConditions.php:71
#: app/Services/Integrations/LifterLms/DeepIntegration.php:119
#: app/Services/Integrations/LearnDash/AutomationConditions.php:75
#: app/Services/Integrations/LearnDash/DeepIntegration.php:110
#: app/Services/Integrations/TutorLms/DeepIntegration.php:437
msgid "Enrollment Categories"
msgstr "Categorías de inscripción"

#: app/Services/Integrations/LifterLms/AutomationConditions.php:32
#: app/Services/Integrations/LifterLms/DeepIntegration.php:100
#: app/Services/Integrations/LearnDash/AutomationConditions.php:35
#: app/Services/Integrations/LearnDash/DeepIntegration.php:90
#: app/Services/Integrations/TutorLms/DeepIntegration.php:428
msgid "Enrollment Courses"
msgstr "Cursos de inscripción"

#: app/Services/Integrations/LearnDash/AutomationConditions.php:53
#: app/Services/Integrations/LearnDash/DeepIntegration.php:99
msgid "Enrollment Groups"
msgstr "Grupos de inscripción"

#: app/Services/Integrations/LifterLms/CourseEnrollTrigger.php:44
msgid "Enrollment in a course in LifterLMS"
msgstr "Inscripción en un curso en LifterLMS"

#: app/Services/Integrations/TutorLms/CourseEnrollTrigger.php:40
msgid "Enrollment in a course in TutorLMS"
msgstr "Inscripción en un curso en TutorLMS"

#: app/Services/Integrations/LifterLms/MembershipEnrollTrigger.php:44
msgid "Enrollment in a Membership in LifterLMS"
msgstr "Inscripción de una membresía en LifterLMS"

#: app/Services/Integrations/PMPro/PMProPMProMembershipTrigger.php:40
msgid "Enrollment in a Membership Level in PMPro"
msgstr "Inscripción en un nivel de membresía en PMPro"

#: app/Services/Integrations/LifterLms/AutomationConditions.php:50
#: app/Services/Integrations/LifterLms/DeepIntegration.php:109
msgid "Enrollment Memberships"
msgstr "Membresías de inscripción"

#: app/Services/Integrations/LifterLms/AutomationConditions.php:80
#: app/Services/Integrations/LifterLms/DeepIntegration.php:128
#: app/Services/Integrations/LearnDash/AutomationConditions.php:84
#: app/Services/Integrations/LearnDash/DeepIntegration.php:119
#: app/Services/Integrations/TutorLms/DeepIntegration.php:446
msgid "Enrollment Tags"
msgstr "Etiquetas de inscripción"

#: app/Services/Integrations/LifterLms/AdvancedReport.php:36
#: app/Services/Integrations/LearnDash/AdvancedReport.php:36
#: app/Services/Integrations/TutorLms/AdvancedReport.php:36
msgid "Enrollments"
msgstr "Inscripciones"

#: app/Services/Integrations/LearnDash/CourseEnrollTrigger.php:41
msgid "Enrolls in a Course"
msgstr "Se inscribe en un curso"

#: app/Services/Integrations/LearnDash/GroupEnrollTrigger.php:39
msgid "Enrolls in a Group"
msgstr "Se inscribe en un grupo"

#: app/Http/Controllers/DynamicSegmentController.php:108
msgid "Equal"
msgstr "Equal"

#: app/Services/Funnel/Actions/UserRegistrationAction.php:131
msgid "Error when creating new User. "
msgstr "Error al crear un nuevo usuario"

#: app/Services/Funnel/Conditions/FunnelABTesting.php:27
#: app/Services/Funnel/Conditions/FunnelABTesting.php:42
msgid "Evenly split the contacts or choose how to distribute them"
msgstr "Dividir uniformemente los contactos o elegir cómo distribuirlos"

#: app/Services/Funnel/Actions/AddEventTrackerAction.php:43
msgid "Event Tracking for Contact to the Contact Profile"
msgstr ""

#: app/Services/Funnel/Actions/AddEventTrackerAction.php:48
msgid "Event Tracking Key"
msgstr ""

#: app/Services/Funnel/Actions/AddEventTrackerAction.php:55
msgid "Event Tracking Title"
msgstr ""

#: app/Services/Integrations/Edd/AdvancedReport.php:72
msgid "Expired"
msgstr "Expirado"

#: app/Services/Integrations/Edd/AdvancedReport.php:164
msgid "Expired Licenses"
msgstr "Licencias caducadas"

#: app/Hooks/Handlers/DataExporter.php:397
msgid "Failed to import"
msgstr ""

#: app/Http/Controllers/DynamicSegmentController.php:223
msgid "Filter By Email Activities"
msgstr "Filtrar por actividades de correo electrónico"

#: app/Http/Controllers/DynamicSegmentController.php:224
msgid ""
"Filter your contacts by from email open or email link click metrics. Leave "
"the values blank for not applying"
msgstr ""
"Filtra tus contactos por las métricas de apertura de correo electrónico o de "
"clics en enlaces de correo electrónico. Deje los valores en blanco para no "
"aplicar"

#: app/Services/Integrations/LifterLms/AutomationConditions.php:65
#: app/Services/Integrations/LifterLms/DeepIntegration.php:94
#: app/Services/Integrations/LearnDash/AutomationConditions.php:69
#: app/Services/Integrations/LearnDash/DeepIntegration.php:84
#: app/Services/Integrations/TutorLms/DeepIntegration.php:422
msgid "First Enrollment Date"
msgstr "Primera fecha de matriculación"

#: app/Http/Controllers/DynamicSegmentController.php:141
#: app/Hooks/Handlers/DataExporter.php:545
#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:140
#: app/Services/Funnel/Conditions/FunnelCondition.php:72
msgid "First Name"
msgstr "Nombre"

#: app/Services/Integrations/WooCommerce/AutomationConditions.php:83
#: app/Services/Integrations/WooCommerce/DeepIntegration.php:226
#: app/Services/Integrations/Edd/AutomationConditions.php:56
#: app/Services/Integrations/Edd/DeepIntegration.php:303
msgid "First Order Date"
msgstr "Fecha del primer pedido"

#. Author of the plugin
msgid "Fluent CRM"
msgstr "CRM fluido"

#: app/Services/Integrations/LifterLms/LifterInit.php:161
#: app/Services/Integrations/LifterLms/LifterInit.php:232
#: app/Services/Integrations/LifterLms/LifterInit.php:366
#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:55
#: app/Services/Integrations/ElementorFormIntegration/FormWidget.php:30
#: app/Services/Integrations/ElementorFormIntegration/FormWidget.php:43
#: app/Services/Integrations/BuddyPress/Group.php:15
msgid "FluentCRM"
msgstr "FluentCRM"

#: app/Services/Integrations/TutorLms/TutorLmsInit.php:117
msgid "FluentCRM - Course Tags"
msgstr "FluentCRM - Etiquetas del curso"

#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:107
msgid "FluentCRM Integration"
msgstr "Integración de FluentCRM"

#. Name of the plugin
msgid "FluentCRM Pro"
msgstr "FluentCRM Pro"

#: app/Services/Integrations/WooCommerce/WooInit.php:193
#: app/Services/Integrations/Edd/EddInit.php:130
msgid "FluentCRM Profile"
msgstr "Perfil de FluentCRM"

#: app/Services/Integrations/LearnPress/LearnPressInit.php:106
#: app/Services/Integrations/LearnPress/LearnPressInit.php:110
#: app/Services/Integrations/BuddyPress/BBMemberType.php:53
#: app/Services/Integrations/BuddyPress/BBMemberType.php:219
#: app/Services/Integrations/Edd/EddMetaBoxes.php:29
#: app/Services/Integrations/Edd/EddMetaBoxes.php:100
msgid "FluentCRM Settings"
msgstr "Configuración de FluentCRM"

#: app/Services/Integrations/WooCommerce/WooOrderCompletedTrigger.php:97
#: app/Services/Integrations/WooCommerce/WooOrderSuccessTrigger.php:99
#: app/Services/Integrations/Edd/EddPaymentSuccessTrigger.php:104
#: app/Services/Integrations/Edd/EddPaymentRefundTrigger.php:104
msgid "For what type of purchase you want to run this funnel"
msgstr "Para qué tipo de compra quieres ejecutar este embudo"

#: app/Services/Integrations/WooCommerce/WooOrderSuccessBenchmark.php:76
#: app/Services/Integrations/Edd/EddOrderSuccessBenchmark.php:75
#| msgid "For what type of purchase you want to run this benchmark"
msgid "For what type of purchase you want to run this goal"
msgstr "Para qué tipo de compra quiere ejecutar este objetivo"

#: app/Services/Integrations/WooCommerce/Helper.php:59
#: app/Services/Integrations/Edd/Helper.php:62
msgid "From 2nd Purchase"
msgstr "A partir de la segunda compra"

#: app/Services/Funnel/Actions/HTTPSendDataAction.php:100
msgid "Full Subscriber Data (Raw)"
msgstr "Datos completos de los abonados (sin procesar)"

#: app/Services/Funnel/Actions/ChangeUserRoleAction.php:69
msgid ""
"Funnel Skipped because administrator user role can not be set for security "
"reason"
msgstr ""

#: app/Services/Integrations/LifterLms/AddToCourseAction.php:89
#: app/Services/Integrations/LearnDash/AddToCourseAction.php:89
#: app/Services/Integrations/TutorLms/AddToCourseAction.php:89
msgid "Funnel Skipped because no course found"
msgstr "Embudo Omitido porque no se ha encontrado ningún curso"

#: app/Services/Integrations/LifterLms/AddToMembershipAction.php:88
#: app/Services/Integrations/LearnDash/AddToGroupAction.php:89
msgid "Funnel Skipped because no group found"
msgstr "Embudo Omitido porque no se ha encontrado ningún grupo"

#: app/Services/Funnel/Actions/UpdateUserMetaAction.php:61
#: app/Services/Funnel/Actions/ChangeUserRoleAction.php:59
msgid "Funnel Skipped because no user found with the email address"
msgstr ""
"Embudo Omitido porque no se ha encontrado ningún usuario con la dirección de "
"correo electrónico"

#: app/Services/Funnel/Actions/HTTPSendDataAction.php:173
msgid "Funnel Skipped because provided url is not valid"
msgstr "Embudo Omitido porque la url proporcionada no es válida"

#: app/Services/Funnel/Actions/UserRegistrationAction.php:100
msgid "Funnel Skipped because user already exist in the database"
msgstr "Embudo Omitido porque el usuario ya existe en la base de datos"

#: app/Services/Integrations/LifterLms/RemoveFromMembershipAction.php:57
#: app/Services/Integrations/LifterLms/RemoveFromCourseAction.php:57
#: app/Services/Integrations/LifterLms/AddToMembershipAction.php:79
#: app/Services/Integrations/LifterLms/AddToCourseAction.php:80
#: app/Services/Integrations/LearnDash/AddToGroupAction.php:80
#: app/Services/Integrations/LearnDash/AddToCourseAction.php:80
#: app/Services/Integrations/TutorLms/RemoveFromCourseAction.php:57
#: app/Services/Integrations/TutorLms/AddToCourseAction.php:79
msgid "Funnel Skipped because user could not be found"
msgstr "Embudo Omitido porque no se pudo encontrar al usuario"

#: app/Services/Integrations/LearnDash/RemoveFromCourseAction.php:59
msgid "Funnel Skipped because user/course could not be found"
msgstr "Embudo Omitido porque no se pudo encontrar el usuario/curso"

#: app/Services/Integrations/LearnDash/RemoveFromGroupAction.php:57
msgid "Funnel Skipped because user/group could not be found"
msgstr "Embudo Omitido porque no se pudo encontrar el usuario/grupo"

#: app/Services/Funnel/Triggers/ContactBirthDayTrigger.php:25
#: app/Services/Funnel/Triggers/ContactBirthDayTrigger.php:34
msgid "Funnel will be initiated on the day of contact's birthday"
msgstr "El embudo se iniciará el día del cumpleaños del contacto"

#: app/Services/Funnel/Actions/UserRegistrationAction.php:57
msgid "Generate Password Automatically"
msgstr "Generar la contraseña automáticamente"

#: app/Services/Funnel/Actions/HTTPSendDataAction.php:69
msgid "GET Method"
msgstr "Método GET"

#: app/Services/Integrations/LifterLms/LifterInit.php:130
msgid "Grade"
msgstr "Grado"

#: app/Services/Integrations/LearnPress/LearnPressInit.php:95
msgid "Graduation Status"
msgstr "Estado de la graduación"

#: app/Services/Integrations/WooCommerce/AdvancedReport.php:40
#: app/Services/Integrations/Edd/AdvancedReport.php:32
msgid "Gross Volume"
msgstr "Volumen bruto"

#: app/Services/Integrations/LearnDash/GroupEnrollTrigger.php:23
msgid "Group Enrolled"
msgstr ""

#: app/Services/Integrations/BuddyPress/BBInit.php:106
msgid "Group ID"
msgstr "Identificación del grupo"

#: app/Services/Integrations/BuddyPress/BBInit.php:107
msgid "Group Name"
msgstr "Nombre del grupo"

#: app/Services/Funnel/Actions/HTTPSendDataAction.php:140
msgid "Header Key"
msgstr "Clave de cabecera"

#: app/Services/Funnel/Actions/HTTPSendDataAction.php:141
msgid "Header Value"
msgstr "Valor de la cabecera"

#: app/Services/PluginManager/LicenseManager.php:52
msgid "Help & Support"
msgstr "Ayuda y soporte técnico"

#. URI of the plugin
#. Author URI of the plugin
msgid "https://fluentcrm.com"
msgstr "https://fluentcrm.com"

#: app/Hooks/Handlers/DataExporter.php:542
#: app/Services/Integrations/LifterLms/LifterInit.php:126
#: app/Services/Integrations/LearnDash/LdInit.php:118
#: app/Services/Integrations/TutorLms/TutorLmsInit.php:105
msgid "ID"
msgstr "ID"

#: app/Services/Integrations/LifterLms/LessonCompletedTrigger.php:80
#: app/Services/Integrations/LifterLms/CourseCompletedTrigger.php:76
#: app/Services/Integrations/LifterLms/CourseEnrollTrigger.php:81
#: app/Services/Integrations/LifterLms/MembershipEnrollTrigger.php:81
#: app/Services/Integrations/PMPro/PMProPMProMembershipTrigger.php:77
#: app/Services/Integrations/LearnDash/GroupEnrollTrigger.php:76
#: app/Services/Integrations/LearnDash/CourseLeaveTrigger.php:79
#: app/Services/Integrations/LearnDash/TopicCompletedTrigger.php:76
#: app/Services/Integrations/LearnDash/LessonCompletedTrigger.php:76
#: app/Services/Integrations/LearnDash/CourseCompletedTrigger.php:75
#: app/Services/Integrations/LearnDash/CourseEnrollTrigger.php:78
#: app/Services/Integrations/TutorLms/LessonCompletedTrigger.php:76
#: app/Services/Integrations/TutorLms/CourseCompletedTrigger.php:76
#: app/Services/Integrations/TutorLms/CourseEnrollTrigger.php:77
#: app/Services/Integrations/AffiliateWP/AffiliateWPAffActiveTrigger.php:104
#: app/Services/Funnel/Triggers/UserLoginTrigger.php:69
msgid "If Contact Already Exist?"
msgstr "¿Si el contacto ya existe?"

#: app/Services/Integrations/SureCart/SureCartPaymentRefundTrigger.php:77
#: app/Services/Integrations/SureCart/SureCartPaymentSuccessTrigger.php:78
#: app/Services/Integrations/Edd/EddPaymentSuccessTrigger.php:79
#: app/Services/Integrations/Edd/EddRecurringExpired.php:77
#: app/Services/Integrations/Edd/EddRecurringPaymentTrigger.php:78
#: app/Services/Integrations/Edd/EddPaymentRefundTrigger.php:79
msgid "If Contact Exist?"
msgstr "¿Si existe el contacto?"

#: app/Services/Funnel/Actions/ChangeUserRoleAction.php:22
msgid "If user exist with the contact email then you can change user role"
msgstr ""
"Si el usuario existe con el correo electrónico de contacto, entonces usted "
"puede cambiar el rol del usuario"

#: app/Services/Funnel/Actions/ChangeUserRoleAction.php:49
msgid ""
"If you disable this then it will append the selected role with existing "
"roles."
msgstr ""
"Si lo desactiva, el rol seleccionado se añadirá a los roles existentes."

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:88
msgid ""
"If you enable, then it will only run this automation for subscribed contacts"
msgstr ""

#: app/Services/FunnelMultiConditionTrait.php:21
#: app/Services/Integrations/RCP/RCPMembershipTrigger.php:85
#: app/Services/Integrations/RCP/RCPMembershipCancelTrigger.php:85
#: app/Services/Integrations/RCP/RCPMembershipExpireTrigger.php:85
#: app/Services/Integrations/LifterLms/LessonCompletedTrigger.php:96
#: app/Services/Integrations/LifterLms/CourseCompletedTrigger.php:92
#: app/Services/Integrations/LifterLms/CourseEnrollTrigger.php:96
#: app/Services/Integrations/LifterLms/MembershipEnrollTrigger.php:98
#: app/Services/Integrations/WooCommerce/WooSubscriptionStartedTrigger.php:63
#: app/Services/Integrations/WooCommerce/WooOrderCompletedTrigger.php:103
#: app/Services/Integrations/WooCommerce/WooOrderStatusChangeTrigger.php:113
#: app/Services/Integrations/WooCommerce/WooSubscriptionRenewalFailedTrigger.php:63
#: app/Services/Integrations/WooCommerce/WooOrderRefundedTrigger.php:95
#: app/Services/Integrations/WooCommerce/WooOrderSuccessTrigger.php:105
#: app/Services/Integrations/WooCommerce/WooSubscriptionExpiredTrigger.php:63
#: app/Services/Integrations/WooCommerce/WooSubscriptionRenewalPaymentTrigger.php:63
#: app/Services/Integrations/PMPro/PMProPMProMembershipTrigger.php:92
#: app/Services/Integrations/PMPro/PMProPMProExpiryLevelTrigger.php:85
#: app/Services/Integrations/PMPro/PMProPMProCancelLevelTrigger.php:85
#: app/Services/Integrations/SureCart/SureCartPaymentRefundTrigger.php:93
#: app/Services/Integrations/SureCart/SureCartPaymentSuccessTrigger.php:94
#: app/Services/Integrations/LearnDash/GroupEnrollTrigger.php:93
#: app/Services/Integrations/LearnDash/CourseLeaveTrigger.php:96
#: app/Services/Integrations/LearnDash/TopicCompletedTrigger.php:107
#: app/Services/Integrations/LearnDash/LessonCompletedTrigger.php:100
#: app/Services/Integrations/LearnDash/CourseCompletedTrigger.php:92
#: app/Services/Integrations/LearnDash/CourseEnrollTrigger.php:95
#: app/Services/Integrations/Edd/EddPaymentSuccessTrigger.php:110
#: app/Services/Integrations/Edd/EddLicenseExpiredTrigger.php:85
#: app/Services/Integrations/Edd/EddRecurringExpired.php:93
#: app/Services/Integrations/Edd/EddRecurringPaymentTrigger.php:102
#: app/Services/Integrations/Edd/EddPaymentRefundTrigger.php:110
#: app/Services/Integrations/WishlistMember/WishlistMembershipTrigger.php:85
#: app/Services/Integrations/TutorLms/LessonCompletedTrigger.php:91
#: app/Services/Integrations/TutorLms/CourseCompletedTrigger.php:91
#: app/Services/Integrations/TutorLms/CourseEnrollTrigger.php:92
#: app/Services/Integrations/MemberPress/MembershipTrigger.php:85
#: app/Services/Integrations/MemberPress/SubscriptionExpiredTrigger.php:84
#: app/Services/Integrations/AffiliateWP/AffiliateWPAffActiveTrigger.php:86
#: app/Services/Funnel/Triggers/UserLoginTrigger.php:86
msgid ""
"If you enable, then it will restart the automation for a contact if the "
"contact already in the automation. Otherwise, It will just skip if already "
"exist"
msgstr ""
"Si se habilita, entonces se reiniciará la automatización para un contacto si "
"el contacto ya está en la automatización. De lo contrario, sólo se saltará "
"si ya existe"

#: app/Services/Funnel/Triggers/TrackingEventRecordedTrigger.php:100
msgid ""
"If you enable, then it will restart the automation for a contact if the "
"contact already in the automation. Please note that, if the automation "
"status is active it will not restart."
msgstr ""

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:39
msgid ""
"If you have multiple automation for abandon cart, you can set the priority. "
"The higher the priority means it will match earlier. Only one abandon cart "
"automation will run per abandonment depends on your conditional logics."
msgstr ""

#: app/Services/Funnel/Actions/UserRegistrationAction.php:63
msgid "If you leave blank then auto generated password will be set"
msgstr ""
"Si se deja en blanco, se establecerá una contraseña generada automáticamente"

#: app/Services/Funnel/Actions/UserRegistrationAction.php:74
msgid ""
"If you leave blank then email will be used as username. If provided username "
"is not available then email address will be used for username"
msgstr ""

#: app/Services/Funnel/Actions/UserRegistrationAction.php:83
msgid ""
"If you want to map user meta properties you can add that here. This is "
"totally optional"
msgstr ""
"Si quieres asignar las meta propiedades del usuario puedes añadirlo aquí. "
"Esto es totalmente opcional"

#: app/Modules/AbandonCart/Views/AbandonCartItems.php:109
#: app/Modules/AbandonCart/Views/AbandonCartItems.php:128
msgid "Image"
msgstr ""

#: app/Services/Integrations/WooCommerce/WooImporter.php:120
#: app/Services/Integrations/Edd/EddImporter.php:114
#, php-format
msgid "Import %s Customers Now"
msgstr "Importar %s clientes ahora"

#: app/Services/Integrations/RCP/RCPImporter.php:141
#: app/Services/Integrations/PMPro/PMProImporter.php:139
#: app/Services/Integrations/BuddyPress/BbImporter.php:202
#: app/Services/Integrations/WishlistMember/WishlistMemberImporter.php:137
#: app/Services/Integrations/MemberPress/MemberPressImporter.php:131
#, php-format
msgid "Import %s Members Now"
msgstr "Importar %s miembros ahora"

#: app/Services/Integrations/LifterLms/LifterImporter.php:181
#: app/Services/Integrations/LearnPress/LearnPressImporter.php:131
#: app/Services/Integrations/LearnDash/LearnDashImporter.php:184
#: app/Services/Integrations/TutorLms/TutorImporter.php:137
#, php-format
msgid "Import %s Students Now"
msgstr "Importar %s estudiantes ahora"

#: app/Services/Integrations/RCP/RCPImporter.php:110
#: app/Services/Integrations/LifterLms/LifterImporter.php:134
#: app/Services/Integrations/LearnPress/LearnPressImporter.php:103
#: app/Services/Integrations/PMPro/PMProImporter.php:108
#: app/Services/Integrations/LearnDash/LearnDashImporter.php:137
#: app/Services/Integrations/WishlistMember/WishlistMemberImporter.php:109
#: app/Services/Integrations/TutorLms/TutorImporter.php:109
#: app/Services/Integrations/MemberPress/MemberPressImporter.php:100
msgid "Import by"
msgstr "Importación por"

#: app/Services/Integrations/LifterLms/LifterImporter.php:140
#: app/Services/Integrations/LearnDash/LearnDashImporter.php:143
#: app/Services/Integrations/TutorLms/TutorImporter.php:115
msgid "Import By Courses"
msgstr "Importación por cursos"

#: app/Services/Integrations/LearnDash/LearnDashImporter.php:147
#: app/Services/Integrations/BuddyPress/BbImporter.php:165
msgid "Import By Member Groups"
msgstr "Importar por grupos de miembros"

#: app/Services/Integrations/BuddyPress/BbImporter.php:161
msgid "Import By Member Type"
msgstr "Importación por tipo de miembro"

#: app/Services/Integrations/RCP/RCPImporter.php:116
#: app/Services/Integrations/PMPro/PMProImporter.php:114
#: app/Services/Integrations/WishlistMember/WishlistMemberImporter.php:115
#: app/Services/Integrations/MemberPress/MemberPressImporter.php:106
msgid "Import By Membership Level"
msgstr "Importar por nivel de membresía"

#: app/Services/Integrations/LifterLms/LifterImporter.php:144
msgid "Import By Memberships"
msgstr "Importar por membresías"

#: app/Http/Controllers/DynamicSegmentController.php:114
#: app/Http/Controllers/DynamicSegmentController.php:201
#: app/Http/Controllers/DynamicSegmentController.php:212
msgid "In"
msgstr "En"

#: app/Services/Integrations/AffiliateWP/DeepIntegration.php:83
msgid "Inactive"
msgstr "Inactivo"

#: app/Services/Integrations/WooCommerce/AdvancedReport.php:85
msgid "Individual Product Sales values are excluded Tax & Shipping amounts"
msgstr ""

#: app/Http/Controllers/CampaignsProController.php:175
msgid "invalid selection"
msgstr "selección inválida"

#: app/Hooks/Handlers/DataExporter.php:555
msgid "IP Address"
msgstr "Dirección IP"

#: app/Services/Integrations/AffiliateWP/DeepIntegration.php:58
msgid "Is Affiliate"
msgstr "Es afiliado"

#: app/Services/Integrations/WooCommerce/AutomationConditions.php:73
msgid "Is guest?"
msgstr "¿Es huésped?"

#: app/Modules/AbandonCart/Views/AbandonCartItems.php:110
#: app/Modules/AbandonCart/Views/AbandonCartItems.php:131
msgid "Item"
msgstr ""

#: app/Services/Integrations/LifterLms/MembershipEnrollTrigger.php:28
msgid "Joined Membership"
msgstr ""

#: app/Http/Controllers/DynamicSegmentController.php:234
#: app/Http/Controllers/DynamicSegmentController.php:240
msgid "Keep days 0/Blank for disable"
msgstr "Mantener los días 0/en blanco para desactivar"

#: app/Services/Integrations/WooCommerce/WooOrderCompletedTrigger.php:90
#: app/Services/Integrations/WooCommerce/WooOrderSuccessBenchmark.php:69
#: app/Services/Integrations/WooCommerce/WooOrderRefundedTrigger.php:89
#: app/Services/Integrations/WooCommerce/WooOrderSuccessTrigger.php:92
#: app/Services/Integrations/Edd/EddPaymentSuccessTrigger.php:97
#: app/Services/Integrations/Edd/EddOrderSuccessBenchmark.php:68
#: app/Services/Integrations/Edd/EddRecurringPaymentTrigger.php:96
#: app/Services/Integrations/Edd/EddPaymentRefundTrigger.php:97
msgid "Keep it blank to run to any category products"
msgstr ""
"Manténgalo en blanco para que se dirija a cualquier categoría de productos"

#: app/Services/Integrations/LifterLms/CourseEnrollTrigger.php:90
#: app/Services/Integrations/LearnDash/CourseLeaveTrigger.php:90
#: app/Services/Integrations/LearnDash/CourseEnrollTrigger.php:89
#: app/Services/Integrations/TutorLms/CourseCompletedTrigger.php:85
#: app/Services/Integrations/TutorLms/CourseEnrollTrigger.php:86
msgid "Keep it blank to run to any Course Enrollment"
msgstr "Manténgalo en blanco para correr a cualquier Curso de Matriculación"

#: app/Services/Integrations/LearnDash/CourseCompletedTrigger.php:86
msgid "Keep it blank to run to any courses"
msgstr ""

#: app/Services/Integrations/LearnDash/GroupEnrollTrigger.php:87
msgid "Keep it blank to run to any group Enrollment"
msgstr "Manténgalo en blanco para ejecutar a cualquier grupo Inscripción"

#: app/Services/Integrations/LifterLms/LessonCompletedTrigger.php:90
#: app/Services/Integrations/LifterLms/CourseCompletedTrigger.php:86
msgid "Keep it blank to run to any Lesson"
msgstr "Manténgalo en blanco para correr a cualquier Lección"

#: app/Services/Integrations/TutorLms/LessonCompletedTrigger.php:85
msgid "Keep it blank to run to any Lesson Complete"
msgstr ""

#: app/Services/Integrations/RCP/RCPMembershipCancelTrigger.php:79
msgid "Keep it blank to run to any Level Cancellation"
msgstr "Manténgalo en blanco para correr a cualquier nivel de cancelación"

#: app/Services/Integrations/PMPro/PMProPMProExpiryLevelTrigger.php:79
#: app/Services/Integrations/PMPro/PMProPMProCancelLevelTrigger.php:79
msgid "Keep it blank to run to any Level cancellation"
msgstr "Manténgalo en blanco para correr a cualquier nivel de cancelación"

#: app/Services/Integrations/RCP/RCPMembershipTrigger.php:79
#: app/Services/Integrations/PMPro/PMProPMProMembershipTrigger.php:86
#: app/Services/Integrations/WishlistMember/WishlistMembershipTrigger.php:79
#: app/Services/Integrations/MemberPress/MembershipTrigger.php:79
msgid "Keep it blank to run to any Level Enrollment"
msgstr "Manténgalo en blanco para correr a cualquier Nivel de Matriculación"

#: app/Services/Integrations/RCP/RCPMembershipExpireTrigger.php:79
msgid "Keep it blank to run to any Level Expiration"
msgstr ""

#: app/Services/Integrations/LifterLms/MembershipEnrollTrigger.php:92
msgid "Keep it blank to run to any Membership"
msgstr "Manténgalo en blanco para correr a cualquier membresía"

#: app/Services/Integrations/Edd/EddPaymentRefundTrigger.php:89
msgid "Keep it blank to run to any product"
msgstr "Manténgalo en blanco para correr a cualquier producto"

#: app/Services/Integrations/WooCommerce/WooOrderCompletedTrigger.php:82
#: app/Services/Integrations/WooCommerce/WooOrderSuccessBenchmark.php:61
#: app/Services/Integrations/WooCommerce/WooOrderSuccessTrigger.php:84
#: app/Services/Integrations/SureCart/SureCartPaymentSuccessBenchmark.php:56
#: app/Services/Integrations/SureCart/SureCartPaymentRefundTrigger.php:87
#: app/Services/Integrations/SureCart/SureCartPaymentSuccessTrigger.php:88
#: app/Services/Integrations/Edd/EddPaymentSuccessTrigger.php:89
#: app/Services/Integrations/Edd/EddRecurringExpired.php:87
#: app/Services/Integrations/Edd/EddOrderSuccessBenchmark.php:60
#: app/Services/Integrations/Edd/EddRecurringPaymentTrigger.php:88
#: app/Services/Integrations/Edd/EddSubscriptionActiveBenchmark.php:56
msgid "Keep it blank to run to any product purchase"
msgstr "Manténgalo en blanco para correr a cualquier compra de productos"

#: app/Services/Integrations/WooCommerce/WooOrderRefundedTrigger.php:81
msgid "Keep it blank to run to any product refund"
msgstr "Manténgalo en blanco para correr a cualquier reembolso del producto"

#: app/Services/Integrations/WooCommerce/WooOrderStatusChangeTrigger.php:95
msgid "Keep it blank to run to any product status changed"
msgstr ""

#: app/Services/Integrations/MemberPress/SubscriptionExpiredTrigger.php:78
msgid "Keep it blank to run to any subscription cancellation"
msgstr ""
"Manténgalo en blanco para ejecutar cualquier cancelación de la suscripción"

#: app/Services/Integrations/WooCommerce/WooSubscriptionExpiredTrigger.php:57
msgid "Keep it blank to run to any subscription expire"
msgstr ""

#: app/Services/Integrations/WooCommerce/WooSubscriptionRenewalFailedTrigger.php:57
msgid "Keep it blank to run to any subscription renewal failed"
msgstr ""

#: app/Services/Integrations/WooCommerce/WooSubscriptionRenewalPaymentTrigger.php:57
msgid "Keep it blank to run to any subscription renewal payment"
msgstr ""

#: app/Services/Integrations/WooCommerce/WooSubscriptionStartedTrigger.php:57
msgid "Keep it blank to run to any subscription starts"
msgstr ""

#: app/Services/Integrations/LearnDash/TopicCompletedTrigger.php:101
msgid "Keep it blank to run to any Topic for that lesson"
msgstr "Manténgalo en blanco para ir a cualquier tema de esa lección"

#: app/Hooks/Handlers/DataExporter.php:561
#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:221
#: app/Services/Funnel/Conditions/FunnelCondition.php:153
msgid "Last Activity"
msgstr "Última actividad"

#: app/Http/Controllers/DynamicSegmentController.php:192
msgid "Last Contact Activity"
msgstr "Última actividad de contacto"

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:272
#: app/Services/Funnel/Conditions/FunnelCondition.php:204
msgid "Last Email Clicked"
msgstr "Último correo electrónico pulsado"

#: app/Http/Controllers/DynamicSegmentController.php:238
msgid "Last Email Link Clicked"
msgstr "Último enlace de correo electrónico pulsado"

#: app/Http/Controllers/DynamicSegmentController.php:232
msgid "Last Email Open"
msgstr "Último correo electrónico abierto"

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:277
#: app/Services/Funnel/Conditions/FunnelCondition.php:209
msgid "Last Email Open (approximately)"
msgstr "Último correo electrónico abierto (aproximadamente)"

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:267
#: app/Services/Funnel/Conditions/FunnelCondition.php:199
msgid "Last Email Sent"
msgstr "Último correo electrónico enviado"

#: app/Services/Integrations/LifterLms/AutomationConditions.php:59
#: app/Services/Integrations/LifterLms/DeepIntegration.php:88
#: app/Services/Integrations/LearnDash/AutomationConditions.php:63
#: app/Services/Integrations/LearnDash/DeepIntegration.php:78
#: app/Services/Integrations/TutorLms/DeepIntegration.php:416
msgid "Last Enrollment Date"
msgstr "Última fecha de inscripción"

#: app/Http/Controllers/DynamicSegmentController.php:147
#: app/Hooks/Handlers/DataExporter.php:546
#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:145
#: app/Services/Funnel/Conditions/FunnelCondition.php:77
msgid "Last Name"
msgstr "Apellido"

#: app/Services/Integrations/WooCommerce/DeepIntegration.php:555
#: app/Services/Integrations/Edd/DeepIntegration.php:227
msgid "Last Order"
msgstr "Último pedido"

#: app/Services/Integrations/WooCommerce/AutomationConditions.php:90
#: app/Services/Integrations/WooCommerce/DeepIntegration.php:219
#: app/Services/Integrations/Edd/AutomationConditions.php:50
#: app/Services/Integrations/Edd/DeepIntegration.php:297
msgid "Last Order Date"
msgstr "Fecha del último pedido"

#: app/Services/Integrations/AffiliateWP/DeepIntegration.php:107
msgid "Last Payout Date"
msgstr "Última fecha de pago"

#: app/Services/Integrations/BuddyPress/BBInit.php:110
msgid "Last Update"
msgstr "Última actualización"

#: app/Services/Integrations/LearnDash/GroupEnrollTrigger.php:22
#: app/Services/Integrations/LearnDash/GroupEnrollTrigger.php:105
#: app/Services/Integrations/LearnDash/CourseLeaveTrigger.php:23
#: app/Services/Integrations/LearnDash/TopicCompletedTrigger.php:22
#: app/Services/Integrations/LearnDash/TopicCompletedTrigger.php:120
#: app/Services/Integrations/LearnDash/LessonCompletedTrigger.php:22
#: app/Services/Integrations/LearnDash/LessonCompletedTrigger.php:111
#: app/Services/Integrations/LearnDash/CourseCompletedTrigger.php:22
#: app/Services/Integrations/LearnDash/AutomationConditions.php:30
#: app/Services/Integrations/LearnDash/RemoveFromCourseAction.php:21
#: app/Services/Integrations/LearnDash/DeepIntegration.php:33
#: app/Services/Integrations/LearnDash/DeepIntegration.php:151
#: app/Services/Integrations/LearnDash/DeepIntegration.php:162
#: app/Services/Integrations/LearnDash/AddToGroupAction.php:21
#: app/Services/Integrations/LearnDash/CourseEnrollTrigger.php:22
#: app/Services/Integrations/LearnDash/AddToCourseAction.php:21
#: app/Services/Integrations/LearnDash/RemoveFromGroupAction.php:21
msgid "LearnDash"
msgstr "LearnDash"

#: app/Services/Integrations/LearnDash/AutomationConditions.php:30
msgid "LearnDash (Sync Required)"
msgstr "LearnDash (requiere sincronización)"

#: app/Services/Integrations/LearnDash/AdvancedReport.php:57
msgid "LearnDash - Advanced Reports"
msgstr "LearnDash - Informes avanzados"

#: app/Services/Integrations/LearnDash/LdInit.php:78
msgid "LearnDash Courses"
msgstr "Cursos de LearnDash"

#: app/Services/Integrations/LearnDash/DeepIntegration.php:164
msgid "LearnDash students are not synced with FluentCRM yet."
msgstr "Los estudiantes de LearnDash aún no están sincronizados con FluentCRM."

#: app/Services/Integrations/LearnPress/LearnPressInit.php:58
msgid "LearnPress Courses"
msgstr "Cursos de LearnPress"

#: app/Services/Integrations/WooCommerce/WooSmartCodeParse.php:356
msgid "Leave a review"
msgstr "Deje una reseña"

#: app/Services/Funnel/Triggers/UserLoginTrigger.php:80
msgid "Leave blank to run for all user roles"
msgstr ""

#: app/Services/Integrations/LearnDash/LessonCompletedTrigger.php:94
msgid "Leave empty to target any lesson of this course"
msgstr "Dejar vacío para apuntar a cualquier lección de este curso"

#: app/Services/Integrations/LearnDash/CourseLeaveTrigger.php:42
msgid "Leave from a Course"
msgstr "Baja de un curso"

#: app/Services/Integrations/LifterLms/LessonCompletedTrigger.php:28
#: app/Services/Integrations/LearnDash/LessonCompletedTrigger.php:23
#: app/Services/Integrations/TutorLms/LessonCompletedTrigger.php:24
msgid "Lesson Completed"
msgstr ""

#: app/Services/Integrations/Edd/EddLicenseExpiredTrigger.php:24
msgid "License Expired"
msgstr "Licencia caducada"

#: app/Services/Integrations/Edd/EddLicenseExpiredTrigger.php:39
msgid "License Expired in EDD"
msgstr "Licencia caducada en EDD"

#: app/Services/Integrations/WooCommerce/DeepIntegration.php:584
#: app/Services/Integrations/Edd/DeepIntegration.php:254
msgid "Lifetime Value"
msgstr "Valor de la vida útil"

#: app/Services/Integrations/LifterLms/LessonCompletedTrigger.php:27
#: app/Services/Integrations/LifterLms/LessonCompletedTrigger.php:108
#: app/Services/Integrations/LifterLms/CourseCompletedTrigger.php:23
#: app/Services/Integrations/LifterLms/CourseCompletedTrigger.php:104
#: app/Services/Integrations/LifterLms/RemoveFromMembershipAction.php:21
#: app/Services/Integrations/LifterLms/AutomationConditions.php:27
#: app/Services/Integrations/LifterLms/RemoveFromCourseAction.php:21
#: app/Services/Integrations/LifterLms/DeepIntegration.php:59
#: app/Services/Integrations/LifterLms/DeepIntegration.php:83
#: app/Services/Integrations/LifterLms/DeepIntegration.php:157
#: app/Services/Integrations/LifterLms/Helper.php:192
#: app/Services/Integrations/LifterLms/CourseEnrollTrigger.php:27
#: app/Services/Integrations/LifterLms/CourseEnrollTrigger.php:108
#: app/Services/Integrations/LifterLms/AddToMembershipAction.php:21
#: app/Services/Integrations/LifterLms/AddToCourseAction.php:22
#: app/Services/Integrations/LifterLms/MembershipEnrollTrigger.php:27
#: app/Services/Integrations/LifterLms/MembershipEnrollTrigger.php:110
msgid "LifterLMS"
msgstr "LifterLMS"

#: app/Services/Integrations/LifterLms/AutomationConditions.php:27
msgid "LifterLMS (Sync Required)"
msgstr "LifterLMS (requiere sincronización)"

#: app/Services/Integrations/LifterLms/AdvancedReport.php:57
msgid "LifterLMS - Advanced Reports"
msgstr "LifterLMS - Informes avanzados"

#: app/Services/Integrations/LifterLms/LifterInit.php:76
msgid "LifterLMS Courses"
msgstr "Cursos de LifterLMS"

#: app/Services/Integrations/LifterLms/DeepIntegration.php:159
msgid "LifterLMS students are not synced with FluentCRM yet."
msgstr "Los estudiantes de LifterLMS aún no están sincronizados con FluentCRM."

#: app/Services/Funnel/Benchmarks/LinkClickBenchmark.php:35
#: app/Services/Funnel/Benchmarks/LinkClickBenchmark.php:51
msgid "Link Click"
msgstr "Enlace Click"

#: app/Services/Integrations/CRM/AdvancedReport.php:48
msgid "Link Clicks Stats"
msgstr "Estadísticas de clics de enlaces"

#: app/Http/Controllers/CampaignsProController.php:125
msgid "Links are required"
msgstr "Los enlaces son necesarios"

#: app/Services/Integrations/CRM/ListAppliedTrigger.php:28
#: app/Services/Integrations/CRM/ListAppliedTrigger.php:45
msgid "List Applied"
msgstr "Lista aplicada"

#: app/Services/Integrations/CRM/RemoveFromListTrigger.php:29
msgid "List Removed"
msgstr "Lista eliminada"

#: app/Http/Controllers/DynamicSegmentController.php:210
#: app/Hooks/Handlers/DataExporter.php:564
#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:245
#: app/Services/Funnel/Conditions/FunnelCondition.php:177
msgid "Lists"
msgstr "Listas"

#: app/Modules/AbandonCart/AbandonCartController.php:141
msgid "Lost Revenue"
msgstr ""

#: app/Http/Controllers/ManagerController.php:84
msgid "Manager has been added"
msgstr "Se ha añadido un gestor"

#: app/Http/Controllers/ManagerController.php:144
msgid "Manager has been removed"
msgstr "El gerente ha sido eliminado"

#: app/Http/Controllers/ManagerController.php:124
msgid "Manager has been updated"
msgstr "El gestor ha sido actualizado"

#: app/Http/Controllers/DynamicSegmentController.php:247
msgid "Match Any One Condition"
msgstr "Coincidir con cualquier condición"

#: app/Http/Controllers/DynamicSegmentController.php:246
msgid "Match Both Open and Click Condition"
msgstr "Coincidir con la condición de abrir y hacer clic"

#: app/Http/Controllers/DynamicSegmentController.php:243
msgid "Match Type"
msgstr "Tipo de partido"

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:73
#: app/Services/Funnel/Triggers/TrackingEventRecordedTrigger.php:90
#: app/Services/Funnel/Conditions/FunnelCondition.php:54
msgid "Match Value"
msgstr "Valor del partido"

#: app/Services/Integrations/BuddyPress/BBInit.php:86
#: app/Services/Integrations/BuddyPress/BBInit.php:142
msgid "Member"
msgstr "Miembro"

#: app/Services/Integrations/MemberPress/MembershipTrigger.php:23
#: app/Services/Integrations/MemberPress/MembershipTrigger.php:104
#: app/Services/Integrations/MemberPress/SubscriptionExpiredTrigger.php:23
#: app/Services/Integrations/MemberPress/SubscriptionExpiredTrigger.php:108
msgid "MemberPress"
msgstr "MiembroPrensa"

#: app/Services/Integrations/RCP/RCPMembershipCancelTrigger.php:24
msgid "Membership cancelled"
msgstr "Membresía cancelada"

#: app/Services/Integrations/RCP/RCPMembershipTrigger.php:24
#: app/Services/Integrations/PMPro/PMProPMProMembershipTrigger.php:24
#: app/Services/Integrations/WishlistMember/WishlistMembershipTrigger.php:24
#: app/Services/Integrations/MemberPress/MembershipTrigger.php:24
msgid "Membership Enrolled"
msgstr ""

#: app/Services/Integrations/RCP/RCPMembershipExpireTrigger.php:24
msgid "Membership expired"
msgstr ""

#: app/Services/Integrations/LearnDash/AdvancedReport.php:46
msgid "Membership Groups"
msgstr "Grupos de membresía"

#: app/Services/Integrations/RCP/RCPMembershipCancelTrigger.php:40
msgid "Membership is cancelled"
msgstr "Se cancela la membresía"

#: app/Services/Integrations/RCP/RCPMembershipExpireTrigger.php:40
msgid "Membership is expired"
msgstr ""

#: app/Services/Integrations/RCP/AutomationConditions.php:24
#: app/Services/Integrations/PMPro/AutomationConditions.php:24
#: app/Services/Integrations/WishlistMember/AutomationConditions.php:24
msgid "Membership Level"
msgstr "Nivel de membresía"

#: app/Services/Integrations/PMPro/PMProPMProCancelLevelTrigger.php:24
#: app/Services/Integrations/PMPro/PMProPMProCancelLevelTrigger.php:40
msgid "Membership Level Cancelled"
msgstr ""

#: app/Services/Integrations/PMPro/PMProPMProExpiryLevelTrigger.php:40
msgid "Membership Level Expiration"
msgstr "Expiración del nivel de membresía"

#: app/Services/Integrations/PMPro/PMProPMProExpiryLevelTrigger.php:24
msgid "Membership Level Expired"
msgstr ""

#: app/Services/Integrations/LifterLms/AdvancedReport.php:46
msgid "Memberships"
msgstr "Membresías"

#: app/Services/Funnel/Actions/UpdateUserMetaAction.php:49
#: app/Services/Funnel/Actions/UserRegistrationAction.php:82
msgid "Meta key"
msgstr "Meta clave"

#: app/Services/Funnel/Actions/UpdateUserMetaAction.php:48
#: app/Services/Funnel/Actions/UserRegistrationAction.php:81
msgid "Meta Value"
msgstr "Meta Valor"

#: app/Services/Funnel/Triggers/TrackingEventRecordedTrigger.php:60
msgid "Minimum Event Count"
msgstr ""

#: app/Services/Funnel/Triggers/TrackingEventRecordedTrigger.php:58
msgid "Minimum Occurrence Count of the selected event"
msgstr ""

#: app/Hooks/Handlers/DataExporter.php:792
msgid "Missing \"day\" for weekly scheduling type."
msgstr ""

#: app/Hooks/Handlers/DataExporter.php:786
msgid "Missing or invalid \"time\" in scheduling settings."
msgstr ""

#: app/Services/Integrations/BuddyPress/BBInit.php:93
#: app/Services/Integrations/BuddyPress/BBInit.php:149
msgid "Moderator"
msgstr "Moderador"

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:208
#: app/Services/Funnel/Conditions/FunnelCondition.php:140
msgid "Name Prefix (Title)"
msgstr ""

#: app/Services/Integrations/AffiliateWP/AffiliateWPAffActiveTrigger.php:63
msgid "New Affiliate Approved/Active Register"
msgstr "Nuevo registro de afiliados aprobado/activo"

#: app/Services/Integrations/AffiliateWP/AffiliateWPAffActiveTrigger.php:48
msgid "New Affiliate Joined"
msgstr ""

#: app/Services/Integrations/Edd/EddPaymentSuccessTrigger.php:40
msgid "New Edd Order (paid) has been places"
msgstr "La nueva orden de Edd (de pago) ha sido colocada"

#: app/Services/Integrations/WooCommerce/WooOrderSuccessTrigger.php:26
msgid "New Order (Processing)"
msgstr "Nueva Orden (Procesamiento)"

#: app/Services/Integrations/Edd/EddOrderSuccessBenchmark.php:24
msgid "New Order Success"
msgstr "Éxito del nuevo orden"

#: app/Services/Integrations/Edd/EddOrderSuccessBenchmark.php:51
msgid "New Order Success in EDD"
msgstr "Éxito del nuevo orden en la EDD"

#: app/Hooks/Handlers/RecurringCampaignHandler.php:232
#, php-format
msgid "New recurring email has been created in your site %s"
msgstr ""

#: app/Services/Integrations/SureCart/SureCartPaymentSuccessTrigger.php:41
msgid "New SureCart Order (paid) has been places"
msgstr ""

#: app/Services/Integrations/WooCommerce/WooOrderSuccessTrigger.php:42
msgid "New WooCommerce Order (Processing) has been places"
msgstr "El nuevo pedido de WooCommerce (procesamiento) ha sido colocado"

#: app/Services/Integrations/RCP/RCPImporter.php:140
#: app/Services/Integrations/LifterLms/LifterImporter.php:180
#: app/Services/Integrations/WooCommerce/WooImporter.php:166
#: app/Services/Integrations/LearnPress/LearnPressImporter.php:130
#: app/Services/Integrations/PMPro/PMProImporter.php:138
#: app/Services/Integrations/LearnDash/LearnDashImporter.php:183
#: app/Services/Integrations/BuddyPress/BbImporter.php:201
#: app/Services/Integrations/Edd/EddImporter.php:160
#: app/Services/Integrations/WishlistMember/WishlistMemberImporter.php:136
#: app/Services/Integrations/TutorLms/TutorImporter.php:136
#: app/Services/Integrations/MemberPress/MemberPressImporter.php:130
#: app/Services/Integrations/AffiliateWP/DeepIntegration.php:228
msgid "Next [Review Data]"
msgstr "Siguiente [Revisión de datos]"

#: app/Services/Integrations/AffiliateWP/DeepIntegration.php:62
msgid "No"
msgstr "No"

#: app/Hooks/Handlers/Cleanup.php:84
msgid "No action found to sync"
msgstr ""

#: app/Modules/AbandonCart/AbandonCartController.php:94
msgid "No carts selected to delete"
msgstr ""

#: app/Services/Integrations/LifterLms/LifterInit.php:81
#: app/Services/Integrations/LifterLms/LifterInit.php:94
#: app/Services/Integrations/LifterLms/LifterInit.php:101
#: app/Services/Integrations/LearnPress/LearnPressInit.php:63
#: app/Services/Integrations/LearnPress/LearnPressInit.php:76
#: app/Services/Integrations/LearnDash/LdInit.php:83
#: app/Services/Integrations/LearnDash/LdInit.php:92
#: app/Services/Integrations/TutorLms/TutorLmsInit.php:69
#: app/Services/Integrations/TutorLms/TutorLmsInit.php:76
msgid "No enrolled courses found for this contact"
msgstr "No se han encontrado cursos inscritos para este contacto"

#: app/Hooks/Handlers/DataExporter.php:774
msgid "No file uploaded"
msgstr ""

#: app/Services/Funnel/Actions/EndFunnel.php:22
msgid "No further action will run once a contact hit this point"
msgstr ""
"No se ejecutará ninguna otra acción una vez que el contacto llegue a este "
"punto"

#: app/Services/Integrations/BuddyPress/BBInit.php:68
#: app/Services/Integrations/BuddyPress/BBInit.php:80
msgid "No groups found for this contact"
msgstr "No se han encontrado grupos para este contacto"

#: app/Services/Funnel/Actions/HTTPSendDataAction.php:129
msgid "No Headers"
msgstr "No hay cabeceras"

#: app/Hooks/Handlers/CampaignArchiveFront.php:39
msgid "No published email newsletter found"
msgstr "No se ha encontrado ningún boletín electrónico publicado"

#: app/Http/Controllers/SequenceController.php:210
msgid "No Subscribers found based on your selection"
msgstr "No se han encontrado suscriptores según su selección"

#: app/Services/Funnel/Actions/HTTPSendDataAction.php:194
msgid "No valid body data found"
msgstr "No se han encontrado datos válidos del cuerpo"

#: app/Services/Integrations/BuddyPress/BBInit.php:100
msgid "Not Confirmed"
msgstr "No confirmado"

#: app/Http/Controllers/DynamicSegmentController.php:111
msgid "Not Contains"
msgstr "No contiene"

#: app/Http/Controllers/DynamicSegmentController.php:109
msgid "Not Equal"
msgstr "No es igual"

#: app/Http/Controllers/DynamicSegmentController.php:115
#: app/Http/Controllers/DynamicSegmentController.php:202
#: app/Http/Controllers/DynamicSegmentController.php:213
msgid "Not In"
msgstr "No en"

#: app/Services/Integrations/WooCommerce/AddOrderNoteAction.php:42
msgid "Note to Customer"
msgstr "Nota al cliente"

#: app/Services/Integrations/WooCommerce/AdvancedReport.php:64
#: app/Services/Integrations/Edd/AdvancedReport.php:56
msgid "Onetime Items"
msgstr "Artículos de una sola vez"

#: app/Services/Integrations/ElementorFormIntegration/FormWidget.php:91
msgid "Only add new contacts, don't update existing ones."
msgstr "Sólo añade nuevos contactos, no actualices los existentes."

#: app/Services/Integrations/WooCommerce/Helper.php:55
#: app/Services/Integrations/Edd/Helper.php:58
msgid "Only for first purchase"
msgstr "Sólo para la primera compra"

#: app/Services/Funnel/Actions/UserRoleRemoveAction.php:43
msgid ""
"Only if user is not Administrator Role then the selected role will be "
"applied. After removing the role, if user does not have any role then "
"subscriber role will be added."
msgstr ""
"Sólo si el usuario no tiene rol de administrador, se aplicará el rol "
"seleccionado. Después de eliminar el rol, si el usuario no tiene ningún rol, "
"se añadirá el rol de suscriptor."

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:87
msgid "Only run this automation for subscribed contacts"
msgstr ""

#: app/Modules/AbandonCart/AbandonCartController.php:151
msgid "Optout Revenue"
msgstr ""

#: app/Services/Integrations/WooCommerce/WooOrderCompletedTrigger.php:88
#: app/Services/Integrations/WooCommerce/WooOrderSuccessBenchmark.php:67
#: app/Services/Integrations/WooCommerce/WooOrderRefundedTrigger.php:87
#: app/Services/Integrations/WooCommerce/WooOrderSuccessTrigger.php:90
#: app/Services/Integrations/Edd/EddPaymentSuccessTrigger.php:95
#: app/Services/Integrations/Edd/EddRecurringPaymentTrigger.php:94
#: app/Services/Integrations/Edd/EddPaymentRefundTrigger.php:95
msgid "OR Target Product Categories"
msgstr "O Categorías de productos objetivo"

#: app/Services/Integrations/Edd/EddOrderSuccessBenchmark.php:66
msgid "Or Target Product Categories"
msgstr "O categorías de productos de destino"

#: app/Services/Integrations/WooCommerce/WooOrderCompletedTrigger.php:24
msgid "Order Completed"
msgstr "Pedido completado"

#: app/Services/Integrations/WooCommerce/DeepIntegration.php:572
#: app/Services/Integrations/Edd/DeepIntegration.php:242
msgid "Order Count"
msgstr "Recuento de pedidos"

#: app/Services/Integrations/WooCommerce/AddOrderNoteAction.php:53
msgid "Order Note"
msgstr "Nota de pedido"

#: app/Services/Integrations/WooCommerce/AddOrderNoteAction.php:58
msgid "Order Note Type"
msgstr "Tipo de nota de pedido"

#: app/Services/Integrations/SureCart/SureCartPaymentSuccessBenchmark.php:24
#: app/Services/Integrations/SureCart/SureCartPaymentSuccessBenchmark.php:47
msgid "Order Received in SureCart"
msgstr ""

#: app/Services/Integrations/WooCommerce/WooOrderSuccessBenchmark.php:25
#: app/Services/Integrations/WooCommerce/WooOrderSuccessBenchmark.php:52
msgid "Order Received in WooCommerce"
msgstr "Pedido recibido en WooCommerce"

#: app/Services/Integrations/WooCommerce/WooOrderRefundedTrigger.php:24
msgid "Order Refunded"
msgstr "Pedido reembolsado"

#: app/Services/Integrations/WooCommerce/AutomationConditions.php:207
#| msgid "New Order Status"
msgid "Order Status"
msgstr "Estado del pedido"

#: app/Services/Integrations/WooCommerce/WooOrderStatusChangeTrigger.php:24
msgid "Order Status Changed"
msgstr "Estado del pedido modificado"

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:339
#: app/Services/Funnel/Conditions/FunnelCondition.php:272
msgid "Other"
msgstr "Otros"

#: app/Services/Funnel/Actions/HTTPSendDataAction.php:27
msgid "Outgoing Webhook"
msgstr "Webhook saliente"

#: app/Services/Integrations/Edd/EddCommerceHelper.php:107
msgid "Overall Sales"
msgstr "Ventas totales"

#: app/Services/Integrations/Edd/EddCommerceHelper.php:30
msgid "Paid Customers"
msgstr "Clientes de pago"

#: app/Services/DynamicSegments/PMProMembersSegment.php:21
msgid "Paid Membership Members"
msgstr "Socios de pago"

#: app/Services/DynamicSegments/PMProMembersSegment.php:22
msgid ""
"Paid Membership Members customers who are also in the contact list as "
"subscribed"
msgstr ""
"Los clientes de los miembros de pago que también están en la lista de "
"contactos como suscritos"

#: app/Services/Integrations/PMPro/AutomationConditions.php:19
msgid "Paid Membership Pro"
msgstr "Membresía de pago Pro"

#: app/Services/Integrations/PMPro/PMProPMProMembershipTrigger.php:23
#: app/Services/Integrations/PMPro/PMProPMProExpiryLevelTrigger.php:23
#: app/Services/Integrations/PMPro/PMProPMProCancelLevelTrigger.php:23
msgid "Paid Memberships Pro"
msgstr ""

#: app/Services/Funnel/Actions/UserRegistrationAction.php:56
msgid "Password"
msgstr "Contraseña"

#: app/Services/Funnel/Benchmarks/LinkClickBenchmark.php:60
msgid ""
"Paste this link in any email or page. When a contact click this link then it "
"will be recorded and redirect to the url as provided bellow."
msgstr ""
"Pegue este enlace en cualquier correo electrónico o página. Cuando un "
"contacto haga clic en este enlace, se registrará y redirigirá a la url que "
"se proporciona a continuación."

#: app/Services/Funnel/Actions/RemoveFromFunnelAction.php:24
msgid "Pause/Cancel another automation for contact"
msgstr "Pausa/Cancelación de otra automatización para el contacto"

#: app/Services/Integrations/WooCommerce/AutomationConditions.php:201
msgid "Payment Gateway"
msgstr "Pasarela de pagos"

#: app/Services/Integrations/Edd/EddPaymentRefundTrigger.php:25
msgid "Payment Refunded"
msgstr "Pago reembolsado"

#: app/Services/Integrations/Edd/EddPaymentRefundTrigger.php:40
msgid "Payment Refunded in EDD"
msgstr "Pago reembolsado en EDD"

#: app/Services/Integrations/AffiliateWP/DeepIntegration.php:84
msgid "Pending"
msgstr "Pendiente"

#: app/Hooks/Handlers/DataExporter.php:556
#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:189
#: app/Services/Funnel/Conditions/FunnelCondition.php:121
msgid "Phone"
msgstr "Teléfono"

#: app/Services/Integrations/BuddyPress/BBMemberType.php:231
msgid ""
"Please add at least one member to this Member type to add FluentCRM Tag "
"Settings"
msgstr ""
"Por favor, añada al menos un miembro a este tipo de miembro para añadir la "
"configuración de la etiqueta FluentCRM"

#: app/Services/Integrations/LifterLms/AdvancedReport.php:63
#: app/Services/Integrations/WooCommerce/AdvancedReport.php:92
#: app/Services/Integrations/LearnDash/AdvancedReport.php:63
#: app/Services/Integrations/Edd/AdvancedReport.php:94
#: app/Services/Integrations/TutorLms/AdvancedReport.php:60
msgid "Please enable data sync first from FluentCRM"
msgstr "Por favor, active primero la sincronización de datos desde FluentCRM"

#: app/Services/Funnel/Actions/HTTPSendDataAction.php:115
msgid "Please map the data for custom sending data type"
msgstr ""
"Por favor, asigne los datos para el tipo de datos de envío personalizado"

#: app/Services/Funnel/Actions/HTTPSendDataAction.php:144
msgid "Please map the data for request headers"
msgstr "Por favor, asigne los datos de las cabeceras de las solicitudes"

#: app/Services/Integrations/LifterLms/LifterImporter.php:149
#: app/Services/Integrations/LearnPress/LearnPressImporter.php:114
#: app/Services/Integrations/LearnDash/LearnDashImporter.php:152
#: app/Services/Integrations/TutorLms/TutorImporter.php:120
msgid "Please map your Courses and associate FluentCRM Tags"
msgstr "Por favor, asigne sus cursos y asocie las etiquetas de FluentCRM"

#: app/Services/Integrations/BuddyPress/BbImporter.php:185
msgid "Please map your Group and associate FluentCRM Tags"
msgstr "Por favor, asigne su grupo y asocie las etiquetas de FluentCRM"

#: app/Services/Integrations/LifterLms/LifterImporter.php:164
#: app/Services/Integrations/LearnDash/LearnDashImporter.php:167
msgid "Please map your LearnDash Group and associate FluentCRM Tags"
msgstr ""
"Por favor, mapee su grupo de LearnDash y asocie las etiquetas de FluentCRM"

#: app/Services/Integrations/BuddyPress/BbImporter.php:170
msgid "Please map your Member Type and associate FluentCRM Tags"
msgstr ""
"Por favor, asigne su tipo de miembro y las etiquetas asociadas de FluentCRM"

#: app/Services/Integrations/RCP/RCPImporter.php:121
#: app/Services/Integrations/PMPro/PMProImporter.php:119
#: app/Services/Integrations/WishlistMember/WishlistMemberImporter.php:120
#: app/Services/Integrations/MemberPress/MemberPressImporter.php:111
msgid "Please map your Membership Levels and associate FluentCRM Tags"
msgstr ""
"Por favor, mapee sus niveles de membresía y asocie las etiquetas de FluentCRM"

#: app/Services/Integrations/WooCommerce/WooImporter.php:135
#: app/Services/Integrations/Edd/EddImporter.php:129
msgid "Please map your Product and associate FluentCRM Tags"
msgstr "Por favor, asigne su producto y asocie las etiquetas de FluentCRM"

#: app/Services/Funnel/Triggers/TrackingEventRecordedTrigger.php:64
msgid ""
"Please note, this trigger will start if the contact is in subscribed status. "
"Otherwise, it will skip this automation."
msgstr ""

#: app/Hooks/Handlers/DataExporter.php:735
msgid "Please provide Campaign ID"
msgstr ""

#: app/Http/Controllers/DynamicSegmentController.php:289
#: app/Http/Controllers/DynamicSegmentController.php:333
msgid "Please provide segment title"
msgstr "Indique el título del segmento"

#: app/Hooks/Handlers/DataExporter.php:455
msgid "Please provide sequence_id"
msgstr ""

#: app/Services/Funnel/Actions/UpdateUserMetaAction.php:50
msgid "Please provide the meta key and meta value. You can use smart tags too"
msgstr ""
"Por favor, proporcione la meta clave y el meta valor. También puede utilizar "
"etiquetas inteligentes"

#: app/Services/Funnel/Benchmarks/LinkClickBenchmark.php:67
msgid "Please provide the url to where the contact will be redirected"
msgstr "Por favor, facilite la url a la que será redirigido el contacto"

#: app/Services/Funnel/Actions/HTTPSendDataAction.php:78
msgid "Please provide valid URL in where you want to send the data"
msgstr "Por favor, proporcione una URL válida en la que desee enviar los datos"

#: app/Services/Funnel/Benchmarks/LinkClickBenchmark.php:56
msgid "Please save to get the sharable link"
msgstr "Por favor, guarde para obtener el enlace compartible"

#: app/Services/Integrations/LearnPress/LearnPressImporter.php:104
#: app/Services/Integrations/TutorLms/TutorImporter.php:110
msgid "Please select import by course enrollment"
msgstr "Por favor, seleccione la importación por la inscripción en el curso"

#: app/Services/Integrations/LearnDash/LearnDashImporter.php:138
msgid "Please select import by group or course enrollment"
msgstr ""
"Por favor, seleccione la importación por grupo o por curso de inscripción"

#: app/Services/Integrations/RCP/RCPImporter.php:111
#: app/Services/Integrations/PMPro/PMProImporter.php:109
#: app/Services/Integrations/WishlistMember/WishlistMemberImporter.php:110
#: app/Services/Integrations/MemberPress/MemberPressImporter.php:101
msgid "Please select import by Membership Level"
msgstr "Seleccione la importación por nivel de membresía"

#: app/Services/Integrations/LifterLms/LifterImporter.php:135
msgid "Please select import by Membership or course enrollment"
msgstr "Seleccione la importación por membresía o por inscripción a un curso"

#: app/Services/Integrations/BuddyPress/BbImporter.php:156
msgid "Please select Member Type or Member group that you want to import"
msgstr "Seleccione el tipo de miembro o grupo de miembros que desea importar"

#: app/Services/Funnel/Benchmarks/EmailSequenceCompletedBenchmark.php:46
msgid "Please select the email sequences that need to be watched for completed"
msgstr ""
"Por favor, seleccione las secuencias de correo electrónico que deben ser "
"vigiladas para completar"

#: app/Services/Integrations/LifterLms/LessonCompletedTrigger.php:81
#: app/Services/Integrations/LifterLms/CourseCompletedTrigger.php:77
#: app/Services/Integrations/LifterLms/CourseEnrollTrigger.php:82
#: app/Services/Integrations/LifterLms/MembershipEnrollTrigger.php:82
#: app/Services/Integrations/PMPro/PMProPMProMembershipTrigger.php:78
#: app/Services/Integrations/SureCart/SureCartPaymentRefundTrigger.php:78
#: app/Services/Integrations/SureCart/SureCartPaymentSuccessTrigger.php:79
#: app/Services/Integrations/LearnDash/GroupEnrollTrigger.php:77
#: app/Services/Integrations/LearnDash/CourseLeaveTrigger.php:80
#: app/Services/Integrations/LearnDash/TopicCompletedTrigger.php:77
#: app/Services/Integrations/LearnDash/LessonCompletedTrigger.php:77
#: app/Services/Integrations/LearnDash/CourseCompletedTrigger.php:76
#: app/Services/Integrations/LearnDash/CourseEnrollTrigger.php:79
#: app/Services/Integrations/Edd/EddPaymentSuccessTrigger.php:80
#: app/Services/Integrations/Edd/EddRecurringExpired.php:78
#: app/Services/Integrations/Edd/EddRecurringPaymentTrigger.php:79
#: app/Services/Integrations/Edd/EddPaymentRefundTrigger.php:80
#: app/Services/Integrations/TutorLms/LessonCompletedTrigger.php:77
#: app/Services/Integrations/TutorLms/CourseCompletedTrigger.php:77
#: app/Services/Integrations/TutorLms/CourseEnrollTrigger.php:78
#: app/Services/Integrations/AffiliateWP/AffiliateWPAffActiveTrigger.php:105
#: app/Services/Funnel/Triggers/UserLoginTrigger.php:70
msgid ""
"Please specify what will happen if the subscriber already exist in the "
"database"
msgstr ""
"Por favor, especifique qué ocurrirá si el abonado ya existe en la base de "
"datos"

#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:110
msgid ""
"Please specify which tags will be added/removed to the contact when purchase"
msgstr ""
"Por favor, especifique qué etiquetas se añadirán/quitarán al contacto cuando "
"lo compre"

#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:144
msgid ""
"Please specify which tags will be added/removed to the contact when refunded"
msgstr ""
"Por favor, especifique qué etiquetas se añadirán/quitarán al contacto cuando "
"se reembolse"

#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:181
msgid ""
"Please specify which tags will be added/removed to the contact when renewal "
"payment failed"
msgstr ""
"Por favor, especifique qué etiquetas se añadirán/eliminarán al contacto "
"cuando falle el pago de la renovación"

#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:218
msgid ""
"Please specify which tags will be added/removed to the contact when "
"subscription is cancelled"
msgstr ""
"Especifique qué etiquetas se añadirán/eliminarán al contacto cuando se "
"cancele la suscripción"

#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:255
msgid ""
"Please specify which tags will be added/removed to the contact when "
"subscription is expired"
msgstr ""
"Por favor, especifique qué etiquetas se añadirán/eliminarán al contacto "
"cuando la suscripción expire"

#: fluentcampaign-pro.php:31
msgid "Please update FluentCRM to latest version"
msgstr "Por favor, actualice FluentCRM a la última versión"

#: app/Services/Funnel/Actions/HTTPSendDataAction.php:65
msgid "POST Method"
msgstr "Método POST"

#: app/Hooks/Handlers/DataExporter.php:551
#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:175
#: app/Services/Funnel/Conditions/FunnelCondition.php:107
msgid "Postal Code"
msgstr "Código postal"

#: app/Modules/AbandonCart/Views/AbandonCartItems.php:112
#: app/Modules/AbandonCart/Views/AbandonCartItems.php:133
msgid "Price"
msgstr ""

#: app/Hooks/Handlers/DataExporter.php:567
msgid "Primary Company"
msgstr ""

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:36
msgid "Priority of this abandon cart automation trigger"
msgstr ""

#: app/Services/Integrations/WooCommerce/AddOrderNoteAction.php:38
msgid "Private Note"
msgstr "Nota privada"

#. Description of the plugin
msgid "Pro Email Automation and Integration Addon for FluentCRM"
msgstr ""
"Complemento de integración y automatización de correo electrónico para "
"FluentCRM"

#: app/Modules/AbandonCart/AbandonCartController.php:136
msgid "Processing Revenue"
msgstr ""

#: app/Services/Integrations/WooCommerce/WooSmartCodeParse.php:268
#: app/Services/Integrations/Edd/EddSmartCodeParse.php:308
msgid "Product"
msgstr "Producto"

#: app/Services/Integrations/WooCommerce/AutomationConditions.php:169
msgid "Products in Order"
msgstr "Productos en orden"

#: app/Services/Integrations/LifterLms/LifterInit.php:131
#: app/Services/Integrations/TutorLms/TutorLmsInit.php:108
msgid "Progress"
msgstr "Progreso"

#: app/Services/Funnel/Actions/UpdateContactPropertyAction.php:48
msgid "Property Value"
msgstr "Valor de la propiedad"

#: app/Services/Funnel/Actions/UserRegistrationAction.php:62
msgid "Provide Custom User Password"
msgstr "Proporcionar una contraseña de usuario personalizada"

#: app/Services/Integrations/WooCommerce/WooOrderCompletedTrigger.php:94
#: app/Services/Integrations/WooCommerce/WooOrderSuccessBenchmark.php:73
#: app/Services/Integrations/WooCommerce/WooOrderSuccessTrigger.php:96
#: app/Services/Integrations/Edd/EddPaymentSuccessTrigger.php:101
#: app/Services/Integrations/Edd/EddOrderSuccessBenchmark.php:72
#: app/Services/Integrations/Edd/EddPaymentRefundTrigger.php:101
msgid "Purchase Type"
msgstr "Tipo de compra"

#: app/Services/Integrations/WooCommerce/AutomationConditions.php:97
#: app/Services/Integrations/WooCommerce/DeepIntegration.php:255
#: app/Services/Integrations/Edd/AutomationConditions.php:62
#: app/Services/Integrations/Edd/DeepIntegration.php:318
msgid "Purchased Categories"
msgstr "Categorías compradas"

#: app/Services/Integrations/WooCommerce/AutomationConditions.php:177
msgid "Purchased From Categories"
msgstr "Comprado en categorías"

#: app/Services/Integrations/WooCommerce/AutomationConditions.php:35
#: app/Services/Integrations/WooCommerce/DeepIntegration.php:242
msgid "Purchased Product Variations"
msgstr ""

#: app/Services/Integrations/WooCommerce/AutomationConditions.php:26
#: app/Services/Integrations/WooCommerce/DeepIntegration.php:115
#: app/Services/Integrations/WooCommerce/DeepIntegration.php:233
#: app/Services/Integrations/Edd/AutomationConditions.php:26
#: app/Services/Integrations/Edd/DeepIntegration.php:166
#: app/Services/Integrations/Edd/DeepIntegration.php:309
msgid "Purchased Products"
msgstr "Productos comprados"

#: app/Services/Integrations/WooCommerce/AutomationConditions.php:107
#: app/Services/Integrations/WooCommerce/DeepIntegration.php:265
#: app/Services/Integrations/Edd/AutomationConditions.php:71
#: app/Services/Integrations/Edd/DeepIntegration.php:327
msgid "Purchased Tags"
msgstr "Etiquetas compradas"

#: app/Modules/AbandonCart/Views/AbandonCartItems.php:111
#: app/Modules/AbandonCart/Views/AbandonCartItems.php:132
msgid "Quantity"
msgstr ""

#: app/Services/Integrations/RCP/AutomationConditions.php:19
msgid "RCP"
msgstr "RCP"

#: app/Services/Funnel/Actions/AddEmailSequenceAction.php:49
msgid "Re-assign Sequence Emails?"
msgstr "¿Reasignar los correos electrónicos de la secuencia?"

#: app/Modules/AbandonCart/AbandonCartController.php:131
msgid "Recovered Revenue"
msgstr ""

#: app/Modules/AbandonCart/AbandonCartController.php:156
msgid "Recovery Rate"
msgstr ""

#: app/Services/Integrations/WooCommerce/AdvancedReport.php:61
#: app/Services/Integrations/Edd/AdvancedReport.php:53
msgid "Recurring (renew only)"
msgstr "Recurrente (sólo renovar)"

#: app/Http/Controllers/RecurringCampaignController.php:275
msgid "Recurring Email campaign has been deleted"
msgstr ""

#: app/Services/Integrations/Edd/EddRecurringExpired.php:24
#: app/Services/Integrations/Edd/EddRecurringExpired.php:40
msgid "Recurring Subscription Expired"
msgstr "Suscripción recurrente caducada"

#: app/Services/Funnel/Benchmarks/LinkClickBenchmark.php:64
msgid "Redirect To"
msgstr "Redirigir a"

#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:143
msgid "Refund Actions"
msgstr "Acciones de reembolso"

#: app/Services/Integrations/AffiliateWP/DeepIntegration.php:101
msgid "Registration Date"
msgstr "Fecha de registro"

#: app/Services/Funnel/Actions/HTTPSendDataAction.php:76
#: app/Services/Funnel/Actions/HTTPSendDataAction.php:77
msgid "Remote URL"
msgstr "URL remoto"

#: app/Services/Funnel/Actions/RemoveFromEmailSequenceAction.php:34
msgid "Remove Email Sequences"
msgstr "Eliminar las secuencias de correo electrónico"

#: app/Services/Integrations/LifterLms/RemoveFromCourseAction.php:22
#: app/Services/Integrations/LifterLms/RemoveFromCourseAction.php:34
#: app/Services/Integrations/TutorLms/RemoveFromCourseAction.php:22
#: app/Services/Integrations/TutorLms/RemoveFromCourseAction.php:34
msgid "Remove From a Course"
msgstr ""

#: app/Services/Integrations/LearnDash/RemoveFromGroupAction.php:34
msgid "Remove From a Group"
msgstr ""

#: app/Services/Integrations/LifterLms/RemoveFromMembershipAction.php:22
msgid "Remove From a LMS Membership"
msgstr ""

#: app/Services/Integrations/LifterLms/RemoveFromMembershipAction.php:34
msgid "Remove From a Membership"
msgstr ""

#: app/Services/Integrations/CRM/RemoveFromCompanyTrigger.php:46
msgid "Remove From Company"
msgstr ""

#: app/Services/Integrations/LearnDash/RemoveFromCourseAction.php:22
#: app/Services/Integrations/LearnDash/RemoveFromCourseAction.php:36
msgid "Remove From Course"
msgstr ""

#: app/Services/Integrations/LearnDash/RemoveFromGroupAction.php:22
msgid "Remove From Group"
msgstr ""

#: app/Services/Integrations/CRM/RemoveFromListTrigger.php:46
msgid "Remove From List"
msgstr "Eliminar de la lista"

#: app/Services/Integrations/CRM/RemoveFromTagTrigger.php:46
msgid "Remove From Tags"
msgstr "Eliminar de las etiquetas"

#: app/Services/Integrations/Edd/EddMetaBoxes.php:121
msgid "Remove selected Tags on refund"
msgstr "Eliminar las etiquetas seleccionadas en el reembolso"

#: app/Services/Integrations/LifterLms/LifterInit.php:191
#: app/Services/Integrations/LifterLms/LifterInit.php:249
#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:127
#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:160
#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:199
#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:236
#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:273
msgid "Remove Tags"
msgstr "Eliminar etiquetas"

#: app/Services/Integrations/BuddyPress/Group.php:55
#: app/Services/Integrations/BuddyPress/BBMemberType.php:77
msgid "Remove Tags on leave defined in \"Apply Tags\""
msgstr "Eliminar las etiquetas de las bajas definidas en \"Aplicar etiquetas\""

#: app/Services/Integrations/Edd/EddMetaBoxes.php:68
msgid "Remove Tags on refund defined in \"Apply Tags\""
msgstr ""
"Eliminar las etiquetas en el reembolso definido en \"Aplicar etiquetas\""

#: app/Services/Integrations/LifterLms/RemoveFromCourseAction.php:23
#: app/Services/Integrations/LifterLms/RemoveFromCourseAction.php:35
#: app/Services/Integrations/LearnDash/RemoveFromCourseAction.php:23
#: app/Services/Integrations/LearnDash/RemoveFromCourseAction.php:37
#: app/Services/Integrations/TutorLms/RemoveFromCourseAction.php:23
#: app/Services/Integrations/TutorLms/RemoveFromCourseAction.php:35
msgid "Remove the contact from a specific LMS Course"
msgstr ""

#: app/Services/Integrations/LearnDash/RemoveFromGroupAction.php:23
#: app/Services/Integrations/LearnDash/RemoveFromGroupAction.php:35
msgid "Remove the contact from a specific LMS Group"
msgstr ""

#: app/Services/Integrations/LifterLms/RemoveFromMembershipAction.php:35
msgid "Remove the contact from a specific LMS Membership"
msgstr ""

#: app/Services/Integrations/LifterLms/RemoveFromMembershipAction.php:23
msgid "Remove the contact from a specific LMS Membership Group"
msgstr ""

#: app/Services/Funnel/Actions/UserRoleRemoveAction.php:22
#: app/Services/Funnel/Actions/UserRoleRemoveAction.php:33
msgid "Remove the Selected Role of User"
msgstr "Eliminar la función seleccionada del usuario"

#: app/Services/Funnel/Actions/UserRoleRemoveAction.php:21
msgid "Remove WP User Role"
msgstr "Eliminar el rol de usuario de WP"

#: app/Services/Integrations/WooCommerce/WooSubscriptionRenewalFailedTrigger.php:25
msgid "Renewal Payment Failed"
msgstr ""

#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:180
msgid "Renewal Payment Failed Actions"
msgstr "Acciones fallidas de Pago de renovaciones"

#: app/Services/Integrations/WooCommerce/WooSubscriptionRenewalPaymentTrigger.php:25
#: app/Services/Integrations/Edd/EddRecurringPaymentTrigger.php:24
#: app/Services/Integrations/Edd/EddRecurringPaymentTrigger.php:40
msgid "Renewal Payment Received"
msgstr "Pago de renovación recibido"

#: app/Services/Integrations/WooCommerce/WooSubscriptionExpiredTrigger.php:25
#: app/Services/Integrations/WooCommerce/WooSubscriptionExpiredTrigger.php:71
msgid "Renewal Subscription Expired"
msgstr ""

#: app/Services/Integrations/WooCommerce/AdvancedReport.php:76
#: app/Services/Integrations/Edd/AdvancedReport.php:69
msgid "Renews"
msgstr "Renueva"

#: app/Services/Funnel/Actions/ChangeUserRoleAction.php:47
msgid "Replace Existing Role"
msgstr "Sustituir la función existente"

#: app/Services/Funnel/Actions/ChangeUserRoleAction.php:48
msgid "Replace user role "
msgstr "Reemplazar el rol de usuario"

#: app/Services/Funnel/Actions/HTTPSendDataAction.php:96
msgid "Request Body"
msgstr "Solicitar cuerpo"

#: app/Services/Funnel/Actions/HTTPSendDataAction.php:109
msgid "Request Body Data"
msgstr "Solicitar datos del cuerpo"

#: app/Services/Funnel/Actions/HTTPSendDataAction.php:82
msgid "Request Format"
msgstr "Formato de solicitud"

#: app/Services/Funnel/Actions/HTTPSendDataAction.php:125
msgid "Request Header"
msgstr "Solicitud de cabecera"

#: app/Services/Funnel/Actions/HTTPSendDataAction.php:138
msgid "Request Headers Data"
msgstr "Solicitar datos de cabecera"

#: app/Services/FunnelMultiConditionTrait.php:20
#: app/Services/Integrations/RCP/RCPMembershipTrigger.php:84
#: app/Services/Integrations/RCP/RCPMembershipCancelTrigger.php:84
#: app/Services/Integrations/RCP/RCPMembershipExpireTrigger.php:84
#: app/Services/Integrations/LifterLms/LessonCompletedTrigger.php:95
#: app/Services/Integrations/LifterLms/CourseCompletedTrigger.php:91
#: app/Services/Integrations/LifterLms/CourseEnrollTrigger.php:95
#: app/Services/Integrations/LifterLms/MembershipEnrollTrigger.php:97
#: app/Services/Integrations/WooCommerce/WooSubscriptionStartedTrigger.php:62
#: app/Services/Integrations/WooCommerce/WooOrderCompletedTrigger.php:102
#: app/Services/Integrations/WooCommerce/WooOrderStatusChangeTrigger.php:112
#: app/Services/Integrations/WooCommerce/WooSubscriptionRenewalFailedTrigger.php:62
#: app/Services/Integrations/WooCommerce/WooOrderRefundedTrigger.php:94
#: app/Services/Integrations/WooCommerce/WooOrderSuccessTrigger.php:104
#: app/Services/Integrations/WooCommerce/WooSubscriptionExpiredTrigger.php:62
#: app/Services/Integrations/WooCommerce/WooSubscriptionRenewalPaymentTrigger.php:62
#: app/Services/Integrations/PMPro/PMProPMProMembershipTrigger.php:91
#: app/Services/Integrations/PMPro/PMProPMProExpiryLevelTrigger.php:84
#: app/Services/Integrations/PMPro/PMProPMProCancelLevelTrigger.php:84
#: app/Services/Integrations/SureCart/SureCartPaymentRefundTrigger.php:92
#: app/Services/Integrations/SureCart/SureCartPaymentSuccessTrigger.php:93
#: app/Services/Integrations/LearnDash/GroupEnrollTrigger.php:92
#: app/Services/Integrations/LearnDash/CourseLeaveTrigger.php:95
#: app/Services/Integrations/LearnDash/TopicCompletedTrigger.php:106
#: app/Services/Integrations/LearnDash/LessonCompletedTrigger.php:99
#: app/Services/Integrations/LearnDash/CourseCompletedTrigger.php:91
#: app/Services/Integrations/LearnDash/CourseEnrollTrigger.php:94
#: app/Services/Integrations/Edd/EddPaymentSuccessTrigger.php:109
#: app/Services/Integrations/Edd/EddLicenseExpiredTrigger.php:84
#: app/Services/Integrations/Edd/EddRecurringExpired.php:92
#: app/Services/Integrations/Edd/EddRecurringPaymentTrigger.php:101
#: app/Services/Integrations/Edd/EddPaymentRefundTrigger.php:109
#: app/Services/Integrations/WishlistMember/WishlistMembershipTrigger.php:84
#: app/Services/Integrations/TutorLms/LessonCompletedTrigger.php:90
#: app/Services/Integrations/TutorLms/CourseCompletedTrigger.php:90
#: app/Services/Integrations/TutorLms/CourseEnrollTrigger.php:91
#: app/Services/Integrations/MemberPress/MembershipTrigger.php:84
#: app/Services/Integrations/MemberPress/SubscriptionExpiredTrigger.php:83
#: app/Services/Integrations/AffiliateWP/AffiliateWPAffActiveTrigger.php:85
#: app/Services/Funnel/Triggers/TrackingEventRecordedTrigger.php:99
#: app/Services/Funnel/Triggers/UserLoginTrigger.php:85
msgid ""
"Restart the Automation Multiple times for a contact for this event. (Only "
"enable if you want to restart automation for the same contact)"
msgstr ""
"Reiniciar la automatización varias veces para un contacto para este evento. "
"(Sólo habilitar si desea reiniciar la automatización para el mismo contacto)"

#: app/Services/Funnel/Actions/AddEmailSequenceAction.php:50
msgid ""
"Restart the sequence emails if the contact already in the email sequence"
msgstr ""
"Reiniciar la secuencia de correos electrónicos si el contacto ya está en la "
"secuencia de correos electrónicos"

#: app/Services/Integrations/RCP/RCPMembershipTrigger.php:23
#: app/Services/Integrations/RCP/RCPMembershipCancelTrigger.php:23
#: app/Services/Integrations/RCP/RCPMembershipExpireTrigger.php:23
msgid "Restrict Content Pro"
msgstr "Restringir contenido Pro"

#: app/Services/Integrations/CRM/ListAppliedTrigger.php:57
#: app/Services/Integrations/CRM/TagAppliedTrigger.php:57
#: app/Services/Integrations/CRM/CompanyAppliedTrigger.php:56
msgid "Run When"
msgstr "Corre cuando"

#: app/Services/Integrations/WooCommerce/WooInit.php:114
msgid "Sales (This Month)"
msgstr "Ventas (este mes)"

#: app/Services/Integrations/WooCommerce/WooInit.php:110
msgid "Sales (Today)"
msgstr "Ventas (hoy)"

#: app/Services/Funnel/Actions/SendCampaignEmailAction.php:35
msgid "Schedule Campaign Email"
msgstr "Programar el correo electrónico de la campaña"

#: app/Http/Controllers/DynamicSegmentController.php:306
msgid "Segment has been created"
msgstr "Se ha creado un segmento"

#: app/Http/Controllers/DynamicSegmentController.php:352
msgid "Segment has been updated"
msgstr "El segmento ha sido actualizado"

#: app/Http/Controllers/DynamicSegmentController.php:322
msgid "Segment successfully duplicated"
msgstr ""

#: app/Services/Integrations/LifterLms/LifterImporter.php:151
#: app/Services/Integrations/LifterLms/LifterImporter.php:153
#: app/Services/Integrations/LearnPress/LearnPressImporter.php:116
#: app/Services/Integrations/LearnPress/LearnPressImporter.php:118
#: app/Services/Integrations/LearnDash/LearnDashImporter.php:154
#: app/Services/Integrations/LearnDash/LearnDashImporter.php:156
#: app/Services/Integrations/TutorLms/TutorImporter.php:122
#: app/Services/Integrations/TutorLms/TutorImporter.php:124
#, php-format
msgid "Select %s Course"
msgstr "Seleccione el curso %s"

#: app/Services/Integrations/LifterLms/LifterImporter.php:166
#: app/Services/Integrations/LearnDash/LearnDashImporter.php:169
#: app/Services/Integrations/LearnDash/LearnDashImporter.php:171
#: app/Services/Integrations/BuddyPress/BbImporter.php:187
#: app/Services/Integrations/BuddyPress/BbImporter.php:189
#, php-format
msgid "Select %s Group"
msgstr "Seleccione el grupo %s"

#: app/Services/Integrations/BuddyPress/BbImporter.php:155
#, php-format
msgid "Select %s Import Type"
msgstr "Seleccione el tipo de importación %s"

#: app/Services/Integrations/BuddyPress/BbImporter.php:172
#: app/Services/Integrations/BuddyPress/BbImporter.php:174
#, php-format
msgid "Select %s Member Type"
msgstr "Seleccione el tipo de miembro %s"

#: app/Services/Integrations/RCP/RCPImporter.php:123
#: app/Services/Integrations/RCP/RCPImporter.php:125
#: app/Services/Integrations/LifterLms/LifterImporter.php:168
#: app/Services/Integrations/PMPro/PMProImporter.php:121
#: app/Services/Integrations/PMPro/PMProImporter.php:123
#: app/Services/Integrations/WishlistMember/WishlistMemberImporter.php:122
#: app/Services/Integrations/WishlistMember/WishlistMemberImporter.php:124
#: app/Services/Integrations/MemberPress/MemberPressImporter.php:113
#: app/Services/Integrations/MemberPress/MemberPressImporter.php:115
#, php-format
msgid "Select %s Membership"
msgstr "Seleccione la membresía %s"

#: app/Services/Integrations/WooCommerce/WooImporter.php:137
#: app/Services/Integrations/WooCommerce/WooImporter.php:139
#: app/Services/Integrations/Edd/EddImporter.php:131
#: app/Services/Integrations/Edd/EddImporter.php:133
#, php-format
msgid "Select %s Product"
msgstr "Seleccione el producto %s"

#: app/Services/Integrations/LifterLms/RemoveFromCourseAction.php:42
#: app/Services/Integrations/TutorLms/RemoveFromCourseAction.php:42
msgid "Select a course that you want to remove from"
msgstr ""

#: app/Services/Integrations/LifterLms/RemoveFromMembershipAction.php:42
msgid "Select a Membership Group that you want to remove from"
msgstr ""

#: app/Services/Funnel/Actions/AddActivityAction.php:49
msgid "Select Activity Type"
msgstr "Seleccione el tipo de actividad"

#: app/Services/Funnel/Actions/RemoveFromFunnelAction.php:42
msgid "Select Automations"
msgstr "Seleccione las automatizaciones"

#: app/Services/Funnel/Actions/RemoveFromFunnelAction.php:40
msgid "Select Automations that you want to cancel"
msgstr "Seleccione las automatizaciones que desea cancelar"

#: app/Services/Funnel/Actions/SendCampaignEmailAction.php:42
msgid "Select Campaign"
msgstr "Seleccione la campaña"

#: app/Services/Funnel/Actions/SendCampaignEmailAction.php:43
msgid "Select Campaign Email"
msgstr "Seleccione el correo electrónico de la campaña"

#: app/Services/Integrations/CRM/RemoveFromCompanyTrigger.php:53
#: app/Services/Integrations/CRM/CompanyAppliedTrigger.php:52
msgid "Select Companies"
msgstr ""

#: app/Services/Integrations/CRM/RemoveFromCompanyTrigger.php:54
#: app/Services/Integrations/CRM/CompanyAppliedTrigger.php:53
msgid "Select Company"
msgstr ""

#: app/Http/Controllers/DynamicSegmentController.php:131
msgid ""
"Select conditions which will define this segment. All Conditions will be "
"applied to filter"
msgstr ""
"Seleccione las condiciones que definirán este segmento. Todas las "
"condiciones se aplicarán al filtro"

#: app/Services/Integrations/LifterLms/RemoveFromCourseAction.php:43
#: app/Services/Integrations/LifterLms/AddToCourseAction.php:46
#: app/Services/Integrations/LearnDash/RemoveFromCourseAction.php:46
#: app/Services/Integrations/LearnDash/AddToCourseAction.php:46
#: app/Services/Integrations/TutorLms/RemoveFromCourseAction.php:43
#: app/Services/Integrations/TutorLms/AddToCourseAction.php:45
msgid "Select Course"
msgstr ""

#: app/Services/Integrations/LifterLms/AddToCourseAction.php:45
#: app/Services/Integrations/LearnDash/RemoveFromCourseAction.php:45
#: app/Services/Integrations/LearnDash/AddToCourseAction.php:45
#: app/Services/Integrations/TutorLms/AddToCourseAction.php:44
msgid "Select Course to Enroll"
msgstr ""

#: app/Services/Integrations/LearnDash/TopicCompletedTrigger.php:85
#: app/Services/Integrations/LearnDash/LessonCompletedTrigger.php:85
msgid "Select Course to find out Lesson"
msgstr "Seleccione el curso para conocer la lección"

#: app/Services/Funnel/Actions/AddEmailSequenceAction.php:44
msgid "Select Email Sequence"
msgstr "Seleccione la secuencia de correos electrónicos"

#: app/Services/Integrations/RCP/RCPImporter.php:126
#: app/Services/Integrations/LifterLms/LifterImporter.php:154
#: app/Services/Integrations/LifterLms/LifterImporter.php:169
#: app/Services/Integrations/WooCommerce/WooImporter.php:140
#: app/Services/Integrations/LearnPress/LearnPressImporter.php:119
#: app/Services/Integrations/PMPro/PMProImporter.php:124
#: app/Services/Integrations/LearnDash/LearnDashImporter.php:157
#: app/Services/Integrations/LearnDash/LearnDashImporter.php:172
#: app/Services/Integrations/BuddyPress/BbImporter.php:175
#: app/Services/Integrations/BuddyPress/BbImporter.php:190
#: app/Services/Integrations/Edd/EddImporter.php:134
#: app/Services/Integrations/WishlistMember/WishlistMemberImporter.php:125
#: app/Services/Integrations/TutorLms/TutorImporter.php:125
#: app/Services/Integrations/MemberPress/MemberPressImporter.php:116
msgid "Select FluentCRM Tag"
msgstr ""

#: app/Services/Integrations/RCP/RCPImporter.php:124
#: app/Services/Integrations/LifterLms/LifterImporter.php:152
#: app/Services/Integrations/LifterLms/LifterImporter.php:167
#: app/Services/Integrations/WooCommerce/WooImporter.php:138
#: app/Services/Integrations/LearnPress/LearnPressImporter.php:117
#: app/Services/Integrations/PMPro/PMProImporter.php:122
#: app/Services/Integrations/LearnDash/LearnDashImporter.php:155
#: app/Services/Integrations/LearnDash/LearnDashImporter.php:170
#: app/Services/Integrations/BuddyPress/BbImporter.php:173
#: app/Services/Integrations/Edd/EddImporter.php:132
#: app/Services/Integrations/WishlistMember/WishlistMemberImporter.php:123
#: app/Services/Integrations/TutorLms/TutorImporter.php:123
#: app/Services/Integrations/MemberPress/MemberPressImporter.php:114
msgid "Select FluentCRM Tag that will be applied"
msgstr "Seleccione la etiqueta FluentCRM que se aplicará"

#: app/Services/Integrations/LifterLms/CourseCompletedTrigger.php:83
#: app/Services/Integrations/LifterLms/CourseEnrollTrigger.php:88
#: app/Services/Integrations/LearnDash/CourseLeaveTrigger.php:89
#: app/Services/Integrations/LearnDash/CourseCompletedTrigger.php:85
#: app/Services/Integrations/LearnDash/CourseEnrollTrigger.php:88
#: app/Services/Integrations/TutorLms/CourseCompletedTrigger.php:83
#: app/Services/Integrations/TutorLms/CourseEnrollTrigger.php:84
msgid "Select for which Courses this automation will run"
msgstr "Seleccione para qué cursos se ejecutará esta automatización"

#: app/Services/Integrations/LearnDash/GroupEnrollTrigger.php:86
msgid "Select for which groups this automation will run"
msgstr "Seleccione para qué grupos se ejecutará esta automatización"

#: app/Services/Integrations/LifterLms/LessonCompletedTrigger.php:87
#: app/Services/Integrations/TutorLms/LessonCompletedTrigger.php:83
msgid "Select for which Lessons this automation will run"
msgstr "Seleccione para qué Lecciones se ejecutará esta automatización"

#: app/Services/Integrations/RCP/RCPMembershipTrigger.php:77
#: app/Services/Integrations/RCP/RCPMembershipCancelTrigger.php:77
#: app/Services/Integrations/RCP/RCPMembershipExpireTrigger.php:77
#: app/Services/Integrations/PMPro/PMProPMProMembershipTrigger.php:84
#: app/Services/Integrations/PMPro/PMProPMProExpiryLevelTrigger.php:77
#: app/Services/Integrations/PMPro/PMProPMProCancelLevelTrigger.php:77
#: app/Services/Integrations/WishlistMember/WishlistMembershipTrigger.php:77
#: app/Services/Integrations/MemberPress/MembershipTrigger.php:77
#: app/Services/Integrations/MemberPress/SubscriptionExpiredTrigger.php:76
msgid "Select for which Membership Levels this automation will run"
msgstr ""
"Seleccione para qué niveles de membresía se ejecutará esta automatización"

#: app/Services/Integrations/LifterLms/MembershipEnrollTrigger.php:91
msgid "Select for which Memberships this automation will run"
msgstr "Seleccione para qué membresías se ejecutará esta automatización"

#: app/Services/Integrations/WooCommerce/WooOrderCompletedTrigger.php:89
#: app/Services/Integrations/WooCommerce/WooOrderRefundedTrigger.php:88
#: app/Services/Integrations/WooCommerce/WooOrderSuccessTrigger.php:91
#: app/Services/Integrations/Edd/EddPaymentSuccessTrigger.php:96
#: app/Services/Integrations/Edd/EddRecurringPaymentTrigger.php:95
#: app/Services/Integrations/Edd/EddPaymentRefundTrigger.php:96
msgid "Select for which product category the automation will run"
msgstr ""
"Seleccione para qué categoría de producto se ejecutará la automatización"

#: app/Services/Integrations/WooCommerce/WooOrderSuccessBenchmark.php:68
#: app/Services/Integrations/Edd/EddOrderSuccessBenchmark.php:67
#| msgid "Select for which product category the benchmark will run"
msgid "Select for which product category the goal will run"
msgstr "Seleccione para qué categoría de producto se ejecutará el objetivo"

#: app/Services/Integrations/Edd/EddLicenseExpiredTrigger.php:78
msgid "Select for which product this automation will run"
msgstr "Seleccione para qué producto se ejecutará esta automatización"

#: app/Services/Integrations/WooCommerce/WooOrderCompletedTrigger.php:81
#: app/Services/Integrations/WooCommerce/WooOrderRefundedTrigger.php:80
#: app/Services/Integrations/WooCommerce/WooOrderSuccessTrigger.php:83
#: app/Services/Integrations/SureCart/SureCartPaymentSuccessBenchmark.php:55
#: app/Services/Integrations/SureCart/SureCartPaymentRefundTrigger.php:86
#: app/Services/Integrations/SureCart/SureCartPaymentSuccessTrigger.php:87
#: app/Services/Integrations/Edd/EddPaymentSuccessTrigger.php:88
#: app/Services/Integrations/Edd/EddRecurringExpired.php:86
#: app/Services/Integrations/Edd/EddRecurringPaymentTrigger.php:87
#: app/Services/Integrations/Edd/EddPaymentRefundTrigger.php:88
msgid "Select for which products this automation will run"
msgstr "Seleccione para qué productos se ejecutará esta automatización"

#: app/Services/Integrations/WooCommerce/WooOrderSuccessBenchmark.php:60
#: app/Services/Integrations/Edd/EddOrderSuccessBenchmark.php:59
#: app/Services/Integrations/Edd/EddSubscriptionActiveBenchmark.php:55
#: app/Services/Funnel/Benchmarks/LinkClickBenchmark.php:59
#| msgid "Select for which products this benchmark will run"
msgid "Select for which products this goal will run"
msgstr "Seleccione para qué productos se ejecutará este objetivo"

#: app/Services/Integrations/WooCommerce/WooSubscriptionStartedTrigger.php:56
#: app/Services/Integrations/WooCommerce/WooSubscriptionRenewalFailedTrigger.php:56
#: app/Services/Integrations/WooCommerce/WooSubscriptionExpiredTrigger.php:56
#: app/Services/Integrations/WooCommerce/WooSubscriptionRenewalPaymentTrigger.php:56
msgid "Select for which subscription products this automation will run"
msgstr ""

#: app/Services/Integrations/LearnDash/TopicCompletedTrigger.php:98
msgid "Select for which Topics this automation will run"
msgstr "Seleccione para qué Temas se ejecutará esta automatización"

#: app/Services/Integrations/LearnDash/AddToGroupAction.php:46
#: app/Services/Integrations/LearnDash/RemoveFromGroupAction.php:44
msgid "Select LearnDash Group"
msgstr ""

#: app/Services/Integrations/LearnDash/AddToGroupAction.php:45
msgid "Select LearnDash Group to Enroll"
msgstr ""

#: app/Services/Integrations/LearnDash/RemoveFromGroupAction.php:43
msgid "Select LearnDash Group to un-enroll contact"
msgstr ""

#: app/Services/Integrations/LearnDash/TopicCompletedTrigger.php:91
msgid "Select Lesson to find out the available topics"
msgstr "Seleccione Lección para conocer los temas disponibles"

#: app/Services/Integrations/LearnDash/LessonCompletedTrigger.php:92
msgid "Select Lesson to find out Topic"
msgstr "Seleccione la lección para conocer el tema"

#: app/Services/Integrations/LifterLms/AddToMembershipAction.php:45
msgid "Select LifterLMS Membership Group"
msgstr ""

#: app/Services/Integrations/LifterLms/AddToMembershipAction.php:44
msgid "Select LifterLMS Membership Group to Enroll"
msgstr ""

#: app/Services/Integrations/CRM/ListAppliedTrigger.php:53
#: app/Services/Integrations/CRM/RemoveFromListTrigger.php:54
msgid "Select List"
msgstr "Seleccione la lista"

#: app/Services/Integrations/CRM/ListAppliedTrigger.php:52
#: app/Services/Integrations/CRM/RemoveFromListTrigger.php:53
#: app/Services/Integrations/ElementorFormIntegration/FormWidget.php:62
msgid "Select Lists"
msgstr "Seleccione las listas"

#: app/Services/Integrations/LifterLms/RemoveFromMembershipAction.php:43
msgid "Select Membership"
msgstr ""

#: app/Services/Integrations/WooCommerce/AddOrderNoteAction.php:59
msgid "Select Note Type for the reference Order."
msgstr "Seleccione el tipo de nota para la orden de referencia."

#: app/Services/Funnel/Actions/UserRoleRemoveAction.php:34
msgid "Select Role that you want to remove from targeted Contact"
msgstr "Seleccione la función que desea eliminar del contacto objetivo"

#: app/Services/Funnel/Triggers/UserLoginTrigger.php:78
msgid "Select Roles"
msgstr ""

#: app/Services/Funnel/Actions/AddEmailSequenceAction.php:45
msgid "Select Sequence Email"
msgstr "Seleccione el correo electrónico de la secuencia"

#: app/Services/Funnel/Benchmarks/EmailSequenceCompletedBenchmark.php:45
#: app/Services/Funnel/Actions/RemoveFromEmailSequenceAction.php:41
msgid "Select Sequences"
msgstr "Seleccionar secuencias"

#: app/Services/Integrations/RCP/RCPMembershipTrigger.php:48
#: app/Services/Integrations/RCP/RCPMembershipCancelTrigger.php:48
#: app/Services/Integrations/RCP/RCPMembershipExpireTrigger.php:48
#: app/Services/Integrations/LifterLms/LessonCompletedTrigger.php:52
#: app/Services/Integrations/LifterLms/CourseCompletedTrigger.php:48
#: app/Services/Integrations/LifterLms/CourseEnrollTrigger.php:52
#: app/Services/Integrations/LifterLms/MembershipEnrollTrigger.php:52
#: app/Services/Integrations/WooCommerce/WooSubscriptionStartedTrigger.php:79
#: app/Services/Integrations/WooCommerce/WooOrderCompletedTrigger.php:48
#: app/Services/Integrations/WooCommerce/WooOrderStatusChangeTrigger.php:48
#: app/Services/Integrations/WooCommerce/WooSubscriptionRenewalFailedTrigger.php:79
#: app/Services/Integrations/WooCommerce/WooOrderRefundedTrigger.php:48
#: app/Services/Integrations/WooCommerce/WooOrderSuccessTrigger.php:50
#: app/Services/Integrations/WooCommerce/WooSubscriptionExpiredTrigger.php:79
#: app/Services/Integrations/WooCommerce/WooSubscriptionRenewalPaymentTrigger.php:79
#: app/Services/Integrations/PMPro/PMProPMProMembershipTrigger.php:48
#: app/Services/Integrations/PMPro/PMProPMProExpiryLevelTrigger.php:48
#: app/Services/Integrations/PMPro/PMProPMProCancelLevelTrigger.php:48
#: app/Services/Integrations/SureCart/SureCartPaymentRefundTrigger.php:48
#: app/Services/Integrations/SureCart/SureCartPaymentSuccessTrigger.php:49
#: app/Services/Integrations/LearnDash/GroupEnrollTrigger.php:47
#: app/Services/Integrations/LearnDash/CourseLeaveTrigger.php:50
#: app/Services/Integrations/LearnDash/TopicCompletedTrigger.php:46
#: app/Services/Integrations/LearnDash/LessonCompletedTrigger.php:47
#: app/Services/Integrations/LearnDash/CourseCompletedTrigger.php:47
#: app/Services/Integrations/LearnDash/CourseEnrollTrigger.php:49
#: app/Services/Integrations/Edd/EddPaymentSuccessTrigger.php:48
#: app/Services/Integrations/Edd/EddLicenseExpiredTrigger.php:47
#: app/Services/Integrations/Edd/EddRecurringExpired.php:48
#: app/Services/Integrations/Edd/EddRecurringPaymentTrigger.php:48
#: app/Services/Integrations/Edd/EddPaymentRefundTrigger.php:48
#: app/Services/Integrations/WishlistMember/WishlistMembershipTrigger.php:48
#: app/Services/Integrations/TutorLms/LessonCompletedTrigger.php:48
#: app/Services/Integrations/TutorLms/CourseCompletedTrigger.php:48
#: app/Services/Integrations/TutorLms/CourseEnrollTrigger.php:48
#: app/Services/Integrations/MemberPress/MembershipTrigger.php:48
#: app/Services/Integrations/MemberPress/SubscriptionExpiredTrigger.php:47
#: app/Services/Integrations/AffiliateWP/AffiliateWPAffActiveTrigger.php:71
#: app/Services/Funnel/Triggers/UserLoginTrigger.php:42
msgid "Select Status"
msgstr "Seleccione el estado"

#: app/Services/Integrations/CRM/TagAppliedTrigger.php:53
#: app/Services/Integrations/CRM/RemoveFromTagTrigger.php:54
msgid "Select Tag"
msgstr "Seleccione la etiqueta"

#: app/Services/Integrations/LifterLms/LifterInit.php:166
#: app/Services/Integrations/LifterLms/LifterInit.php:179
#: app/Services/Integrations/LifterLms/LifterInit.php:237
#: app/Services/Integrations/LifterLms/LifterInit.php:371
#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:114
#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:128
#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:148
#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:161
#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:185
#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:200
#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:222
#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:237
#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:259
#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:274
#: app/Services/Integrations/LearnPress/LearnPressInit.php:136
#: app/Services/Integrations/LearnPress/LearnPressInit.php:156
#: app/Services/Integrations/LearnPress/LearnPressInit.php:196
#: app/Services/Integrations/LearnDash/LdInit.php:164
#: app/Services/Integrations/CRM/TagAppliedTrigger.php:52
#: app/Services/Integrations/CRM/RemoveFromTagTrigger.php:53
#: app/Services/Integrations/BuddyPress/Group.php:40
#: app/Services/Integrations/BuddyPress/BBMemberType.php:62
#: app/Services/Integrations/Edd/EddMetaBoxes.php:53
#: app/Services/Integrations/Edd/EddMetaBoxes.php:105
#: app/Services/Integrations/TutorLms/TutorLmsInit.php:136
#: app/Services/Integrations/TutorLms/TutorLmsInit.php:150
msgid "Select Tags"
msgstr "Seleccione las etiquetas"

#: app/Services/Integrations/ElementorFormIntegration/FormWidget.php:79
msgid "Select tags"
msgstr "Seleccione las etiquetas"

#: app/Services/Integrations/WooCommerce/WooOrderCompletedTrigger.php:95
#: app/Services/Integrations/WooCommerce/WooOrderSuccessBenchmark.php:74
#: app/Services/Integrations/WooCommerce/WooOrderSuccessTrigger.php:97
#: app/Services/Integrations/Edd/EddPaymentSuccessTrigger.php:102
#: app/Services/Integrations/Edd/EddOrderSuccessBenchmark.php:73
#: app/Services/Integrations/Edd/EddPaymentRefundTrigger.php:102
msgid "Select the purchase type"
msgstr "Seleccione el tipo de compra"

#: app/Services/Funnel/Actions/RemoveFromFunnelAction.php:37
msgid "Select which automations will be cancelled from the contact"
msgstr "Seleccione las automatizaciones que se cancelarán desde el contacto"

#: app/Services/Funnel/Actions/SendCampaignEmailAction.php:36
msgid "Select which campaign email will be scheduled to this contact"
msgstr ""
"Seleccione el correo electrónico de la campaña que se programará para este "
"contacto"

#: app/Services/Funnel/Triggers/UserLoginTrigger.php:77
msgid "Select which roles registration will run this automation Funnel"
msgstr ""

#: app/Services/Funnel/Actions/AddEmailSequenceAction.php:38
msgid "Select which sequence will be assigned to this contact"
msgstr "Seleccione la secuencia que se asignará a este contacto"

#: app/Services/Funnel/Actions/RemoveFromEmailSequenceAction.php:35
msgid "Select which sequences will be removed from this contact"
msgstr "Seleccione las secuencias que se eliminarán de este contacto"

#: app/Services/Integrations/BuddyPress/Group.php:46
#: app/Services/Integrations/BuddyPress/BBMemberType.php:68
msgid "selected"
msgstr "seleccionado"

#: app/Http/Controllers/RecurringCampaignController.php:229
msgid "Selected Campaign has been successfully duplicated"
msgstr ""

#: app/Modules/AbandonCart/AbandonCartController.php:105
msgid "Selected carts has been deleted successfully"
msgstr ""

#: app/Services/Integrations/LearnDash/LdInit.php:207
msgid ""
"selected contact tags (defined in previous field) will be removed when user "
"leave this group"
msgstr ""
"las etiquetas de contacto seleccionadas (definidas en el campo anterior) se "
"eliminarán cuando el usuario abandone este grupo"

#: app/Services/Integrations/LearnDash/LdInit.php:204
msgid "selected contact tags will be removed when user leave this group"
msgstr ""
"las etiquetas de contacto seleccionadas se eliminarán cuando el usuario "
"abandone este grupo"

#: app/Services/Funnel/Actions/ChangeUserRoleAction.php:43
msgid ""
"Selected Role will be applied if there has a user with contact's email "
"address"
msgstr ""
"La función seleccionada se aplicará si hay un usuario con la dirección de "
"correo electrónico del contacto"

#: app/Http/Controllers/DynamicSegmentController.php:370
msgid "Selected segment has been deleted"
msgstr "El segmento seleccionado ha sido eliminado"

#: app/Http/Controllers/SequenceController.php:146
msgid "Selected sequence has been successfully duplicated"
msgstr "La secuencia seleccionada ha sido duplicada con éxito"

#: app/Http/Controllers/SequenceController.php:304
msgid "Selected Sequences has been deleted permanently"
msgstr ""

#: app/Http/Controllers/SmartLinksController.php:104
msgid "Selected Smart Link has been deleted"
msgstr "El Smart Link seleccionado ha sido eliminado"

#: app/Http/Controllers/SequenceController.php:264
msgid "Selected subscribers has been successfully removed from this sequence"
msgstr ""
"Los abonados seleccionados han sido eliminados con éxito de esta secuencia"

#: app/Services/Integrations/BuddyPress/Group.php:50
msgid "Selected tags will be added to the member on joining"
msgstr "Las etiquetas seleccionadas se añadirán al afiliado al incorporarse"

#: app/Services/Integrations/BuddyPress/BBMemberType.php:72
msgid "Selected tags will be added to the member on joining this member type"
msgstr ""
"Las etiquetas seleccionadas se añadirán al miembro al unirse a este tipo de "
"miembro"

#: app/Services/Integrations/LearnDash/LdInit.php:165
msgid "Selected tags will be applied to the contact on course completion"
msgstr ""
"Las etiquetas seleccionadas se aplicarán al contacto al finalizar el curso"

#: app/Services/Integrations/LifterLms/LifterInit.php:181
#: app/Services/Integrations/TutorLms/TutorLmsInit.php:152
msgid "Selected tags will be applied to the contact on course completion."
msgstr ""
"Las etiquetas seleccionadas se aplicarán al contacto al finalizar el curso."

#: app/Services/Integrations/LearnDash/LdInit.php:153
msgid "Selected tags will be applied to the contact on course enrollment"
msgstr ""
"Las etiquetas seleccionadas se aplicarán al contacto al inscribirse en el "
"curso"

#: app/Services/Integrations/LifterLms/LifterInit.php:168
#: app/Services/Integrations/TutorLms/TutorLmsInit.php:138
msgid "Selected tags will be applied to the contact on course enrollment."
msgstr ""
"Las etiquetas seleccionadas se aplicarán al contacto al inscribirse en el "
"curso."

#: app/Services/Integrations/LearnDash/LdInit.php:193
msgid "Selected tags will be applied to the contact on group enrollment"
msgstr ""
"Las etiquetas seleccionadas se aplicarán al contacto al inscribirse en el "
"grupo"

#: app/Services/Integrations/LifterLms/LifterInit.php:373
msgid "Selected tags will be applied to the contact on lesson completed."
msgstr ""
"Las etiquetas seleccionadas se aplicarán al contacto al finalizar la lección."

#: app/Services/Integrations/LifterLms/LifterInit.php:239
msgid "Selected tags will be applied to the contact on membership enrollment."
msgstr ""
"Las etiquetas seleccionadas se aplicarán al contacto en el momento de la "
"membresía."

#: app/Services/Funnel/Actions/SendCampaignEmailAction.php:24
msgid "Send an Email from your existing campaign"
msgstr "Envíe un correo electrónico desde su campaña actual"

#: app/Services/Funnel/Actions/HTTPSendDataAction.php:90
msgid "Send as Form Method"
msgstr "Enviar como método de formulario"

#: app/Services/Funnel/Actions/HTTPSendDataAction.php:86
msgid "Send as JSON format"
msgstr "Enviar en formato JSON"

#: app/Services/Funnel/Actions/AddEmailSequenceAction.php:25
msgid "Send Automated Emails based on your Sequence settings"
msgstr ""
"Envíe correos electrónicos automatizados basados en la configuración de su "
"secuencia"

#: app/Services/Funnel/Actions/SendCampaignEmailAction.php:23
msgid "Send Campaign Email"
msgstr "Envíe el correo electrónico de la campaña"

#: app/Services/Funnel/Actions/HTTPSendDataAction.php:155
msgid ""
"Send Data as Background Process. (You may enable this if you have lots of "
"tasks)"
msgstr ""
"Enviar datos como proceso de fondo. (Puede activar esta opción si tiene "
"muchas tareas)"

#: app/Services/Funnel/Actions/HTTPSendDataAction.php:56
msgid "Send Data to External Server"
msgstr "Enviar datos a un servidor externo"

#: app/Services/Funnel/Actions/HTTPSendDataAction.php:28
#: app/Services/Funnel/Actions/HTTPSendDataAction.php:57
msgid "Send Data to external server via GET or POST Method"
msgstr "Enviar datos a un servidor externo mediante el método GET o POST"

#: app/Services/Integrations/LifterLms/AddToMembershipAction.php:53
#: app/Services/Integrations/LifterLms/AddToCourseAction.php:54
#: app/Services/Integrations/LearnDash/AddToGroupAction.php:54
#: app/Services/Integrations/LearnDash/AddToCourseAction.php:54
#: app/Services/Integrations/TutorLms/AddToCourseAction.php:53
msgid "Send default WordPress Welcome Email for new WordPress users"
msgstr ""
"Enviar el correo electrónico de bienvenida de WordPress por defecto para los "
"nuevos usuarios de WordPress"

#: app/Services/Integrations/ElementorFormIntegration/FormWidget.php:102
msgid ""
"Send Double Optin Email for new or pending contacts. If you don't enable "
"this then contact will be added as subscribed state."
msgstr ""
"Envíe un correo electrónico de doble opción para los contactos nuevos o "
"pendientes. Si no lo habilita, el contacto se añadirá como estado suscrito."

#: app/Services/Funnel/Actions/UserRegistrationAction.php:89
msgid "Send WordPress user notification email"
msgstr "Enviar correo electrónico de notificación al usuario de WordPress"

#: app/Http/Controllers/SequenceMailController.php:83
msgid "Sequence email has been created"
msgstr "Se ha creado el correo electrónico de la secuencia"

#: app/Http/Controllers/SequenceMailController.php:116
msgid "Sequence email has been duplicated"
msgstr "El correo electrónico de la secuencia ha sido duplicado"

#: app/Http/Controllers/SequenceMailController.php:165
msgid "Sequence email has been updated"
msgstr "Se ha actualizado el correo electrónico de la secuencia"

#: app/Http/Controllers/SequenceController.php:49
msgid "Sequence has been created"
msgstr "Se ha creado la secuencia"

#: app/Http/Controllers/SequenceController.php:85
msgid "Sequence has been updated"
msgstr "Se ha actualizado la secuencia"

#: app/Services/Funnel/Actions/AddEmailSequenceAction.php:24
#: app/Services/Funnel/Actions/AddEmailSequenceAction.php:37
msgid "Set Sequence Emails"
msgstr "Establecer la secuencia de correos electrónicos"

#: app/Services/Integrations/Edd/AdvancedReport.php:94
msgid "Settings -> Integrations Settings -> Edd"
msgstr "Ajustes -> Ajustes de integración -> Edd"

#: app/Services/Integrations/LearnDash/AdvancedReport.php:63
msgid "Settings -> Integrations Settings -> LearnDash"
msgstr "Ajustes -> Ajustes de integración -> LearnDash"

#: app/Services/Integrations/LifterLms/AdvancedReport.php:63
msgid "Settings -> Integrations Settings -> LifterLMS"
msgstr "Ajustes -> Ajustes de integración -> LifterLMS"

#: app/Services/Integrations/TutorLms/AdvancedReport.php:60
msgid "Settings -> Integrations Settings -> TutorLms"
msgstr ""

#: app/Services/Integrations/WooCommerce/AdvancedReport.php:92
msgid "Settings -> Integrations Settings -> WooCommerce"
msgstr "Configuración -> Configuración de integraciones -> WooCommerce"

#: app/Modules/AbandonCart/SettingsController.php:70
msgid "Settings has been saved successfully"
msgstr ""

#: app/Http/Controllers/RecurringCampaignController.php:390
msgid "Settings has been successfully updated"
msgstr ""

#: app/Http/Controllers/RecurringCampaignController.php:165
msgid "Settings has been updated"
msgstr ""

#: app/Services/Integrations/WooCommerce/DeepIntegration.php:356
#: app/Services/Integrations/LearnDash/DeepIntegration.php:213
#: app/Services/Integrations/Edd/DeepIntegration.php:416
#: app/Services/Integrations/TutorLms/DeepIntegration.php:125
msgid "Settings have been saved"
msgstr "Se han guardado los ajustes"

#: app/Services/Funnel/Actions/UpdateContactPropertyAction.php:46
msgid "Setup contact properties that you want to update"
msgstr "Configurar las propiedades de los contactos que desea actualizar"

#: app/Services/Integrations/WooCommerce/AutomationConditions.php:195
msgid "Shipping Method"
msgstr "Método de envío"

#: app/Http/Controllers/DynamicSegmentController.php:244
msgid "Should Match Both Open & Click Condition?"
msgstr "¿Debe coincidir la condición de apertura y la de clic?"

#: app/Services/Integrations/WooCommerce/AdvancedReport.php:73
#: app/Services/Integrations/Edd/AdvancedReport.php:66
msgid "Signups"
msgstr "Inscripciones"

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:81
msgid "Skip this automation if the contact is already in active state."
msgstr ""

#: app/Http/Controllers/SmartLinksController.php:72
msgid "SmartLink has be created"
msgstr "Se ha creado SmartLink"

#: app/Http/Controllers/SmartLinksController.php:95
msgid "SmartLink has be updated"
msgstr "SmartLink ha sido actualizado"

#: app/Http/Controllers/SmartLinksController.php:52
msgid "SmartLinks module has been successfully activated"
msgstr "El módulo SmartLinks se ha activado con éxito"

#: app/Http/Controllers/CampaignsProController.php:20
msgid "Sorry no failed campaign emails found"
msgstr ""
"Lo sentimos, no se han encontrado correos electrónicos de campañas fallidas"

#: app/Http/Controllers/CampaignsProController.php:47
#: app/Http/Controllers/CampaignsProController.php:57
msgid "Sorry! No emails found"
msgstr "Lo siento No se han encontrado correos electrónicos"

#: app/Http/Controllers/DynamicSegmentController.php:361
msgid "Sorry! No segment found"
msgstr "Lo siento No se ha encontrado ningún segmento"

#: app/Modules/AbandonCart/Woo/WooCartTrackingInit.php:342
msgid "Sorry, we could not retrieve your cart"
msgstr ""

#: app/Http/Controllers/DynamicSegmentController.php:181
#: app/Hooks/Handlers/DataExporter.php:559
msgid "Source"
msgstr "Fuente"

#: app/Services/Integrations/WooCommerce/AutomationConditions.php:127
msgid "Specific Product Purchase Times"
msgstr "Tiempos de compra de productos específicos"

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:66
#: app/Services/Funnel/Conditions/FunnelCondition.php:47
msgid "Specify Matching Conditions"
msgstr "Especifique las condiciones de concordancia"

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:67
#: app/Services/Funnel/Conditions/FunnelCondition.php:48
msgid ""
"Specify which contact properties need to matched. Based on the conditions it "
"will run yes blocks or no blocks"
msgstr ""
"Especifique qué propiedades de los contactos deben coincidir. En función de "
"las condiciones, se ejecutarán bloques de sí o de no"

#: app/Services/Funnel/Conditions/FunnelABTesting.php:26
#: app/Services/Funnel/Conditions/FunnelABTesting.php:41
msgid "Split (A/B Testing)"
msgstr "Dividir (pruebas A/B)"

#: app/Services/Integrations/LearnPress/LearnPressInit.php:96
#: app/Services/Integrations/LearnDash/LdInit.php:120
#: app/Services/Integrations/TutorLms/TutorLmsInit.php:107
msgid "Started At"
msgstr "Comenzó en"

#: app/Http/Controllers/DynamicSegmentController.php:159
#: app/Hooks/Handlers/DataExporter.php:553
#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:170
#: app/Services/Funnel/Conditions/FunnelCondition.php:102
msgid "State"
msgstr "Estado"

#: app/Hooks/Handlers/DataExporter.php:557
#: app/Services/Integrations/LifterLms/LifterInit.php:129
#: app/Services/Integrations/LearnPress/LearnPressInit.php:94
#: app/Services/Integrations/LearnDash/LdInit.php:121
#: app/Services/Integrations/AffiliateWP/DeepIntegration.php:79
msgid "Status"
msgstr "Estatus"

#: app/Services/Integrations/LifterLms/CourseCompletedTrigger.php:40
msgid "Student completes a Course in LifterLMS"
msgstr "El estudiante completa un curso en LifterLMS"

#: app/Services/Integrations/TutorLms/CourseCompletedTrigger.php:40
msgid "Student completes a Course in TutorLMS"
msgstr "El estudiante completa un curso en TutorLMS"

#: app/Services/Integrations/LifterLms/LessonCompletedTrigger.php:44
msgid "Student completes a Lesson in LifterLMS"
msgstr "El estudiante completa una lección en LifterLMS"

#: app/Services/Integrations/TutorLms/LessonCompletedTrigger.php:40
msgid "Student completes a Lesson in TutorLMS"
msgstr ""

#: app/Services/Integrations/LifterLms/AdvancedReport.php:51
#: app/Services/Integrations/LearnDash/AdvancedReport.php:51
#: app/Services/Integrations/TutorLms/AdvancedReport.php:48
msgid "Students Growth"
msgstr "Crecimiento de los estudiantes"

#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:178
msgid "Subscription"
msgstr "Suscripción"

#: app/Services/Integrations/WooCommerce/WooSubscriptionStartedTrigger.php:71
msgid "Subscription Activated"
msgstr ""

#: app/Services/Integrations/WooCommerce/WooSubscriptionStartedTrigger.php:25
msgid "Subscription activated"
msgstr ""

#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:217
msgid "Subscription Cancelled Actions"
msgstr "Acciones de cancelación de la suscripción"

#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:254
msgid "Subscription Expire Actions"
msgstr "Acciones de caducidad de la suscripción"

#: app/Services/Integrations/MemberPress/SubscriptionExpiredTrigger.php:24
msgid "Subscription Expired"
msgstr ""

#: app/Services/Integrations/WooCommerce/AdvancedReport.php:55
#: app/Services/Integrations/Edd/AdvancedReport.php:47
msgid "Subscription Revenue (All)"
msgstr "Ingresos por suscripción (todos)"

#: app/Http/Controllers/DynamicSegmentController.php:174
#: app/Services/Integrations/RCP/RCPMembershipTrigger.php:47
#: app/Services/Integrations/RCP/RCPMembershipCancelTrigger.php:47
#: app/Services/Integrations/RCP/RCPMembershipExpireTrigger.php:47
#: app/Services/Integrations/LifterLms/LessonCompletedTrigger.php:51
#: app/Services/Integrations/LifterLms/CourseCompletedTrigger.php:47
#: app/Services/Integrations/LifterLms/CourseEnrollTrigger.php:51
#: app/Services/Integrations/LifterLms/MembershipEnrollTrigger.php:51
#: app/Services/Integrations/WooCommerce/WooOrderCompletedTrigger.php:47
#: app/Services/Integrations/WooCommerce/WooOrderStatusChangeTrigger.php:47
#: app/Services/Integrations/WooCommerce/WooOrderRefundedTrigger.php:47
#: app/Services/Integrations/WooCommerce/WooOrderSuccessTrigger.php:49
#: app/Services/Integrations/PMPro/PMProPMProMembershipTrigger.php:47
#: app/Services/Integrations/PMPro/PMProPMProExpiryLevelTrigger.php:47
#: app/Services/Integrations/PMPro/PMProPMProCancelLevelTrigger.php:47
#: app/Services/Integrations/SureCart/SureCartPaymentRefundTrigger.php:47
#: app/Services/Integrations/SureCart/SureCartPaymentSuccessTrigger.php:48
#: app/Services/Integrations/LearnDash/GroupEnrollTrigger.php:46
#: app/Services/Integrations/LearnDash/CourseLeaveTrigger.php:49
#: app/Services/Integrations/LearnDash/TopicCompletedTrigger.php:45
#: app/Services/Integrations/LearnDash/LessonCompletedTrigger.php:46
#: app/Services/Integrations/LearnDash/CourseCompletedTrigger.php:46
#: app/Services/Integrations/LearnDash/CourseEnrollTrigger.php:48
#: app/Services/Integrations/Edd/EddPaymentSuccessTrigger.php:47
#: app/Services/Integrations/Edd/EddLicenseExpiredTrigger.php:46
#: app/Services/Integrations/Edd/EddRecurringPaymentTrigger.php:47
#: app/Services/Integrations/Edd/EddPaymentRefundTrigger.php:47
#: app/Services/Integrations/WishlistMember/WishlistMembershipTrigger.php:47
#: app/Services/Integrations/TutorLms/LessonCompletedTrigger.php:47
#: app/Services/Integrations/TutorLms/CourseCompletedTrigger.php:47
#: app/Services/Integrations/TutorLms/CourseEnrollTrigger.php:47
#: app/Services/Integrations/MemberPress/MembershipTrigger.php:47
#: app/Services/Integrations/MemberPress/SubscriptionExpiredTrigger.php:46
#: app/Services/Integrations/AffiliateWP/AffiliateWPAffActiveTrigger.php:70
#: app/Services/Funnel/Triggers/UserLoginTrigger.php:41
msgid "Subscription Status"
msgstr "Estado de la suscripción"

#: app/Services/Integrations/Edd/AdvancedReport.php:61
msgid "Subscriptions"
msgstr "Suscripciones"

#: app/Services/Integrations/WooCommerce/AdvancedReport.php:58
#: app/Services/Integrations/Edd/AdvancedReport.php:50
msgid "Subscriptions (New)"
msgstr "Suscripciones (Nuevo)"

#: app/Services/Integrations/WooCommerce/WooProductAdmin.php:109
msgid "Successful Purchase Actions"
msgstr "Acciones de compra con éxito"

#: app/Services/Integrations/SureCart/SureCartInit.php:137
msgid "SureCart"
msgstr ""

#: app/Services/Integrations/SureCart/SureCartPaymentSuccessTrigger.php:25
msgid "SureCart - New Order Success"
msgstr ""

#: app/Services/Integrations/SureCart/SureCartPaymentRefundTrigger.php:24
msgid "SureCart - Order Revoked"
msgstr ""

#: app/Services/Integrations/SureCart/SureCartPaymentRefundTrigger.php:40
msgid "SureCart Order Revoke"
msgstr ""

#: app/Services/Integrations/SureCart/SureCartInit.php:136
msgid "SureCart Purchase History"
msgstr ""

#: app/Services/Integrations/WooCommerce/WooImporter.php:116
#: app/Services/Integrations/Edd/EddImporter.php:110
#, php-format
msgid "Sync %s Customers Now"
msgstr "Sincronizar %s clientes ahora"

#: app/Services/Integrations/AffiliateWP/DeepIntegration.php:214
msgid "Sync AffiliateWP Affiliates Now"
msgstr "Sincronizar afiliados de AffiliateWP ahora"

#: app/Services/Integrations/Edd/DeepIntegration.php:369
msgid "Sync EDD Customers"
msgstr "Sincronización de clientes de EDD"

#: app/Services/Integrations/Edd/DeepIntegration.php:41
msgid ""
"Sync EDD Customers to FluentCRM to segment them by their purchases, lifetime "
"values and other purchase data."
msgstr ""
"Sincronice los clientes de EDD con FluentCRM para segmentarlos por sus "
"compras, valores de vida y otros datos de compra."

#: app/Services/Integrations/LearnDash/DeepIntegration.php:166
msgid "Sync LearnDash Students"
msgstr "Sincronizar estudiantes de LearnDash"

#: app/Services/Integrations/LearnDash/DeepIntegration.php:41
msgid ""
"Sync LearnDash Students to FluentCRM to segment them by their enrollment, "
"membership groups data."
msgstr ""
"Sincronice los estudiantes de LearnDash con FluentCRM para segmentarlos por "
"sus datos de inscripción y grupos de miembros."

#: app/Services/Integrations/LifterLms/DeepIntegration.php:161
msgid "Sync LifterLMS Students"
msgstr "Sincronizar estudiantes de LifterLMS"

#: app/Services/Integrations/LifterLms/DeepIntegration.php:47
msgid ""
"Sync LifterLMS Students to FluentCRM to segment them by their courses data."
msgstr ""
"Sincronizar los alumnos de LifterLMS con FluentCRM para segmentarlos por los "
"datos de sus cursos."

#: app/Services/Integrations/LearnDash/DeepIntegration.php:144
msgid "Sync Required From Settings"
msgstr "Sincronización requerida desde la configuración"

#: app/Services/Integrations/TutorLms/DeepIntegration.php:78
msgid "Sync TutorLMS Students"
msgstr ""

#: app/Services/Integrations/TutorLms/DeepIntegration.php:41
msgid ""
"Sync TutorLMS Students to FluentCRM to segment them by their courses data."
msgstr ""

#: app/Services/Integrations/WooCommerce/DeepIntegration.php:309
msgid "Sync WooCommerce Customers"
msgstr "Sincronizar clientes de WooCommerce"

#: app/Services/Integrations/WooCommerce/DeepIntegration.php:42
msgid ""
"Sync WooCommerce Customers to FluentCRM to segment them by their purchases, "
"lifetime values and other purchase data."
msgstr ""
"Sincroniza los clientes de WooCommerce con FluentCRM para segmentarlos por "
"sus compras, valores de vida y otros datos de compra."

#: app/Services/Integrations/CRM/TagAppliedTrigger.php:28
#: app/Services/Integrations/CRM/TagAppliedTrigger.php:45
msgid "Tag Applied"
msgstr "Etiqueta aplicada"

#: app/Services/Integrations/CRM/RemoveFromTagTrigger.php:29
msgid "Tag Removed"
msgstr "Etiqueta eliminada"

#: app/Http/Controllers/DynamicSegmentController.php:199
#: app/Hooks/Handlers/DataExporter.php:565
#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:237
#: app/Services/Funnel/Conditions/FunnelCondition.php:169
msgid "Tags"
msgstr "Etiquetas"

#: app/Services/Integrations/LearnDash/TopicCompletedTrigger.php:84
#: app/Services/Integrations/LearnDash/LessonCompletedTrigger.php:84
msgid "Target Course"
msgstr "Curso objetivo"

#: app/Services/Integrations/LifterLms/CourseCompletedTrigger.php:82
#: app/Services/Integrations/LifterLms/CourseEnrollTrigger.php:87
#: app/Services/Integrations/LearnDash/CourseLeaveTrigger.php:88
#: app/Services/Integrations/LearnDash/CourseCompletedTrigger.php:84
#: app/Services/Integrations/LearnDash/CourseEnrollTrigger.php:87
#: app/Services/Integrations/TutorLms/CourseCompletedTrigger.php:82
#: app/Services/Integrations/TutorLms/CourseEnrollTrigger.php:83
msgid "Target Courses"
msgstr "Cursos de destino"

#: app/Services/Funnel/Triggers/TrackingEventRecordedTrigger.php:44
msgid "Target Event Key"
msgstr ""

#: app/Services/Integrations/LearnDash/GroupEnrollTrigger.php:85
msgid "Target Groups"
msgstr "Grupos objetivo"

#: app/Services/Integrations/LearnDash/TopicCompletedTrigger.php:90
#: app/Services/Integrations/LearnDash/LessonCompletedTrigger.php:91
msgid "Target Lesson"
msgstr "Lección objetivo"

#: app/Services/Integrations/LifterLms/LessonCompletedTrigger.php:86
#: app/Services/Integrations/TutorLms/LessonCompletedTrigger.php:82
msgid "Target Lessons"
msgstr "Lecciones de destino"

#: app/Services/Integrations/RCP/RCPMembershipTrigger.php:76
#: app/Services/Integrations/RCP/RCPMembershipCancelTrigger.php:76
#: app/Services/Integrations/RCP/RCPMembershipExpireTrigger.php:76
#: app/Services/Integrations/PMPro/PMProPMProMembershipTrigger.php:83
#: app/Services/Integrations/PMPro/PMProPMProExpiryLevelTrigger.php:76
#: app/Services/Integrations/PMPro/PMProPMProCancelLevelTrigger.php:76
#: app/Services/Integrations/WishlistMember/WishlistMembershipTrigger.php:76
#: app/Services/Integrations/MemberPress/MembershipTrigger.php:76
#: app/Services/Integrations/MemberPress/SubscriptionExpiredTrigger.php:75
msgid "Target Membership Levels"
msgstr "Niveles de membresía objetivo"

#: app/Services/Integrations/LifterLms/MembershipEnrollTrigger.php:90
msgid "Target Memberships"
msgstr "Membresías de destino"

#: app/Services/Integrations/Edd/EddLicenseExpiredTrigger.php:77
msgid "Target Product"
msgstr "Producto objetivo"

#: app/Services/Integrations/WooCommerce/WooSubscriptionStartedTrigger.php:55
#: app/Services/Integrations/WooCommerce/WooOrderCompletedTrigger.php:80
#: app/Services/Integrations/WooCommerce/WooOrderStatusChangeTrigger.php:94
#: app/Services/Integrations/WooCommerce/WooOrderSuccessBenchmark.php:59
#: app/Services/Integrations/WooCommerce/WooSubscriptionRenewalFailedTrigger.php:55
#: app/Services/Integrations/WooCommerce/WooOrderRefundedTrigger.php:79
#: app/Services/Integrations/WooCommerce/WooOrderSuccessTrigger.php:82
#: app/Services/Integrations/WooCommerce/WooSubscriptionExpiredTrigger.php:55
#: app/Services/Integrations/WooCommerce/WooSubscriptionRenewalPaymentTrigger.php:55
#: app/Services/Integrations/SureCart/SureCartPaymentSuccessBenchmark.php:54
#: app/Services/Integrations/SureCart/SureCartPaymentRefundTrigger.php:85
#: app/Services/Integrations/SureCart/SureCartPaymentSuccessTrigger.php:86
#: app/Services/Integrations/Edd/EddPaymentSuccessTrigger.php:87
#: app/Services/Integrations/Edd/EddRecurringExpired.php:85
#: app/Services/Integrations/Edd/EddOrderSuccessBenchmark.php:58
#: app/Services/Integrations/Edd/EddRecurringPaymentTrigger.php:86
#: app/Services/Integrations/Edd/EddSubscriptionActiveBenchmark.php:54
#: app/Services/Integrations/Edd/EddPaymentRefundTrigger.php:87
msgid "Target Products"
msgstr "Productos objetivo"

#: app/Services/Integrations/LearnDash/TopicCompletedTrigger.php:97
msgid "Target Topics"
msgstr "Temas de interés"

#: app/Services/Funnel/Triggers/UserLoginTrigger.php:76
msgid "Targeted User Roles"
msgstr ""

#: app/Hooks/Handlers/DataExporter.php:695
msgid "Templates has been successfully imported"
msgstr ""

#: app/Services/PluginManager/LicenseManager.php:111
#, php-format
msgid "The %s license needs to be activated. %sActivate Now%s"
msgstr "La licencia %s necesita ser activada. %sActivar ahora%s"

#: app/Hooks/Handlers/DataExporter.php:341
msgid ""
"The Block Action defined in the JSON file is not available on your site."
msgstr ""
"La Acción de Bloqueo definida en el archivo JSON no está disponible en su "
"sitio."

#: app/Services/PluginManager/LicenseManager.php:347
msgid ""
"The given license key is not valid. Please verify that your license is "
"correct. You may login to wpmanageninja.com account and get your valid "
"license key for your purchase."
msgstr ""
"La clave de licencia dada no es válida. Por favor, verifique que su licencia "
"es correcta. Puede iniciar sesión en la cuenta de wpmanageninja.com y "
"obtener su clave de licencia válida para su compra."

#: app/Hooks/Handlers/DataExporter.php:308
msgid "The provided JSON file is not valid"
msgstr "El archivo JSON proporcionado no es válido"

#: app/Hooks/Handlers/DataExporter.php:633
msgid "The provided JSON file is not valid."
msgstr ""

#: app/Hooks/Handlers/DataExporter.php:385
msgid "The provided JSON file is not valid. No valid email sequence found"
msgstr ""
"El archivo JSON proporcionado no es válido. No se ha encontrado una "
"secuencia de correo electrónico válida"

#: app/Hooks/Handlers/DataExporter.php:517
msgid ""
"The provided JSON file is not valid. object type is required in the JSON File"
msgstr ""

#: app/Hooks/Handlers/DataExporter.php:377
msgid ""
"The provided JSON file is not valid. sequence key is required in the JSON "
"File"
msgstr ""
"El archivo JSON proporcionado no es válido. Se requiere una clave de "
"secuencia en el archivo JSON"

#: app/Hooks/Handlers/DataExporter.php:319
msgid "The trigger defined in the JSON file is not available on your site."
msgstr ""
"El activador definido en el archivo JSON no está disponible en su sitio."

#: app/Services/Funnel/Triggers/ContactBirthDayTrigger.php:38
msgid ""
"This automation will be initiated for contact on his/her birthday. Will only "
"initiated only for subscribed status contacts"
msgstr ""
"Esta automatización se iniciará para el contacto en su cumpleaños. Sólo se "
"iniciará para los contactos con estado suscrito"

#: app/Services/Funnel/Actions/EndFunnel.php:34
msgid "This automation will be marked as completed in this point for a contact"
msgstr ""
"Esta automatización se marcará como completada en este punto para un contacto"

#: app/Services/Integrations/WooCommerce/WooOrderRefundedTrigger.php:41
msgid "This Automation will start once an order get refunded"
msgstr "Esta automatización se iniciará cuando se reembolse un pedido"

#: app/Services/Integrations/WooCommerce/WooSubscriptionRenewalFailedTrigger.php:72
msgid "This Automation will start when a payment fails for a subscription"
msgstr ""

#: app/Services/Integrations/WooCommerce/WooSubscriptionRenewalPaymentTrigger.php:72
msgid ""
"This Automation will start when a recurring payment received  for a "
"subscription"
msgstr ""

#: app/Services/Integrations/WooCommerce/WooSubscriptionExpiredTrigger.php:72
msgid "This Automation will start when a subscription expires"
msgstr ""

#: app/Services/Integrations/WooCommerce/WooSubscriptionStartedTrigger.php:72
msgid ""
"This Automation will start when a woo subscription starts or status changed "
"to active"
msgstr ""

#: app/Services/Integrations/LearnDash/LessonCompletedTrigger.php:25
msgid "This funnel runs a student completes a lesson"
msgstr ""

#: app/Services/Integrations/LifterLms/CourseEnrollTrigger.php:30
msgid "This funnel runs when a contact is enrolled in a course"
msgstr ""

#: app/Services/Integrations/RCP/RCPMembershipTrigger.php:26
#: app/Services/Integrations/PMPro/PMProPMProMembershipTrigger.php:26
#: app/Services/Integrations/WishlistMember/WishlistMembershipTrigger.php:26
#: app/Services/Integrations/MemberPress/MembershipTrigger.php:26
msgid "This funnel runs when a member is added to a membership level"
msgstr ""

#: app/Services/Integrations/RCP/RCPMembershipExpireTrigger.php:26
#: app/Services/Integrations/PMPro/PMProPMProExpiryLevelTrigger.php:26
msgid "This funnel runs when a membership expires"
msgstr ""

#: app/Services/Integrations/RCP/RCPMembershipCancelTrigger.php:26
msgid "This funnel runs when a membership is cancelled"
msgstr ""

#: app/Services/Integrations/PMPro/PMProPMProCancelLevelTrigger.php:26
msgid "This funnel runs when a membership level is cancelled"
msgstr ""

#: app/Services/Integrations/PMPro/PMProPMProCancelLevelTrigger.php:41
msgid "This funnel runs when a membership level is cancelled for a user"
msgstr ""

#: app/Services/Integrations/LifterLms/CourseCompletedTrigger.php:26
#: app/Services/Integrations/TutorLms/CourseCompletedTrigger.php:26
#, fuzzy
#| msgid "This Funnel will start a student completes a Course"
msgid "This funnel runs when a student completes a Course"
msgstr "Este embudo comenzará un estudiante completa un curso"

#: app/Services/Integrations/LearnDash/CourseCompletedTrigger.php:25
#, fuzzy
#| msgid "This Funnel will start when a student completes a course"
msgid "This funnel runs when a student completes a course"
msgstr "Este embudo se iniciará cuando un estudiante complete un curso"

#: app/Services/Integrations/TutorLms/LessonCompletedTrigger.php:26
msgid "This funnel runs when a student completes a Lesson"
msgstr ""

#: app/Services/Integrations/LifterLms/LessonCompletedTrigger.php:30
#, fuzzy
#| msgid "This Funnel will start a student completes a lesson"
msgid "This funnel runs when a student completes a lesson"
msgstr "Este embudo comenzará un estudiante completa una lección"

#: app/Services/Integrations/LearnDash/TopicCompletedTrigger.php:24
msgid "This funnel runs when a student completes a lesson topic"
msgstr ""

#: app/Services/Integrations/LifterLms/MembershipEnrollTrigger.php:30
#, fuzzy
#| msgid ""
#| "This Funnel will start when a student has been enrolled in a membership "
#| "level"
msgid "This funnel runs when a student has been enrolled in a membership level"
msgstr ""
"Este embudo se iniciará cuando un estudiante se haya inscrito en un nivel de "
"membresía"

#: app/Services/Integrations/LearnDash/CourseEnrollTrigger.php:25
#: app/Services/Integrations/TutorLms/CourseEnrollTrigger.php:26
#, fuzzy
#| msgid "This funnel will start when a student is enrolled in a course"
msgid "This funnel runs when a student is enrolled in a course"
msgstr "Este embudo se iniciará cuando un estudiante se inscriba en un curso"

#: app/Services/Integrations/LearnDash/CourseLeaveTrigger.php:26
msgid "This funnel runs when a student leaves a course"
msgstr ""

#: app/Services/Integrations/MemberPress/SubscriptionExpiredTrigger.php:25
msgid "This funnel runs when a subscription expires"
msgstr ""

#: app/Services/Integrations/LearnDash/GroupEnrollTrigger.php:25
#, fuzzy
#| msgid "This funnel will start when a user is enrolled in a group"
msgid "This funnel runs when a user is enrolled in a group"
msgstr "Este embudo se iniciará cuando un usuario se inscriba en un grupo"

#: app/Services/Funnel/Triggers/TrackingEventRecordedTrigger.php:32
#: app/Services/Funnel/Triggers/TrackingEventRecordedTrigger.php:41
msgid ""
"This Funnel will be initiated a tracking event has been recorded for a "
"contact"
msgstr ""

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:24
#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:33
msgid ""
"This Funnel will be initiated when a cart has been abandoned in WooCommerce"
msgstr ""

#: app/Services/Integrations/AffiliateWP/AffiliateWPAffActiveTrigger.php:49
msgid ""
"This funnel will be initiated when a new affiliate gets approved/registered "
"directly"
msgstr ""

#: app/Services/Funnel/Triggers/UserLoginTrigger.php:26
#: app/Services/Funnel/Triggers/UserLoginTrigger.php:35
msgid "This Funnel will be initiated when a user login to your site"
msgstr "Este embudo se iniciará cuando un usuario se conecte a su sitio"

#: app/Services/Integrations/AffiliateWP/AffiliateWPAffActiveTrigger.php:64
msgid ""
"This Funnel will be initiated when affiliate will be approved or register as "
"direct approved"
msgstr ""
"Este embudo se iniciará cuando el afiliado sea aprobado o se registre como "
"aprobado directamente"

#: app/Services/Integrations/Edd/EddLicenseExpiredTrigger.php:40
msgid "This Funnel will start a license status get marked as expired"
msgstr ""
"Este embudo iniciará un estado de licencia que se marcará como caducado"

#: app/Services/Integrations/WooCommerce/WooOrderSuccessTrigger.php:27
#, fuzzy
#| msgid "This Funnel will start once new order has been placed as processing"
msgid "This funnel will start once a new order will be added as processing"
msgstr ""
"Este embudo se iniciará una vez que se haya realizado un nuevo pedido como "
"procesamiento"

#: app/Services/Integrations/Edd/EddRecurringExpired.php:41
msgid ""
"This Funnel will start once a Recurring Subscription status changed to "
"expired"
msgstr ""
"Este embudo se iniciará una vez que el estado de la suscripción recurrente "
"haya cambiado a expirado"

#: app/Services/Integrations/Edd/EddRecurringPaymentTrigger.php:41
msgid ""
"This Funnel will start once a Renewal Payment received for an active "
"subscription"
msgstr ""
"Este embudo se iniciará cuando se reciba un pago de renovación de una "
"suscripción activa"

#: app/Services/Integrations/Edd/EddPaymentRefundTrigger.php:41
msgid "This Funnel will start once an order get marked as refunded"
msgstr "Este embudo se iniciará cuando un pedido se marque como reembolsado"

#: app/Services/Integrations/Edd/EddPaymentSuccessTrigger.php:25
msgid "This funnel will start once new order payment is successful"
msgstr ""

#: app/Services/Integrations/SureCart/SureCartPaymentSuccessTrigger.php:42
#: app/Services/Integrations/Edd/EddPaymentSuccessTrigger.php:41
msgid ""
"This Funnel will start once new order will be added as successful payment"
msgstr ""
"Este embudo se iniciará una vez que la nueva orden se agregue como pago "
"exitoso"

#: app/Services/Integrations/WooCommerce/WooOrderCompletedTrigger.php:41
msgid "This Funnel will start once new order will be marked as completed"
msgstr ""
"Este embudo se iniciará una vez que el nuevo pedido se marque como completado"

#: app/Services/Integrations/WooCommerce/WooOrderSuccessTrigger.php:43
msgid ""
"This Funnel will start once new order will be marked as successful payment"
msgstr ""
"Este embudo se iniciará una vez que el nuevo pedido sea marcado como pago "
"exitoso"

#: app/Services/Integrations/SureCart/SureCartPaymentRefundTrigger.php:41
msgid "This Funnel will start once order will be refunded"
msgstr ""

#: app/Services/Integrations/Edd/EddPaymentRefundTrigger.php:26
msgid "This Funnel will start once payment refunded for an order"
msgstr ""
"Este embudo se iniciará una vez que se haya reembolsado el pago de un pedido"

#: app/Services/Integrations/Edd/EddLicenseExpiredTrigger.php:25
msgid "This funnel will start when a license gets expired"
msgstr ""

#: app/Services/Integrations/WishlistMember/WishlistMembershipTrigger.php:41
msgid "This funnel will start when a member is added to a level"
msgstr "Este embudo se iniciará cuando se añada un miembro a un nivel"

#: app/Services/Integrations/RCP/RCPMembershipTrigger.php:41
msgid ""
"This funnel will start when a member is added to a level for the first time"
msgstr ""
"Este embudo se iniciará cuando un miembro se añada a un nivel por primera vez"

#: app/Services/Integrations/RCP/RCPMembershipCancelTrigger.php:41
msgid "This funnel will start when a membership has been cancelled"
msgstr "Este embudo se iniciará cuando se cancele una membresía"

#: app/Services/Integrations/RCP/RCPMembershipExpireTrigger.php:41
msgid "This funnel will start when a membership has been expired"
msgstr ""

#: app/Services/Integrations/PMPro/PMProPMProExpiryLevelTrigger.php:41
msgid "This funnel will start when a membership has been expired for a user"
msgstr ""
"Este embudo se iniciará cuando la membresía de un usuario haya expirado"

#: app/Services/Integrations/MemberPress/MembershipTrigger.php:41
msgid ""
"This funnel will start when a membership level get activated for a member"
msgstr ""
"Este embudo se iniciará cuando se active un nivel de membresía para un "
"miembro"

#: app/Services/Integrations/WooCommerce/WooOrderStatusChangeTrigger.php:41
msgid ""
"This Funnel will start when a Order status will change from one state to "
"another"
msgstr ""
"Este embudo se iniciará cuando el estado de un pedido cambie de un estado a "
"otro"

#: app/Services/Integrations/WooCommerce/WooSubscriptionRenewalPaymentTrigger.php:27
msgid ""
"This funnel will start when a recurring payment received for a subscription"
msgstr ""

#: app/Services/Integrations/Edd/EddRecurringExpired.php:25
msgid "This funnel will start when a recurring subscription gets expired"
msgstr ""

#: app/Services/Integrations/Edd/EddRecurringPaymentTrigger.php:25
msgid ""
"This funnel will start when a renewal payment is received for an active "
"subscription"
msgstr ""

#: app/Services/Integrations/LifterLms/CourseCompletedTrigger.php:41
#: app/Services/Integrations/LearnDash/CourseCompletedTrigger.php:40
#: app/Services/Integrations/TutorLms/CourseCompletedTrigger.php:41
msgid "This Funnel will start when a student completes a Course"
msgstr "Este embudo se iniciará cuando un estudiante complete un curso"

#: app/Services/Integrations/TutorLms/LessonCompletedTrigger.php:41
msgid "This Funnel will start when a student completes a Lesson"
msgstr ""

#: app/Services/Integrations/LifterLms/LessonCompletedTrigger.php:45
#: app/Services/Integrations/LearnDash/LessonCompletedTrigger.php:40
msgid "This Funnel will start when a student completes a lesson"
msgstr "Este embudo se iniciará cuando un estudiante complete una lección"

#: app/Services/Integrations/LifterLms/MembershipEnrollTrigger.php:45
msgid "This funnel will start when a student is added in a Membership"
msgstr "Este embudo se iniciará cuando se añada un estudiante en una Membresía"

#: app/Services/Integrations/LifterLms/CourseEnrollTrigger.php:45
#: app/Services/Integrations/LearnDash/CourseEnrollTrigger.php:42
#: app/Services/Integrations/TutorLms/CourseEnrollTrigger.php:41
msgid "This Funnel will start when a student is enrolled in a course"
msgstr "Este embudo se iniciará cuando un estudiante se inscriba en un curso"

#: app/Services/Integrations/LearnDash/GroupEnrollTrigger.php:40
msgid "This Funnel will start when a student is enrolled in a group"
msgstr "Este embudo se iniciará cuando un estudiante se inscriba en un grupo"

#: app/Services/Integrations/LearnDash/CourseLeaveTrigger.php:43
msgid "This Funnel will start when a student is leave from a course"
msgstr "Este embudo se iniciará cuando un estudiante se dé de baja de un curso"

#: app/Services/Integrations/WooCommerce/WooSubscriptionExpiredTrigger.php:27
msgid "This funnel will start when a subscription expires"
msgstr ""

#: app/Services/Integrations/MemberPress/SubscriptionExpiredTrigger.php:40
msgid "This funnel will start when a subscription has been expired"
msgstr "Este embudo se iniciará cuando una suscripción haya expirado"

#: app/Services/Integrations/WooCommerce/WooSubscriptionRenewalFailedTrigger.php:27
msgid "This funnel will start when a subscription payment fails"
msgstr ""

#: app/Services/Integrations/LearnDash/TopicCompletedTrigger.php:39
msgid "This funnel will start when a user is completes a lesson topic"
msgstr ""
"Este embudo se iniciará cuando un usuario complete un tema de la lección"

#: app/Services/Integrations/WooCommerce/WooSubscriptionStartedTrigger.php:27
msgid ""
"This funnel will start when a WooCommerce subscription begins or its status "
"changes to active."
msgstr ""

#: app/Services/Integrations/WooCommerce/WooOrderCompletedTrigger.php:26
msgid "This funnel will start when an order is completed"
msgstr ""

#: app/Services/Integrations/WooCommerce/WooOrderRefundedTrigger.php:26
msgid "This funnel will start when an order is refunded"
msgstr ""

#: app/Services/Integrations/WooCommerce/WooOrderStatusChangeTrigger.php:25
msgid "This funnel will start when an order status changes"
msgstr ""

#: app/Services/Integrations/PMPro/PMProPMProMembershipTrigger.php:41
msgid "This funnel will start when an user is enrolled in Membership Levels"
msgstr ""
"Este embudo se iniciará cuando un usuario se inscriba en los niveles de "
"Membresía"

#: app/Services/Integrations/SureCart/SureCartPaymentSuccessTrigger.php:26
msgid "This funnel will start when new order payment is successful"
msgstr ""

#: app/Services/Integrations/SureCart/SureCartPaymentRefundTrigger.php:25
msgid "This funnel will start when order will be revoked"
msgstr ""

#: app/Services/DynamicSegments/CustomSegment.php:51
msgid ""
"This is a custom segment and contacts are filter based your provided filters "
"on real time data."
msgstr ""
"Este es un segmento personalizado y los contactos son filtrados en base a "
"sus filtros proporcionados en los datos en tiempo real."

#: app/Modules/AbandonCart/Woo/WooCartTrackingInit.php:605
#, php-format
msgid "This order has been recovered from an FluentCRM abandoned cart. %s"
msgstr ""

#: app/Services/DynamicSegments/AffiliateWPSegment.php:22
msgid ""
"This segment contains all your Subscribed contacts which are also your "
"active Affiliates"
msgstr ""
"Este segmento contiene todos sus contactos suscritos que también son sus "
"afiliados activos"

#: app/Services/DynamicSegments/EddActiveCustomerSegment.php:22
msgid ""
"This segment contains all your Subscribed contacts which are also your EDD "
"Customers with atleast one purchase"
msgstr ""
"Este segmento contiene todos sus contactos suscritos que también son sus "
"clientes de EDD con al menos una compra"

#: app/Services/DynamicSegments/PMProMembersSegment.php:23
msgid ""
"This segment contains all your Subscribed contacts which are also your Paid "
"Membership Members"
msgstr ""
"Este segmento contiene todos sus contactos suscritos que también son sus "
"miembros de una membresía de pago"

#: app/Services/DynamicSegments/WooCustomerSegment.php:22
msgid ""
"This segment contains all your Subscribed contacts which are also your "
"WooCommerce Customers"
msgstr ""
"Este segmento contiene todos tus contactos suscritos que también son tus "
"clientes de WooCommerce"

#: app/Services/DynamicSegments/WpUserSegment.php:22
msgid ""
"This segment contains all your Subscribed contacts which are also your "
"WordPress users"
msgstr ""
"Este segmento contiene todos sus contactos suscritos que también son sus "
"usuarios de WordPress"

#: app/Services/Funnel/Benchmarks/EmailSequenceCompletedBenchmark.php:24
#: app/Services/Funnel/Benchmarks/EmailSequenceCompletedBenchmark.php:39
msgid "This will run once a selected email sequence is completed for a contact"
msgstr ""
"Se ejecutará una vez que se haya completado una secuencia de correo "
"electrónico seleccionada para un contacto"

#: app/Services/Funnel/Benchmarks/LinkClickBenchmark.php:36
#: app/Services/Funnel/Benchmarks/LinkClickBenchmark.php:52
msgid "This will run once a subscriber click on this provided link"
msgstr ""
"Esto se ejecutará una vez que el suscriptor haga clic en este enlace "
"proporcionado"

#: app/Services/Integrations/Edd/EddSubscriptionActiveBenchmark.php:25
#: app/Services/Integrations/Edd/EddSubscriptionActiveBenchmark.php:48
msgid "This will run once a subscription gets active"
msgstr "Esto se ejecutará una vez que se active una suscripción"

#: app/Services/Integrations/WooCommerce/WooOrderSuccessBenchmark.php:26
#: app/Services/Integrations/SureCart/SureCartPaymentSuccessBenchmark.php:25
msgid "This will run once new order has been placed as processing"
msgstr ""
"Se ejecutará una vez que se haya realizado un nuevo pedido como procesamiento"

#: app/Services/Integrations/Edd/EddOrderSuccessBenchmark.php:25
#: app/Services/Integrations/Edd/EddOrderSuccessBenchmark.php:52
#| msgid "This will run once new order will be placed as processing in EDD"
msgid "This will run once new order will be placed as completed in EDD"
msgstr ""
"Esto se ejecutará una vez que la nueva orden sea colocada como completada en "
"EDD"

#: app/Services/Integrations/WooCommerce/WooOrderSuccessBenchmark.php:53
#: app/Services/Integrations/SureCart/SureCartPaymentSuccessBenchmark.php:48
msgid "This will run once new order will be placed as processing"
msgstr ""
"Esto se ejecutará una vez que la nueva orden se colocará como procesamiento"

#: app/Services/Integrations/CRM/ContactCreatedTrigger.php:29
#: app/Services/Integrations/CRM/ContactCreatedTrigger.php:43
msgid "This will run when a new contact will be added"
msgstr ""

#: app/Services/Integrations/CRM/RemoveFromCompanyTrigger.php:47
msgid ""
"This will run when any of the selected companies have been removed from a "
"contact"
msgstr ""

#: app/Services/Integrations/CRM/RemoveFromListTrigger.php:47
msgid ""
"This will run when any of the selected lists have been removed from a contact"
msgstr ""
"Se ejecutará cuando cualquiera de las listas seleccionadas haya sido "
"eliminada de un contacto"

#: app/Services/Integrations/CRM/RemoveFromTagTrigger.php:47
msgid ""
"This will run when any of the selected tags have been removed from a contact"
msgstr ""
"Esto se ejecutará cuando cualquiera de las etiquetas seleccionadas haya sido "
"eliminada de un contacto"

#: app/Services/Integrations/CRM/CompanyAppliedTrigger.php:29
#: app/Services/Integrations/CRM/CompanyAppliedTrigger.php:46
msgid "This will run when selected companies have been applied to a contact"
msgstr ""

#: app/Services/Integrations/CRM/RemoveFromCompanyTrigger.php:30
msgid "This will run when selected companies have been removed from a contact"
msgstr ""

#: app/Services/Integrations/CRM/ListAppliedTrigger.php:29
#: app/Services/Integrations/CRM/ListAppliedTrigger.php:46
msgid "This will run when selected lists have been applied to a contact"
msgstr ""
"Se ejecutará cuando las listas seleccionadas se hayan aplicado a un contacto"

#: app/Services/Integrations/CRM/RemoveFromListTrigger.php:30
msgid "This will run when selected lists have been removed from a contact"
msgstr ""
"Se ejecutará cuando las listas seleccionadas hayan sido eliminadas de un "
"contacto"

#: app/Services/Integrations/CRM/TagAppliedTrigger.php:29
#: app/Services/Integrations/CRM/TagAppliedTrigger.php:46
msgid "This will run when selected tags have been applied to a contact"
msgstr ""
"Se ejecutará cuando las etiquetas seleccionadas se hayan aplicado a un "
"contacto"

#: app/Services/Integrations/CRM/RemoveFromTagTrigger.php:30
msgid "This will run when selected Tags have been removed from a contact"
msgstr ""
"Esto se ejecutará cuando las Etiquetas seleccionadas hayan sido eliminadas "
"de un contacto"

#: app/Hooks/Handlers/DataExporter.php:548
msgid "Timezone"
msgstr "Zona horaria"

#: app/Hooks/Handlers/DataExporter.php:544
msgid "Title"
msgstr "Título"

#: app/Services/Integrations/Edd/DeepIntegration.php:368
msgid ""
"To sync and enable deep integration with Easy Digital Downloads customers "
"with FluentCRM, please configure and enable sync."
msgstr ""
"Para sincronizar y habilitar la integración profunda con los clientes de "
"Easy Digital Downloads con FluentCRM, por favor configure y habilite la "
"sincronización."

#: app/Services/Integrations/LearnDash/DeepIntegration.php:165
msgid ""
"To sync and enable deep integration with LearnDash students with FluentCRM, "
"please configure and enable sync."
msgstr ""
"Para sincronizar y habilitar la integración profunda de los estudiantes de "
"LearnDash con FluentCRM, configure y habilite la sincronización."

#: app/Services/Integrations/LifterLms/DeepIntegration.php:160
msgid ""
"To sync and enable deep integration with LifterLMS students with FluentCRM, "
"please configure and enable sync."
msgstr ""
"Para sincronizar y habilitar la integración profunda con los estudiantes de "
"LifterLMS con FluentCRM, por favor configure y habilite la sincronización."

#: app/Services/Integrations/TutorLms/DeepIntegration.php:77
msgid ""
"To sync and enable deep integration with TutorLMS students with FluentCRM, "
"please configure and enable sync."
msgstr ""

#: app/Services/Integrations/WooCommerce/DeepIntegration.php:308
msgid ""
"To sync and enable deep integration with WooCommerce customers with "
"FluentCRM, please configure and enable sync."
msgstr ""
"Para sincronizar y habilitar la integración profunda con los clientes de "
"WooCommerce con FluentCRM, por favor configure y habilite la sincronización."

#: app/Services/Integrations/Edd/AdvancedReport.php:94
msgid "to view in details edd reports"
msgstr "para ver en detalle los informes de la edd"

#: app/Services/Integrations/LearnDash/AdvancedReport.php:63
msgid "to view in details LearnDash reports"
msgstr "para ver en detalle los informes de LearnDash"

#: app/Services/Integrations/LifterLms/AdvancedReport.php:63
msgid "to view in details LifterLMS reports"
msgstr "para ver en detalle los informes de LifterLMS"

#: app/Services/Integrations/TutorLms/AdvancedReport.php:60
msgid "to view in details TutorLms reports"
msgstr ""

#: app/Services/Integrations/WooCommerce/AdvancedReport.php:92
msgid "to view in details WooCommerce reports"
msgstr "para ver en detalle los informes de WooCommerce"

#: app/Services/Integrations/LearnDash/TopicCompletedTrigger.php:23
msgid "Topic Completed"
msgstr ""

#: app/Services/Integrations/WooCommerce/WooSmartCodeParse.php:269
#: app/Services/Integrations/Edd/EddSmartCodeParse.php:309
msgid "Total"
msgstr "Total"

#: app/Services/Integrations/Edd/AdvancedReport.php:155
msgid "Total Activated Sites (Licenses)"
msgstr "Total de sitios activados (licencias)"

#: app/Services/Integrations/LifterLms/AdvancedReport.php:73
#: app/Services/Integrations/LearnDash/AdvancedReport.php:73
#: app/Services/Integrations/TutorLms/AdvancedReport.php:70
msgid "Total Course Enrollments"
msgstr "Total de inscripciones a cursos"

#: app/Services/Integrations/WooCommerce/AdvancedReport.php:117
#: app/Services/Integrations/Edd/AdvancedReport.php:119
msgid "Total Customers"
msgstr "Total de clientes"

#: app/Services/Integrations/Edd/EddCommerceHelper.php:182
msgid "Total Lifetime Licenses"
msgstr "Total de licencias de por vida"

#: app/Services/Integrations/LifterLms/AdvancedReport.php:78
#: app/Services/Integrations/LearnDash/AdvancedReport.php:78
msgid "Total Membership Enrollments"
msgstr "Total de Membresías Matriculadas"

#: app/Services/Integrations/WooCommerce/AutomationConditions.php:48
#: app/Services/Integrations/WooCommerce/DeepIntegration.php:203
#: app/Services/Integrations/Edd/AutomationConditions.php:36
#: app/Services/Integrations/Edd/DeepIntegration.php:283
msgid "Total Order Count"
msgstr "Recuento total de pedidos"

#: app/Services/Integrations/WooCommerce/AutomationConditions.php:56
#: app/Services/Integrations/WooCommerce/AutomationConditions.php:163
#: app/Services/Integrations/WooCommerce/DeepIntegration.php:211
#: app/Services/Integrations/Edd/AutomationConditions.php:43
#: app/Services/Integrations/Edd/DeepIntegration.php:290
msgid "Total Order Value"
msgstr "Valor total del pedido"

#: app/Services/Integrations/WooCommerce/AdvancedReport.php:113
#: app/Services/Integrations/Edd/AdvancedReport.php:115
#: app/Services/Integrations/Edd/EddCommerceHelper.php:34
msgid "Total Orders"
msgstr "Total de pedidos"

#: app/Services/Integrations/Edd/EddCommerceHelper.php:187
msgid "Total Recurring Licenses"
msgstr "Total de licencias recurrentes"

#: app/Services/Integrations/AffiliateWP/DeepIntegration.php:73
msgid "Total Referrals"
msgstr "Total de remisiones"

#: app/Services/Integrations/WooCommerce/AdvancedReport.php:108
#: app/Services/Integrations/Edd/AdvancedReport.php:110
msgid "Total Revenue"
msgstr "Ingresos totales"

#: app/Services/Integrations/Edd/EddCommerceHelper.php:26
msgid "Total Sales"
msgstr "Ventas totales"

#: app/Services/Integrations/LifterLms/AdvancedReport.php:68
#: app/Services/Integrations/LearnDash/AdvancedReport.php:68
#: app/Services/Integrations/TutorLms/AdvancedReport.php:65
msgid "Total Students"
msgstr "Total de estudiantes"

#: app/Services/Funnel/Triggers/TrackingEventRecordedTrigger.php:31
#: app/Services/Funnel/Triggers/TrackingEventRecordedTrigger.php:40
msgid "Tracking Event Recorded"
msgstr ""

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:69
#: app/Services/Funnel/Triggers/TrackingEventRecordedTrigger.php:86
#: app/Services/Funnel/Conditions/FunnelCondition.php:50
msgid "True if all conditions match"
msgstr "Verdadero si todas las condiciones coinciden"

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:70
#: app/Services/Funnel/Triggers/TrackingEventRecordedTrigger.php:87
#: app/Services/Funnel/Conditions/FunnelCondition.php:51
msgid "True if any of the conditions match"
msgstr "Verdadero si alguna de las condiciones coincide"

#: app/Services/Integrations/TutorLms/LessonCompletedTrigger.php:23
#: app/Services/Integrations/TutorLms/LessonCompletedTrigger.php:107
#: app/Services/Integrations/TutorLms/CourseCompletedTrigger.php:23
#: app/Services/Integrations/TutorLms/CourseCompletedTrigger.php:107
#: app/Services/Integrations/TutorLms/AutomationConditions.php:17
#: app/Services/Integrations/TutorLms/RemoveFromCourseAction.php:21
#: app/Services/Integrations/TutorLms/DeepIntegration.php:26
#: app/Services/Integrations/TutorLms/DeepIntegration.php:74
#: app/Services/Integrations/TutorLms/Helper.php:115
#: app/Services/Integrations/TutorLms/CourseEnrollTrigger.php:23
#: app/Services/Integrations/TutorLms/CourseEnrollTrigger.php:104
#: app/Services/Integrations/TutorLms/AddToCourseAction.php:21
msgid "TutorLMS"
msgstr "TutorLMS"

#: app/Services/Integrations/TutorLms/AdvancedReport.php:54
msgid "TutorLms - Advanced Reports"
msgstr ""

#: app/Services/Integrations/TutorLms/TutorLmsInit.php:64
msgid "TutorLMS Courses"
msgstr "Cursos de TutorLMS"

#: app/Services/Integrations/TutorLms/DeepIntegration.php:76
msgid "TutorLMS students are not synced with FluentCRM yet."
msgstr ""

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:199
#: app/Services/Integrations/BuddyPress/BBInit.php:108
#: app/Services/Funnel/Conditions/FunnelCondition.php:131
msgid "Type"
msgstr "Tipo"

#: app/Services/Integrations/WooCommerce/AddOrderNoteAction.php:54
msgid ""
"Type the note that you want to add to the reference order. You can also use "
"smart tags"
msgstr ""
"Escriba la nota que desea añadir a la orden de referencia. También puede "
"utilizar etiquetas inteligentes"

#: app/Services/Integrations/AffiliateWP/DeepIntegration.php:95
msgid "Unpaid Earnings"
msgstr "Ingresos no pagados"

#: app/Services/Integrations/CRM/AdvancedReport.php:51
msgid "Unsubscribe Stats"
msgstr ""

#: app/Services/Funnel/Actions/UpdateContactPropertyAction.php:23
#: app/Services/Funnel/Actions/UpdateContactPropertyAction.php:40
msgid "Update Contact Property"
msgstr "Actualizar la propiedad del contacto"

#: app/Services/Funnel/Actions/UpdateContactPropertyAction.php:24
#: app/Services/Funnel/Actions/UpdateContactPropertyAction.php:41
msgid "Update custom fields or few main property of a contact"
msgstr ""
"Actualizar los campos personalizados o algunas propiedades principales de un "
"contacto"

#: app/Services/Funnel/Actions/UpdateUserMetaAction.php:23
#: app/Services/Funnel/Actions/UpdateUserMetaAction.php:41
msgid "Update WordPress User Meta Data"
msgstr "Actualizar los metadatos del usuario de WordPress"

#: app/Services/Funnel/Actions/UpdateUserMetaAction.php:22
#: app/Services/Funnel/Actions/UpdateUserMetaAction.php:40
msgid "Update WP User Meta"
msgstr "Actualizar el Meta de Usuario de WP"

#: app/Hooks/Handlers/DataExporter.php:563
msgid "Updated At"
msgstr "Actualizado en"

#: app/Services/Integrations/WooCommerce/AutomationConditions.php:117
#: app/Services/Integrations/WooCommerce/DeepIntegration.php:275
#: app/Services/Integrations/Edd/AutomationConditions.php:80
#: app/Services/Integrations/Edd/DeepIntegration.php:336
msgid "Used Coupons"
msgstr "Cupones usados"

#: app/Services/Integrations/LearnDash/AddToCourseAction.php:109
msgid "User already in the course"
msgstr "Usuario ya en el curso"

#: app/Services/Integrations/LifterLms/AddToMembershipAction.php:114
#: app/Services/Integrations/LearnDash/AddToGroupAction.php:109
msgid "User already in the group"
msgstr "Usuario ya en el grupo"

#: app/Services/Integrations/LifterLms/AddToCourseAction.php:111
#: app/Services/Integrations/LearnDash/AddToCourseAction.php:118
msgid "User could not be enrolled to the selected course"
msgstr "El usuario no ha podido inscribirse en el curso seleccionado"

#: app/Services/Integrations/TutorLms/AddToCourseAction.php:120
msgid ""
"User could not be enrolled to the selected course. Maybe course is already "
"enrolled or Tutor failed to enroll the course"
msgstr ""
"El usuario no pudo inscribirse en el curso seleccionado. Puede que el curso "
"ya esté inscrito o que el tutor no haya podido inscribir el curso"

#: app/Services/Integrations/LearnDash/RemoveFromCourseAction.php:76
msgid "User could not be removed from the selected course"
msgstr "El usuario no ha podido ser eliminado del curso seleccionado"

#: app/Services/Integrations/LearnDash/RemoveFromCourseAction.php:67
msgid "User does not have this course access"
msgstr "El usuario no tiene acceso a este curso"

#: app/Hooks/Handlers/DataExporter.php:543
msgid "User ID"
msgstr "ID de usuario"

#: app/Services/Funnel/Triggers/UserLoginTrigger.php:25
#: app/Services/Funnel/Triggers/UserLoginTrigger.php:34
msgid "User Login"
msgstr "Inicio de sesión del usuario"

#: app/Services/Funnel/Actions/UpdateUserMetaAction.php:46
#: app/Services/Funnel/Actions/UserRegistrationAction.php:79
msgid "User Meta Key"
msgstr "Meta clave de usuario"

#: app/Services/Funnel/Actions/UpdateUserMetaAction.php:44
#: app/Services/Funnel/Actions/UserRegistrationAction.php:77
msgid "User Meta Mapping"
msgstr "Meta-cartografía del usuario"

#: app/Services/Funnel/Actions/UpdateUserMetaAction.php:47
#: app/Services/Funnel/Actions/UserRegistrationAction.php:80
msgid "User Meta Value"
msgstr "Metavalor de usuario"

#: app/Services/Funnel/Actions/UserRegistrationAction.php:88
msgid "User Notification"
msgstr "Notificación al usuario"

#: app/Services/Funnel/Actions/UserRegistrationAction.php:51
#: app/Services/Funnel/Actions/ChangeUserRoleAction.php:41
#: app/Services/Funnel/Actions/UserRoleRemoveAction.php:38
msgid "User Role"
msgstr "Función del usuario"

#: app/Services/Integrations/Edd/AutomationConditions.php:106
msgid "Valid License"
msgstr "Licencia válida"

#: app/Services/PluginManager/LicenseManager.php:51
msgid "View FluentCRM documentation"
msgstr "Ver documentación de FluentCRM"

#: app/Services/Integrations/LifterLms/DeepIntegration.php:48
#: app/Services/Integrations/WooCommerce/DeepIntegration.php:43
#: app/Services/Integrations/LearnDash/DeepIntegration.php:42
#: app/Services/Integrations/Edd/DeepIntegration.php:42
#: app/Services/Integrations/TutorLms/DeepIntegration.php:42
msgid "View Settings"
msgstr "Ver la configuración"

#: app/Hooks/Handlers/VisualEmailBuilderHandler.php:78
msgid "Visual Builder"
msgstr "Constructor visual"

#: app/Services/Integrations/WishlistMember/WishlistMembershipTrigger.php:23
#: app/Services/Integrations/WishlistMember/WishlistMembershipTrigger.php:101
#: app/Services/Integrations/WishlistMember/AutomationConditions.php:19
msgid "Wishlist Member"
msgstr "Miembro de la lista de deseos"

#: app/Services/Integrations/Edd/DeepIntegration.php:366
msgid ""
"With EDD deep integration with FluentCRM, you easily segment your purchases, "
"lifetime values, purchase dates and target your customers more efficiently."
msgstr ""
"Con la profunda integración de EDD con FluentCRM, podrá segmentar fácilmente "
"las compras, los valores de por vida, las fechas de compra y dirigirse a sus "
"clientes de forma más eficiente."

#: app/Services/Funnel/Actions/HTTPSendDataAction.php:133
msgid "With Headers"
msgstr "Con cabeceras"

#: app/Services/Integrations/LearnDash/DeepIntegration.php:163
msgid ""
"With LearnDash deep integration with FluentCRM, you easily segment your "
"students by their enrollment, course dates and target your students more "
"efficiently."
msgstr ""
"Con la profunda integración de LearnDash con FluentCRM, puedes segmentar "
"fácilmente a tus alumnos por su inscripción, fechas de cursos y dirigirte a "
"tus alumnos de manera más eficiente."

#: app/Services/Integrations/LifterLms/DeepIntegration.php:158
msgid ""
"With LifterLMS deep integration with FluentCRM, you easily segment your "
"students by their enrollment, course dates and target your students more "
"efficiently."
msgstr ""
"Con la profunda integración de LifterLMS con FluentCRM, podrá segmentar "
"fácilmente a sus estudiantes por su inscripción, fechas de cursos y "
"dirigirse a sus estudiantes de manera más eficiente."

#: app/Services/Integrations/TutorLms/DeepIntegration.php:75
msgid ""
"With TutorLMS deep integration with FluentCRM, you easily segment your "
"students by their enrollment, course dates and target your students more "
"efficiently."
msgstr ""

#: app/Services/Integrations/WooCommerce/DeepIntegration.php:306
msgid ""
"With WooCommerce deep integration with FluentCRM, you easily segment your "
"purchases, lifetime values, purchase dates and target your customers more "
"efficiently."
msgstr ""
"Con la profunda integración de WooCommerce con FluentCRM, podrá segmentar "
"fácilmente sus compras, valores de vida, fechas de compra y dirigirse a sus "
"clientes de forma más eficiente."

#: app/Http/Controllers/DynamicSegmentController.php:118
msgid "Within"
msgstr "En"

#: app/Services/Integrations/WooCommerce/AutomationConditions.php:213
msgid "Woo Current Order"
msgstr "Orden actual de Woo"

#: app/Services/Integrations/WooCommerce/WooInit.php:338
msgid "Woo Subscriptions (%d)"
msgstr ""

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:22
#: app/Services/Integrations/WooCommerce/WooSubscriptionStartedTrigger.php:24
#: app/Services/Integrations/WooCommerce/WooOrderCompletedTrigger.php:23
#: app/Services/Integrations/WooCommerce/AutomationConditions.php:154
#: app/Services/Integrations/WooCommerce/WooOrderStatusChangeTrigger.php:23
#: app/Services/Integrations/WooCommerce/AddOrderNoteAction.php:20
#: app/Services/Integrations/WooCommerce/WooSubscriptionRenewalFailedTrigger.php:24
#: app/Services/Integrations/WooCommerce/OrderStatusChangeAction.php:19
#: app/Services/Integrations/WooCommerce/DeepIntegration.php:32
#: app/Services/Integrations/WooCommerce/DeepIntegration.php:198
#: app/Services/Integrations/WooCommerce/DeepIntegration.php:305
#: app/Services/Integrations/WooCommerce/WooOrderRefundedTrigger.php:23
#: app/Services/Integrations/WooCommerce/WooOrderSuccessTrigger.php:25
#: app/Services/Integrations/WooCommerce/CreateCouponAction.php:22
#: app/Services/Integrations/WooCommerce/WooSubscriptionExpiredTrigger.php:24
#: app/Services/Integrations/WooCommerce/WooSubscriptionRenewalPaymentTrigger.php:24
msgid "WooCommerce"
msgstr "WooCommerce"

#: app/Services/Integrations/WooCommerce/AdvancedReport.php:84
msgid "WooCommerce - Advanced Reports"
msgstr "WooCommerce - Informes avanzados"

#: app/Services/DynamicSegments/WooCustomerSegment.php:20
msgid "WooCommerce Customers"
msgstr "Clientes de WooCommerce"

#: app/Services/Integrations/WooCommerce/DeepIntegration.php:307
msgid "WooCommerce customers are not synced with FluentCRM yet."
msgstr "Los clientes de WooCommerce aún no están sincronizados con FluentCRM."

#: app/Services/DynamicSegments/WooCustomerSegment.php:21
msgid "WooCommerce customers who are also in the contact list as subscribed"
msgstr ""
"Clientes de WooCommerce que también están en la lista de contactos como "
"suscritos"

#: app/Services/Integrations/WooCommerce/WooOrderCompletedTrigger.php:40
msgid "WooCommerce Order has been completed"
msgstr "El pedido de WooCommerce se ha completado"

#: app/Services/Integrations/WooCommerce/WooOrderRefundedTrigger.php:40
msgid "WooCommerce Order has been refunded"
msgstr "El pedido de WooCommerce ha sido reembolsado"

#: app/Services/Integrations/WooCommerce/WooOrderStatusChangeTrigger.php:40
msgid "WooCommerce Order Status Changed"
msgstr "Estado del pedido en WooCommerce cambiado"

#: app/Services/Integrations/WooCommerce/WooSubscriptionRenewalFailedTrigger.php:71
msgid "WooCommerce Renewal Payment failed"
msgstr ""

#: app/Services/Integrations/WooCommerce/WooSubscriptionRenewalPaymentTrigger.php:71
msgid "WooCommerce Renewal Payment received"
msgstr ""

#: app/Services/Funnel/Actions/UpdateUserMetaAction.php:21
#: app/Services/Funnel/Actions/UserRegistrationAction.php:22
#: app/Services/Funnel/Actions/ChangeUserRoleAction.php:20
#: app/Services/Funnel/Actions/UserRoleRemoveAction.php:20
msgid "WordPress"
msgstr "WordPress"

#: app/Services/Funnel/Triggers/UserLoginTrigger.php:24
msgid "WordPress Triggers"
msgstr "Activadores de WordPress"

#: app/Services/Integrations/LifterLms/AddToMembershipAction.php:62
#: app/Services/Integrations/LifterLms/AddToCourseAction.php:63
#: app/Services/Integrations/LearnDash/AddToGroupAction.php:63
#: app/Services/Integrations/LearnDash/AddToCourseAction.php:63
#: app/Services/Integrations/TutorLms/AddToCourseAction.php:62
msgid ""
"WordPress user will be created if no user found with the contact's email "
"address"
msgstr ""
"Se creará un usuario de WordPress si no se encuentra un usuario con la "
"dirección de correo electrónico del contacto"

#: app/Services/DynamicSegments/WpUserSegment.php:20
msgid "WordPress Users"
msgstr "Usuarios de WordPress"

#: app/Services/DynamicSegments/WpUserSegment.php:21
msgid "WordPress users who are also in the contact list as subscribed"
msgstr ""
"Usuarios de WordPress que también están en la lista de contactos como "
"suscritos"

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:194
#: app/Services/Funnel/Conditions/FunnelCondition.php:126
msgid "WP User ID"
msgstr "ID de usuario de WP"

#: app/Modules/AbandonCart/Woo/AbandonCartAutomationTrigger.php:253
#: app/Services/Funnel/Conditions/FunnelCondition.php:185
msgid "WP User Role"
msgstr "Rol de usuario de WP"

#: app/Services/Integrations/AffiliateWP/DeepIntegration.php:61
msgid "Yes"
msgstr "Sí"

#: app/Http/Controllers/CampaignsProController.php:100
msgid "You can do this action if campaign is in archived status only"
msgstr ""
"Puede realizar esta acción si la campaña está en estado archivado solamente"

#: app/Services/Integrations/AffiliateWP/DeepIntegration.php:224
msgid ""
"You can sync all your Affiliates into FluentCRM. After this sync you can "
"segment your contacts easily"
msgstr ""
"Puede sincronizar todos sus afiliados en FluentCRM. Después de esta "
"sincronización puede segmentar sus contactos fácilmente"

#: app/Services/Integrations/Edd/EddImporter.php:151
msgid ""
"You can sync all your Easy Digital Downloads Customers into FluentCRM and "
"all future customers and purchase data will be synced."
msgstr ""
"Puede sincronizar todos sus clientes de Easy Digital Downloads en FluentCRM "
"y todos los futuros clientes y datos de compra se sincronizarán."

#: app/Services/Integrations/WooCommerce/WooImporter.php:157
msgid ""
"You can sync all your WooCommerce Customers into FluentCRM and all future "
"customers and purchase data will be synced."
msgstr ""
"Puede sincronizar todos sus clientes de WooCommerce en FluentCRM y todos los "
"futuros clientes y datos de compra serán sincronizados."

#: app/Services/Integrations/LifterLms/LifterInit.php:87
msgid "You do not have permission to access this student\\'s reports"
msgstr "No tiene permiso para acceder a los informes de este estudiante"

#: app/Modules/AbandonCart/Woo/WooCartTrackingInit.php:144
msgid "You have opted out from cart tracking"
msgstr ""

#: app/Services/Integrations/LearnDash/TopicCompletedTrigger.php:86
#: app/Services/Integrations/LearnDash/LessonCompletedTrigger.php:86
msgid "You must select a course"
msgstr "Debe seleccionar un curso"

#: app/Services/Integrations/LearnDash/TopicCompletedTrigger.php:93
msgid "You must select a topic"
msgstr "Debe seleccionar un tema"

#: app/Modules/AbandonCart/Woo/WooCartTrackingInit.php:451
msgid "Your cart has been restored"
msgstr ""

#: app/Http/Controllers/LicenseController.php:57
msgid "Your license key has been successfully deactivated"
msgstr "Su clave de licencia ha sido desactivada con éxito"

#: app/Http/Controllers/LicenseController.php:40
msgid "Your license key has been successfully updated"
msgstr "Su clave de licencia ha sido actualizada con éxito"

#: app/Services/Funnel/Benchmarks/LinkClickBenchmark.php:65
msgid "Your Target URL"
msgstr "Su URL de destino"
